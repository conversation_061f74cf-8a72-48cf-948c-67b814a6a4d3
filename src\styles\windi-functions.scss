@mixin color-darken($color, $amount) {
  color: darken($color, $amount);
}

@mixin color-lighten($color, $amount) {
  color: lighten($color, $amount);
}
//透明
@mixin color-transparentize($color, $amount) {
  color: transparentize($color, $amount);
}

@mixin background-color-transparentize($color, $amount) {
  background-color: transparentize($color, $amount);
}

@mixin background-color-darken($color, $amount) {
  background-color: darken($color, $amount);
}

@mixin background-color-lighten($color, $amount) {
  background-color: lighten($color, $amount);
}

@mixin background-color-transparentize($color, $amount) {
  background-color: transparentize($color, $amount);
}

// 等等其他颜色函数
