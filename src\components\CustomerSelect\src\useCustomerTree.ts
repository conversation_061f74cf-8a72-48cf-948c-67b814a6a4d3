/**
@Author:kai-feng.tan
@Date:2024-09-18
@Description:客户树Hook
*/
import { getCustomerPageApi, getCustomerInfoApi, getUsersByCusCode } from './api'
import { ref } from 'vue'

import { recursionSortListFn } from './pinyin'
//import { flatMap } from 'lodash-es'

/**客户树VO */
export interface CustomerTreeVO {
  parentId: string
  customerId: string
  customerName: string
  customerCode: string
  k3CustomerCode: string
  relatedCusId: string
  children: CustomerTreeVO[]
}

/**发送到后端的VO */
export interface CustomerServerVO extends CustomerTreeVO {
  isHeadBank: boolean
  subBankIds: string[]
  allBankIds: Object
}

export const useCustomerTree = () => {
  const list = ref<CustomerTreeVO[]>([])
  const currentList = ref({
    total: 0,
    lsit: []
  })
  /**
   * 键为 customerId,值是CustomerTreeVO
   */
  const dicCustomer = ref<Map<string, CustomerTreeVO>>(new Map()) //键为 customerId,值是CustomerTreeVO
  const customerSelectLoading = ref<boolean>(false)

  const searchCustomerList = async (_searchData?: any, preOption = []) => {
    if (customerSelectLoading.value) return
    customerSelectLoading.value = true
    try {
      let params: any = {}
      if (_searchData) {
        params = {
          ..._searchData
        }
      } else {
        params = {
          customerName: '',
          pageSize: 10,
          pageNum: 1
        }
      }

      const res = await getCustomerPageApi(params)

      const tempList = res.list || []

      //通过搜索条件过滤掉银行
      // if (_searchData.customerName) {
      //   tempList = tempList.filter((item) => item.customerName.includes(_searchData.customerName))
      // }

      currentList.value = res //当前页数据

      //翻页的时候需要将上一次搜索出来的数据和本次搜索出来的数据进行拼接
      let preSortList = params.pageNum === 1 ? [...tempList] : [...list.value, ...tempList]

      if (preOption.length > 0) {
        const hasMap: any = []
        preSortList.forEach((item) => {
          preOption.map((el: any) => {
            if (item.customerId === el.customerId) {
              hasMap.push(el.customerId)
            }
          })
        })
        // 没有在搜索出来的数据里面的数组
        const hasNoList =
          hasMap.length === 0
            ? preOption
            : preOption.filter((el: any) => {
                return hasMap.indexOf(el.customerId) === -1
              })
        preSortList = [...hasNoList, ...preSortList]
      }

      //进行拼音首字母的排序
      list.value = recursionSortListFn(preSortList, 'customerName')
      // list.value = preSortList
      // handleList(list.value)
      initDic(list.value)
    } catch (err) {
      list.value = []
    } finally {
      customerSelectLoading.value = false
      console.log('done')
    }
  }

  /**
   * 处理树形结构列表，将每个节点的expand属性设置为false，并根据是否有子节点设置isLeaf属性
   *
   * @param tree 树形结构列表
   */
  function handleList(tree) {
    // 遍历树中的每个节点
    tree.forEach((node) => {
      // 将当前节点的expand属性设置为false
      node['expanded'] = false
      node['isLeaf'] = node?.children?.length < 1

      // 如果当前节点有子节点，则递归处理子节点
      if (node.hasOwnProperty('children') && Array.isArray(node.children)) {
        handleList(node.children)
      }
    })
  }

  /**
   * 初始化客户字典dicCustomer
   *
   * @param list 客户树形结构数据数组
   */
  function initDic(list: CustomerTreeVO[]) {
    // dicCustomer.value.clear()
    for (const item of list) {
      dicCustomer.value.set(item.customerId, item)
      for (const subItem of item.children) {
        dicCustomer.value.set(subItem.customerId, subItem)
      }
    }
  }

  const findCustomer = (customerId: string) => {
    return dicCustomer.value.get(customerId)
  }

  /**
   * 通过CustomerTreeVO对象获取CustomerServerVO对象
   *
   * @param customer CustomerTreeVO类型的参数
   * @returns 返回CustomerServerVO类型的对象
   */
  const getCustomerServerVOByObj = (customer: CustomerTreeVO) => {
    if (!customer) return
    const retObj = customer as CustomerServerVO
    if (customer?.children && customer?.children.length > 0) {
      //总行
      retObj.isHeadBank = true
      retObj.subBankIds = []
      customer.children.forEach((item) => {
        retObj.subBankIds.push(item.customerId)
      })
    } else {
      retObj.isHeadBank = false
      retObj.subBankIds = []
    }

    return retObj
  }
  /**
   * 获取所有客户的映射关系
   *
   * @returns CustomerServerVO对象，包含allBankIds和subBankIds属性
   */
  const getAllCustomerMap = () => {
    const retObj = {} as CustomerServerVO
    const tmpList = list.value
    const tmpMap = new Map<string, string[]>()
    tmpList.forEach((item) => {
      let arr = tmpMap.get(item.customerName)
      if (arr == null) {
        arr = []
        tmpMap.set(item.customerName, arr)
      }

      item.children.forEach((subItem) => {
        arr?.push(subItem.customerId)
      })
    })

    const obj = {}
    tmpMap.forEach((value, key) => {
      obj[key] = value
    })

    retObj.allBankIds = obj
    retObj.subBankIds = []
    return retObj
  }

  /**
   * 根据客户ID获取客户信息
   *
   * @param id 客户ID
   * @returns 客户信息对象
   */
  const getCustomerInfoById = async (id: string) => {
    const res = await getCustomerInfoApi({ customerId: id })
    return res
  }

  /**
   * 通过客户代码获取客户ID列表
   *
   * @param code 客户代码
   * @returns 返回一个包含客户ID的字符串数组
   */
  const getCustomerIdByCode = async (code: string): Promise<string[]> => {
    if (!code) return []
    const res = await getUsersByCusCode(code)
    return res as string[]
  }

  return {
    customerList: list,
    findCustomer,
    searchCustomerList,
    getCustomerInfoById,
    getCustomerIdByCode,
    customerSelectLoading,
    getCustomerServerVOByObj,
    getAllCustomerMap,
    initDic,
    dicCustomer,
    currentList
  }
}
