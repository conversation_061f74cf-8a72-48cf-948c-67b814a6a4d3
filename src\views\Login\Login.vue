<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-25 11:47:52
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-06-09 09:55:06
 * @Description:   登录弹出框
-->
<template>
  <Transition
    appear
    enter-active-class="animate__animated animate__bounceInRight animate__fadeIn"
    leave-active-class="animate__fadeOut animate__animated"
  >
    <div
      v-show="ifShowLoginForm"
      class="login-main z-1111 bg-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100%] @2xl:max-w-600px @xl:max-w-600px @md:max-w-600px @lg:max-w-600px"
    >
      <el-icon :size="20" class="!absolute top-5 right-8 cursor-pointer z-100">
        <Close @click="ifShowLoginForm = false" />
      </el-icon>

      <!-- 账号登录 -->
      <LoginForm
        class="p-20px h-auto m-auto <xl:(rounded-3xl light:bg-white)"
        @change-form-type="changeFormType"
        v-show="loginType === LOGIN_TYPE.LOGIN"
        ref="loginForm"
      />
      <!-- 忘记密码 -->
      <ForgetPasswordForm
        class="p-20px h-auto m-auto <xl:(rounded-3xl light:bg-white)"
        @change-form-type="changeFormType"
        v-show="loginType === LOGIN_TYPE.FORGET_PASSWORD"
        ref="ForgetPasswordFormRef"
      />
      <ResetForm
        class="p-20px h-auto m-auto <xl:(rounded-3xl light:bg-white)"
        :code="code"
        :email="email"
        @change-form-type="changeFormType"
        v-show="loginType === LOGIN_TYPE.RESET_PASSWORD"
      />
    </div>
  </Transition>

  <!-- 蒙板 -->
  <teleport to="body">
    <div class="modal-mask" v-show="ifShowLoginForm"></div>
  </teleport>
</template>
<script setup lang="ts">
defineOptions({
  name: 'Login'
})

import { useI18n } from '@/hooks/web/useI18n'
import { useDesign } from '@/hooks/web/useDesign'
import { useCache } from '@/hooks/web/useCache'
import { Close } from '@element-plus/icons-vue'
import { ForgetPasswordForm, LoginForm, ResetForm } from './components'
import { LOGIN_TYPE, AccessTokenKey, RefreshTokenKey } from './components/useLogin'
// 登录页面重置用户信息
import * as authUtil from '@/utils/auth'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

onMounted(() => {
  authUtil.removeTenantId()
  authUtil.removeToken()
  userStore.resetState()
})

// 组合式API调用
const { t } = useI18n()
const { getPrefixCls } = useDesign()
const { wsCache } = useCache()
const prefixCls = getPrefixCls('login')

// 组件状态
const loginType = ref<number>(LOGIN_TYPE.LOGIN)
const ifShowLoginForm = ref<boolean>(false)
const loginForm = ref<InstanceType<typeof LoginForm> | null>(null)
const ForgetPasswordFormRef = ref<InstanceType<typeof ForgetPasswordForm> | null>(null)
const code = ref<string>('')
const email = ref<string>('')

// 表单类型切换处理
const changeFormType = async (type: number) => {
  if (type === LOGIN_TYPE.RESET_PASSWORD) {
    // 重置密码表单需要获取验证码和邮箱信息(只有是通过忘记密码后重置密码,才需要拿忘记密码表单里面的code和email)
    if (ForgetPasswordFormRef.value?.loginData?.loginForm) {
      code.value = ForgetPasswordFormRef.value.loginData.loginForm.code || ''
      email.value = ForgetPasswordFormRef.value.loginData.loginForm.email || ''
    }
  }
  loginType.value = type
}

/**
 * 显示登录表单
 */
const showLoginForm = () => {
  loginType.value = LOGIN_TYPE.LOGIN
  ifShowLoginForm.value = true
}
// 对外暴露方法
defineExpose({ showLoginForm })

// 首次登录信息监听
const firstLoginInfo = computed(() => sessionStorage.getItem('firstLoginInfo'))

watch(
  firstLoginInfo,
  (newVal) => {
    if (newVal) {
      loginType.value = LOGIN_TYPE.RESET_PASSWORD
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-login;

.bg-white {
  background-color: white;
}

// 蒙板
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.el-button) {
  &:hover {
    border-color: var(--client-color-primary-light-3);
    background-color: var(--client-color-primary-light-7);
  }
}
</style>
