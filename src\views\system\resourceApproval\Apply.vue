<template>
  <div class="resource-approval">
    <!-- <ContentWrap>
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item :label="t('res.approval.name')" prop="name">
          <el-input
            v-model="queryParams.code"
            class="!w-240px"
            clearable
            :placeholder="t('res.approval.codePlaceholder')"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <icon-ep-search class="mr-5px" />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <icon-ep-refresh class="mr-5px" style="font-size: 12px" />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap> -->
    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list">
        <el-table-column type="expand">
          <template #default="props">
            <el-table :data="props.row.showResources" :border="true">
              <el-table-column align="center" :label="t('res.approval.scene')" prop="scene">
                <template #default="{ row }">
                  {{ systemBusinessSceneMap[row.scene] }}
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                :label="t('res.approval.permissionTypes')"
                prop="permissionTypes"
              >
                <template #default="{ row }">
                  {{ row.permissionTypes.map((el) => permissionPointMap[el]).join() }}
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="t('res.approval.applicationNo')"
          prop="applicationNo"
        />
        <el-table-column align="center" :label="'资源编号'" prop="exposeNo" />
        <el-table-column
          align="center"
          :label="t('res.approval.roleName')"
          prop="roleExpose.name"
        />
        <el-table-column align="center" :label="'申请方'" prop="applicantName" />
        <el-table-column align="center" :label="'授权方'" prop="authorizerName" />

        <el-table-column
          align="center"
          :label="t('res.approval.applicationReason')"
          prop="applicationReason"
        />
        <el-table-column align="center" :label="'授权意见'" width="100px" prop="approvalOpinion" />
        <el-table-column
          align="center"
          :label="t('res.approval.authStatusName')"
          prop="authStatusName"
        />
      </el-table>
      <!-- 分页 -->
      <!-- <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      /> -->
    </ContentWrap>
    <Dialog :title="t('res.approval.applicationReason')" v-model="visible" width="600">
      <el-input
        v-model="applicationReason"
        :placeholder="t('res.approval.applicationReasonPlaceholder')"
        type="textarea"
      />
      <template #footer>
        <el-button type="primary" @click="application">{{ t('common.ok') }}</el-button>
        <el-button @click="close">{{ t('common.cancel') }}</el-button>
      </template>
    </Dialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'resourceApproval'
})

import {
  getResourceExposePage,
  resourceRowVO,
  updateTag,
  getDataPermissionScope,
  getResourceApplyOfTenantPage
} from '@/api/system/resource'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
const { t } = useI18n() // 国际化
const loading = ref(false)

const list: Ref<Array<resourceRowVO>> = ref([])

const queryParams = ref({
  // pageNo: 1,
  // pageSize: 10,
  code: undefined
})

// 转业务场景为map，查名字用
const systemBusinessSceneMap = computed(() => {
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

const permissionPointMap = computed(() => {
  return getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

const total = ref(0)

const handleQuery = async () => {
  try {
    loading.value = true
    const res = await getResourceApplyOfTenantPage({ ...queryParams.value })
    total.value = res.total
    const tempList = res.list
    list.value = tempList.map((el) => {
      let resourcesMap = {}
      let showResources = []
      el.resourceExposes.forEach((e) => {
        if (!resourcesMap[e.scene]) {
          resourcesMap[e.scene] = [e.permissionType]
        } else {
          resourcesMap[e.scene].push(e.permissionType)
        }
      })
      for (let key in resourcesMap) {
        showResources.push({
          scene: key,
          permissionTypes: resourcesMap[key]
        })
      }
      return {
        ...el,
        showResources
      }
    })
  } catch (e) {
  } finally {
    loading.value = false
  }
}

const resetQuery = () => {
  queryParams.value = {
    // pageNo: 1,
    // pageSize: 10,
    code: undefined
  }
}

const visible = ref(false)

const activeRow = ref()

// 申请理由
const applicationReason = ref()

// 用户自身的权限信息
const ownDataPermissionScope = ref()

// 打开申请窗口
const showApplication = (row) => {
  activeRow.value = row
  visible.value = true
}

const close = () => {
  applicationReason.value = undefined
  visible.value = false
}

// 申请资源
const application = async () => {
  try {
    loading.value = true
    await updateTag({
      ...ownDataPermissionScope.value,
      exposeNo: activeRow.value.exposeNo,
      applicationReason: applicationReason.value
    })
    close()
  } finally {
    loading.value = false
  }
}

// 获取当前用户的数据权限
const getOwnDataPermissionScope = async () => {
  try {
    const res = await getDataPermissionScope()
    ownDataPermissionScope.value = {
      authorizer: '49003',
      defaultResourceHolder: 1000
    }
  } catch {
    ownDataPermissionScope.value = {
      authorizer: '49003',
      defaultResourceHolder: 1000
    }
  } finally {
  }
}

onMounted(() => {
  handleQuery()
  getOwnDataPermissionScope()
})
</script>
