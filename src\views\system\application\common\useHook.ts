/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-16 16:25:38
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2025-02-17 12:07:12
 * @Description:
 */
import { ClientVO } from '@/store/modules/user'
import { getTenantClientPage } from '@/api/system/businessSide'

export const useHook = () => {
  //获取客户端列表
  const clientList: Ref<Array<ClientVO>> = ref([])
  const getClientList = async () => {
    // 后端限制最大100
    const res = await getTenantClientPage({ pageNo: 1, pageSize: 100 })
    clientList.value = res.list
  }
  /**
   * 根据客户端ID获取客户端名称
   *
   * @param id 客户端ID
   * @returns 返回客户端名称，如果未找到则返回undefined
   */
  const getClientNameById = (id: number) => {
    return clientList.value.find((item) => item.id === id)?.name
  }

  /**
   * 根据客户端ID获取客户端代码
   *
   * @param id 客户端ID
   * @returns 客户端代码，若未找到则返回undefined
   */
  const getClientCodeById = (id: number) => {
    return clientList.value.find((item) => item.id === id)?.code
  }

  return {
    clientList,
    getClientList,
    getClientNameById,
    getClientCodeById
  }
}
