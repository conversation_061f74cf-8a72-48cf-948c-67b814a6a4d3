export default {
  welcome: 'Welcome to the system',
  message: 'Backstage management system',
  tenantname: '<PERSON>ant<PERSON><PERSON>',
  username: '<PERSON><PERSON><PERSON>',
  password: 'Password',
  code: 'verification code',
  login: 'Sign in',
  relogin: 'Sign in again',
  otherLogin: 'Sign in with',
  register: 'Register',
  checkPassword: 'Confirm password',
  remember: 'Remember me',
  hasUser: 'Existing account? Go to login',
  forgetPassword: 'Forget password?',
  tenantNamePlaceholder: 'Please Enter Tenant Name',
  usernamePlaceholder: 'Please Enter Username',
  passwordPlaceholder: 'Please Enter Password',
  codePlaceholder: 'Please Enter Verification Code',
  mobileTitle: 'Mobile sign in',
  mobileNumber: 'Mobile Number',
  mobileNumberPlaceholder: 'Plaease Enter Mobile Number',
  backLogin: 'back',
  getSmsCode: 'Get SMS Code',
  btnMobile: 'Mobile sign in',
  btnQRCode: 'QR code sign in',
  qrcode: 'Scan the QR code to log in',
  btnRegister: 'Sign up',
  SmsSendMsg: 'code has been sent',
  forgetPwd: 'Forget Password',
  emailPlaceholder: "Please enter the account, such as *******{'@'}goldpac.com",
  pleaseInput: 'Please enter',
  next: 'Next',
  resend: 'Resend',
  secdSuccess: 'Send Successfully',
  account: 'Account',
  sendCode: 'Send Verification Code',
  pwdCheck: 'Please enter 6-18 digits, letters, symbols (not including spaces) password',
  findNone: ' Tenant information not found',
  loadingSystem: 'Loading system...',
  nowaySetting: 'This method is not configured',
  reget: 'Can re-get in seconds',
  resetPwd: 'Reset Password',
  pwdSet: 'Set 6-18 digits, letters, symbols (no spaces) password',
  confirmPwd: 'Confirm Password',
  thiredPartyGetAuth: 'This third-party app requests the following permissions:',
  agreeAuth: 'Agree to authorize',
  authing: 'Authorizing...',
  refuse: 'Refuse',
  getYourInfo: 'Access your personal information',
  changeYourInfo: 'Modify your personal information',
  sendCardService: 'DIY Cloud Card Issuance Service',
  introOne:
    'Provide card face, card number, card level, benefits and other customized services for banks by integrating AI, 3D, VR and other technologies.',
  introTwo:
    'Personalized card face customization: DIY puzzle, select beautiful pictures, password customization, upload photos',
  introThree:
    'Exclusive lucky number customization: parent-child numbers, romantic numbers, commemorative numbers, business travel lucky numbers',
  introFour:
    'Quality benefit selection: quality benefits, free combination, package selection, flexibility and affordability',
  introFive:
    'Cost allocation report: Support multi-level cost allocation within banks or industries',
  introSix: 'Data statistics and analysis: User data analysis to assist operations decision-making',
  introSeven: 'Image review: Provide professional image review services',
  loginError: 'Login Error',
  noClient:
    'The user does not have permission for the current interaction endpoint, please contact the administrator!',
  noClientLoginOut:
    'The user does not have permission for the current interaction endpoint, would you like to return to the login page?',
  thirdplatformLogin: 'third platform login',
  thirdplatformLoginGoogle: 'google login'
}
