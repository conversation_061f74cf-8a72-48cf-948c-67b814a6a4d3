<template>
  <!-- 列表 -->
  <UmvContent>
    <UmvQuery v-model="queryParams" :opts="queryOpts" @check="handleQuery" @reset="handleQuery" />

    <UmvTable v-loading="loading" :data="list" :columns="columns" ref="tableRef" @refresh="getList">
      <template #tools>
        <el-button
          plain
          type="primary"
          size="small"
          @click="openForm('create')"
          v-hasPermi="['system:oauth2-client:create']"
        >
          <icon-ep-plus style="font-size: 12px" class="mr-5px" /> 新增
        </el-button>
      </template>

      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>
    <!-- 表单弹窗：添加/修改 -->
    <ClientForm ref="formRef" @success="getList" />
  </UmvContent>
</template>
<script lang="tsx" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as ClientApi from '@/api/system/oauth2/client'
import ClientForm from './ClientForm.vue'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { checkPermi } from '@/utils/permission'
import UmvContent from '@/components/UmvContent'

defineOptions({ name: 'SystemOAuth2Client' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: '',
  status: undefined
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '应用名',
    defaultVal: '',
    controlRender: (form) => <el-input v-model={form.name} placeholder="请输入应用名" clearable />
  },
  status: {
    label: '状态',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.status} placeholder="请选择状态" clearable>
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'clientId', label: '客户端编号', align: 'center' },
  { prop: 'secret', label: '客户端密钥', align: 'center' },
  { prop: 'name', label: '应用名', align: 'center' },
  {
    prop: 'logo',
    label: '应用图标',
    align: 'center',
    renderTemplate: (scope) => <img width="40px" height="40px" src={scope.row.logo} />
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    renderTemplate: (scope) => <dict-tag type={DICT_TYPE.COMMON_STATUS} value={scope.row.status} />
  },
  {
    prop: 'accessTokenValiditySeconds',
    label: '访问令牌的有效期',
    align: 'center',
    renderTemplate: (scope) => <span>{scope.row.accessTokenValiditySeconds} 秒</span>
  },
  {
    prop: 'refreshTokenValiditySeconds',
    label: '刷新令牌的有效期',
    align: 'center',
    renderTemplate: (scope) => <span>{scope.row.refreshTokenValiditySeconds} 秒</span>
  },
  {
    prop: 'authorizedGrantTypes',
    label: '授权类型',
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        {scope.row.authorizedGrantTypes.map((authorizedGrantType, index) => (
          <el-tag disableTransitions={true} key={index} class="mr-5px">
            {authorizedGrantType}
          </el-tag>
        ))}
      </div>
    )
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        {checkPermi(['system:oauth2-client:update']) && (
          <el-button link type="primary" onClick={() => openForm('update', scope.row.id)}>
            编辑
          </el-button>
        )}
        {checkPermi(['system:oauth2-client:delete']) && (
          <el-button link type="danger" onClick={() => handleDelete(scope.row.id)}>
            删除
          </el-button>
        )}
      </div>
    )
  }
])

const tableRef = ref() // 表格引用

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ClientApi.getOAuth2ClientPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ClientApi.deleteOAuth2Client(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
