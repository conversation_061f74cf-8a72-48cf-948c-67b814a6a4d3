<template>
  <el-descriptions border class="w-[600px]">
    <el-descriptions-item :label="t('system.user.username')" width="100">
      <p class="w-35">{{ route.query.username }}</p>
    </el-descriptions-item>
    <el-descriptions-item :label="t('system.user.nickname')" width="100">
      <p class="w-35"> {{ route.query.nickname }} </p>
    </el-descriptions-item>
  </el-descriptions>

  <ContentWrap ifTable>
    <!-- 搜索条件 -->
    <el-form :model="queryParams" ref="queryFormRef" inline>
      <el-form-item :label="t('system.role.name')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="t('system.role.name')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item :label="t('system.role.code')" prop="code">
        <el-input
          v-model="queryParams.code"
          :placeholder="t('system.role.code')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <!-- 新增应用搜索 -->
      <el-form-item :label="t('system.role.applicationCode')" prop="applicationCode">
        <el-select
          v-model="queryParams.applicationCode"
          class="!w-200px"
          clearable
          :placeholder="t('system.role.applicationCodePlaceholder')"
        >
          <el-option
            v-for="dict in tenantAppList"
            :key="dict.applicationCode"
            :label="dict.name"
            :value="dict.applicationCode"
          />
        </el-select>
      </el-form-item>
      <!-- 新增角色类型搜索 -->
      <el-form-item :label="t('system.role.type')" prop="types">
        <el-select
          v-model="queryParams.types"
          class="!w-200px"
          clearable
          :placeholder="t('common.selectText') + t('system.role.type')"
        >
          <el-option
            v-for="(item, index) in typesOptions"
            :label="item.label"
            :value="item.value"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" plain @click="handleQuery">
          <icon-ep-search style="font-size: 12px; margin-right: 2px" />{{ t('common.query') }}
        </el-button>
        <el-button @click="resetQuery">
          <icon-ep-refresh style="font-size: 12px; margin-right: 2px" />{{ t('common.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
    <div class="mb-1">
      <el-button type="primary" plain @click="openAddRoleDialog">
        <icon-ep-plus style="font-size: 12px; margin-right: 2px" />{{ t('system.role.addRole') }}
      </el-button>
      <el-button
        type="danger"
        plain
        :disabled="selectedRoles.length === 0"
        @click="batchRemoveRoles"
      >
        <icon-ep-delete style="font-size: 12px; margin-right: 2px" />{{
          t('system.role.batchDelete')
        }}
      </el-button>
    </div>

    <!-- 用戶已分配角色列表 -->
    <el-table border v-loading="loading" :data="roleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column :label="t('system.role.id')" prop="id" width="100" show-overflow-tooltip />
      <el-table-column :label="t('system.role.name')" prop="name" />
      <el-table-column :label="t('system.role.code')" prop="code" />
      <el-table-column
        :label="t('system.role.applicationCode')"
        prop="applicationCode"
        width="150 "
      >
        <template #default="scope">
          <span>{{ getAppName(scope.row.applicationCode) }}</span>
        </template>
      </el-table-column>
      <!-- 新增角色类型列 -->
      <el-table-column :label="t('system.role.type')" prop="type" width="100">
        <template #default="scope">
          <span>{{ roleTypeEnum[scope.row.type] }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('system.role.status')" prop="status" width="80" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
            {{ scope.row.status === 0 ? t('common.enable') : t('common.disable') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('common.createTime')"
        prop="createTime"
        :formatter="dateFormatter"
        width="180"
        align="center"
      />
      <el-table-column :label="t('common.operate')" width="100" fixed="right" align="center">
        <template #default="scope">
          <el-button type="danger" link @click="removeRole(scope.row)">
            {{ t('system.role.cancelAssign') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #pagination>
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getAssignedRoleList"
      />
    </template>
  </ContentWrap>

  <!-- 添加角色弹窗 -->
  <el-drawer v-model="dialogVisible" :title="t('system.role.addRole')" size="1200px">
    <el-form :model="unassignedQueryParams" inline>
      <el-form-item :label="t('system.role.name')">
        <el-input
          class="!w-180px"
          v-model="unassignedQueryParams.name"
          clearable
          @keyup.enter="getUnassignedRoleList"
        />
      </el-form-item>
      <el-form-item :label="t('system.role.code')">
        <el-input
          class="!w-180px"
          v-model="unassignedQueryParams.code"
          clearable
          @keyup.enter="getUnassignedRoleList"
        />
      </el-form-item>
      <el-form-item :label="t('system.role.applicationCode')">
        <el-select
          v-model="unassignedQueryParams.applicationCode"
          clearable
          :placeholder="t('system.role.applicationCodePlaceholder')"
        >
          <el-option
            v-for="dict in tenantAppList"
            :key="dict.applicationCode"
            :label="dict.name"
            :value="dict.applicationCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('system.role.type')">
        <el-select
          v-model="unassignedQueryParams.types"
          clearable
          :placeholder="t('common.selectText') + t('system.role.type')"
        >
          <el-option
            v-for="(item, index) in typesOptions"
            :label="item.label"
            :value="item.value"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getUnassignedRoleList">
          <icon-ep-search style="font-size: 12px; margin-right: 2px" />{{ t('common.query') }}
        </el-button>
        <el-button @click="resetUnassignedQuery">
          <icon-ep-refresh style="font-size: 12px; margin-right: 2px" />{{ t('common.reset') }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="unassignedLoading"
      :data="unassignedRoleList"
      row-key="code"
      ref="unassignedTableRef"
      @selection-change="handleUnassignedSelectionChange"
    >
      <el-table-column type="selection" width="55" :reserve-selection="true" />
      <el-table-column :label="t('system.role.id')" prop="id" width="80" show-overflow-tooltip />
      <el-table-column :label="t('system.role.name')" prop="name" />
      <el-table-column :label="t('system.role.code')" prop="code" />
      <el-table-column :label="t('system.role.applicationCode')" prop="applicationCode" width="150">
        <template #default="scope">
          <span>{{ getAppName(scope.row.applicationCode) }}</span>
        </template>
      </el-table-column>
      <!-- 新增角色类型列 -->
      <el-table-column :label="t('system.role.type')" prop="type" width="100">
        <template #default="scope">
          <span>{{ roleTypeEnum[scope.row.type] }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('system.role.status')" prop="status" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
            {{ scope.row.status === 0 ? t('common.enable') : t('common.disable') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('common.createTime')"
        prop="createTime"
        :formatter="dateFormatter"
        width="180"
      />
    </el-table>

    <Pagination
      :total="unassignedTotal"
      v-model:page="unassignedQueryParams.pageNo"
      v-model:limit="unassignedQueryParams.pageSize"
      @pagination="getUnassignedRoleList"
    />

    <template #footer>
      <div class="flex justify-end">
        <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
        <el-button
          type="primary"
          @click="assignRoles"
          :disabled="selectedUnassignedRoles.length === 0"
        >
          {{ t('system.role.save') }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SystemUserRole'
})

import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useMessage } from '@/hooks/web/useMessage'
import { dateFormatter } from '@/utils/formatTime'
import { listSimpleAppData } from '@/api/system/apply'
import {
  getRoleListOfUser,
  getRoleListNotOfUser,
  setRolesOfUser,
  removeRolesOfUser
} from './common/api'
import { NotOfUserRes, OfUserRes } from './common/types'

// ========== 基础设置 ==========
const route = useRoute()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// ========== 用户信息 ==========
const userId = ref(route.params.userId)

// ========== 应用列表 ==========
const tenantAppList = ref<any>([])
const getTenantList = async () => {
  if (tenantAppList.value.length === 0) {
    tenantAppList.value = await listSimpleAppData({ pageNo: 1, pageSize: 100 })
  }
}

// 根据应用编码获取应用名称
const getAppName = (applicationCode) => {
  if (!applicationCode) return '-'
  const app = tenantAppList.value.find((item) => item.applicationCode === applicationCode)
  return app ? app.name : applicationCode
}

// ========== 角色类型 ==========
// 角色类型选项
const typesOptions = ref([
  {
    label: t('system.role.systemRole'),
    value: '1'
  },
  {
    label: t('system.role.customRole'),
    value: '2'
  },
  {
    label: t('system.role.ortherRole'),
    value: '3,4,5,6'
  }
])

// 角色类型枚举
const roleTypeEnum = ref({
  1: t('system.role.systemRole'),
  2: t('system.role.customRole'),
  3: t('system.role.ortherRole'),
  4: t('system.role.ortherRole'),
  5: t('system.role.ortherRole'),
  6: t('system.role.ortherRole')
})

// ========== 已分配角色管理 ==========
const loading = ref(false)
const roleList = ref<OfUserRes[]>([])
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  code: undefined,
  applicationCode: undefined,
  types: undefined
})
const selectedRoles = ref<string[]>([])
const queryFormRef = ref() // 添加表单引用

// 获取已分配角色列表
const getAssignedRoleList = async () => {
  loading.value = true
  try {
    const res = await getRoleListOfUser(userId.value, queryParams)
    roleList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNo = 1
  getAssignedRoleList()
}

// 重置按钮操作
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

// 已分配角色选择变更
const handleSelectionChange = (selection) => {
  selectedRoles.value = selection
}

// 移除单个角色
const removeRole = async (role) => {
  try {
    await message.confirm(t('system.role.confirmCancelAssign'))
    await removeRolesOfUser(userId.value, [role.id])
    message.success(t('common.handleSuccess'))
    getAssignedRoleList() // 刷新已分配角色列表
  } catch (error) {
    console.error(error)
  }
}

// 批量移除角色
const batchRemoveRoles = async () => {
  if (selectedRoles.value.length === 0) {
    return
  }

  try {
    await message.confirm(t('system.role.confirmCancelAssign'))
    const roleIds = selectedRoles.value.map((role: any) => role.id as unknown as string)
    await removeRolesOfUser(userId.value, roleIds)
    message.success(t('common.handleSuccess'))
    getAssignedRoleList() // 刷新已分配角色列表
  } catch (error) {
    console.error(error)
  }
}

// ========== 未分配角色管理 ==========
const unassignedTableRef = ref()

const dialogVisible = ref(false)
const unassignedLoading = ref(false)
const unassignedRoleList = ref<NotOfUserRes[]>([])
const unassignedTotal = ref(0)
const unassignedQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  code: undefined,
  applicationCode: undefined,
  types: undefined
})
const selectedUnassignedRoles = ref<string[]>([])

// 打开添加角色弹窗
const openAddRoleDialog = () => {
  unassignedTableRef.value?.clearSelection()
  dialogVisible.value = true
  unassignedQueryParams.pageNo = 1
  getUnassignedRoleList()
}

// 获取未分配角色列表
const getUnassignedRoleList = async () => {
  unassignedLoading.value = true
  try {
    const res = await getRoleListNotOfUser(userId.value, unassignedQueryParams)
    unassignedRoleList.value = res.list
    unassignedTotal.value = res.total
  } finally {
    unassignedLoading.value = false
  }
}

// 重置未分配角色查询条件
const resetUnassignedQuery = () => {
  unassignedQueryParams.name = undefined
  unassignedQueryParams.code = undefined
  unassignedQueryParams.applicationCode = undefined
  unassignedQueryParams.types = undefined
  getUnassignedRoleList()
}

// 未分配角色选择变更
const handleUnassignedSelectionChange = (selection) => {
  selectedUnassignedRoles.value = selection
}

// 分配角色
const assignRoles = async () => {
  try {
    const roleIds = selectedUnassignedRoles.value.map((role: any) => role.id)
    await setRolesOfUser(userId.value, roleIds)
    message.success(t('common.handleSuccess'))
    dialogVisible.value = false
    getAssignedRoleList() // 刷新已分配角色列表
  } catch (error) {
    console.error(error)
  }
}

// ========== 生命周期钩子 ==========
onMounted(() => {
  getAssignedRoleList()
  getTenantList() // 获取应用列表
})
</script>
