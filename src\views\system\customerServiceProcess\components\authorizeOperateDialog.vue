<template>
  <Dialog
    v-model="dialogVisible"
    :title="'代客授权操作'"
    destroy-on-close
    :close-on-click-modal="false"
    width="800"
  >
    <el-form
      ref="ruleFormRef"
      label-width="120px"
      :model="formData"
      :rules="rules"
      class="mt-16px mb-16px"
    >
      <el-form-item :label="'代操作功能'" prop="proxyFunctions">
        <el-select
          v-model="formData.proxyFunctions"
          class="!w-240px"
          placeholder="请选择代操作功能"
          clearable
          :disabled="selectDisabled"
        >
          <el-option
            v-for="(item, index) in proxyFunctionsDict"
            :label="item.label"
            :value="item.value"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="'被代理租户'">
        <el-table :data="rowData.proxyTenants" height="400px">
          <el-table-column label="id" prop="id" />
          <el-table-column label="名称" prop="name" />
          <el-table-column
            label="操作"
            prop="operate"
            v-if="formData.proxyFunctions === 'singleProxyTenantOperation'"
          >
            <template #default="{ row }">
              <el-button @click="authorizeFn(row)" link type="primary">代客授权</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
        type="primary"
        @click="batchAuthor"
        v-if="formData.proxyFunctions === 'batchProxyTenantAuth'"
        >批量代客授权</el-button
      >
      <el-button @click="close">{{ t('common.close') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
const dialogVisible = ref(false)

import { proxyTenantOperationToken } from '@/api/system/customerServiceProcess/index'

const { t } = useI18n() // 国际化

import { useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache('sessionStorage')

const formData = ref({
  proxyFunctions: undefined
})

// 被代理操作的功能
const proxyFunctionsDict = ref([
  {
    value: 'batchProxyTenantAuth',
    label: '批量代客授权'
  },
  {
    value: 'singleProxyTenantOperation',
    label: '替某个租户授权'
  }
])

const proxyFunctionsMap = ref({
  batchProxyTenantAuth: '批量',
  singleProxyTenantOperation: '单个'
})

// 校验规则
const rules = ref({
  proxyFunctions: [{ required: true, message: '请选择被代理操作的功能', trigger: 'blur' }]
})

const rowData: Ref<any> = ref({})

import { useAgentOperationStore } from '@/store/modules/agentOperation'
const agentOperationStore = useAgentOperationStore()

const authorizeFn = (_row) => {
  let obj = { ..._row, ...proxyMap.value[_row.id], refreshToken: '', customerName: _row.name }
  // obj.refreshToken = ''
  agentOperationStore.openAgentOperationMode(false)
  agentOperationStore.setCurrentCustomer(obj)
}

const proxyMap: Ref<any> = ref({})
const proxyList: Ref<any[]> = ref([])

const selectDisabled = ref(false) //是否禁止选择代客操作功能

const open = async (_row) => {
  formData.value.proxyFunctions = undefined
  if (_row.proxyFunctions.length <= 1) {
    // 如果是只有一个的话，就固定不让选择其他
    formData.value.proxyFunctions = _row.proxyFunctions[0].id
    selectDisabled.value = true
  } else {
    selectDisabled.value = false
  }
  dialogVisible.value = true
  rowData.value = _row
  const res = await proxyTenantOperationToken(_row.id)
  proxyList.value = res
  proxyMap.value = res.reduce((a, b) => {
    return {
      ...a,
      [b.proxyTenantId]: b
    }
  }, {})
  console.log('proxyMap.value', proxyMap.value)
}

const close = () => {
  dialogVisible.value = false
}

const router = useRouter()

const batchAuthor = () => {
  wsCache.set('batchAuthProxyTenants', proxyMap.value)
  router.push({
    path: '/resourceSetting/index',
    query: {
      isEdit: true
    }
  })
}

defineExpose({
  open
})
</script>
