<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-21 11:25:53
 * @LastEditors: <PERSON>J<PERSON>
 * @LastEditTime: 2023-06-28 10:35:32
 * @Description:  单点登录回调页面,授权码换取token(当前页面没用,在路由守卫中处理了授权码获取token的逻辑)
-->
<template>
  <SsoSlot>
    <img src="/umvLogo.svg" class="w-[150px]" alt="Logo" />
    <!-- <div class="flex justify-center text-2xl">{{ t('sys.login.ssoLoading') }}</div> -->
    <!-- <div class="flex justify-center h-20%">{{ t('sys.login.ssoAuthTip') }}</div>
    <div class="flex justify-center h-20%"
      >{{ t('sys.login.authCode') }} {{ route.query.code }}</div
    >
    <div class="flex justify-center h-20%">
      {{ t('sys.login.usingCode') }}
    </div>
    <div class="flex justify-center h-20%">
      {{ t('sys.login.getToken') }}{{ ifSuccess ? t('common.success') : t('common.fail') }}
    </div> -->
  </SsoSlot>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SsoCallback'
})

import SsoSlot from './SsoSlot.vue'

import { setTenantId } from '@/utils/auth'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const { t } = useI18n()

//用户信息store
import { useUserStoreWithOut } from '@/store/modules/user'
const userStore = useUserStoreWithOut()

//单点登录store
import { useSsoStoreWithOut } from '@/store/modules/sso'
const ssoStore = useSsoStoreWithOut()

//loading
const loading = ElLoading.service({
  lock: true,
  text: t('sys.login.ssoLoading'),
  background: 'rgba(255, 255, 255, 0.3)'
})

// 导入缓存工具
import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

// 导入token工具函数
import { getAccessToken } from '@/utils/auth'

// 授权码处理状态标记
const isProcessingCode = ref<boolean>(false)
const codeProcessed = ref<boolean>(false)

onMounted(async () => {
  try {
    // 获取URL中的授权码
    const oauthCode = route.query.code as string
    const tenantId = route.query.tenantId as string

    if (oauthCode) {
      // 有授权码时，直接处理授权码
      // 版本检测已在 main.ts 中的应用启动前完成
      console.log('检测到授权码，开始处理授权码')
      await processAuthCode(oauthCode, tenantId)
    } else {
      // 没有授权码时，检查是否已经有有效的token
      const existingToken = getAccessToken()

      if (existingToken) {
        console.log('检测到已有有效token，直接跳转到首页')
        router.push('/')
        return
      }

      // 没有授权码也没有token，说明登录状态异常，跳转到错误页面
      console.log('没有授权码和token，登录状态异常，跳转到ssoError页面')
      router.push('/ssoError')
    }
  } catch (error) {
    console.error('初始化失败:', error)
    router.push('/ssoError')
  }
})

//离开当前路由时关闭loading
onUnmounted(() => {
  loading.close()
})

//获取token状态
const ifSuccess = ref<boolean>(false)

// 处理授权码的函数
const processAuthCode = async (oauthCode: string, tenantId?: string) => {
  if (isProcessingCode.value || codeProcessed.value || !oauthCode) {
    return
  }

  isProcessingCode.value = true

  try {
    console.log('开始处理授权码:', oauthCode)

    //保存租户id
    const finalTenantId = tenantId || (route.query.tenantId as string)
    setTenantId(finalTenantId)

    //通过授权获取token
    ifSuccess.value = await ssoStore.getTokenByCode(oauthCode)
    codeProcessed.value = true

    //成功跳转到保存的地址或首页
    if (ifSuccess.value) {
      //重置用户信息,路由守卫中重新获取用户信息,避免登录前后信息不一致
      userStore.resetState()

      // 清除URL中的授权码参数，防止页面刷新时重复处理
      const newQuery = { ...route.query }
      delete newQuery.code
      delete newQuery.tenantId

      // 获取保存的重定向地址
      const savedRedirectUrl = wsCache.get('sso_redirect_url')
      let redirectUrl = savedRedirectUrl || '/'

      // 安全检查：避免跳转到登录相关页面
      const unsafeRoutes = ['/ssoCallback', '/ssoError', '/404']
      if (unsafeRoutes.some((route) => redirectUrl.includes(route))) {
        redirectUrl = '/'
        console.log('检测到不安全的重定向地址，改为跳转到首页')
      }

      // 清除保存的重定向地址
      wsCache.delete('sso_redirect_url')

      console.log('登录成功，准备跳转到:', redirectUrl)

      router
        .replace({
          path: route.path,
          query: newQuery
        })
        .then(() => {
          router.push(redirectUrl)
        })
    }
  } catch (error) {
    console.error('处理授权码失败:', error)
    codeProcessed.value = true
    router.push('/ssoError')
  } finally {
    isProcessingCode.value = false
  }
}
</script>
<style lang="less" scope></style>
