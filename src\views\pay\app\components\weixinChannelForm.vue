<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="formType === 'create' ? t('pay.app.title1') : t('pay.app.title2')"
      @close="close"
      append-to-body
      width="800px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        size="default"
        label-width="120px"
        v-loading="formLoading"
      >
        <el-form-item label-width="180px" :label="'渠道类型'" prop="code">
          <el-select v-model="formData.code" :disabled="formType !== 'create'">
            <el-option
              v-for="(item, index) in codeMap"
              :label="item.name"
              :value="item.code"
              :key="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label-width="180px" :label="t('pay.app.feeRate')" prop="feeRate">
          <el-input
            v-model="formData.feeRate"
            :placeholder="t('pay.app.feeRatePlaceholder')"
            clearable
            :style="{ width: '100%' }"
          >
            <template #append>%</template>
          </el-input>
        </el-form-item>
        <el-form-item label-width="180px" :label="t('pay.app.config.appId')" prop="config.appId">
          <el-input
            v-model="formData.config.appId"
            :placeholder="t('pay.app.appIdPlaceholder')"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label-width="180px" :label="t('pay.app.config.mchId')" prop="config.mchId">
          <el-input v-model="formData.config.mchId" :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label-width="180px" :label="t('pay.app.status')" prop="status">
          <el-radio-group v-model="formData.status" size="default">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label-width="180px"
          :label="t('pay.app.config.apiVersion')"
          prop="config.apiVersion"
        >
          <el-radio-group v-model="formData.config.apiVersion" size="default">
            <el-radio label="v2">v2</el-radio>
            <el-radio label="v3">v3</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="formData.config.apiVersion === 'v2'">
          <el-form-item
            label-width="180px"
            :label="t('pay.app.config.mchKey')"
            prop="config.mchKey"
          >
            <el-input
              v-model="formData.config.mchKey"
              :placeholder="t('pay.app.mchKeyPlaceholder')"
              clearable
              :style="{ width: '100%' }"
              type="textarea"
              :autosize="{ minRows: 8, maxRows: 8 }"
            />
          </el-form-item>
          <el-form-item
            label-width="180px"
            :label="t('pay.app.config.keyContent')"
            prop="config.keyContent"
          >
            <el-input
              v-model="formData.config.keyContent"
              type="textarea"
              :placeholder="t('pay.app.keyContentPlaceholder')"
              readonly
              :autosize="{ minRows: 8, maxRows: 8 }"
              :style="{ width: '100%' }"
            />
          </el-form-item>
          <el-form-item label-width="180px" label="">
            <el-upload
              :limit="1"
              accept=".p12"
              action=""
              :before-upload="p12FileBeforeUpload"
              :http-request="keyContentUpload"
            >
              <el-button size="small" type="primary" icon="el-icon-upload">{{
                t('pay.app.clickUpload')
              }}</el-button>
            </el-upload>
          </el-form-item>
        </div>
        <div v-if="formData.config.apiVersion === 'v3'">
          <el-form-item
            label-width="180px"
            :label="t('pay.app.config.apiV3Key')"
            prop="config.apiV3Key"
          >
            <el-input
              v-model="formData.config.apiV3Key"
              :placeholder="t('pay.app.apiV3KeyPlaceholder')"
              clearable
              :style="{ width: '100%' }"
              type="textarea"
              :autosize="{ minRows: 8, maxRows: 8 }"
            />
          </el-form-item>
          <el-form-item
            label-width="180px"
            :label="t('pay.app.config.privateKeyContent')"
            prop="config.privateKeyContent"
          >
            <el-input
              v-model="formData.config.privateKeyContent"
              type="textarea"
              :placeholder="t('pay.app.privateKeyContentPlaceholder')"
              readonly
              :autosize="{ minRows: 8, maxRows: 8 }"
              :style="{ width: '100%' }"
            />
          </el-form-item>
          <el-form-item label-width="180px" label="" prop="privateKeyContentFile">
            <el-upload
              ref="privateKeyContentFile"
              :limit="1"
              accept=".pem"
              action=""
              :before-upload="pemFileBeforeUpload"
              :http-request="privateKeyContentUpload"
            >
              <el-button size="small" type="primary" icon="el-icon-upload">{{
                t('pay.app.clickUpload')
              }}</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item
            label-width="180px"
            :label="t('pay.app.config.privateCertContent')"
            prop="config.privateCertContent"
          >
            <el-input
              v-model="formData.config.privateCertContent"
              type="textarea"
              :placeholder="t('pay.app.privateCertContentPlaceholder')"
              readonly
              :autosize="{ minRows: 8, maxRows: 8 }"
              :style="{ width: '100%' }"
            />
          </el-form-item>
          <el-form-item label-width="180px" label="" prop="privateCertContentFile">
            <el-upload
              ref="privateCertContentFile"
              :limit="1"
              accept=".pem"
              action=""
              :before-upload="pemFileBeforeUpload"
              :http-request="privateCertContentUpload"
            >
              <el-button size="small" type="primary" icon="el-icon-upload">{{
                t('pay.app.clickUpload')
              }}</el-button>
            </el-upload>
          </el-form-item>
        </div>
        <el-form-item label-width="180px" :label="t('pay.app.remark')" prop="remark">
          <el-input v-model="formData.remark" :style="{ width: '100%' }" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="submitForm">{{ t('common.ok') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'weixinChannelForm'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

import * as ChannelApi from '@/api/pay/channel'
import { CommonStatusEnum } from '@/utils/constants'
import type { FormInstance } from 'element-plus'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

let formRef = ref<FormInstance>()

let dialogVisible = ref<boolean>(false)
let formLoading = ref<boolean>(false)

let formData = ref<ChannelApi.ChannelVO>({
  id: 0,
  appId: 0,
  merchantId: 0, //商户id
  code: '',
  status: 0,
  feeRate: 0,
  remark: '',
  config: {
    appId: '',
    mchId: '',
    apiVersion: '',
    mchKey: '',
    keyContent: '',
    privateKeyContent: '',
    privateCertContent: '',
    apiV3Key: ''
  }
})

let rules = reactive({
  code: [{ required: true, message: '请选择渠道类型', trigger: 'blur' }],
  feeRate: [{ required: true, message: t('pay.app.rWxDesc1'), trigger: 'blur' }],
  status: [{ required: true, message: t('pay.app.rWxDesc2'), trigger: 'blur' }],
  'config.mchId': [{ required: true, message: t('pay.app.rWxDesc3'), trigger: 'blur' }],
  'config.appId': [{ required: true, message: t('pay.app.rWxDesc4'), trigger: 'blur' }],
  'config.apiVersion': [{ required: true, message: t('pay.app.rWxDesc5'), trigger: 'blur' }],
  'config.mchKey': [{ required: true, message: t('pay.app.rWxDesc6'), trigger: 'blur' }],
  'config.keyContent': [{ required: true, message: t('pay.app.rWxDesc7'), trigger: 'blur' }],
  'config.privateKeyContent': [{ required: true, message: t('pay.app.rWxDesc8'), trigger: 'blur' }],
  'config.privateCertContent': [
    { required: true, message: t('pay.app.rWxDesc9'), trigger: 'blur' }
  ],
  'config.apiV3Key': [{ required: true, message: t('pay.app.rWxDesc10'), trigger: 'blur' }]
})

/** 打开弹窗 */
let formType = ref('create')

const codeMap: Ref<any[]> = ref([]) //渠道类型

/**
 * 修改支付渠道信息
 *
 * @param row 行记录
 * @param code 支付编码  例如:wx_h5  wx_native
 */
const open = async (row, code) => {
  dialogVisible.value = true
  formLoading.value = true
  console.log(row, code)

  resetForm(row, code)

  try {
    const res = await ChannelApi.getChannelListChannelTypesByAppType(2) //2为微信
    codeMap.value = res
    if (code) {
      let response = await ChannelApi.getChannel(row.merchantId, row.id, code)

      if (response && response?.id) {
        formData.value = response
        formData.value.config = JSON.parse(response?.config)
      }
      formType.value = !response?.id ? 'create' : 'edit'
    } else {
      formType.value = 'create'
    }
  } catch (error) {
    console.log(error)
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 重置表单 */
let resetForm = (row, code) => {
  console.log(row)

  formData.value = {
    id: row?.id,
    appId: row?.id,
    merchantId: row?.merchantId,
    code: code,
    status: CommonStatusEnum.ENABLE,
    feeRate: 0,
    remark: '',
    config: {
      appId: '',
      mchId: '',
      apiVersion: '',
      mchKey: '',
      keyContent: '',
      privateKeyContent: '',
      privateCertContent: '',
      apiV3Key: ''
    }
  }
  formRef.value?.resetFields()
}

const close = () => {
  dialogVisible.value = false
  resetForm(undefined, undefined)
}

//提交表单
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value?.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ChannelApi.ChannelVO
    data.config = JSON.stringify(formData.value.config)

    if (formType.value === 'create') {
      await ChannelApi.createChannel(data)
      message.success(t('common.createSuccess'))
    } else {
      await ChannelApi.updateChannel(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
/**
 * apiclient_cert.p12、apiclient_cert.pem、apiclient_key.pem 上传前的校验
 */
const p12FileBeforeUpload = (file) => {
  fileBeforeUpload(file, '.p12')
}
const pemFileBeforeUpload = (file) => {
  fileBeforeUpload(file, '.pem')
}

const fileBeforeUpload = (file, fileAccept) => {
  let format = '.' + file.name.split('.')[1]
  if (format !== fileAccept) {
    const str = t('pay.app.desc2', {
      fileAccept: fileAccept
    })
    message.error(str)
    return false
  }
  let isRightSize = file.size / 1024 / 1024 < 2
  if (!isRightSize) {
    message.error(t('pay.app.desc3'))
  }
  return isRightSize
}
/**
 * 读取 apiclient_key.pem 到 privateKeyContent 字段
 */
const privateKeyContentUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.privateKeyContent = e.target.result
  }
  readFile.readAsText(event.file)
}
/**
 * 读取 apiclient_cert.pem 到 privateCertContent 字段
 */
const privateCertContentUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.privateCertContent = e.target.result
  }
  readFile.readAsText(event.file)
}
/**
 * 读取 apiclient_cert.p12 到 keyContent 字段
 */
const keyContentUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.keyContent = e.target.result.split(',')[1]
  }
  readFile.readAsDataURL(event.file) // 读成 base64
}
</script>
