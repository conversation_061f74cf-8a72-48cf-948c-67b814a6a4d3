{"name": "common-service-center-web", "version": "1.0.0", "description": "基于vue3、vite4、element-plus、typesScript", "author": "umv", "private": false, "scripts": {"i": "pnpm install", "base": "vite", "dev": "vite --mode dev", "test": "vite --mode test", "sit": "node --max-http-header-size=1000000000000 ./node_modules/vite/bin/vite.js --mode sit", "uat": "node --max-http-header-size=1000000000000 ./node_modules/vite/bin/vite.js --mode uat", "exhibit": "vite --mode exhibit", "pro": "vite --mode pro", "dev:international": "set VITE_APP_INTERNATIONAL=en_US&&vite --mode dev ", "sit:international": "set VITE_APP_INTERNATIONAL=en_US&&vite --mode sit", "uat:international": "set VITE_APP_INTERNATIONAL=en_US&&node --max-http-header-size=1000000000000 ./node_modules/vite/bin/vite.js --mode uat", "pro:international": "set VITE_APP_INTERNATIONAL=en_US&&vite --mode pro", "ts:check": "vue-tsc --noEmit", "build": "node --max_old_space_size=8000 ./node_modules/vite/bin/vite.js build", "build:international": "node --max_old_space_size=8000 ./node_modules/vite/bin/vite.js build  --mode international", "build:exhibit": "node --max_old_space_size=8000 ./node_modules/vite/bin/vite.js build --mode exhibit", "serve:pro": "vite preview --mode pro", "serve:dev": "vite preview --mode dev", "serve:uat": "vite preview --mode uat", "serve:exhibit": "vite preview --mode exhibit", "serve:test": "vite preview --mode test", "serve:sit": "vite preview --mode sit", "preview": "pnpm build && vite preview", "npm:check": "npx npm-check-updates", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ", "lint:pretty": "pretty-quick --staged"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@form-create/designer": "^3.1.0", "@form-create/element-ui": "^3.1.17", "@iconify/vue": "^4.1.2", "@peculiar/x509": "^1.12.3", "@videojs-player/vue": "^1.0.0", "@vue/compiler-sfc": "^3.5.13", "@vueuse/core": "^10.1.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.1", "animate.css": "^4.1.1", "axios": "^1.4.0", "benz-amr-recorder": "^1.1.5", "bpmn-js-token-simulation": "^0.10.0", "camunda-bpmn-moddle": "^7.0.1", "cropperjs": "^1.5.13", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "diagram-js": "^11.6.0", "echarts": "^5.4.2", "echarts-wordcloud": "^2.1.0", "element-plus": "2.6.0", "encryptlong": "^3.1.4", "fast-xml-parser": "^4.2.2", "highlight.js": "^11.8.0", "intro.js": "^7.0.1", "js-base64": "^3.7.7", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "magic-string": "^0.30.17", "min-dash": "^4.1.1", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.1.3", "pinia-plugin-persistedstate": "^4.1.3", "pinyin-pro": "^3.26.0", "qrcode": "^1.5.3", "qs": "^6.11.2", "rollup-plugin-visualizer": "^5.9.3", "spark-md5": "^3.0.2", "steady-xml": "^0.1.0", "swiper": "^11.1.4", "unplugin-icons": "^0.19.0", "url": "^0.11.0", "uuid": "^11.0.5", "vue": "^3.4.15", "vue-eslint-parser": "^9.3.2", "vue-i18n": "9.2.2", "vue-json-pretty": "^2.4.0", "vue-router": "^4.2.1", "vue-types": "^5.0.3", "vue3-ace-editor": "^2.2.4", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1", "xe-utils": "^3.5.7", "xml-js": "^1.6.11"}, "devDependencies": {"@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@iconify-json/akar-icons": "^1.1.24", "@iconify-json/bi": "^1.2.0", "@iconify-json/ep": "^1.1.15", "@iconify-json/fa": "^1.1.8", "@iconify-json/ion": "^1.1.18", "@iconify-json/mdi": "^1.1.67", "@iconify-json/zmdi": "^1.1.8", "@intlify/unplugin-vue-i18n": "^0.10.0", "@types/intro.js": "^5.1.1", "@types/lodash-es": "^4.17.7", "@types/node": "^18.16.0", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.59.6", "@typescript-eslint/parser": "^5.59.6", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "^5.0.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/runtime-core": "^3.3.4", "autoprefixer": "^10.4.14", "bpmn-js": "^8.9.0", "bpmn-js-properties-panel": "^0.46.0", "consola": "^3.1.0", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-define-config": "^1.20.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.13.0", "lint-staged": "^13.2.2", "postcss": "^8.4.23", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.6", "prettier": "^2.8.8", "rimraf": "^5.0.1", "rollup": "^4.15.0", "sass": "^1.62.1", "stylelint": "^15.6.2", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^12.0.0", "stylelint-config-standard": "^33.0.0", "stylelint-order": "^6.0.3", "terser": "^5.39.0", "typescript": "^5.0.4", "unplugin-auto-import": "^0.16.0", "unplugin-element-plus": "^0.7.1", "unplugin-vue-components": "^0.24.1", "vite": "5.2.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-ejs": "^1.6.4", "vite-plugin-eslint": "^1.8.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-top-level-await": "^1.3.0", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-windicss": "^1.9.0", "vue-tsc": "^1.6.5", "windicss": "^3.5.6"}, "engines": {"node": ">=16.0.0"}, "license": "MIT"}