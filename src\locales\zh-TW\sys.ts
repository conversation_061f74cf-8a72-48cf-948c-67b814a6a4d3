/*
 * @Author: HoJack
 * @Date: 2023-12-13 14:12:14
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-13 14:19:53
 * @Description:
 */
export default {
  api: {
    operationFailed: '操作失敗',
    errorTip: '錯誤提示',
    errorMessage: '操作失敗,系統異常!',
    timeoutMessage: '登錄超時,請重新登錄!',
    apiTimeoutMessage: '接口請求超時,請刷新頁面重試!',
    apiRequestFailed: '請求出錯，請稍候重試',
    networkException: '網絡異常',
    networkExceptionMsg: '網絡異常，請檢查您的網絡連接是否正常!',
    errMsg401: '用戶沒有權限（令牌、用戶名、密碼錯誤）!',
    errMsg403: '用戶得到授權，但是訪問是被禁止的。!',
    errMsg404: '網絡請求錯誤,未找到該資源!',
    errMsg405: '網絡請求錯誤,請求方法未允許!',
    errMsg408: '網絡請求超時!',
    errMsg500: '服務器錯誤,請聯繫管理員!',
    errMsg501: '網絡未實現!',
    errMsg502: '網絡錯誤!',
    errMsg503: '服務不可用，服務器暫時過載或維護!',
    errMsg504: '網絡超時!',
    errMsg505: 'http版本不支持該請求!',
    errMsg901: '演示模式，無法進行寫操作!'
  },
  app: {
    title: 'UMV卡雲',
    logoutTip: '溫馨提醒',
    logoutMessage: '是否確認退出系統?',
    menuLoading: '菜單加載中...'
  },
  exception: {
    backLogin: '返回登錄',
    backHome: '返回首頁',
    subTitle403: '抱歉，您無權訪問此頁面。',
    subTitle404: '抱歉，您訪問的頁面不存在。',
    subTitle500: '抱歉，服務器報告錯誤。',
    noDataTitle: '當前頁無數據',
    networkErrorTitle: '網絡錯誤',
    networkErrorSubTitle: '抱歉，您的網絡連接已斷開，請檢查您的網絡！'
  },
  lock: {
    unlock: '點擊解鎖',
    alert: '鎖屏密碼錯誤',
    backToLogin: '返回登錄',
    entry: '進入系統',
    placeholder: '請輸入鎖屏密碼或者用戶密碼'
  },
  login: {
    backSignIn: '返回',
    signInFormTitle: '登錄',
    ssoFormTitle: '三方授權',
    mobileSignInFormTitle: '手機登錄',
    qrSignInFormTitle: '二維碼登錄',
    signUpFormTitle: '註冊',
    forgetFormTitle: '重置密碼',
    signInTitle: '開箱即用的中後臺管理系統',
    signInDesc: '輸入您的個人詳細信息開始使用！',
    policy: '我同意xxx隱私政策',
    scanSign: `掃碼後點擊"確認"，即可完成登錄`,
    loginButton: '登錄',
    registerButton: '註冊',
    rememberMe: '記住我',
    forgetPassword: '忘記密碼?',
    otherSignIn: '其他登錄方式',
    // notify
    loginSuccessTitle: '登錄成功',
    loginSuccessDesc: '歡迎回來',
    // placeholder
    accountPlaceholder: '請輸入賬號',
    passwordPlaceholder: '請輸入密碼',
    smsPlaceholder: '請輸入驗證碼',
    mobilePlaceholder: '請輸入手機號碼',
    policyPlaceholder: '勾選後才能註冊',
    diffPwd: '兩次輸入密碼不一致',
    userName: '賬號',
    password: '密碼',
    confirmPassword: '確認密碼',
    email: '郵箱',
    smsCode: '短信驗證碼',
    mobile: '手機號碼',
    ssoAuthTip: 'SSO 授權後的回調頁',
    authCode: '授權碼: ',
    usingCode: '正在使用 code 授權碼，進行 accessToken 訪問令牌的獲取',
    getToken: '獲取token',
    loginFail: '登錄已失效',
    ssoLoading: '正在獲取訪問權限,請稍等...',
    mailLogin: '郵箱登錄',
    inputMail: '請輸入郵箱',
    secSend: 's後重新發送',
    mobileLogin: '手機登錄',
    inputMobile: '請輸入手機號',
    inputMainAccount: '請輸入主賬號',
    pwdLogin: '密碼登錄',
    ramLogin: 'RAM賬號登錄',
    verLogin: '驗證碼登錄',
    sendVer: '發送驗證碼',
    sendSuccess: '發送成功',
    sendFail: '發送失敗',
    inputRightMail: '請輸入正確格式的郵箱',
    inputNickname: '請輸入暱稱',
    phone: '手機',
    authMethod: '認證方式',
    inputPhoneVer: '請輸入手機驗證碼',
    inputEmailVer: '請輸入郵箱驗證碼',
    ver: '認證',
    selectAuthMethod: '請選擇認證方式',
    phoneVer: '手機認證',
    mailVer: '郵箱認證',
    inputVer: '請輸入驗證碼',
    phoneVered: '手機已認證',
    mailVered: '郵箱已認證',
    linkOverdute: '鏈接已過期',
    verSuccess: '認證成功',
    inputMobileOrMail: '請輸入手機號碼或郵箱',
    inputMobileOrMailRight: '請輸入正確格式的手機號碼或郵箱'
  },
  permission: {
    hasPermission: `請設置操作權限標籤值`,
    hasRole: `請設置角色權限標籤值`
  },
  user: {
    gerUserInfoFail: '獲取用戶信息失敗',
    noRole: '該用戶未分配角色!請聯繫管理員分配用戶角色!',
    loginVer: '登錄認證',
    phoneVered: '手機號碼已認證',
    mailVered: '郵箱已認證',
    certified: '已認證',
    email: '郵箱',
    linkValidTerm: '鏈接有效期為：',
    phoneLink: '手機認證鏈接：',
    emailLink: '郵箱認證鏈接：',
    copyAll: '複製全部',
    copy: '複製',
    min: '分鐘',
    rDesc1: '新增成功，認證鏈接請在 操作>更多>登錄認證 中獲取',
    tips: '提示',
    confirms: '確 認',
    unlock: '解鎖',
    isUnlock: '是否鎖定',
    selectUnlockUser: '請選擇需要解鎖的用戶',
    sureUnlockUser: '確定解鎖用戶？'
  },
  dictFail: '獲取數據字典失敗',
  welcomeUse: '歡迎使用',
  errorCode: {
    code401: '認證失敗，無法訪問系統資源',
    code403: '當前操作沒有權限',
    code404: '訪問資源不存在',
    codeDefault: '系統未知錯誤，請反饋給管理員'
  },
  service: {
    invalidToken: '無效的刷新令牌',
    expiredToken: '刷新令牌已過期',
    code901: 'code為901,請聯繫管理員',
    unFindRole: '未能找到用戶角色,登錄已失效,請重新登錄!',
    loginInvalid: '登錄已失效',
    pleaseRelogin: ',請重新登錄!'
  },
  hooks: {
    web: {
      validfail: '校驗失敗'
    }
  }
}
