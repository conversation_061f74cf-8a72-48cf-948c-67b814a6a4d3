<template>
  <Dialog
    v-model="dialogVisible"
    :title="t('common.detail')"
    width="60%"
    maxHeight="70vh"
    :scroll="true"
  >
    <el-descriptions :column="2">
      <el-descriptions-item :label="t('pay.refund.payMerchantName')">
        {{ detailData?.payMerchantName }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.appName')">{{
        detailData.appName
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider />
    <el-descriptions :column="2">
      <el-descriptions-item label="业务退款订单号">
        <el-tag>{{ detailData.merchantRefundId }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label=" 业务支付订单号">
        {{ detailData.merchantOrderId }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.orderId')">{{
        detailData.orderId
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider />
    <el-descriptions :column="2">
      <el-descriptions-item :label="t('pay.refund.payPrice')">
        <el-tag type="success">￥{{ (detailData.payPrice / 100).toFixed(2) }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.refundPrice')">
        <el-tag class="tag-purple"> ￥{{ (detailData.refundPrice / 100).toFixed(2) }} </el-tag>
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.type')">
        {{ t('common.emptyText') }}
        <!-- <dict-tag :type="DICT_TYPE.PAY_REFUND_ORDER_TYPE" :value="detailData.type" /> -->
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.status')">
        <!-- <dict-tag :type="DICT_TYPE.PAY_REFUND_ORDER_STATUS" :value="detailData.status" /> -->
        <el-tag type="warning" v-if="detailData.status === 0">{{
          t('pay.refund.status_0')
        }}</el-tag>
        <el-tag type="success" v-else-if="detailData.status === 10">{{
          t('pay.refund.status_10')
        }}</el-tag>
        <el-tag type="danger" v-else-if="detailData.status === 20">{{
          t('pay.refund.status_20')
        }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item :label="t('common.createTime')">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.successTime')">
        {{ formatDate(detailData.successTime) }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.expireTime')">
        {{ formatDate(detailData?.expireTime) ?? t('common.emptyText') }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('common.updateTime')">
        {{ formatDate(detailData.updateTime) }}
      </el-descriptions-item>
    </el-descriptions>
    <el-divider />
    <el-descriptions :column="2">
      <el-descriptions-item :label="t('pay.refund.channelCode2')">
        {{ PayChannelEnum[detailData.channelCode.toLocaleUpperCase()]?.name }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.userIp')">
        {{ detailData.userIp }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.notifyUrl')">{{
        detailData.notifyUrl
      }}</el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.notifyStatus2')">
        {{ t('common.emptyText') }}
        <!-- <dict-tag :type="DICT_TYPE.PAY_ORDER_NOTIFY_STATUS" :value="detailData.notifyStatus" /> -->
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.notifyTime')">
        {{ t('common.emptyText') }}
        <!-- {{ formatDate(detailData.notifyTime) }} -->
      </el-descriptions-item>
    </el-descriptions>
    <el-divider />
    <el-descriptions :column="2">
      <el-descriptions-item :label="t('pay.refund.channelOrderNo')">
        {{ detailData.channelOrderNo }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.channelRefundNo')">
        {{ detailData.channelRefundNo }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.channelErrorCode')">
        {{ detailData.channelErrorCode }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.refund.channelErrorMsg')">
        {{ detailData.channelErrorMsg }}
      </el-descriptions-item>
    </el-descriptions>
    <br />
    <el-descriptions :column="1" border direction="vertical">
      <el-descriptions-item :label="t('pay.refund.channelNotifyData')">
        <div class="w-full overflow-auto">
          <!-- {{ detailData.channelNotifyData }} -->
          <vue-json-pretty :data="jsonContent" />
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="退款原因">{{ detailData.reason }}</el-descriptions-item>
    </el-descriptions>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import * as RefundApi from '@/api/pay/refund'
import { PayChannelEnum } from '@/utils/constants'

//json美化插件
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
let jsonContent = ref<any>()

const { t } = useI18n() // 国际化
defineOptions({ name: 'PayRefundDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单的加载中
let detailData = ref({
  payMerchantName: '',
  appName: '',
  channelCode: '',
  channelOrderNo: '',
  channelRefundNo: '',
  merchantOrderId: '',
  merchantRefundId: '',
  order: null,
  orderId: '',
  no: '',
  notifyUrl: '',
  payPrice: 0,
  refundPrice: 0,
  status: 0,
  createTime: '',
  successTime: '',
  updateTime: '',
  userIp: '',
  channelErrorCode: '',
  channelErrorMsg: '',
  channelNotifyData: '',
  reason: ''
})

/** 打开弹窗 */
const open = async (id: number) => {
  jsonContent.value = '' //先重置数据,避免重复打开弹窗时数据错乱

  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    detailData.value = await RefundApi.getRefund(id)
    jsonContent.value = JSON.parse(detailData.value?.channelNotifyData)
    if (jsonContent.value?.rawData) {
      jsonContent.value.rawData = JSON.parse(jsonContent.value.rawData)
    }
  } finally {
    detailLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
<style>
.tag-purple {
  color: #722ed1;
  background: #f9f0ff;
  border-color: #d3adf7;
}
</style>
