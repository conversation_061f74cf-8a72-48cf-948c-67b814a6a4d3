<template>
  <el-dialog
    :title="type == 1 ? '添加' : '编辑'"
    v-model="visible"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <div class="warp" style="max-height: 600px; overflow: auto">
      <el-form :model="formData" label-width="96px" :rules="rules" style="width: 90%" ref="formRef">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" clearable placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="时间" prop="time">
          <el-time-picker
            v-model="formData.time"
            is-range
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="服务" prop="applicationCode">
          <el-select v-model="formData.applicationCode">
            <el-option
              v-for="item in appOpions"
              :key="item.id"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="租户" prop="tenantIds">
          <el-tree-select
            ref="treeRef"
            v-model="formData.tenantIds"
            :placeholder="'请选择租户'"
            :data="tenantOptions"
            :props="tenantTreePorps"
            node-key="id"
            check-strictly
            default-expand-all
            filterable
            multiple
            show-checkbox
            :validate-event="false"
            @click="clickTree"
            @check-change="changeTenant"
          >
            <template #header
              ><el-checkbox v-model="treeNodeAll" @change="handleCheckedTreeNodeAll"
                >全选/全不选</el-checkbox
              ><el-checkbox v-model="checkStrictly"
                >父子联动(选中父节点，自动选择子节点)</el-checkbox
              ></template
            >
          </el-tree-select>
        </el-form-item>
        <!-- <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="0">启用</el-radio>
          <el-radio :label="1">停用</el-radio>
        </el-radio-group>
      </el-form-item> -->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" placeholder="请输入备注" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="close" :loading="loading">取 消</el-button>
      <el-button type="primary" @click="submit" :loading="loading">确 定</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'UserTaskListeners'
})

import {
  getTenantApplicationListAll,
  createTenantUpgradeRule,
  updateTenantUpgradeRule
} from '@/api/system/systemStrategy/tenantUpgrade/index'
const visible = ref(false)

const tenantTreePorps = {
  label: 'name'
}

const tenantMap: Ref<any> = ref({})

const tenantOptions: Ref<any> = ref()

const allTenantId: Ref<String[]> = ref([])

const allTenantMap: Ref<any> = ref({})

import { getTenantTree } from '@/api/system/tenant'

const getAllTenantId = (arr) => {
  arr.forEach((el) => {
    allTenantId.value.push(el.id)
    allTenantMap.value[el.id] = el
    if (el.children && el.children.length > 0) {
      getAllTenantId(el.children)
    }
  })
}

// 初始化父级租户
const initTenantParent = async () => {
  try {
    if (!tenantOptions.value) {
      const res = await getTenantTree()
      tenantOptions.value = [res]
    }
    if (allTenantId.value.length == 0) {
      getAllTenantId(tenantOptions.value)
    }
  } finally {
  }
}

const formData: Ref<any> = ref({
  name: undefined,
  time: undefined,
  applicationCode: undefined,
  tenantIds: undefined,
  status: 1,
  remark: undefined
})

const rules = ref({
  name: [{ required: true, message: '名字不能为空', trigger: 'blur' }],
  time: [{ required: true, message: '时间不能为空', trigger: 'blur' }],
  applicationCode: [{ required: true, message: '服务不能为空', trigger: 'blur' }],
  tenantIds: [{ required: true, message: '租户不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})

const type = ref(1)

const appOpions: Ref<any> = ref([])

const initData = async () => {
  isActiveStrictly.value = false
  formData.value = {
    name: undefined,
    time: undefined,
    applicationCode: undefined,
    tenantIds: undefined,
    status: 1,
    remark: undefined
  }
  // if (appOpions.value.length == 0) {
  //   const res: any = await getTenantApplicationListAll()
  //   appOpions.value = res
  // }
}

const isInit = ref(false)

const open = async (_appOpions, _type, _row?) => {
  appOpions.value = [..._appOpions]
  await initData()
  await initTenantParent()
  if (_row) {
    isInit.value = true
    checkStrictly.value = false
    // nextTick(() => {
    formData.value = { ..._row }
    formData.value.time = [_row.startTime, _row.endTime]
    console.log('formData.value.cond', formData.value.cond)
    formData.value.tenantIds = formData.value.cond?.split(',')
    delete formData.value.startTime
    delete formData.value.endTime
    delete formData.value.cond
    // })
  }
  type.value = _type
  visible.value = true
  nextTick(() => {
    checkStrictly.value = true
  })
}

const close = () => {
  visible.value = false
}

const formRef = ref()

const loading = ref(false)

const message = useMessage()

const emit = defineEmits(['success'])

const submit = async () => {
  await formRef.value.validate()
  console.log('formData', formData.value)
  let obj = { ...formData.value }
  obj.cond = formData.value.tenantIds.join(',')
  obj.startTime = formData.value.time[0]
  obj.endTime = formData.value.time[1]
  delete obj.tenantIds
  delete obj.time
  try {
    loading.value = true
    const fn = formData.value.id ? updateTenantUpgradeRule : createTenantUpgradeRule
    await fn(obj)
    message.success('操作成功')
    emit('success')
    close()
  } finally {
    loading.value = false
  }
}

/**------------------------------------------------------- 租户筛选start ---------------------------------------------------------*/

const treeRef = ref()

// 是否选中全部
const treeNodeAll = ref(false)

// 全选/全不选
const handleCheckedTreeNodeAll = () => {
  console.log('全选/全不选', treeNodeAll.value)
  if (treeNodeAll.value) {
    formData.value.tenantIds = [...allTenantId.value]
  } else {
    formData.value.tenantIds = []
  }
}

const checkChildTenant = (_tenantObj, _type) => {
  const emun = ['add', 'cancel']
  if (formData.value.tenantIds.indexOf(_tenantObj.id) === -1) {
    if (emun[_type] === 'add') {
      formData.value.tenantIds.push(_tenantObj.id)
    }
  } else {
    if (emun[_type] === 'cancel') {
      formData.value.tenantIds.splice(formData.value.tenantIds.indexOf(_tenantObj.id), 1)
    }
  }
  if (_tenantObj.children && _tenantObj.children.length > 0) {
    _tenantObj.children.forEach((el) => {
      checkChildTenant(el, _type)
    })
  }
}

//父子联动(选中父节点，自动选择子节点)
const checkStrictly = ref(true)

/**
 * 选中租户时，如果选择了父子联动，那么选中当前节点有子节点就需要都选上
 * @param _val是选中的值
 * @param _status是选中还是取消
 */
const changeTenant = (_val, _status) => {
  if (checkStrictly.value && isActiveStrictly.value) {
    if (_status) {
      // 如果是选中
      checkChildTenant(allTenantMap.value[_val.id], 0)
    } else {
      checkChildTenant(allTenantMap.value[_val.id], 1)
    }
  }
}

const isActiveStrictly = ref(false) //是否激活父子联动，这是为了禁止初始化时调用changeTenant导致所有的都被选上

const clickTree = () => {
  //点击选择框时激活isActiveStrictly，这样初始化时就不会父子联动，只有点击了才会激活
  isActiveStrictly.value = true
}

/**------------------------------------------------------- 租户筛选end ---------------------------------------------------------*/

defineExpose({
  open
})
</script>
