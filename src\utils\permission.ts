/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 13:59:22
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-13 14:20:31
 * @Description:
 */
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkPermi(value: string[]) {
  if (value && value instanceof Array && value.length > 0) {
    const { wsCache } = useCache()
    const permissionDatas = value
    const all_permission = '*:*:*'
    const permissions = wsCache.get(CACHE_KEY.ROLE_PERMISSIONS)

    const hasPermission = permissions.some((permission) => {
      return all_permission === permission || permissionDatas.includes(permission)
    })
    return !!hasPermission
  } else {
    const { t } = useI18n()
    console.error(t('sys.permission.hasPermission'))
    return false
  }
}

/**
 * 角色权限校验
 * @param {string[]} value 校验值
 * @returns {Boolean}
 */
export function checkRole(value: string[]) {
  if (value && value instanceof Array && value.length > 0) {
    const { wsCache } = useCache()
    const permissionRoles = value
    const super_admin = 'admin'
    const roles = wsCache.get(CACHE_KEY.ROLE_ROUTERS)
    const hasRole = roles.some((role) => {
      return super_admin === role || permissionRoles.includes(role)
    })
    return !!hasRole
  } else {
    const { t } = useI18n()
    console.error(t('sys.permission.hasRole'))
    return false
  }
}
