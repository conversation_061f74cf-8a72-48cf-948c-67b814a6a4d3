<!--
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-14 14:58:46
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2025-04-15 09:35:34
 * @Description: 流程表单信息
-->
<template>
  <el-card v-loading="loading" class="box-card">
    <template #header>
      <span class="el-icon-document">表单信息【{{ processInstance?.name }}】</span>
    </template>
    <!-- 情况一：流程表单 -->
    <el-col v-if="processInstance?.processDefinition?.formType === 10" :offset="6" :span="16">
      <form-create
        ref="fApiRef"
        v-model="detailForm.value"
        :option="detailForm.option"
        :rule="detailForm.rule"
        :disabled="true"
      />
    </el-col>
    <!-- 情况二：业务表单 -->
    <div v-if="processInstance?.processDefinition?.formType === 20">
      <component :is="businessFormComponent" :id="processInstance.businessKey" />
    </div>
  </el-card>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ProcessInstanceFormInfo'
})

import { ref, PropType, defineProps, nextTick } from 'vue'
import { setConfAndFields2 } from '@/utils/formCreate'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { registerComponent } from '@/utils/routerHelper'

const props = defineProps({
  processInstance: {
    type: Object as PropType<ProcessInstanceApi.ProcessInstanceType>,
    required: false,
    default: undefined
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 使用any类型以适应form-create组件实际情况
const fApiRef = ref<any>()
const detailForm = ref({
  // 流程表单详情
  rule: [],
  option: {},
  value: {}
})

const businessFormComponent = ref()

// 监听processInstance变化并初始化表单
watch(
  () => props.processInstance,
  (newVal) => {
    if (!newVal) return
    initForm(newVal)
  },
  { immediate: true }
)

// 初始化表单
const initForm = (instance: ProcessInstanceApi.ProcessInstanceType) => {
  if (!instance) return

  const processDefinition = instance.processDefinition
  if (processDefinition.formType === 10) {
    // 设置conf表单数据
    let conf = JSON.parse(processDefinition.formConf)
    conf.submitBtn.show = false
    setConfAndFields2(
      detailForm,
      JSON.stringify(conf),
      processDefinition.formFields,
      instance.formVariables
    )
    nextTick().then(() => {
      if (fApiRef.value?.fApi) {
        fApiRef.value.fApi.btn.show(false)
        fApiRef.value.fApi.resetBtn.show(false)
        fApiRef.value.fApi.disabled(true)
        fApiRef.value.fApi.config.submitBtn.show = false
      }
    })
  } else if (processDefinition.formType === 20) {
    businessFormComponent.value = registerComponent(processDefinition.formCustomCreatePath)
  }
}
</script>
