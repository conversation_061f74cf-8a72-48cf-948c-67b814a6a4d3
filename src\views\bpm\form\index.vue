<template>
  <UmvContent>
    <!-- 搜索工作栏 -->
    <template #search>
      <UmvQuery
        ref="queryFormRef"
        v-model="queryParams"
        :opts="queryOpts"
        @reset="getList"
        @check="handleQuery"
      />
    </template>

    <!-- 列表 -->
    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="getList">
      <template #tools>
        <el-button
          v-hasPermi="['bpm:form:create']"
          plain
          type="primary"
          size="small"
          @click="() => openForm()"
        >
          <icon-ep-plus style="font-size: 12px" class="mr-5px" />
          新增
        </el-button>
      </template>

      <template #pagination>
        <Pagination
          v-model:limit="queryParams.pageSize"
          v-model:page="queryParams.pageNo"
          :total="total"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单详情的弹窗 -->
    <Dialog v-model="detailVisible" title="表单详情" width="800">
      <form-create :option="detailData.option" :rule="detailData.rule" />
    </Dialog>
  </UmvContent>
</template>

<script setup lang="tsx">
defineOptions({
  name: 'BpmForm'
})

import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as FormApi from '@/api/bpm/form'
import { setConfAndFields2 } from '@/utils/formCreate'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { checkPermi } from '@/utils/permission'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { currentRoute, push } = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: null
})

// 搜索条件配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '表单名',
    defaultVal: '',
    controlRender: (form) => <el-input v-model={form.name} placeholder="请输入表单名" clearable />
  }
})

const queryFormRef = ref() // 搜索的表单

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: '编号', align: 'center' },
  { prop: 'name', label: '表单名', align: 'center' },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    renderTemplate: (scope) => <dict-tag type={DICT_TYPE.COMMON_STATUS} value={scope.row.status} />
  },
  { prop: 'remark', label: '备注', align: 'center' },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    renderTemplate: (scope) => (
      <>
        {checkPermi(['bpm:form:update']) && (
          <el-button link type="primary" onClick={() => openForm(scope.row.id)}>
            编辑
          </el-button>
        )}
        {checkPermi(['bpm:form:query']) && (
          <el-button link onClick={() => openDetail(scope.row.id)}>
            详情
          </el-button>
        )}
        {checkPermi(['bpm:form:delete']) && (
          <el-button link type="danger" onClick={() => handleDelete(scope.row.id)}>
            删除
          </el-button>
        )}
      </>
    )
  }
])

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await FormApi.getFormPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 添加/修改操作 */
const openForm = (id?: number) => {
  const toRouter: { name: string; query?: { id: number } } = {
    name: 'BpmFormEditor'
  }
  // 表单新建的时候id传的是event需要排除
  if (typeof id === 'number') {
    toRouter.query = {
      id
    }
  }
  push(toRouter)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await FormApi.deleteForm(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 详情操作 */
const detailVisible = ref(false)
const detailData = ref({
  rule: [],
  option: {}
})
const openDetail = async (rowId: number) => {
  // 设置表单
  const data = await FormApi.getForm(rowId)
  setConfAndFields2(detailData, data.conf, data.fields)
  // 弹窗打开
  detailVisible.value = true
}
/**表单保存返回后重新加载列表 */
watch(
  () => currentRoute.value,
  () => {
    getList()
  },
  {
    immediate: true
  }
)
/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
