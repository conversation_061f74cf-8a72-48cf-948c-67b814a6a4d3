<template>
  <div class="approval">
    <!-- <el-tabs v-model="activeName">
      <el-tab-pane label="未审核" name="audit"><approvalAudit /></el-tab-pane>
      <el-tab-pane label="已审核" name="audited"><approvalAudited /></el-tab-pane>
    </el-tabs> -->
    <approvalAudit />
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'approval'
})

import approvalAudit from './audit.vue'
import approvalAudited from './audited.vue'
const activeName = ref('audit')
</script>
