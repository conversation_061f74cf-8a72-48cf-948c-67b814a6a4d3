<!--
 * @Author: hao-jie.chen <EMAIL>
 * @Date: 2025-02-28 09:39:49
 * @LastEditors: hao-jie.chen <EMAIL>
 * @LastEditTime: 2025-02-28 15:32:48
 * @Description: 
-->
<template>
  <!-- 列表 -->
  <ContentWrap ifTable>
    <el-table v-loading="loading" :data="list">
      <el-table-column :label="t('system.subscribe.name')" align="center" prop="name" />
      <el-table-column :label="t('system.subscribe.checkoutValue1')" align="center">
        <template #default="{ row }">
          <el-tooltip placement="top" :content="transferToStr(row.applicationVOlist)">
            <span class="w-full overflow-hidden whitespace-nowrap text-ellipsis">{{
              transferToStr(row.applicationVOlist)
            }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column :label="t('common.operate')" align="center" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="showDetail(row)">
            {{ t('system.subscribe.homeSetting') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <SettingDailog v-if="ifResetDialog" ref="settingDailogRef" @success="ifResetDialog = false" />
  </ContentWrap>
</template>
<script setup lang="ts">
defineOptions({
  name: 'homepageSetting'
})

const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据

const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  status: null,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
import { getSubscriptionClientPage } from '@/api/system/apply'

const getList = async () => {
  loading.value = true
  try {
    const data = await getSubscriptionClientPage({ ...queryParams.value })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  getList()
})

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.value.pageNo = 1
  getList()
}

// 数组提取name转为字符串
const transferToStr = (arr) => {
  if (!arr || arr.length === 0) return ''
  return arr.reduce(
    (a, b, index) => {
      if (index === 0) {
        return { name: `${a.name}${b.name}` }
      } else {
        return { name: `${a.name},${b.name}` }
      }
    },
    { name: '' }
  ).name
}

//菜单详情弹出框
import SettingDailog from './components/settingDailog.vue'
import { nextTick } from 'vue'
const settingDailogRef = ref()
const ifResetDialog = ref(false)
// 展示详情
const showDetail = (row) => {
  ifResetDialog.value = true
  nextTick(() => {
    settingDailogRef.value?.open(row)
  })
}
</script>
