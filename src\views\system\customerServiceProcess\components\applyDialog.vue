<template>
  <Dialog
    v-model="dialogVisible"
    :title="'申请代客操作'"
    destroy-on-close
    :close-on-click-modal="false"
    width="1200"
  >
    <el-form
      ref="ruleFormRef"
      label-width="140px"
      :model="formData"
      :rules="rules"
      class="mt-16px mb-16px"
    >
      <el-form-item :label="'申请理由'" prop="applyReason">
        <!-- <Editor v-model="formData.editor" :height="300" /> -->
        <el-input
          type="textarea"
          placeholder="请输入申请理由"
          v-model="formData.applyReason"
          :disabled="isDisable"
        />
      </el-form-item>
      <el-form-item :label="'文件上传'">
        <Upload
          ref="uploadRef"
          :showFileList="externalList"
          :btnText="'选择文件'"
          :uploadTip="'文件大小20M以内，文件格式'"
          :limit="10"
          :multiple="true"
          accept="*"
          :limitFormat="['*']"
          :maxSize="1024 * 1024 * 20"
          :disabled="isDisable"
        />
      </el-form-item>
      <el-form-item :label="'被代理租户'" style="width: 100%" prop="proxyTenants">
        <el-card class="card mb-16px" shadow="never" ref="authCardRef" style="width: 100%">
          <template #header>
            <div class="flex" style="justify-content: space-between; align-items: center"
              ><span></span>
              <el-button type="primary" @click="addPrincipal" v-if="!isDisable"
                >添加被代理租户</el-button
              >
            </div>
          </template>
          <div style="width: 100%; height: 500px">
            <el-auto-resizer>
              <template #default="{ height, width }">
                <el-table-v2
                  :data="formData.proxyTenants"
                  :height="height"
                  ref="tableRef"
                  :width="width"
                  row-key="id"
                  :columns="columns"
                  v-model:expanded-row-keys="expandedRowKeys"
                  :default-expanded-row-keys="defaultExpandedRowKeys"
                  size="small"
                  fixed
                />
              </template>
            </el-auto-resizer>
          </div>
        </el-card>
      </el-form-item>
      <el-form-item :label="'代操作功能'" prop="proxyFunctions">
        <el-select
          v-model="formData.proxyFunctions"
          class="!w-240px"
          placeholder="请选择流程实例处理结果"
          multiple
          clearable
          :disabled="isDisable"
        >
          <el-option
            v-for="(item, index) in proxyFunctionsDict"
            :label="item.label"
            :value="item.value"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="'期望令牌时长'" prop="tokenEffectiveDuration">
        <el-input
          type="number"
          placeholder="请输入期望令牌时长"
          v-model="formData.tokenEffectiveDuration"
          class="!w-240px"
          :min="0"
          :disabled="isDisable"
          ><template #suffix><span class="ml-16px">秒</span></template></el-input
        >
      </el-form-item>
      <el-form-item :label="'操作有效期'" prop="expireAt">
        <el-date-picker
          v-model="formData.expireAt"
          type="datetime"
          placeholder="请选择操作有效期"
          class="!w-240px"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled="isDisable"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="primary" @click="submit" :loading="loading" v-if="!isDisable"
        >提交</el-button
      >
      <el-button type="primary" @click="save" :loading="loading" v-if="!isDisable"
        >保存草稿</el-button
      >
      <el-button @click="close" :loading="loading" v-if="!isDisable">{{
        t('common.cancel')
      }}</el-button>
      <el-button @click="close" :loading="loading" v-else>{{ t('common.close') }}</el-button>
    </template>
    <selectTenantDialog ref="selectTenantRef" @success="getSelectTenant" />
  </Dialog>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'ResourceApplicationApplyDialog'
})

import {
  createProxyTenantOperation,
  getProxyTenantOperation,
  updateProxyTenantOperation,
  proxyTenantOperationSubmitApply
} from '@/api/system/customerServiceProcess/index'
import selectTenantDialog from './selectTenantDialog.vue'
import Upload from './Upload.vue'

const { t } = useI18n() // 国际化
const dialogVisible = ref(false)

const loading = ref(false)

const formData: Ref<any> = ref({
  tokenEffectiveDuration: undefined,
  expireAt: undefined,
  applyReason: undefined,
  proxyTenants: [],
  proxyFunctions: undefined,
  id: undefined
})

const type = ref()

import { formatDate } from '@/utils/formatTime'

const isDisable = ref(false)

const open = async (_type, _row?) => {
  externalList.value = undefined
  formData.value = {
    tokenEffectiveDuration: undefined,
    expireAt: undefined,
    applyReason: undefined,
    proxyTenants: [],
    proxyFunctions: undefined,
    id: undefined
  }
  isDisable.value = false
  authorizers.value = []
  authorizersRow.value = []
  dialogVisible.value = true
  type.value = _type
  if (type.value === 'edit' || type.value === 'detail') {
    try {
      const id = _row.id
      const res = await getProxyTenantOperation({ id })
      formData.value.tokenEffectiveDuration = res.tokenEffectiveDuration
      formData.value.expireAt = res.expireAt
        ? formatDate(res.expireAt, 'YYYY-MM-DD HH:mm:ss')
        : undefined
      formData.value.applyReason = res.applyReason
      formData.value.proxyFunctions = res.proxyFunctions.map((el) => el.id)
      formData.value.id = id
      authorizers.value = res.proxyTenants.map((el) => el.id)
      formData.value.proxyTenants = res.proxyTenants
      if (res.attachments && res.attachments.length > 0) {
        externalList.value = res.attachments
      }
      if (type.value === 'detail') {
        isDisable.value = true
      }
    } finally {
    }
  }
}

const close = () => {
  dialogVisible.value = false
}

const ruleFormRef = ref()

const emit = defineEmits(['success'])

// 保存草稿
const save = async () => {
  // await uploadRef.value.submitFile()
  await ruleFormRef.value.validate()
  loading.value = true
  try {
    const obj = deepClone(formData.value)

    const fileList = await uploadRef.value.submitFile()
    if (fileList.length > 0) {
      console.log('fileList', fileList)
      obj.attachments = fileList
    }

    obj.proxyFunctions = formData.value.proxyFunctions.map((el) => {
      return {
        id: el,
        name: proxyFunctionsMap.value[el]
      }
    })
    const fn = formData.value.id ? updateProxyTenantOperation : createProxyTenantOperation
    await fn(obj)
    message.success('操作成功')
    emit('success')
    close()
  } finally {
    loading.value = false
  }
}

// 提交
const submit = async () => {
  await ruleFormRef.value.validate()
  loading.value = true
  try {
    const obj = deepClone(formData.value)
    const fileList = await uploadRef.value.submitFile()
    if (fileList.length > 0) {
      console.log('fileList', fileList)
      obj.attachments = fileList
    }
    obj.proxyFunctions = formData.value.proxyFunctions.map((el) => {
      return {
        id: el,
        name: proxyFunctionsMap.value[el]
      }
    })
    const fn = formData.value.id ? updateProxyTenantOperation : createProxyTenantOperation
    const res = await fn(obj)
    await proxyTenantOperationSubmitApply(formData.value.id ? formData.value.id : res)
    message.success('操作成功')
    emit('success')
    close()
  } finally {
    loading.value = false
  }
}

import type { FormRules } from 'element-plus'

const validatorTokenEffective = (_rule: any, _value: any, callback: any) => {
  if (formData.value.tokenEffectiveDuration <= 0) {
    callback(new Error('期望令牌时长必须大于0'))
    return
  } else if (formData.value.tokenEffectiveDuration > 2147483647) {
    callback(new Error('期望令牌时长必须小于2147483647'))
    return
  } else {
    callback()
  }
}

// 校验规则
const rules = reactive<FormRules>({
  tokenEffectiveDuration: [
    { required: true, message: '请输入期望令牌时长', trigger: 'blur' },
    { validator: validatorTokenEffective, trigger: 'blur' }
  ],
  expireAt: [{ required: true, message: '请选择操作有效期', trigger: 'blur' }],
  applyReason: [{ required: true, message: '请输入申请理由', trigger: 'blur' }],
  proxyTenants: [{ required: true, message: '请选择被代理租户', trigger: 'blur' }],
  proxyFunctions: [{ required: true, message: '请选择被代理操作的功能', trigger: 'blur' }]
})

const selectTenantRef = ref()

const authorizers: Ref<string[]> = ref([])

const authorizersRow: Ref<any[]> = ref([])

// 添加被代理人
const addPrincipal = () => {
  selectTenantRef.value.open(authorizers.value, formData.value.proxyTenants)
}

/**------------------------------------ 获取被代理租户 start ------------------------------------------*/
import { ElButton } from 'element-plus'
import { deepClone } from '@/utils/deep'
const expandedRowKeys = ref(['id'])
// 默认展开的租户，取第一层
const defaultExpandedRowKeys = ref([])

const getSelectTenant = ({ _authorizers, _authorizersRow }) => {
  authorizers.value = deepClone(_authorizers)
  // authorizersRow.value = _authorizersRow
  formData.value.proxyTenants = deepClone(_authorizersRow)
}

const delTenant = (_row) => {
  console.log('formData.value.proxyTenants', formData.value.proxyTenants)
  if (authorizers.value.indexOf(_row.id) === -1) {
  } else {
    // authorizersRow.value.splice(authorizers.value.indexOf(_row.id), 1)
    const index = authorizers.value.indexOf(_row.id)
    console.log('index', index)
    let tenantIndex = -1
    formData.value.proxyTenants.forEach((el, index) => {
      if (el.id === _row.id) {
        tenantIndex = index
      }
    })
    console.log('tenantIndex', tenantIndex)
    tenantIndex !== -1 && formData.value.proxyTenants.splice(tenantIndex, 1)
    authorizers.value.splice(index, 1)
  }
}

const columns = computed(() => {
  if (isDisable.value) {
    return [
      {
        dataKey: 'name',
        key: 'name',
        width: 640,
        title: '租户',
        cellRenderer: ({ rowData }) => <span>{`${rowData.name}（${rowData.id}）`}</span>
      },
      {
        dataKey: 'id',
        key: 'id',
        width: 200,
        title: '租户id'
      }
    ]
  } else {
    return [
      {
        dataKey: 'name',
        key: 'name',
        width: 640,
        title: '租户',
        cellRenderer: ({ rowData }) => <span>{`${rowData.name}（${rowData.id}）`}</span>
      },
      {
        dataKey: 'id',
        key: 'id',
        width: 200,
        title: '租户id'
      },
      {
        width: 60,
        title: '操作',
        cellRenderer: ({ rowData }) => (
          <ElButton link type="danger" onClick={() => delTenant(rowData)}>
            删除
          </ElButton>
        )
      }
    ]
  }
})
/**------------------------------------ 获取被代理租户 end ------------------------------------------*/

/**-------------------------------------------- 上传文件start --------------------------------------------------------*/

const url = import.meta.env.VITE_UPLOAD_URL

const fileList = ref([]) // 文件列表

const data = ref({ path: '' })

const formLoading = ref(false) // 表单的加载中
const uploadHeaders = ref() // 上传 Header 头
const uploadRef = ref()

/** 处理上传的文件发生变化 */
const handleFileChange = (file) => {
  data.value.path = file.name
}

const message = useMessage()

/** 上传错误提示 */
const submitFormError = (): void => {
  message.error('上传失败，请您重新上传！')
  formLoading.value = false
}

/** 文件数超出提示 */
const handleExceed = (): void => {
  message.error('最多只能上传一个文件！')
}

const submitFormSuccess = () => {
  // 清理
  dialogVisible.value = false
  formLoading.value = false
  unref(uploadRef)?.clearFiles()
  // 提示成功，并刷新
  message.success(t('common.createSuccess'))
}

const externalList = ref()

/**-------------------------------------------- 上传文件end --------------------------------------------------------*/

// 被代理操作的功能
const proxyFunctionsDict = ref([
  {
    value: 'batchProxyTenantAuth',
    label: '批量'
  },
  {
    value: 'singleProxyTenantOperation',
    label: '单个'
  }
])

const proxyFunctionsMap = ref({
  batchProxyTenantAuth: '批量',
  singleProxyTenantOperation: '单个'
})

defineExpose({
  open
})
</script>
