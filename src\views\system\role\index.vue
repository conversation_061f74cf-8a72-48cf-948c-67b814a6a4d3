<template>
  <UmvContent>
    <!-- 搜索工作栏 -->
    <UmvQuery
      v-model="queryParams"
      :opts="queryOpts"
      :label-width="ifEn ? '110px' : '68px'"
      @check="handleQuery"
      @reset="handleQuery"
    />

    <UmvTable v-loading="loading" :data="list" row-key="id" :columns="columns" @refresh="getList">
      <!-- 工具栏插槽 -->
      <template #tools>
        <!-- <el-button
          v-hasPermi="['system:role:create']"
          plain
          type="primary"
          @click="openForm('create')"
        >
          <icon-ep-plus class="mr-5px" style="font-size: 12px" />
          {{ t('common.create') }}
        </el-button> -->
        <!-- <el-button
          v-hasPermi="['system:role:export']"
          :loading="exportLoading"
          plain
          type="success"
          @click="handleExport"
        >
          <icon-ep-download class="mr-5px" style="font-size: 12px" />
          {{ t('common.export') }}
        </el-button> -->
      </template>

      <!-- 分页组件 -->
      <template #pagination>
        <Pagination
          v-model:limit="queryParams.pageSize"
          v-model:page="queryParams.pageNo"
          :total="total"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单弹窗：添加/修改 -->
    <RoleForm ref="formRef" :tenantAppList="tenantAppList" @success="getNewQuery" />
    <!-- 表单弹窗：菜单权限 -->
    <RoleAssignMenuForm ref="assignMenuFormRef" @success="getList" />
    <!-- 表单弹窗：资源权限 -->
    <RoleResourceForm ref="resourceFormRef" @success="getList" />
    <!-- 标签 -->
    <TagForm ref="tagFormRef" @success="getList" />
    <!-- 授权规则 -->
    <RoleAuthRuleDialog ref="authRuleDialogRef" @success="getList" />
  </UmvContent>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'SystemRole'
})

import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as RoleApi from '@/api/system/role'
import RoleForm from './RoleForm.vue'
import RoleAssignMenuForm from './RoleAssignMenuForm.vue'
import TagForm from './TagForm.vue'
import { listSimpleAppData } from '@/api/system/apply'
import RoleResourceForm from './RoleResourceForm.vue'
import RoleAuthRuleDialog from './RoleAuthRuleDialog.vue'
import { getTenantId } from '@/utils/auth'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { ElButton, ElTooltip } from 'element-plus'
import { checkPermi } from '@/utils/permission'
import { Icon } from '@/components/Icon'
import UmvContent from '@/components/UmvContent'

const message = useMessage() // 消息弹窗
const { t, ifEn } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
let tenantAppList = ref<any>([]) //应用列表

const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  applicationCode: undefined,
  createTime: [],
  types: '2'
})

const typesOptions = ref([
  {
    label: t('system.role.systemRole'),
    value: '1'
  },
  {
    label: t('system.role.customRole'),
    value: '2'
  },
  {
    label: t('system.role.ortherRole'),
    value: '3,4,5,6'
  }
])

// 查询参数配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: t('system.role.name'),
    defaultVal: '',
    controlRender: (form) => (
      <el-input
        v-model={form.name}
        placeholder={t('system.role.namePlaceholder')}
        clearable
        onKeyup={(e) => e.key === 'Enter' && handleQuery()}
      />
    )
  },
  code: {
    label: t('system.role.code'),
    defaultVal: '',
    controlRender: (form) => (
      <el-input
        v-model={form.code}
        placeholder={t('system.role.codePlaceholder')}
        clearable
        onKeyup={(e) => e.key === 'Enter' && handleQuery()}
      />
    )
  },
  applicationCode: {
    label: t('system.role.applicationCode'),
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select
        v-model={form.applicationCode}
        placeholder={t('system.role.applicationCodePlaceholder')}
        clearable
      >
        {tenantAppList.value.map((item) => (
          <el-option key={item.applicationCode} label={item.name} value={item.applicationCode} />
        ))}
      </el-select>
    )
  },
  types: {
    label: t('system.role.type'),
    defaultVal: '2',
    controlRender: (form) => (
      <el-select
        v-model={form.types}
        placeholder={t('common.selectText') + t('system.role.type')}
        clearable
      >
        {typesOptions.value.map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  status: {
    label: t('system.role.status'),
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.status} placeholder={t('system.role.statusPlaceholder')} clearable>
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  createTime: {
    label: t('system.role.createTime'),
    defaultVal: [],
    controlRender: (form) => (
      <el-date-picker
        v-model={form.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        clearable
        start-placeholder={t('common.startDateText')}
        end-placeholder={t('common.endDateText')}
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
      />
    )
  }
})

const formRef = ref()
const exportLoading = ref(false) // 导出的加载中

// 角色类型枚举
const roleTypeEnum = ref({
  1: t('system.role.systemRole'),
  2: t('system.role.customRole'),
  3: t('system.role.ortherRole'),
  4: t('system.role.ortherRole'),
  5: t('system.role.ortherRole'),
  6: t('system.role.ortherRole')
})

const roleTagsMap = computed(() => {
  const arr = getStrDictOptions(DICT_TYPE.SYSTEM_ROLE_TAG)
  return arr.reduce((a, b: any) => {
    return {
      ...a,
      [b.value]: b['label']
    }
  }, {})
})

// 表格列定义
const columns = ref<TableColumn[]>([
  {
    prop: 'id',
    label: t('system.role.id'),
    align: 'center',
    showOverflowTooltip: true
  },
  {
    prop: 'name',
    label: t('system.role.name'),
    align: 'left',
    width: '200',
    renderTemplate: (scope) => (
      <div class="flex">
        <span>{scope.row.name}</span>
        <ElTooltip placement="right" content={toolTipName(scope.row.clientName, scope.row.name)}>
          {scope.row.isStale && <Icon size={18} class="ml-5px mt-1px" icon="ep:warning-filled" />}
        </ElTooltip>
      </div>
    )
  },
  {
    prop: 'type',
    label: t('system.role.type'),
    align: 'center',
    width: '100',
    renderTemplate: (scope) => <span>{roleTypeEnum.value[scope.row.type]}</span>
  },
  {
    prop: 'code',
    label: t('system.role.code'),
    align: 'left',
    minWidth: '200',
    showOverflowTooltip: true
  },
  {
    prop: 'remark',
    label: t('system.role.remark'),
    align: 'center'
  },
  {
    prop: 'status',
    label: t('system.role.status'),
    align: 'center',
    width: '150',
    renderTemplate: (scope) => <dict-tag type={DICT_TYPE.COMMON_STATUS} value={scope.row.status} />
  },
  {
    prop: 'createTime',
    label: t('system.role.createTime'),
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'sort',
    label: t('system.role.sort'),
    align: 'center',
    width: '150'
  },
  {
    prop: 'operation',
    label: t('common.operate'),
    fixed: 'right',
    width: '520',
    renderTemplate: (scope) => (
      <div>
        {scope.row.status != 1 && scope.row.type === 2 && checkPermi(['system:role:update']) ? (
          <ElButton link type="primary" onClick={() => openForm('update', scope.row.id)}>
            {t('common.edit')}
          </ElButton>
        ) : (
          <ElButton link type="primary" onClick={() => openForm('detail', scope.row.id)}>
            {t('common.see')}
          </ElButton>
        )}

        {scope.row.type === 1 && scope.row.status != 1 && checkPermi(['system:role:update']) && (
          <ElTooltip placement="top" content={t('system.role.desc1')}>
            <ElButton link type="primary" onClick={() => openForm('add', scope.row.id)}>
              {t('system.role.createRole')}
            </ElButton>
          </ElTooltip>
        )}

        {scope.row.status != 1 &&
          (scope.row.type === 1 || scope.row.type === 2) &&
          checkPermi(['system:permission:assign-role-menu']) && (
            <ElButton
              link
              type="primary"
              title={t('system.role.menuPerm')}
              onClick={() => openAssignMenuForm(scope.row, scope.row.type)}
            >
              {t('system.role.menuPerm')}
            </ElButton>
          )}

        {scope.row.status != 1 && scope.row.type === 2 && (
          <ElButton link type="primary" onClick={() => openAuthRuleForm(scope.row)}>
            {t('system.role.autoAuthRule')}
          </ElButton>
        )}

        {getTenantId() === 'goldpac.com' &&
          scope.row.status != 1 &&
          (scope.row.type === 1 || scope.row.type === 2) && (
            <ElButton link type="primary" onClick={() => openDataPermissionForm(scope.row)}>
              {t('system.role.advanceDataPerm')}
            </ElButton>
          )}

        {scope.row.type !== 1 && checkPermi(['system:role:delete']) && (
          <ElButton link type="danger" onClick={() => handleDelete(scope.row.id)}>
            {t('common.delete')}
          </ElButton>
        )}
      </div>
    )
  }
])

/** 查询角色列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RoleApi.getRolePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 获取应用列表
const getTenantList = async () => {
  tenantAppList.value.length === 0 &&
    (tenantAppList.value = await listSimpleAppData({ pageNo: 1, pageSize: 100 }))

  // tenantAppList.value = tenantAppList.value.filter((el: any) => el.appType !== 0)  //区分是平台还是其他应用

  console.log('tenantAppList.value', tenantAppList.value)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 添加/修改操作 */
const openForm = async (type: string, id?: number) => {
  formRef.value.open(type, id)
}

// 搜索后返回数据
const getNewQuery = () => {
  queryParams.value.applicationCode = formRef.value.applicationCode
  handleQuery()
}

const router = useRouter()

//优化后的高级数据权限( 后续之前的高级数据权限废弃掉 )
const openDataPermissionForm = async (row: RoleApi.RoleVO) => {
  router.push({
    name: 'RoleSence',
    query: {
      // ...row,
      id: row.id,
      name: row.name,
      code: row.code,
      autoAuthRuleIds: row.autoAuthRuleIds ? row.autoAuthRuleIds.join(',') : '',
      dataScope: row.dataScope,
      type: row.type
    }
  })
}
const resourceFormRef = ref()
const openDataResourceForm = async (row: RoleApi.RoleVO, isAdvanced = false) => {
  resourceFormRef.value.open(row, isAdvanced)
}

/** 标签操作 */
const tagFormRef = ref()
const openTag = async (row: RoleApi.RoleVO) => {
  tagFormRef.value.open(row)
}

/** 菜单权限操作 */
const assignMenuFormRef = ref()
const openAssignMenuForm = async (row: RoleApi.RoleVO, roleType: number) => {
  assignMenuFormRef.value.open(row, roleType)
}

/** 授权规则操作 */
const authRuleDialogRef = ref()
const openAuthRuleForm = (row: RoleApi.RoleVO) => {
  authRuleDialogRef.value.open(row)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await RoleApi.deleteRole(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

function toolTipName(tenantAppName, name) {
  const str = t('system.role.desc10', {
    tenantAppName: tenantAppName,
    name: name
  })
  return str
}

/** 初始化 **/
onMounted(async () => {
  // 02240507要求去除默认选中应用
  getList()
  getTenantList()
})
</script>
