<template>
  <div class="example-container">
    <h2>方式一：使用el-table-column子组件</h2>
    <umv-table
      ref="tableRef"
      title="用户数据表格"
      :data="tableData"
      height="350px"
      @selection-change="handleSelectionChange"
      @refresh="handleRefresh"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" width="60" label="序号" />
      <el-table-column prop="id" label="ID" width="80" sortable />
      <el-table-column prop="name" label="用户名" width="120" />
      <el-table-column prop="age" label="年龄" width="80" sortable />
      <el-table-column prop="address" label="地址" show-overflow-tooltip />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </umv-table>

    <h2 style="margin-top: 20px">方式二：使用columns属性配置</h2>
    <umv-table
      title="用户数据表格"
      :data="tableData"
      :columns="columns"
      :disabled-column-keys="['id']"
      height="350px"
      border
      @refresh="handleRefresh"
    >
      <template #action="{ row }">
        <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
        <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
      </template>

      <template #pagination>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          class="mt-15px float-right"
        />
      </template>
    </umv-table>

    <div class="method-demo" style="margin-top: 20px">
      <h2>调用表格方法示例</h2>
      <el-button type="primary" @click="toggleFullscreen">切换全屏</el-button>
      <el-button type="info" @click="toggleBorder">切换边框</el-button>
      <el-button type="warning" @click="doLayout">重新布局</el-button>
      <el-button type="success" @click="clearSelection">清除选择</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import UmvTable from '../index'
import type { UmvTableInstance } from '../index'
import type { TableColumn } from '../src/types'

defineOptions({
  name: 'BasicTableExample'
})

interface User {
  id: number
  name: string
  age: number
  address: string
}

// 表格数据
const tableData = ref<User[]>([
  { id: 1, name: '张三', age: 25, address: '北京市朝阳区' },
  { id: 2, name: '李四', age: 30, address: '上海市浦东新区' },
  { id: 3, name: '王五', age: 28, address: '广州市天河区' },
  { id: 4, name: '赵六', age: 35, address: '深圳市南山区' },
  { id: 5, name: '钱七', age: 27, address: '杭州市西湖区' }
])

// 列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: 'ID', width: '80px', sortable: true },
  { prop: 'name', label: '用户名', width: '120px' },
  { prop: 'age', label: '年龄', width: '80px', sortable: true },
  { prop: 'address', label: '地址', minWidth: '200px', showOverflowTooltip: true },
  { prop: 'action', label: '操作', slot: 'action', fixed: 'right', width: '180px' }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 表格实例引用
const tableRef = ref<UmvTableInstance>()

// 处理刷新
const handleRefresh = () => {
  ElMessage.success('刷新表格数据...')
  // 模拟加载数据
  setTimeout(() => {
    tableData.value.push({
      id: Math.floor(Math.random() * 1000),
      name: `用户${Math.floor(Math.random() * 100)}`,
      age: Math.floor(Math.random() * 50) + 18,
      address: '新增地址'
    })
    ElMessage.success('数据刷新成功')
  }, 1000)
}

// 处理编辑操作
const handleEdit = (row: User) => {
  ElMessage.info(`正在编辑: ${row.name}`)
}

// 处理删除操作
const handleDelete = (row: User) => {
  ElMessage.warning(`确定要删除 ${row.name} 吗?`)
}

// 处理选择变化
const handleSelectionChange = (selection: User[]) => {
  console.log('选中的行:', selection)
}

// 示例方法调用
const toggleFullscreen = () => {
  tableRef.value?.toggleFullscreen()
}

const toggleBorder = () => {
  tableRef.value?.toggleBorder()
}

const doLayout = () => {
  tableRef.value?.doLayout()
}

const clearSelection = () => {
  tableRef.value?.clearSelection()
}

onMounted(() => {
  console.log('表格实例:', tableRef.value)
})
</script>

<style scoped>
.example-container {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

h2 {
  margin-bottom: 16px;
  font-size: 18px;
  color: #303133;
}

.method-demo {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.method-demo .el-button {
  margin-right: 12px;
}
</style>
