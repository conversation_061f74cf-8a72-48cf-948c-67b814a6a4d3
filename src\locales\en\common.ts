/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-12-11 20:22:21
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-13 14:32:03
 * @Description:
 */
export default {
  appClient: 'service client',
  inputText: 'Please input',
  selectText: 'Please select',
  startTimeText: 'Start time',
  endTimeText: 'End time',
  login: 'Login',
  required: 'This is required',
  loginOut: 'Login out',
  document: 'Document',
  profile: 'User Center',
  reminder: 'Reminder',
  loginOutMessage: 'Exit the system?',
  back: 'Back',
  ok: 'OK',
  save: 'Save',
  cancel: 'Cancel',
  close: 'Close',
  reload: 'Reload current',
  success: 'Success',
  closeTab: 'Close current',
  closeTheLeftTab: 'Close left',
  closeTheRightTab: 'Close right',
  closeOther: 'Close other',
  closeAll: 'Close all',
  prevLabel: 'Prev',
  nextLabel: 'Next',
  skipLabel: 'Jump',
  doneLabel: 'End',
  menu: 'Menu',
  menuDes: 'Menu bar rendered in routed structure',
  collapse: 'Collapse',
  collapseDes: 'Expand and zoom the menu bar',
  tagsView: 'Tags view',
  tagsViewDes: 'Used to record routing history',
  tool: 'Tool',
  toolDes: 'Used to set up custom systems',
  query: 'Query',
  reset: 'Reset',
  shrink: 'Put away',
  expand: 'Expand',
  confirmTitle: 'System Hint',
  exportMessage: 'Whether to confirm export data item?',
  importMessage: 'Whether to confirm import data item?',
  createSuccess: 'Create Success',
  updateSuccess: 'Update Success',
  delMessage: 'Delete the selected data?',
  delDataMessage: 'Delete the data?',
  delNoData: 'Please select the data to delete',
  delSuccess: 'Deleted successfully',
  index: 'Index',
  status: 'Status',
  createTime: 'Create Time',
  updateTime: 'Update Time',
  copy: 'Copy',
  copySuccess: 'Copy Success',
  copyError: 'Copy Error',
  setting: 'Setting',

  operate: 'Actions',
  modify: 'Modify',
  delete: 'Delete',
  newAdd: 'Add New',
  edit: 'Edit',
  remove: 'Remove',
  withdraw: 'Withdraw',
  newCreate: 'Create New',
  detail: 'Details',
  attachment: 'Attachment',
  selectFile: 'Select File',
  submit: 'Submit',
  select: 'Select',
  handleSuccess: 'Operation Successful!',
  tip: 'Tip',
  startDateText: 'Start Date',
  endDateText: 'End Date',
  day0: 'Last Three Days',
  day1: 'Last Week',
  day2: 'Last Month',
  day3: 'Last Three Months',
  day4: 'Last Year',
  export: 'Export',
  exportFile: 'Export File',
  delFail: 'Deletion Failed',
  fail: 'Failed',
  download: 'Download',
  see: 'View',
  serialNumber: 'Serial Number',
  importFile: 'Import File',
  import: 'Import',
  more: 'More',
  enable: 'Enable',
  disable: 'Disable',
  emptyText: 'No Data Available',
  yes: 'Yes',
  no: 'No',
  submitSuccess: 'Submission Successful'
}
