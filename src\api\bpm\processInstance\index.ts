/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-03 10:05:06
 * @LastEditors: Ho<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-14 14:23:52
 * @Description:
 */
import request from '@/config/axios'

export type Task = {
  id: string
  name: string
}

export type ProcessInstanceVO = {
  id: number
  name: string
  processDefinitionId: string
  category: string
  result: number
  tasks: Task[]
  fields: string[]
  status: number
  remark: string
  businessKey: string
  createTime: string
  endTime: string
}

export const getMyProcessInstancePage = async (params) => {
  return await request.get({ url: '/bpm/process-instance/my-page', params })
}

export const createProcessInstance = async (data) => {
  return await request.post({ url: '/bpm/process-instance/create', data: data })
}

export const cancelProcessInstance = async (id: number, reason: string) => {
  const data = {
    id: id,
    reason: reason
  }
  return await request.delete({ url: '/bpm/process-instance/cancel', data: data })
}

export type ProcessInstanceType = {
  id: string
  name: string
  category: string
  status: number
  result: number
  createTime: number
  endTime: number | null
  formVariables: {
    [key: string]: any
  }
  businessKey: string
  approvalKey: string
  startUser: {
    id: number
    nickname: string
    deptId: number
    deptName: string | null
  }
  processDefinition: {
    id: string
    key: string
    formType: number
    formId: number | null
    formConf: any | null
    formFields: any | null
    formCustomCreatePath: string
    formCustomViewPath: string
    bpmnXml: string
    userTaskForms: any[]
  }
}

export const getProcessInstance = async (id: number): Promise<ProcessInstanceType> => {
  return await request.get({ url: '/bpm/process-instance/get?id=' + id })
}
