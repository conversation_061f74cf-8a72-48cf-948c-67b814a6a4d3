<template>
  <ContentWrap ifTable>
    <div class="voucher-wrap" v-loading="loading">
      <el-row class="new-add-block">
        <el-col :span="12">
          <el-button v-if="vocherInfo.caCrt" type="primary" @click="handleDownloadCaCrt">
            根证书下载
          </el-button>
          <el-button
            v-if="vocherInfo.caCrt"
            type="primary"
            @click="showCertInfoDlg(vocherInfo.caCrt)"
          >
            根证书查看
          </el-button>
          <el-button v-if="!vocherInfo.caCrt" type="danger" @click="showInitVisable">
            初始化密钥
          </el-button>
          <!-- <el-button v-if="vocherInfo.caKeyPhrase" type="danger" @click="handleShowCaKeyPhrase"
          >密钥查看</el-button
        >
        <el-button v-if="vocherInfo.caKeyPhrase" type="danger" @click="handleDownloadCaKeyPhrase"
          >密钥移交</el-button
        > -->
        </el-col>
        <el-col :span="12" style="text-align: right">
          <el-button v-if="vocherInfo.caCrt" type="primary" @click="handleNewAdd">新增</el-button>
        </el-col>
      </el-row>
      <section class="table-block">
        <el-table
          :data="tableData"
          :header-cell-style="{ background: '#F7F7F9', color: '#606266' }"
          style="width: 100%; border: 1px solid #f1f1f1; height: 80%"
        >
          <el-table-column label="序号" width="80" align="center">
            <template #default="scope">
              <!-- {{ scope.$index + 1 + (queryPage.pageNum - 1) * queryPage.pageSize }} -->
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="name" label="服务凭证名称" />
          <el-table-column prop="credentialCrt" label="证书" align="center">
            <template #default="scope">
              <el-button
                v-if="scope.row.credentialCrt"
                type="primary"
                size="small"
                style="margin-left: 6px"
                @click="showCertInfoDlg(scope.row.credentialCrt)"
                >查看</el-button
              >
              <el-button
                v-if="scope.row.credentialCrt"
                type="primary"
                size="small"
                style="margin-left: 6px"
                @click="handleDownloadBtn(scope.row)"
                >下载</el-button
              >
            </template>
          </el-table-column>
          <el-table-column prop="netWhiteList" label="接入网络白名单" />
          <el-table-column prop="expireTime" label="凭证到期时间" width="260">
            <template #default="{ row }">
              {{ formatDate(row.expireTime, 'YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column prop="credentialCrt" label="密钥状态" align="center" width="160">
            <template #default="scope">
              <el-text type="danger" v-if="scope.row.peerKey">未生效</el-text>
              <el-text type="primary" v-else>已生效</el-text>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="150" align="center">
            <template #default="scope">
              <el-link type="primary" @click="handleShowDetail(scope.row)">详情</el-link>
              <el-link
                v-if="scope.row.peerKey"
                style="margin-left: 10px"
                type="danger"
                @click="handleTransferKey(scope.row)"
                >移交
              </el-link>
              <el-link class="ml-2" type="primary" @click="handleEdit(scope.row)">编辑</el-link>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="pageParams.pageNo"
          v-model:limit="pageParams.pageSize"
          @pagination="getVocherList"
        />
      </section>

      <!-- 新增弹窗 -->
      <el-dialog
        v-model="dlgVisible"
        :title="dlgType == 'edit' ? '编辑' : '新增'"
        :close-on-click-modal="false"
        width="50%"
        destroy-on-close
      >
        <NewAdd
          :type="dlgType"
          :editForm="editForm"
          :apiManagementList="apiManagementList"
          @close-dlg="closeDlg"
        />
      </el-dialog>

      <!-- 初始化密钥 -->
      <el-dialog
        v-model="initVisible"
        title="初始化密钥"
        :close-on-click-modal="false"
        width="36%"
        destroy-on-close
        top="20vh"
        show-close
      >
        <el-form style="width: 80%; padding-top: 16px" label-width="100">
          <el-form-item required label="密钥：">
            <el-input
              v-model="initCaKeyPhrase"
              placeholder="请输入密钥，由大小写字母和数字组成"
              clearable
              style="width: 100%"
              maxlength="40"
              show-word-limit
            />
          </el-form-item>
          <el-form-item required label="算法类型：">
            <el-select v-model="encryptionType" placeholder="请选择算法类型">
              <el-option label="国密算法" value="GM" />
              <el-option label="国际算法" value="GJ" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button :loading="initBtnLoading" type="primary" @click="handleInitCaKeyPhrase"
            >确定</el-button
          >
        </template>
      </el-dialog>

      <!-- 详情 -->
      <el-dialog
        v-model="detailVisible"
        title="详情"
        :close-on-click-modal="false"
        width="80%"
        destroy-on-close
        class="detail-info-dlg"
      >
        <DetailInfo
          style="height: 100%"
          :detailInfoData="detailInfoData"
          :apiManagementList="apiManagementList"
          @close-dlg="detailVisible = false"
        />
      </el-dialog>

      <!-- 证书查看 -->
      <el-dialog
        draggable
        v-model="cretInfoDlgVisable"
        title="证书查看"
        :close-on-click-modal="false"
        destroy-on-close
        width="600px"
        top="5vh"
        custom-class="cert-info-dlg"
      >
        <CretInfo :cretStr="cretStr" @close-dlg="cretInfoDlgVisable = false" />
      </el-dialog>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import DetailInfo from './components/DetailInfo.vue'
import NewAdd from './components/NewAdd.vue'
import CretInfo from './components/CretInfo.vue'
import { formatDate } from '@/utils/formatTime'
import {
  getVocherListApi,
  getTenantByIdApi,
  getTenantListApi,
  initCaKeyPhraseApi,
  transferPeerKeyApi
} from '@/api/system/vocherManagement'
import { getSimpleMenusList } from '@/api/system/menu'
import { handleTree } from '@/utils/tree'
import { getTenantId } from '@/utils/auth'
import { useClipboard } from '@vueuse/core'
// import { CopyDocument } from '@element-plus/icons-vue'

const loading = ref(false)
const tableData = ref<any>([])

onMounted(() => {
  init()
})

const init = async () => {
  loading.value = true
  Promise.all([
    // 获取根证书、密钥
    new Promise(async (resolve, reject) => {
      await getVocherInfo()
      resolve(null)
    }),
    // 获取列表数据
    new Promise(async (resolve, reject) => {
      await getVocherList()
      resolve(null)
    }),
    // 获取api勾选树数据，用于新增和详情
    new Promise(async (resolve, reject) => {
      await getApiMenuList()
      resolve(null)
    })
  ]).then(() => {
    loading.value = false
  })
}

const vocherInfo = ref<any>({
  caKeyPhrase: '',
  caCrt: ''
})
const getVocherInfo = async () => {
  try {
    let id = getTenantId()
    const res = await getTenantByIdApi({ id })
    vocherInfo.value = res
    console.log('vocherInfo === ', vocherInfo.value)
  } catch (error) {}
}

/** 获取列表数据 */
const getVocherList = async () => {
  try {
    let params = {
      pageNo: pageParams.value.pageNo,
      pageSize: pageParams.value.pageSize
    }
    const res = await getVocherListApi(params)
    console.log('getVocherList res === ', res)
    let list = res?.list || []
    tableData.value = [...list]
    total.value = res?.total
  } catch (error) {
    ElMessage.error('获取列表数据失败：' + error)
  } finally {
  }
}

const total = ref(0)

const pageParams = ref({
  pageSize: 10,
  pageNo: 1
})

const apiManagementList = ref<any>([])
/** 获取菜单数据，从菜单中过滤出api的菜单 */
const getApiMenuList = async () => {
  try {
    // -------先获取访客列表，找到 APIGW 的对象，提取 applicationId
    const tenantRes = await getTenantListApi({
      pageSize: 100,
      pageNum: 1
    })
    console.log('tenantRes res === ', tenantRes)
    let tenantList = tenantRes.list || []
    let find = tenantList.find((item) => item.applicationCode == 'APIGW')
    let applicationId = (find && find.applicationId) || ''
    console.log('获取 applicationId === ', applicationId)

    if (!applicationId) {
      console.error('未能获取 APIGW 菜单， 获取applicationId失败')
      return
    }

    // 用 applicationId 去请求api列表数据
    const res = await getSimpleMenusList({ applicationId })

    let filterType4List = res.filter((item) => item.type == 4) // tyep = 4 是接口按钮创建的数据
    console.log('过滤type = 4的菜单', filterType4List)
    let parentsList = res.filter((item) => filterType4List.some((e) => e.parentId === item.id))
    let resList = handleTree([...filterType4List, ...parentsList])

    // 组织 onlyPathKey = 父节点的path+子节点的 requestUri，组成唯一的key(因为requestUri可能存在相同的情况)
    let list = resList.map((item) => {
      let path = item.path
      item.children.forEach((e) => {
        e.onlyPathKey = `${path}${e.requestUri}` // 注意分隔符 | 前面的是父path，后是子的requestUri
      })
      return { ...item }
    })
    apiManagementList.value = [...list]
    console.log('最终的api列表 resList === ', resList)
  } catch (error) {
    console.error('获取api列表失败', error)
  }
}

const editForm = ref<any>(null)

const dlgVisible = ref(false)
const dlgType = ref('newAdd') // newAdd-新增 edit-编辑
const handleNewAdd = () => {
  dlgVisible.value = true
  dlgType.value = 'newAdd'
  editForm.value = {
    name: '',
    apiListData: [],
    token: '',
    netWhiteList: '',
    caKeyPhrase: '',
    isEncrypted: false,
    antiReplayTime: 5000
  }
}
const closeDlg = (flag) => {
  dlgVisible.value = false
  // editForm.value = null
  if (flag) {
    getVocherList()
  }
}

//编辑功能
const handleEdit = (row) => {
  dlgVisible.value = true
  dlgType.value = 'edit'
  editForm.value = {
    id: row.id,
    name: row.name,
    apiListData: row.apiListData,
    // token: '',
    netWhiteList: row.netWhiteList,
    // caKeyPhrase: row.caKeyPhrase,
    isEncrypted: row.isEncrypted,
    antiReplayTime: row.antiReplayTime
  }
}

const detailVisible = ref(false)
const detailInfoData = ref<any>(null)
const handleShowDetail = (row) => {
  detailVisible.value = true
  detailInfoData.value = row || {}
  console.log('detail row === ', row)
}

const handleTransferKey = (row) => {
  console.log('移交', row)
  let peerKey = row.peerKey
  if (!peerKey) {
    ElMessage.success('密钥为空，无法移交！')
    return
  }
  ElMessageBox({
    title: '注意',
    message: `确认移交【${row.name}】的密钥吗？移交前会进行密钥下载，请妥善保管密钥！移交后此数据库不再保存该密钥！`,
    showCancelButton: true,
    type: 'warning',
    closeOnClickModal: false,
    beforeClose: async (action, instance, done) => {
      if (action != 'confirm') {
        done()
        return
      }
      // TODO:
      instance.confirmButtonLoading = true
      let res = await transferPeerKeyApi({ id: row.id })
      console.log('移交 res === ', res)
      if (res && res.code == 0 && res.data) {
        instance.confirmButtonLoading = false
        ElMessage.success('移交成功！')
        downloadStringAsTxt(
          res.data,
          `${row.name}-密钥移交备份-${formatDate(new Date(), 'YYYYMMDDHHmmss')}`
        )
        getVocherList()
        done()
      } else {
        instance.confirmButtonLoading = false
        ElMessage.error('移交失败，请重试')
      }
    }
  })
    .then(() => {
      downloadTokenBtn.value = false
    })
    .catch(() => {
      // 取消删除
    })
}

const downloadTokenBtn = ref(true)
const handleDownloadCaCrt = () => {
  let name = vocherInfo.value.name
  let caCrt = vocherInfo.value.caCrt
  if (!caCrt) return
  downloadStringAsTxt(caCrt, name + '_' + '证书')
  ElMessage.success('下载根证书成功')
}

const cretInfoDlgVisable = ref(false)
const cretStr = ref('')
const showCertInfoDlg = (str) => {
  cretInfoDlgVisable.value = true
  cretStr.value = str
}

const initVisible = ref(false)
const initCaKeyPhrase = ref('') // 初始化密钥，由用户输入
const initBtnLoading = ref(false)
const showInitVisable = () => {
  encryptionType.value = ''
  ElMessageBox({
    title: '注意',
    message: `注意：初始化密钥后，请妥善保管密钥！`,
    showCancelButton: true,
    type: 'warning',
    closeOnClickModal: false,
    beforeClose: async (action, instance, done) => {
      if (action != 'confirm') {
        done()
        return
      }
      done()
      initCaKeyPhrase.value = ''
      initVisible.value = true
    }
  })
    .then(() => {
      downloadTokenBtn.value = false
    })
    .catch(() => {
      // 取消删除
    })
}

const encryptionType = ref('')

// 初始化密钥请求
const handleInitCaKeyPhrase = async () => {
  if (!encryptionType.value) {
    ElMessage.error('算法类型不可为空！')
    return
  }

  if (!initCaKeyPhrase.value) {
    ElMessage.error('密钥不可为空！')
    return
  }
  if (initCaKeyPhrase.value.length < 8) {
    ElMessage.error('密钥长度为8~40位！')
    return
  }
  // 正则检验输入
  let ruleRexg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]+$/
  let flag = ruleRexg.test(initCaKeyPhrase.value)
  if (!flag) {
    ElMessage.error('密钥长度为8~40位，由大写字母、小写字母、数字组成！')
    return
  }
  initBtnLoading.value = true
  // 输入校验通过，发起请求
  try {
    // 接口逻辑写在此处
    const initRes = await initCaKeyPhraseApi({
      phrase: initCaKeyPhrase.value,
      encryptionType: encryptionType.value
    })

    if (initRes && initRes.code == 0) {
      initVisible.value = false // 关闭弹窗
      initBtnLoading.value = false
      ElMessage.success('初始化密钥成功，请妥善保管密钥！')
      init()
    }
  } catch (error) {
    ElMessage.error('初始化密钥失败：' + error + '，请重试')
    initBtnLoading.value = false
  }
}

const handleDownloadBtn = (row) => {
  let peerCrt = row.credentialCrt
  if (!peerCrt) return
  downloadStringAsTxt(peerCrt, row.name + '证书')
  ElMessage.success('下载成功')
}

const copyContent = async (content) => {
  const { copy } = useClipboard()
  try {
    await copy(content)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败！')
    console.error('复制失败', error)
  }
}

/** 字符串下载为TXT文本 */
const downloadStringAsTxt = async (str, fileName) => {
  try {
    let blob = new Blob([str], { type: 'text/plain;charset=utf-8' })
    let url = URL.createObjectURL(blob)
    let link = document.createElement('a')
    link.href = url
    link.download = fileName
    await link.click()
  } catch (error) {
    ElMessage.error('下载失败：' + error)
  }
}
</script>

<style scoped lang="scss">
.voucher-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .query-block {
  }
  .new-add-block {
    padding: 10px 0 10px 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  .table-block {
    flex: 1;
    max-height: 100%;
    overflow: hidden;
    .long-text-wrap {
      padding: 6px 0;
    }
  }
  .detail-info-dlg {
  }
  .copy-icon {
    font-size: 16px;
    margin-left: 10px;
    cursor: pointer;
  }
  .copy-icon:hover {
    font-size: 18px;
  }
  :deep(.cert-info-dlg) {
    .el-dialog__body {
      padding: 20px 20px !important;
    }
  }
}
</style>
