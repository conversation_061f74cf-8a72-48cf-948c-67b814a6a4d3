<!--
 * @Author: Ho<PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJack
 * @LastEditTime: 2023-10-31 14:55:49
 * @Description: 
-->
<script setup lang="ts">
defineOptions({
  name: 'AppView'
})

import { useTagsViewStore } from '@/store/modules/tagsView'
import { useAppStore } from '@/store/modules/app'
import { Footer } from '@/layout/components/Footer'
import { nextTick } from 'vue'

const appStore = useAppStore()

const layout = computed(() => appStore.getLayout)

const fixedHeader = computed(() => appStore.getFixedHeader)

const footer = computed(() => appStore.getFooter)

const tagsViewStore = useTagsViewStore()

const getCaches = computed((): string[] => {
  return tagsViewStore.getCachedViews
})

// 转场动画完成后触发自定义事件，以便ContentWrap重新计算表格高度
const handleTransitionAfterEnter = () => {
  nextTick(() => {
    // 创建一个自定义事件，避免和其他resize事件处理冲突
    const contentWrapResizeEvent = new CustomEvent('content-wrap-resize')
    window.dispatchEvent(contentWrapResizeEvent)
  })
}
</script>

<template>
  <section
    :class="[
      'p-[var(--app-content-padding)] w-[100%]  h-[100%] bg-[var(--app-content-bg-color)] dark:bg-[var(--el-bg-color)]',
      {
        '!min-h-[calc(100%-var(--app-footer-height))]':
          ((fixedHeader && (layout === 'classic' || layout === 'topLeft')) || layout === 'top') &&
          footer,

        '!min-h-[calc(100%-var(--tags-view-height)-var(--top-tool-height)-var(--app-footer-height))]':
          !fixedHeader && layout === 'classic' && footer,

        '!min-h-[calc(100%-var(--tags-view-height)-var(--app-footer-height))]':
          !fixedHeader && (layout === 'topLeft' || layout === 'top') && footer,

        '!min-h-[calc(100%-var(--top-tool-height))]': fixedHeader && layout === 'cutMenu' && footer,

        '!min-h-[calc(100%-var(--top-tool-height)-var(--tags-view-height))]':
          !fixedHeader && layout === 'cutMenu' && footer
      }
    ]"
  >
    <transition name="zoom-fade" mode="out-in" appear @after-enter="handleTransitionAfterEnter">
      <router-view v-slot="{ Component, route }">
        <keep-alive :include="getCaches">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </router-view>
    </transition>
  </section>
  <Footer v-if="footer" />
</template>

<style lang="scss">
/* zoom-fade */
.zoom-fade-enter-active,
.zoom-fade-leave-active {
  transition: transform 0.2s, opacity 0.3s ease-out;
}
.zoom-fade-enter-from {
  opacity: 0;
  transform: scale(0.92);
}
.zoom-fade-leave-to {
  opacity: 0;
  transform: scale(1.06);
}

/* zoom-out */
.zoom-out-enter-active,
.zoom-out-leave-active {
  transition: opacity 0.1s ease-in-out, transform 0.15s ease-out;
}
.zoom-out-enter-from,
.zoom-out-leave-to {
  opacity: 0;
  transform: scale(0);
}
</style>
