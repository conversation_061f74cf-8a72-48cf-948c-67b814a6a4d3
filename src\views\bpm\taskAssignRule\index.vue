<template>
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="任务名" align="center" prop="taskDefinitionName" />
      <el-table-column label="任务标识" align="center" prop="taskDefinitionKey" />
      <el-table-column label="规则类型" align="center" prop="type">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_ASSIGN_RULE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="规则范围" align="center" prop="options">
        <template #default="scope">
          <el-tag class="mr-5px" :key="option" v-for="option in scope.row.options">
            {{ getAssignRuleOptionName(scope.row.type, option) }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- 新增表单信息列 -->
      <el-table-column label="表单信息" align="center">
        <template #default="scope">
          <el-tag
            v-if="getTaskFormInfo(scope.row)"
            :type="getTaskFormInfo(scope.row)?.formType == '10' ? 'success' : 'danger'"
          >
            {{ getTaskFormTypeLabel(getTaskFormInfo(scope.row)?.formType) }}
          </el-tag>
          <el-tag v-else type="info">未配置</el-tag>
        </template>
      </el-table-column>
      <!-- 新增表单详情列 -->
      <el-table-column label="表单详情" align="center">
        <template #default="scope">
          <div v-if="getTaskFormInfo(scope.row)">
            <el-button
              text
              type="primary"
              size="mini"
              v-if="getTaskFormInfo(scope.row)?.formType === 10"
              @click="viewForm(getTaskFormInfo(scope.row)?.formId)"
            >
              {{ getFormName(getTaskFormInfo(scope.row)?.formId) }}
            </el-button>
            <div v-else-if="getTaskFormInfo(scope.row)?.formType === 20">
              <div>编辑路径: {{ getTaskFormInfo(scope.row)?.formCustomCreatePath }}</div>
              <div>详情路径: {{ getTaskFormInfo(scope.row)?.formCustomViewPath }}</div>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column v-if="queryParams.modelId" label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm(scope.row)"
            v-hasPermi="['bpm:task-assign-rule:update']"
          >
            修改
          </el-button>
          <el-button
            link
            type="primary"
            @click="openFormConfig(scope.row)"
            v-hasPermi="['bpm:task-assign-rule:update']"
          >
            配置表单
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加/修改弹窗 -->
    <TaskAssignRuleForm ref="formRef" @success="getList" />
    <!-- 配置任务表单弹窗 -->
    <TaskFormConfig ref="formConfigRef" @success="getModelAndList" />

    <!-- 新增表单详情的弹窗 -->
    <Dialog v-model="detailVisible" title="表单详情" width="800">
      <form-create :option="detailData.option" :rule="detailData.rule" />
    </Dialog>
  </ContentWrap>
</template>
<script setup lang="ts">
defineOptions({
  name: 'BpmTaskAssignRule'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as TaskAssignRuleApi from '@/api/bpm/taskAssignRule'
import * as ModelApi from '@/api/bpm/model'
import * as FormApi from '@/api/bpm/form'
import * as RoleApi from '@/api/system/role'
import * as DeptApi from '@/api/system/dept'
import * as PostApi from '@/api/system/post'
import * as UserApi from '@/api/system/user'
import * as UserGroupApi from '@/api/bpm/userGroup'
import { setConfAndFields2 } from '@/utils/formCreate'
import TaskAssignRuleForm from './TaskAssignRuleForm.vue'
import TaskFormConfig from './TaskFormConfig.vue'
const { query } = useRoute() // 查询参数

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  modelId: query.modelId, // 流程模型的编号。如果 modelId 非空，则用于流程模型的查看与配置
  processDefinitionId: query.processDefinitionId // 流程定义的编号。如果 processDefinitionId 非空，则用于流程定义的查看，不支持配置
})
const roleOptions = ref<RoleApi.RoleVO[]>([]) // 角色列表
const deptOptions = ref<DeptApi.DeptVO[]>([]) // 部门列表
const postOptions = ref<PostApi.PostVO[]>([]) // 岗位列表
const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表
const userGroupOptions = ref<UserGroupApi.UserGroupVO[]>([]) // 用户组列表
const taskAssignScriptDictDatas = getIntDictOptions(DICT_TYPE.BPM_TASK_ASSIGN_SCRIPT)

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    list.value = await TaskAssignRuleApi.getTaskAssignRuleList(queryParams)
  } finally {
    loading.value = false
  }
}
/** 获取模型数据 */
const modelData = ref<ModelApi.ModelCreateReqVO>()

const getModelData = async () => {
  if (!queryParams.modelId) return
  try {
    const res = await ModelApi.getModel(queryParams.modelId)
    modelData.value = res
  } catch (error) {
    console.error('获取模型数据失败', error)
  }
}
/** 获取任务表单信息 */
const getTaskFormInfo = (row) => {
  if (!modelData.value?.userTaskForms) return null
  return modelData.value.userTaskForms.find(
    (item) => item.taskDefinitionKey === row.taskDefinitionKey
  )
}

/** 获取表单类型标签 */
const getTaskFormTypeLabel = (formType) => {
  if (!formType) return '未配置'
  const dict = getIntDictOptions(DICT_TYPE.BPM_MODEL_FORM_TYPE).find(
    (item) => item.value === formType
  )
  return dict ? dict.label : '未知类型'
}

/** 翻译规则范围 */
// TODO 芋艿：各种 ts 报错
const getAssignRuleOptionName = (type, option) => {
  if (type === 10) {
    for (const roleOption of roleOptions.value) {
      if (roleOption.code == option) {
        return roleOption.name
      }
    }
  } else if (type === 20 || type === 21) {
    for (const deptOption of deptOptions.value) {
      if (deptOption.id == option) {
        return deptOption.name
      }
    }
  } else if (type === 22) {
    for (const postOption of postOptions.value) {
      if (postOption.id == option) {
        return postOption.name
      }
    }
  } else if (type === 30 || type === 31 || type === 32) {
    for (const userOption of userOptions.value) {
      if (userOption.id == option) {
        return userOption.nickname
      }
    }
  } else if (type === 40) {
    for (const userGroupOption of userGroupOptions.value) {
      if (userGroupOption.id == option) {
        return userGroupOption.name
      }
    }
  } else if (type === 50) {
    option = option
    for (const dictData of taskAssignScriptDictDatas) {
      if (dictData.value == option) {
        return dictData.label
      }
    }
  }
  return '未知(' + option + ')'
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (row: TaskAssignRuleApi.TaskAssignVO) => {
  formRef.value.open(queryParams.modelId, row)
}

/** 配置任务表单 */
const formConfigRef = ref()
const openFormConfig = (row: TaskAssignRuleApi.TaskAssignVO) => {
  formConfigRef.value.open(queryParams.modelId, row.taskDefinitionKey, row.taskDefinitionName)
}

/** 查看表单详情 */
const detailVisible = ref(false)
const detailData = ref({
  rule: [],
  option: {}
})

const viewForm = async (formId) => {
  if (!formId) {
    ElMessage.warning('表单ID不存在')
    return
  }

  try {
    // 获取表单数据
    const data = await FormApi.getForm(formId)
    // 设置表单数据
    setConfAndFields2(detailData, data.conf, data.fields)
    // 打开弹窗
    detailVisible.value = true
  } catch (error) {
    console.error('获取表单详情失败', error)
    ElMessage.error('获取表单详情失败')
  }
}

/** 获取模型和列表数据 */
const getModelAndList = async () => {
  await getModelData()
  await getList()
}

/** 获取表单列表 */
const formList = ref([]) // 表单列表数据
const getFormList = async () => {
  formList.value = await FormApi.getSimpleFormList()
}

/** 获取表单名称 */
const getFormName = (formId) => {
  const form = formList.value.find((item) => item.id === formId)
  return form ? form.name : `表单ID: ${formId}`
}

/** 初始化 */
onMounted(async () => {
  // 获取模型数据
  await getModelData()
  await getList()
  // 获得角色列表
  roleOptions.value = await RoleApi.getSimpleRoleList()
  // 获得部门列表
  deptOptions.value = await DeptApi.getSimpleDeptList()
  // 获得岗位列表
  postOptions.value = await PostApi.getSimplePostList()
  // 获得用户列表
  userOptions.value = await UserApi.getSimpleUserList()
  // 获得用户组列表
  userGroupOptions.value = await UserGroupApi.getSimpleUserGroupList()
  // 获得表单列表
  await getFormList()
})
</script>
