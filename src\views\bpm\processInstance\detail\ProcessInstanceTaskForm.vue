<!--
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-14 16:30:46
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2025-04-15 09:34:02
 * @Description: 任务表单组件
-->
<template>
  <template v-if="taskForm">
    <el-divider content-position="center">{{ taskName }}任务表单</el-divider>
    <!-- 情况一：流程表单 -->
    <template v-if="taskForm.formType === 10">
      <form-create
        ref="formRef"
        v-model="formData.value"
        :option="formData.option"
        :rule="formData.rule"
      />
    </template>
    <!-- 情况二：业务表单 -->
    <template v-if="taskForm.formType === 20">
      <component :is="businessFormComponent" :id="businessKey" />
    </template>
  </template>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ProcessInstanceTaskForm'
})

import { ref, computed, PropType, watch, defineExpose } from 'vue'
import { registerComponent } from '@/utils/routerHelper'
import * as FormApi from '@/api/bpm/form'
import { setConfAndFields2 } from '@/utils/formCreate'

// 定义表单类型
interface TaskForm {
  taskDefinitionKey: string
  formType: number
  formId: number | string
  formCustomViewPath?: string
  [key: string]: any
}

// 定义任务类型
interface Task {
  id: string
  definitionKey: string
  name: string
  [key: string]: any
}

const props = defineProps({
  // 任务信息
  task: {
    type: Object as PropType<Task>,
    required: true
  },
  // 业务表单的业务键
  businessKey: {
    type: String,
    default: ''
  },
  // 流程定义中的任务表单配置列表
  userTaskForms: {
    type: Array as PropType<TaskForm[]>,
    default: () => []
  }
})

// 表单相关数据
const formRef = ref()
const formData = ref({
  rule: [],
  option: {},
  value: {}
})

// 计算任务名称
const taskName = computed(() => props.task?.name || '')

// 计算当前任务的表单配置
const taskForm = computed<TaskForm | null>(() => {
  if (!props.userTaskForms || props.userTaskForms.length === 0) return null

  return (
    props.userTaskForms.find((form) => form.taskDefinitionKey === props.task.definitionKey) || null
  )
})

// 业务表单组件
const businessFormComponent = ref<any>(null)

// 初始化流程表单
const initFlowForm = async (formId) => {
  if (!formId) return

  try {
    // 这里应该通过API获取表单详情
    // 可以根据实际需求实现
    // 示例：获取表单并设置
    const data = await FormApi.getForm(formId)
    // 设置conf表单数据
    let conf = JSON.parse(data.conf)
    conf.submitBtn.show = false
    setConfAndFields2(formData, JSON.stringify(conf), data.fields)
    nextTick().then(() => {
      if (formRef.value?.fApi) {
        formRef.value.fApi.btn.show(false)
        formRef.value.fApi.resetBtn.show(false)
        formRef.value.fApi.disabled(false)
        formRef.value.fApi.config.submitBtn.show = false
      }
    })
  } catch (error) {
    console.error('获取表单详情失败', error)
  }
}

// 监听taskForm变化，初始化表单
watch(
  taskForm,
  async (newVal) => {
    if (!newVal) return

    if (newVal.formType === 10) {
      await initFlowForm(newVal.formId)
    } else if (newVal.formType === 20 && newVal.formCustomCreatePath) {
      businessFormComponent.value = registerComponent(newVal.formCustomCreatePath)
    }
  },
  { immediate: true }
)

// 提供获取表单数据的方法
const getFormData = () => {
  if (taskForm.value?.formType === 10) {
    return formData.value.value
  }
  return {}
}

// 暴露方法给父组件
defineExpose({
  getFormData
})
</script>
