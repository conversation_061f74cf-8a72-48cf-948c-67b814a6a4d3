<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>链接未生效</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #2482f4 0%, #1e6fd9 50%, #1a5bb8 100%);
            background-attachment: fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(36, 130, 244, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(36, 130, 244, 0.2) 0%, transparent 50%);
            pointer-events: none;
        }

        .container {
            text-align: center;
            padding: 3rem 2rem;
            max-width: 600px;
            width: 100%;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 24px;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.12),
                0 16px 32px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: fadeInUp 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        }

        .error-icon {
            font-size: clamp(2rem, 5vw, 3rem);
            margin-bottom: 1.5rem;
            display: inline-block;
            opacity: 0.8;
        }

        .error-title {
            font-size: clamp(2rem, 6vw, 3.5rem);
            font-weight: 700;
            color: #2482f4;
            margin-bottom: 1.5rem;
            letter-spacing: -0.02em;
        }

        .error-message {
            font-size: clamp(1rem, 2.5vw, 1.3rem);
            color: #4a5568;
            margin-bottom: 2rem;
            line-height: 1.8;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(36, 130, 244, 0.05);
            border-radius: 50%;
        }

        .shape:nth-child(1) {
            width: 120px;
            height: 120px;
            top: 15%;
            left: 8%;
        }

        .shape:nth-child(2) {
            width: 80px;
            height: 80px;
            top: 65%;
            right: 12%;
        }

        .shape:nth-child(3) {
            width: 100px;
            height: 100px;
            bottom: 25%;
            left: 15%;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }



        /* 移动端适配 */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .container {
                margin: 0;
                padding: 2rem 1.5rem;
                border-radius: 15px;
                max-width: 100%;
            }

            .error-icon {
                font-size: clamp(1.5rem, 6vw, 2.5rem);
                margin-bottom: 1rem;
            }

            .error-title {
                font-size: clamp(1.5rem, 8vw, 2.5rem);
                margin-bottom: 1rem;
            }

            .error-message {
                font-size: clamp(0.9rem, 3vw, 1.1rem);
                margin-bottom: 1rem;
                padding: 0 0.5rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 0.5rem;
            }

            .container {
                padding: 1.5rem 1rem;
                border-radius: 12px;
            }

            .error-icon {
                font-size: clamp(1.2rem, 8vw, 2rem);
                margin-bottom: 1rem;
            }

            .error-title {
                font-size: clamp(1.2rem, 10vw, 2rem);
                margin-bottom: 0.8rem;
            }

            .error-message {
                font-size: clamp(0.85rem, 4vw, 1rem);
                line-height: 1.5;
            }

            .shape {
                display: none;
            }
        }

        /* 超小屏幕适配 */
        @media (max-width: 320px) {
            .container {
                padding: 1rem 0.8rem;
            }

            .error-icon {
                font-size: 1.5rem;
            }

            .error-title {
                font-size: 1.2rem;
            }

            .error-message {
                font-size: 0.8rem;
            }
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            .container {
                background: white;
                border: 2px solid #333;
            }

            .error-title {
                color: #2482f4;
            }
        }

        /* 减少动画模式支持 */
        @media (prefers-reduced-motion: reduce) {
            .container {
                animation: none;
            }
        }

        /* 鼠标悬停效果 */
        .container:hover {
            transform: translateY(-2px);
            box-shadow:
                0 40px 80px rgba(0, 0, 0, 0.15),
                0 20px 40px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        /* 添加一些微妙的渐变效果 */
        .error-message::selection {
            background: rgba(36, 130, 244, 0.2);
        }

        .error-title::selection {
            background: rgba(36, 130, 244, 0.2);
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <div class="error-icon">⚠️</div>
        <h1 class="error-title">链接未生效</h1>
        <p class="error-message">  
           很抱歉，您所访问的链接目前未生效。<br>
           如有进一步需求，可联系相关负责人员获取帮助。
        </p>
    </div>
</body>
</html>
