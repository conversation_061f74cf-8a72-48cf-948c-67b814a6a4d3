<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-25 11:47:52
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-06-09 09:55:06
 * @Description: 
-->
<template>
  <div :class="prefixCls" class="h-[100%] relative <sm:px-0px <xl:px-10px <md:px-10px login-main">
    <div
      :class="`${prefixCls}__top w-full h-14 flex items-center justify-between px-16 bg-white <sm:px-5`"
    >
      <img class="h-1/2" src="@/assets/imgs/goldpac.png" />
      <GoldButton :title="t('common.login')" class="w-80px" @click="showLoginForm" />
    </div>
    <div
      :class="`${prefixCls}__bottom h-full w-full flex mx-auto flex items-center justify-center <sm:justify-end`"
    >
      <div class="relative left-50 font-color <sm:(w-[66%] left-0)">
        <div class="text-bold text-36px py-12px text-black">{{ t('login.sendCardService') }}</div>
        <div class="text-18px py-12px leading-[1.2]"> {{ t('login.introOne') }} </div>
        <div class="text-14px <sm:leading-[1.2]">
          <div class="py-1 dotted my-2"> {{ t('login.introTwo') }} </div>
          <div class="py-1 dotted my-2">{{ t('login.introThree') }}</div>
          <div class="py-1 dotted my-2">{{ t('login.introFour') }}</div>
          <div class="py-1 dotted my-2">{{ t('login.introFive') }}</div>
          <div class="py-1 dotted my-2">{{ t('login.introSix') }}</div>
          <div class="py-1 dotted my-2">{{ t('login.introSeven') }}</div>
        </div>
      </div>
      <Transition
        appear
        enter-active-class="animate__animated animate__bounceInRight animate__fadeIn"
        leave-active-class="animate__fadeOut animate__animated"
      >
        <div
          v-show="ifShowLoginForm"
          class="z-1111 bg-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[100%] @2xl:max-w-600px @xl:max-w-600px @md:max-w-600px @lg:max-w-600px"
        >
          <el-icon :size="20" class="!absolute top-5 right-8 cursor-pointer z-100">
            <Close @click="ifShowLoginForm = false" />
          </el-icon>

          <!-- 账号登录 -->
          <LoginForm
            class="p-20px h-auto m-auto <xl:(rounded-3xl light:bg-white)"
            @change-form-type="changeFormType"
            v-show="loginType === LOGIN_TYPE.LOGIN"
            ref="loginForm"
          />
          <ForgetPasswordForm
            class="p-20px h-auto m-auto <xl:(rounded-3xl light:bg-white)"
            @change-form-type="changeFormType"
            v-show="loginType === LOGIN_TYPE.FORGET_PASSWORD"
            ref="ForgetPasswordFormRef"
          />
          <ResetForm
            class="p-20px h-auto m-auto <xl:(rounded-3xl light:bg-white)"
            :code="code"
            :email="email"
            @change-form-type="changeFormType"
            v-show="loginType === LOGIN_TYPE.RESET_PASSWORD"
          />
        </div>
      </Transition>
    </div>
    <!-- 蒙板 -->
    <teleport to="body">
      <div class="modal-mask" v-show="ifShowLoginForm"></div>
    </teleport>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'Login'
})

// import { underlineToHump } from '@/utils'

import { useDesign } from '@/hooks/web/useDesign'
const { t } = useI18n()
import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

import { ForgetPasswordForm, LoginForm, ResetForm } from './components'

import { Close } from '@element-plus/icons-vue' //图标
import { ref } from 'vue'
import { LOGIN_TYPE, AccessTokenKey, RefreshTokenKey } from './components/useLogin'

const loginType = ref<number>(LOGIN_TYPE.LOGIN) //1是登录，2是忘记密码，3是重置密码

const loginForm = ref()

const ForgetPasswordFormRef = ref()

const code = ref('')

const email = ref('')

// 移除 timer 和 timeCount
const changeFormType = async (type: number) => {
  if (type === LOGIN_TYPE.RESET_PASSWORD) {
    // 重置密码表单需要获取验证码和邮箱信息(只有是通过忘记密码后重置密码,才需要拿忘记密码表单里面的code和email)
    if (ForgetPasswordFormRef.value?.loginData?.loginForm) {
      code.value = ForgetPasswordFormRef.value.loginData.loginForm.code || ''
      email.value = ForgetPasswordFormRef.value.loginData.loginForm.email || ''
    }
  }
  loginType.value = type
}
// const { t } = useI18n()
// const appStore = useAppStore()
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('login')

//是否显示登录form
let ifShowLoginForm = ref<boolean>(true)

const showLoginForm = () => {
  loginType.value = LOGIN_TYPE.LOGIN
  ifShowLoginForm.value = true
}

onMounted(() => {
  wsCache.delete(AccessTokenKey)
  wsCache.delete(RefreshTokenKey)
})
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-login;

.#{$prefix-cls} {
  &__bottom {
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      background-image: url('@/assets/imgs/login-bg.png');
      background-position: center;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      content: '';
    }
  }
}
.font-color {
  color: #7d8291;
}
.dotted::before {
  content: '•';
  margin-right: 10px;
}

.bg-white {
  background-color: white;
}
// 蒙板
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.el-button) {
  &:hover {
    border-color: var(--client-color-primary-light-3);
    background-color: var(--client-color-primary-light-7);
  }
}
</style>
