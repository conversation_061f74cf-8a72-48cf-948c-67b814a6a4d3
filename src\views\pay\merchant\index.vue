<template>
  <!-- 列表 -->
  <UmvContent>
    <!-- 搜索工作栏 -->
    <UmvQuery
      v-model="queryParams"
      ref="queryFormRef"
      label-width="68px"
      @check="handleQuery"
      :opts="queryOpts"
      :check="false"
      :reset="false"
    >
      <!-- 自定义底部按钮，实现自己的重置逻辑 -->
      <template #footerBtn="{ check }">
        <el-button class="btn_check" @click="check" type="primary">
          {{ t('common.query') }}
        </el-button>
        <el-button class="btn_reset" @click="customResetHandle">
          {{ t('common.reset') }}
        </el-button>
      </template>
    </UmvQuery>

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="handleQuery">
      <template #tools>
        <el-button
          plain
          type="primary"
          @click="openForm('create')"
          v-hasPermi="['pay:merchant:create']"
        >
          <icon-ep-plus style="font-size: 12px" class="mr-5px" /> {{ t('common.newAdd') }}
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['pay:merchant:export']"
        >
          <icon-ep-download style="font-size: 12px" class="mr-5px" />{{ t('common.export') }}
        </el-button>
      </template>

      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单弹窗：添加/修改 -->
    <MerchantForm ref="formRef" @success="getList" />
  </UmvContent>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'PayMerchant'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as MerchantApi from '@/api/pay/merchant'
import MerchantForm from './MerchantForm.vue'
import { defaultTime } from '@/utils/formatTime'
import { checkPermi } from '@/utils/permission'

import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { UmvContent } from '@/components/UmvContent'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<MerchantApi.MerchantVO[]>([]) // 列表的数据
const queryParams = ref<MerchantApi.MerchantPageReqVO>({
  pageNo: 1,
  pageSize: 10,
  name: '',
  shortName: '',
  status: undefined,
  no: '',
  remark: undefined,
  createTime: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 搜索表单配置
const queryOpts: Record<string, QueryOption> = {
  no: {
    label: t('pay.merchant.no'),
    prop: 'no',
    defaultVal: '',
    controlRender: () => (
      <el-input
        v-model={queryParams.value.no}
        placeholder={t('pay.merchant.noPlaceholder')}
        clearable
        maxlength={32}
      />
    )
  },
  name: {
    label: t('pay.merchant.name'),
    prop: 'name',
    defaultVal: '',
    controlRender: () => (
      <el-input
        v-model={queryParams.value.name}
        placeholder={t('pay.merchant.namePlaceholder')}
        clearable
        maxlength={64}
      />
    )
  },
  shortName: {
    label: t('pay.merchant.shortName'),
    prop: 'shortName',
    defaultVal: '',
    controlRender: () => (
      <el-input
        v-model={queryParams.value.shortName}
        placeholder={t('pay.merchant.shortNamePlaceholder')}
        clearable
        maxlength={64}
      />
    )
  },
  status: {
    label: t('pay.merchant.status'),
    prop: 'status',
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.status}
        placeholder={t('pay.merchant.statusPlaceholder')}
        clearable
      >
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  remark: {
    label: t('pay.merchant.remark'),
    prop: 'remark',
    defaultVal: '',
    controlRender: () => (
      <el-input
        v-model={queryParams.value.remark}
        placeholder={t('pay.merchant.remarkPlaceholder')}
        clearable
      />
    )
  },
  createTime: {
    label: t('pay.merchant.createTime'),
    prop: 'createTime',
    defaultVal: undefined,
    controlRender: () => (
      <el-date-picker
        v-model={queryParams.value.createTime}
        type="daterange"
        start-placeholder={t('common.startDateText')}
        end-placeholder={t('common.endDateText')}
        default-time={defaultTime()}
        value-format="YYYY-MM-DD HH:mm:ss"
      />
    )
  }
}

// 表格列配置
const columns: TableColumn[] = [
  {
    prop: 'id',
    label: t('pay.merchant.id'),
    align: 'center'
  },
  {
    prop: 'no',
    label: t('pay.merchant.no'),
    align: 'center',
    width: '180',
    showOverflowTooltip: true
  },
  {
    prop: 'name',
    label: t('pay.merchant.name'),
    align: 'center',
    showOverflowTooltip: true
  },
  {
    prop: 'shortName',
    label: t('pay.merchant.shortName'),
    align: 'center'
  },
  {
    prop: 'status',
    label: t('pay.merchant.status'),
    align: 'center',
    renderTemplate: ({ row }) => (
      <el-switch
        v-model={row.status}
        active-value={0}
        inactive-value={1}
        onChange={() => handleStatusChange(row)}
      />
    )
  },
  {
    prop: 'remark',
    label: t('pay.merchant.remark'),
    align: 'center',
    showOverflowTooltip: true
  },
  {
    prop: 'createTime',
    label: t('pay.merchant.createTime'),
    align: 'center',
    width: '180',
    renderTemplate: (scope) => {
      return <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    }
  },
  {
    prop: 'operate',
    label: t('common.operate'),
    align: 'center',
    renderTemplate: ({ row }) => (
      <div>
        {checkPermi(['pay:merchant:update']) && (
          <el-button link type="primary" onClick={() => openForm('update', row.id)}>
            {t('common.modify')}
          </el-button>
        )}
        {checkPermi(['pay:merchant:delete']) && (
          <el-button link type="danger" onClick={() => handleDelete(row.id)}>
            {t('common.delete')}
          </el-button>
        )}
      </div>
    )
  }
]

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MerchantApi.getMerchantPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 自定义重置按钮操作 */
const customResetHandle = async () => {
  // 1. 然后调用子组件的重置方法
  await queryFormRef.value.resetFields()
  // 2. 先执行自定义逻辑
  queryParams.value.remark = undefined

  // 3. 最后手动触发查询（因为已经移除了子组件resetHandle中的自动查询）
  await handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MerchantApi.deleteMerchant(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 修改状态操作 */
const handleStatusChange = async (row: MerchantApi.MerchantVO) => {
  try {
    // 修改状态的二次确认
    const text = row.status === CommonStatusEnum.ENABLE ? t('common.enable') : t('common.disable')
    const str = t('pay.merchant.desc1', {
      text: text,
      name: row.name
    })

    await message.confirm(str, t('common.reminder'))
    // 发起修改状态
    await MerchantApi.updateMerchantStatus(row.id, row.status)
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.status =
      row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await MerchantApi.exportMerchant(queryParams.value)
    download.excel(data.data, t('pay.merchant.merchantXls') + '.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
