/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-21 10:08:00
 * @LastEditors: HoJack
 * @LastEditTime: 2023-07-21 12:02:50
 * @Description:
 */
import { store } from '../index'
import { defineStore } from 'pinia'
import { getToken, autoAuthorize } from '@/api/login/oauth2'
import * as authUtil from '@/utils/auth'
import { resetRouter } from '@/router'
import { useCache } from '@/hooks/web/useCache'
import { useUserStoreWithOut } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
const { wsCache } = useCache()
import { useLocaleStoreWithOut } from '@/store/modules/locale'
import envController from '@/controller/envController'

const localeStore = useLocaleStoreWithOut()
interface ssoVO {
  clientId: string
  authUrl: string
  appUrl: string
  redirectUri: string
  responseType: string
}

export const useSsoStore = defineStore('sso', {
  state: (): ssoVO => ({
    clientId: import.meta.env.VITE_APP_CLIENT_ID,
    authUrl: '',
    appUrl: window.location.origin, //不带目录
    redirectUri: '',
    responseType: 'code'
  }),
  getters: {},
  actions: {
    /**
     * 设置重定向 URI
     *
     * 将当前应用的 URL 编码并拼接 '/#/' 后缀，然后赋值给 redirectUri 属性
     *
     * @returns 返回编码后的重定向 URI
     */
    setRedirectUri() {
      // 如果是专线IP模式，需要特殊处理appUrl
      if (envController.isDirectIPMode && envController.isDirectIPMode()) {
        this.appUrl = envController.getServiceUrl() as string
      } else {
        this.appUrl = window.location.origin
      }

      this.redirectUri = encodeURIComponent(this.appUrl + '/#/') //应用地址编码后拼接/#/路径

      return this.redirectUri
    },
    /**
     * 设置授权URL
     *
     * @returns 无返回值，但会更新实例的 redirectUri 属性
     */
    setAuthUrl() {
      try {
        // 专线模式下直接使用getServiceUrl获取服务URL
        if (envController.isDirectIPMode()) {
          this.authUrl = envController.getServiceUrl() as string
          if (!this.authUrl) {
            throw new Error('专线模式下未找到授权地址')
          }
        } else {
          // 非专线模式下的原有逻辑
          if (envController.getEnvMapServiceUrl().size === 0) {
            throw new Error('未找到授权地址')
          }
          if (envController.getEnvironment() === '') {
            throw new Error('未找到环境')
          }
          this.authUrl = envController.getServiceUrl() as string
        }
      } catch (error) {
        console.log(error)
      }
    },
    //授权码获取token
    async getTokenByCode(code: string) {
      const { t } = useI18n()
      return new Promise<boolean>(async (resolve, reject) => {
        try {
          const params = {
            code: code,
            redirect_uri: decodeURIComponent(this.setRedirectUri()),
            grant_type: 'authorization_code',
            'client-id': this.clientId
          }
          console.log('获取授权码参数', params)

          const ssoRes = await getToken(params)

          if (ssoRes.code !== 0) {
            reject(false)
            return
          }

          authUtil.setToken(ssoRes.data)

          resolve(true)
        } catch (error) {
          ElMessage.error(t('store.store.getCodeFail'))
        }
        reject(false)
      })
    },
    /**
     * 单点登录授权-获取授权码并重定向到第三方应用
     *
     * @param clientId 客户端ID(oauth2.0中配置的clientId)
     * @param redirectUri 重定向URI(正常是从URL上的参数获取)
     * @returns 无返回值
     */
    async ssoAuth(clientId, redirectUri) {
      const loadingInstance = ElLoading.service({
        lock: true
      })
      try {
        // 处理IP专线模式下的重定向URI
        if (envController.isDirectIPMode()) {
          const thisUrl = new URL(redirectUri)

          // 判断是客户端还是服务端
          if (thisUrl.pathname.includes('client')) {
            redirectUri = `${envController.getClientUrl()}/#/`
          } else if (thisUrl.pathname.includes('service')) {
            redirectUri = `${envController.getServiceUrl()}/#/`
          }
        }
        // 非专线模式，直接使用传入的 redirectUri
        console.log('重定向URI:', redirectUri)

        // 获取所有配置的环境源
        const trustedOrigins = envController.getAllEnvOrigins()
        const redirectUriOrigin = new URL(redirectUri).origin
        // 添加localhost:4000为信任源
        if (!trustedOrigins.has(redirectUriOrigin)) {
          trustedOrigins.add('http://localhost:4000')
        }

        // 如果redirectUri在信任源中且不含/#/，则添加/#/
        if (trustedOrigins.has(redirectUriOrigin) && !redirectUri.includes('#/')) {
          // 确保URL末尾没有斜杠，避免重复
          redirectUri = redirectUri.endsWith('/') ? `${redirectUri}#/` : `${redirectUri}/#/`
        }
        //获取授权码
        const data = (await autoAuthorize(clientId, redirectUri)) as string
        console.log('重定向第三方应用地址:', data)
        if (!data) {
          return
        }
        const currentLang = localeStore.getCurrentLocale.lang

        let ssoCallbackRedirectUri = redirectUri

        // 只有当 redirectUri 的源在信任列表中时，才添加 /ssoCallback
        if (trustedOrigins.has(redirectUriOrigin) && !redirectUri.includes('ssoCallback')) {
          ssoCallbackRedirectUri = redirectUri.endsWith('/')
            ? `${redirectUri}ssoCallback`
            : `${redirectUri}/ssoCallback`
        }

        const url =
          data.replace(redirectUri, ssoCallbackRedirectUri) + //修改重定向地址,跳转至单点登录回调地址
          `&lang=${currentLang}` +
          `&tenantId=${authUtil.getTenantId()}` +
          `&goPath=ssoCallback`
        console.log('url', url)
        location.href = url
      } catch (error) {
        console.log(error)
      } finally {
        loadingInstance.close()
      }
    },
    async ssoLogin() {
      const lang = localeStore.getCurrentLocale.lang
      resetRouter() // 重置静态路由表
      wsCache.clear()
      authUtil.removeTenantId()
      authUtil.removeToken()
      const userStore = useUserStoreWithOut()
      userStore.resetState() //清除用户信息

      this.setRedirectUri()
      this.setAuthUrl() //设置授权地址

      const url =
        this.authUrl +
        '/#/sso?client_id=' +
        this.clientId +
        `${lang ? `&lang=${lang}` : ''}` +
        '&redirect_uri=' +
        this.redirectUri +
        '&response_type=' +
        this.responseType

      window.location.href = url
    }
  }
})

export const useSsoStoreWithOut = () => {
  return useSsoStore(store)
}
