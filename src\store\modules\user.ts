import { store } from '../index'
import { defineStore } from 'pinia'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { getInfo, getUserInfo, loginOut } from '@/api/login'
import * as authUtil from '@/utils/auth'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { isEmpty } from 'lodash-es'
import router from '@/router/index'
import { useLocaleStore } from '@/store/modules/locale'
import { useLocale } from '@/hooks/web/useLocale'

const { wsCache } = useCache()
const message = useMessage()

//代客操作
import { useAgentOperationStore } from '@/store/modules/agentOperation'

interface UserVO {
  id: number
  avatar: string
  nickname: string
}
interface RoleVO {
  isStale: any
  id: number
  name: string
  code: string
  sort: number
  type: number
}

interface UserInfoVO {
  roles: RoleVO[]
  clients: ClientVO[]
  currentClientId: number | undefined
  isSetUser: boolean
  user: UserVO
}

export interface ClientVO {
  applicationVOlist: any[]
  code: string
  createTime: number
  description: string
  id: number
  name: string
  oauthClient: string
  status: number
  url: string
}

export const useUserStore = defineStore('admin-user', {
  state: (): UserInfoVO => ({
    roles: [],
    isSetUser: false,
    currentClientId: undefined,
    clients: [],
    user: {
      id: 0,
      avatar: '',
      nickname: ''
    }
  }),
  getters: {
    getRoles(): RoleVO[] {
      return this.roles
    },

    getCurrentClientId(): number | undefined {
      const cacheClientId = wsCache.get(CACHE_KEY.CURRENT_CLIENT_ID)
      if (cacheClientId) {
        this.currentClientId = cacheClientId
      }
      return this.currentClientId
    },
    getClients(): ClientVO[] {
      return this.clients
    },
    getIsSetUser(): boolean {
      return this.isSetUser
    },
    getUser(): UserVO {
      // 如果内存中没有用户信息但缓存中有，则从缓存恢复
      if (this.user.id === 0) {
        const cachedUser = wsCache.get(CACHE_KEY.USER)
        if (!isEmpty(cachedUser) && cachedUser.user) {
          this.user = cachedUser.user
          this.isSetUser = true
        }
      }
      return this.user
    }
  },
  actions: {
    async setUserInfoAction(refreshUser?: boolean) {
      if (!authUtil.getAccessToken()) {
        this.resetState()
        return null
      }
      let userInfo = wsCache.get(CACHE_KEY.USER)

      if (isEmpty(userInfo) || refreshUser) {
        try {
          // userInfo = await getInfo()
          userInfo = await getUserInfo()
          this.user = userInfo.user
        } catch (error) {
          const { t } = useI18n()
          // message.error('获取用户信息失败')
          console.log('获取用户信息失败' + error)
          throw t('sys.user.gerUserInfoFail')
        }
      }

      this.isSetUser = true
      wsCache.set(CACHE_KEY.USER, userInfo)

      // 无交互端ID缓存则设置当前交互端ID
      if (!userInfo.clients || userInfo.clients.length === 0) {
        const agentOperationStore = useAgentOperationStore()

        if (agentOperationStore.agentOperationMode) {
          ElMessageBox.alert('代客操作模式失败，将重新切回超管账号', '提示', {
            confirmButtonText: '确定',
            callback: () => {
              agentOperationStore.setLoginToken(true)
              router.push({
                path: '/'
              })
            }
          })

          return
        } else {
          ElMessage.error('该用户无当前交互端的权限，请联系管理员！!')
          router.push({
            name: 'ssoError',
            query: {
              noClient: 1
            }
          })
          return
        }
      }

      if (!userInfo?.roles || userInfo.roles.length === 0) {
        const { t } = useI18n()
        message.warning(t('sys.user.noRole'))
        return
      }

      this.roles = userInfo.roles
      this.clients = userInfo.clients

      // 获取client第一个作为默认交互端，只有登录时才会获取第一个为默认交互端，切换端跳转时url中会带上clientId，到达时将url中的clientId作为默认端
      const cacheClientId = userInfo.clients[0].id //当前客户id
      this.setCurrentClientId(cacheClientId)
    },

    // 退出登录
    loginOut(title?: string) {
      const { t } = useI18n()
      ElMessageBox.confirm(title ? title : t('common.loginOutMessage'), t('common.reminder'), {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
        .then(async () => {
          const localeStore = useLocaleStore()
          // const tenantId = authUtil.getTenantId() //记录租户id，退出时带至登录
          const lang = localeStore.getCurrentLocale.lang
          await loginOut()
          authUtil.removeToken()
          const loginForm = authUtil.getLoginForm()
          wsCache.clear()
          localeStore.setCurrentLocale({
            lang
          })
          const { changeLocale } = useLocale()
          changeLocale(lang)
          loginForm && authUtil.setLoginForm(loginForm)
          this.resetState()
          const tagsViewStore = useTagsViewStore()
          tagsViewStore.delAllViews()

          window.location.replace(`#/home?l=1&redirect=/HomePage`)
        })
        .catch(() => {
          // this.handleClear()
        })
    },
    async regetUserInfo() {
      // 重新获取用户信息
      let userInfo
      try {
        // userInfo = await getInfo()
        userInfo = await getUserInfo()
        console.log('userInfo', userInfo)
      } catch (error) {
        // message.error('获取用户信息失败')
        console.log('获取用户信息失败')
        throw '获取用户信息失败'
      }
      this.isSetUser = true
      this.user = userInfo.user
      wsCache.set(CACHE_KEY.USER, userInfo)
    },

    resetState() {
      this.roles = []
      this.isSetUser = false
      this.user = {
        id: 0,
        avatar: '',
        nickname: ''
      }
      wsCache.delete(CACHE_KEY.USER) // 删除用户信息缓存
      wsCache.delete(CACHE_KEY.ROLE_PERMISSIONS) // 删除角色权限缓存
      wsCache.delete(CACHE_KEY.ROLE_ROUTERS) // 删除角色路由缓存
    },

    setCurrentClientId(clientId: number | undefined) {
      this.currentClientId = clientId
      wsCache.set(CACHE_KEY.CURRENT_CLIENT_ID, clientId)
    },
    setRoles(rolesList) {
      this.roles = rolesList
    },
    setUser(userInfo) {
      this.user = userInfo
      wsCache.set(CACHE_KEY.USER, userInfo)
    }
  }
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
