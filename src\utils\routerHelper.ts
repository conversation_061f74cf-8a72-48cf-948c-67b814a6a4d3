import type { RouteLocationNormalized, Router, RouteRecordNormalized } from 'vue-router'
import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { isUrl } from '@/utils/is'
import { cloneDeep, isEmpty, omit } from 'lodash-es'

const modules = import.meta.glob('../views/**/*.{vue,tsx}')

/**
 * 注册一个异步组件
 * @param componentPath 例:/bpm/oa/leave/detail
 */
export const registerComponent = (componentPath: string) => {
  for (const item in modules) {
    if (item.includes(componentPath)) {
      // 使用异步组件的方式来动态加载组件
      // @ts-ignore
      return defineAsyncComponent(modules[item])
    }
  }
}

/**
 *
 * @param route
 * @returns  返回异步组件
 */
export const setComponent = (route) => {
  const modulesRoutesKeys = Object.keys(modules)
  const index = route?.component
    ? modulesRoutesKeys.findIndex((ev) => ev.includes(route.component))
    : modulesRoutesKeys.findIndex((ev) => ev.includes(route.path))
  return {
    index,
    component: modules[modulesRoutesKeys[index]]
  }
}
import router from '@/router/index'

/**
 * 设置首页,选择addRoute第一个子菜单为首页
 * @param addRouteList
 */

export const setHome = (addRouteList: AppCustomRouteRecordRaw[]) => {
  console.log(addRouteList)

  const { t } = useI18n()
  let homeComponent = ''

  //过滤首页菜单
  const homePageList: string[] = []
  //存储 菜单
  const ortherPageList: string[] = []
  const filterHomePae = (routeList) => {
    routeList.forEach((item: AppCustomRouteRecordRaw) => {
      if (item.isHomePage) {
        homePageList.push(item.component)
      }
      if (item?.parentId != 0 && !item.meta.hidden && item?.component) {
        ortherPageList.push(item.component)
      }
      if (item.children && item.children.length > 0) {
        filterHomePae(item.children)
      }
    })

    return homePageList
  }
  filterHomePae(addRouteList)

  //如果有设置首页则不设置默认首页
  if (homePageList.length > 0) {
    //todo:可能存在多个首页,只取第一个
    homeComponent = homePageList[0]
  } else {
    //找出第一个路由
    homeComponent = ortherPageList[0]
  }
  const homeRoute = {
    path: '/',
    component: Layout,
    name: 'HomePage',
    redirect: '/HomePage',
    meta: {
      // title: t('router.home'),
      icon: 'ep:home-filled',
      noTagsView: true
    },
    children: [
      {
        path: 'HomePage',
        component: homeComponent,
        name: 'Index',
        meta: {
          title: t('router.home'),
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          noTagsView: false
        }
      }
    ]
  }
  router.addRoute(homeRoute as unknown as RouteRecordRaw)
  return homeRoute
}

/* Layout */
export const Layout = () => import('@/layout/Layout.vue')

export const getParentLayout = () => {
  return () =>
    new Promise((resolve) => {
      resolve({
        name: 'ParentLayout'
      })
    })
}

// 按照路由中meta下的rank等级升序来排序路由
export const ascending = (arr: any[]) => {
  arr.forEach((v) => {
    if (v?.meta?.rank === null) v.meta.rank = undefined
    if (v?.meta?.rank === 0) {
      if (v.name !== 'home' && v.path !== '/') {
        console.warn('rank only the home page can be 0')
      }
    }
  })
  return arr.sort((a: { meta: { rank: number } }, b: { meta: { rank: number } }) => {
    return a?.meta?.rank - b?.meta?.rank
  })
}

export const getRawRoute = (route: RouteLocationNormalized): RouteLocationNormalized => {
  if (!route) return route
  const { matched, ...opt } = route
  return {
    ...opt,
    matched: (matched
      ? matched.map((item) => ({
          meta: item.meta,
          name: item.name,
          path: item.path
        }))
      : undefined) as RouteRecordNormalized[]
  }
}

//判断路由是否顶级目录且是路由(顶级非目录路由)
const routeIfTop = (route: AppCustomRouteRecordRaw) => {
  //     if (!route.children && route.parentId === 0 && route.component) {
  if (route.parentId === 0 && route.component) {
    //如果无子路由，且父级是0，则认为是顶级目录路由
    if (!route.children) return true
    //如果子路由有且都是隐藏，则认为是顶级目录路由
    if (route.children && route.children.length > 0) {
      //子路由是否都是隐藏
      const childrenRoutIfHidden = route.children.every((item) => {
        if (item.visible === false) {
          return true
        }
      })

      return childrenRoutIfHidden
    }
    return false
  }
}

// 后端控制路由生成
export const generateRoute = (routes: AppCustomRouteRecordRaw[]): AppRouteRecordRaw[] => {
  const res: AppRouteRecordRaw[] = []
  for (const route of routes) {
    const meta = {
      title: route.name,
      icon: route.icon,
      hidden: !route.visible,
      noCache: !route.keepAlive,
      alwaysShow:
        route?.children &&
        route?.children.length === 1 &&
        (route.alwaysShow !== undefined ? route.alwaysShow : true),
      scene: route.scene,
      code: route.code
    }

    // 路由地址转首字母大写驼峰，作为路由名称，适配keepAlive
    let data: AppRouteRecordRaw = {
      path: route.path,
      name:
        route.componentName && route.componentName.length > 0
          ? route.componentName
          : toCamelCase(route.path, true),
      redirect: route.redirect,
      meta: meta,
      catalog: route.catalog,
      parentId: route.parentId,
      isHomePage: route.isHomePage
    }
    //处理顶级非目录路由 todo: 顶级目录且是路由,底下有子路由，将判断错误，需要修改

    if (routeIfTop(route)) {
      data.component = Layout
      data.meta = {}

      data.name = toCamelCase(route.path, true) + 'Parent'
      data.redirect = ''
      meta.alwaysShow = true
      const childrenData: AppRouteRecordRaw = {
        path: '',
        name: toCamelCase(route.path, true),
        redirect: route.redirect,
        meta: meta,
        isHomePage: data.isHomePage
      }
      data.isHomePage = false

      const { index, component } = setComponent(route)

      childrenData.component = component
      //顶级目录且是路由,且有子路由，则将子路由作为children
      if (route.children) {
        childrenData.children = generateRoute(route.children)
      }
      data.children = [childrenData]
      if (index === -1) {
        // 对路由路径对不上的进行标记
        data.meta.isEmptyComponent = true
      }
    } else {
      // 目录
      if (route.children && route.parentId === 0) {
        data.component = Layout
        data.redirect = getRedirect(route.path, route.children)
        // 外链
      } else if (isUrl(route.path)) {
        data = {
          path: '/external-link',
          component: Layout,
          meta: {
            name: route.name
          },
          children: [data]
        } as AppRouteRecordRaw
        // 菜单
      } else {
        // 对后端传component组件路径和不传做兼容（如果后端传component组件路径，那么path可以随便写，如果不传，component组件路径会根path保持一致）
        const { index, component } = setComponent(route)
        data.component = component
        if (index === -1) {
          // 对路由路径对不上的进行标记
          data.meta.isEmptyComponent = true
        }
      }
      if (route.children) {
        data.children = generateRoute(route.children)
      }
    }
    res.push(data as AppRouteRecordRaw)
  }

  // console.log('route-res', res)
  // debugger

  return res
}
export const getRedirect = (parentPath: string, children: AppCustomRouteRecordRaw[]) => {
  if (!children || children.length == 0) {
    return parentPath
  }
  const path = generateRoutePath(parentPath, children[0].path)
  // 递归子节点
  if (children[0].children) return getRedirect(path, children[0].children)
}
const generateRoutePath = (parentPath: string, path: string) => {
  // ;(parentPath === null || parentPath === 0) && (parentPath = '')
  if (parentPath.endsWith('/')) {
    parentPath = parentPath.slice(0, -1) // 移除默认的 /
  }
  if (!path.startsWith('/')) {
    path = '/' + path
  }
  return parentPath + path
}
export const pathResolve = (parentPath: string, path: string) => {
  if (isUrl(path)) return path
  // ;(path === null || path === 0) && (path = '')
  const childPath = path.startsWith('/') || !path ? path : `/${path}`
  return `${parentPath}${childPath}`.replace(/\/\//g, '/')
}

// 面包屑路径处理，兼容路径前包含'/'和不包含的情况，如果路径前不含有'/'，则在前方加上父级路径，以此进行hidden匹配
export const breadcrumbPathResolve = (parentPath: string, path: string) => {
  if (isUrl(path)) return path
  const pathRes = path.startsWith('/') || !path ? path : `${parentPath}/${path}`
  return pathRes.replace(/\/\//g, '/')
}

// 路由降级
export const flatMultiLevelRoutes = (routes: AppRouteRecordRaw[]) => {
  const modules: AppRouteRecordRaw[] = cloneDeep(routes)
  for (let index = 0; index < modules.length; index++) {
    const route = modules[index]
    if (!isMultipleRoute(route)) {
      continue
    }
    promoteRouteLevel(route)
  }
  return modules
}

// 层级是否大于2
const isMultipleRoute = (route: AppRouteRecordRaw) => {
  if (!route || !Reflect.has(route, 'children') || !route.children?.length) {
    return false
  }

  const children = route.children

  let flag = false
  for (let index = 0; index < children.length; index++) {
    const child = children[index]
    if (child.children?.length) {
      flag = true
      break
    }
  }
  return flag
}

// 生成二级路由
const promoteRouteLevel = (route: AppRouteRecordRaw) => {
  let router: Router | null = createRouter({
    routes: [route as RouteRecordRaw],
    history: createWebHashHistory()
  })

  const routes = router.getRoutes()
  addToChildren(routes, route.children || [], route)
  router = null

  route.children = route.children?.map((item) => omit(item, 'children'))
}

// 添加所有子菜单
const addToChildren = (
  routes: RouteRecordNormalized[],
  children: AppRouteRecordRaw[],
  routeModule: AppRouteRecordRaw
) => {
  for (let index = 0; index < children.length; index++) {
    const child = children[index]
    const route = routes.find((item) => item.name === child.name)
    if (!route) {
      continue
    }
    routeModule.children = routeModule.children || []
    if (!routeModule.children.find((item) => item.name === route.name)) {
      routeModule.children?.push(route as unknown as AppRouteRecordRaw)
    }
    if (child.children?.length) {
      addToChildren(routes, child.children, routeModule)
    }
  }
}
const toCamelCase = (str: string, upperCaseFirst: boolean) => {
  str = (str || '')
    .replace(/-(.)/g, function (group1: string) {
      return group1.toUpperCase()
    })
    .replaceAll('-', '')

  if (upperCaseFirst && str) {
    str = str.charAt(0).toUpperCase() + str.slice(1)
  }

  return str
}
// 获取url中的参数
export interface urlQueryParems {
  accessToken?: String
  refreshToken?: String
  clientId?: String | Number
  expiresTime?: String | Number
  expires_in?: String | Number
  lang?: String
  tenantId?: String | Number
  loginType?: String
  roleId?: String | Number
}
export const GetQueryString = (): urlQueryParems => {
  const currentUrl = window.location.href
  let arr = currentUrl.split('?')
  if (arr.length > 1) {
    arr = arr[1].split('&')
    const temp = arr.reduce((a, b) => {
      const l = b.indexOf('=')
      return {
        ...a,
        [b.split('=')[0]]: b.slice(l + 1)
      }
    }, {})
    return temp
  } else {
    return {}
  }
}

import { sortTreeArrayByParam } from '@/utils/sort'

/**
 * 路由去重
 * 前提条件：判断菜单的唯一值是code，不是id，后端路由返回来的路由去除掉最外层的套餐衣，第一层的parentId固定为0，相同的code是不会出现在两个不同的层级的，
 * 相同的code在比较时，保留id大的那个，这是为了后端兼容套餐和服务。
 * 设计思路：
 *  1.获取并记录菜单树最多有多少层级，把每一层级的数据都打散成平面，构成{1:[{目录1},{目录2}],2:[{菜单1},{菜单2}]}这样的结构
 *  2.把从步骤1获取来的map，根据id大的为基准，进行去重，具体思路如下：
 *      2.1 设置记录去重后的映射对象duplicationLevelMap，和父级id关系映射对象parentIdRelationMap
 *      2.2 对步骤1的map进行逐层遍历，如果duplicationLevelMap中该层没有存有相同的id，就把自己赋值进去，并且判断一下parentIdRelationMap有
 *          没有记录下自己的父级id，如果有就说明父级id已经因为没有别的菜单id大被干掉了，就需要把自己的父级id转成parentIdRelationMap的映射关系；
 *          如果存有相同的id，就看看谁的id比较大，大的那个要覆盖掉小的id，并且要把parentIdRelationMap中依赖小的id都转成大的
 *      2.3 步骤2.2可以获得一个{1:[{目录1code:目录1},{目录2code:目录2}],2:[{菜单1code:菜单1},{菜单2code:菜单2}]}这样的已经去重并且重新绑定父级菜单的结构，再进一步转为
 *          {1:[{目录1id:目录1},{目录2id:目录2}],2:[{菜单1id:菜单1},{菜单2id:菜单2}]}这样的结构，这是因为下层菜单只有上层菜单的id，没有code
 *      2.4 通过步骤2.3获得的map，因为对象指向的是地址，所以只需要遍历每一层，将自己推入到上一层中parentId的菜单中，最后第一层就会是完整的路由树
 *  3.根据原数组将路由树进行分组，再根据服务排序最优先的原则对最外层进行排序
 *
 * */
export const routeDuplication = (_routeList, _originRoutes) => {
  // 获取每层路由，组成数组，在映射到哪一层
  const { levelDataMap, maxNum } = getRoutersLevel(_routeList)

  // 去重后的每一层路由对象
  const duplicationLevelMap: any = {}

  // 父级关系映射
  const parentIdRelationMap: any = {}

  for (const key in levelDataMap) {
    duplicationLevelMap[key] = {}
    // 处理每一层之间的关系，以大的id为基准进行去重
    duplicationLevelDataTool(levelDataMap[key], key, duplicationLevelMap[key], parentIdRelationMap)
  }
  // 转化一层变为id，便于整合
  const routeLevelMap: any = {}

  for (const key in duplicationLevelMap) {
    routeLevelMap[key] = {}
    transformLevelMap(duplicationLevelMap[key], routeLevelMap[key])
  }

  // 一层一层地把自己绑定到上一级去，因为对象是引用的，所以最后第一层就会获取到所有层级
  integrationRouter(routeLevelMap, maxNum)

  let resList: any = []

  for (const key in routeLevelMap[1]) {
    //只拿第一层就够了
    resList.push(routeLevelMap[1][key])
  }

  // 获取服务的映射，以服务的排序为最高级， 结构为 {服务catalog:{sort: 排序号, children:[]}}
  const serviceMap = _originRoutes.reduce((a, b) => {
    return {
      ...a,
      [b['catalog']]: {
        sort: b.sort,
        children: []
      }
    }
  }, {})

  resList.forEach((el) => {
    serviceMap[el.parentCatalog].children.push(el)
  })

  // 分组列表，以服务做排序，结构为[[],[],[],[]]
  const preResList: any = []

  // 重新根据sort排序路由结构，需要用服务的排序作为最高层级，所以需要将菜单再做一次服务的分层并重组
  for (const key in serviceMap) {
    if (serviceMap[key].children && serviceMap[key].children.length > 0) {
      sortTreeArrayByParam(serviceMap[key].children, 'sort', 'parentCatalogSort')
    }
    preResList.push(serviceMap[key].children)
  }

  //把分组重新打散回扁平，因为服务信息不能出现在菜单
  resList = preResList.reduce((a, b) => {
    return [...a, ...b]
  }, [])

  //只遍历最外面一层
  sortTreeArrayByParam(resList, 'sort', 'parentCatalogSort', false)

  console.log('路由去重结果', resList)

  return resList
}

import { deepClone } from '@/utils/deep'

/**
 * 转化已去重的map
 */
const transformLevelMap = (_duplicationLevelMap, _routeLevelMap) => {
  for (const key in _duplicationLevelMap) {
    _routeLevelMap[_duplicationLevelMap[key].id] = _duplicationLevelMap[key]
  }
}

/**
 * 整合路由父子关系
 */
const integrationRouter = (_routeLevelMap, _maxNum) => {
  for (const level in _routeLevelMap) {
    //第几层
    for (const idKey in _routeLevelMap[level]) {
      _routeLevelMap[level][idKey]
      if (_routeLevelMap[level][idKey].parentId != 0) {
        //等于0表示第一层，不用理
        const pid = _routeLevelMap[level][idKey].parentId
        // 前面已经把每一层的parentId从小的转为最大的了，这里只需要将自己绑定到上一个层级的parentId的children就可以了。
        if (_routeLevelMap[level - 1][pid].children) {
          _routeLevelMap[level - 1][pid].children.push(_routeLevelMap[level][idKey])
        } else {
          _routeLevelMap[level - 1][pid].children = [_routeLevelMap[level][idKey]]
        }
      }
    }
  }
}

/**
 * 每一层级去重的工具函数
 * @param levelNum 当前处于第几层
 *
 */
const duplicationLevelDataTool = (
  _routeList,
  _levelNum,
  _duplicationLevelMap,
  _parentIdRelationMap
) => {
  _routeList.forEach((el) => {
    if (_duplicationLevelMap[el.code]) {
      //重复了
      //两种情况：1. 已经存进map的id比较大
      if (_duplicationLevelMap[el.code].id > el.id) {
        _parentIdRelationMap[el.id] = _duplicationLevelMap[el.code].id
      } else {
        // 2. 目前id比较大
        const originId = _duplicationLevelMap[el.code].id

        // 如果当前的id，比已记录的id更大，除了需要把记录的那个id，改成当前id外，还需要把关系映射map里其他已经绑定先前id的key值也改成当前id
        for (const key in _parentIdRelationMap) {
          if (_parentIdRelationMap[key] == originId) {
            _parentIdRelationMap[key] = el.id
          }
        }

        _parentIdRelationMap[_duplicationLevelMap[el.code].id] = el.id
        // 打赢的（id比较大的）要看看自己的父级id是不是已经被干掉，干掉的需要收编，parentId为0的不用理
        if (el.parentId != 0) {
          if (_parentIdRelationMap[el.parentId]) {
            el.parentId = _parentIdRelationMap[el.parentId]
          }
        }
        _duplicationLevelMap[el.code] = el
      }
    } else {
      _duplicationLevelMap[el.code] = el
      // 要看看自己的父级id是不是已经被干掉，干掉的需要收编，parentId为0的不用理
      if (el.parentId != 0) {
        if (_parentIdRelationMap[el.parentId]) {
          el.parentId = _parentIdRelationMap[el.parentId]
        }
      }
    }
  })
}

// 计算路由层级，获取每一层的菜单，集合到一个对象中
const getRoutersLevel = (_routeList) => {
  // maxNum需要是对象，在传入方法后修改才会生效
  const maxNum = { maxNum: 1 }
  const levelDataMap: any = {} //每一层级的数据
  _routeList.forEach((el) => {
    // 因为每一层都要由1开始算层数，所以第一层不用递归，从第二层起做递归算层数，maxNum记录最大的层数
    if (el.children && el.children.length > 0) {
      recursionGetLevelTool(el.children, 1, maxNum, levelDataMap)
    }
    // 需要克隆，不然会影响原本的路由，把原本的路由children删掉
    const routeData = deepClone(el)
    delete routeData.children
    if (!levelDataMap[1]) {
      levelDataMap[1] = [routeData]
    } else {
      levelDataMap[1].push(routeData)
    }
  })

  return { levelDataMap, maxNum: maxNum.maxNum }
}

/**
 * 递归获取层级路由的工具
 *
 */
const recursionGetLevelTool = (_routeList, _num: number, _maxNum, _levelDataMap) => {
  // 每下一层就加一
  _num++
  _routeList.forEach((el) => {
    if (el.children && el.children.length > 0) {
      recursionGetLevelTool(el.children, _num, _maxNum, _levelDataMap)
    }
    const routeData = deepClone(el)
    delete routeData.children
    if (!_levelDataMap[_num]) {
      _levelDataMap[_num] = [routeData]
    } else {
      _levelDataMap[_num].push(routeData)
    }
  })
  if (_num > _maxNum.maxnum) {
    //计算出最大的层级
    _maxNum.maxNum = _num
  }
}

/**----------------------------------- 根据条件筛选路由 ------------------------------------------------*/
// 筛选路由的条件，由于路由筛选条件可能会变，所以分成了方法
const filterCondition = (_route) => {
  if (_route.type !== 3) {
    return true
  } else {
    return false
  }
}

// 根据条件进行路由筛选，当前路由会返回按钮路由，需要筛选路由
export const filterRouteByCondition = (_routesList) => {
  return _routesList.filter((el) => {
    if (filterCondition(el)) {
      if (el.children && el.children.length > 0) {
        el.children = filterRouteByCondition(el.children)
      }
    }
    return filterCondition(el)
  })
}
/**
 * 处理菜单列表，将菜单项的路径标准化，并添加父级目录的唯一值和排序值。
 *
 * @param resMenusList 菜单列表
 * @returns 处理后的路由列表
 */
export const getHandleRoute = (resMenusList: []): AppCustomRouteRecordRaw[] => {
  const handledRoute: AppCustomRouteRecordRaw[] = []
  // 所有的层级都需要以首层服务的排序为优先，使用递归让所有节点都记录上最外层服务的catalog和排序
  const remeberCatalog = (_list, _parentCatalog, _parentCatalogSort) => {
    _list.forEach((el) => {
      if (el.children && el.children.length > 0) {
        remeberCatalog(el.children, _parentCatalog, _parentCatalogSort)
      }
      el.parentCatalog = _parentCatalog
      el.parentCatalogSort = _parentCatalogSort
    })
  }

  resMenusList.forEach((el) => {
    if (el.catalog && el.children) {
      el.children.forEach((item) => {
        !item.path.startsWith('/') && (item.path = `/${item.path}`)
        // 记录父级的唯一值catalog，用作分服务排序
        item.parentCatalog = el.catalog
        item.parentCatalogSort = el.sort
        // 遍历首层让所有节点都记录上最外层服务的catalog和排序
        if (item.children && item.children.length > 0) {
          remeberCatalog(item.children, el.catalog, el.sort)
        }
      })
      handledRoute.push(...el.children)
    }
  })
  return handledRoute
}
