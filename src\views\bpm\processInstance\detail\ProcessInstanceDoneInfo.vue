<!--
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-14 17:30:46
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2025-04-15 09:35:22
 * @Description: 已办任务信息组件
-->
<template>
  <el-card v-loading="loading" class="box-card">
    <template #header>
      <span class="el-icon-document">已办任务信息【{{ task?.name }}】</span>
    </template>
    <el-col :offset="6" :span="16">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="任务编号">{{ task?.id }}</el-descriptions-item>
        <el-descriptions-item label="任务名称">{{ task?.name }}</el-descriptions-item>
        <el-descriptions-item label="任务状态">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="task?.result" />
        </el-descriptions-item>
        <el-descriptions-item label="审批意见">{{ task?.reason }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          formatDate(task?.createTime)
        }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{
          formatDate(task?.endTime)
        }}</el-descriptions-item>
      </el-descriptions>

      <!-- 任务表单 -->
      <template v-if="taskForm">
        <el-divider content-position="center">{{ task?.name }}任务表单</el-divider>
        <!-- 情况一：流程表单 -->
        <template v-if="taskForm.formType === 10">
          <form-create
            ref="formRef"
            v-model="formData.value"
            :option="formData.option"
            :rule="formData.rule"
            :disabled="true"
          />
        </template>
        <!-- 情况二：业务表单 -->
        <template v-if="taskForm.formType === 20">
          <component :is="businessFormComponent" :id="businessKey" />
        </template>
      </template>
    </el-col>
  </el-card>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ProcessInstanceDoneInfo'
})

import { ref, computed, PropType, watch } from 'vue'
import { registerComponent } from '@/utils/routerHelper'
import * as FormApi from '@/api/bpm/form'
import { setConfAndFields2 } from '@/utils/formCreate'
import { formatDate } from '@/utils/formatTime'
import { DICT_TYPE } from '@/utils/dict'

// 定义表单类型
interface TaskForm {
  taskDefinitionKey: string
  formType: number
  formId: number | string
  formCustomViewPath?: string
  [key: string]: any
}

// 定义任务类型
interface Task {
  id: string
  name: string
  definitionKey: string
  result: number
  reason: string
  createTime: string
  endTime: string
  [key: string]: any
}

const props = defineProps({
  // 任务信息
  task: {
    type: Object as PropType<Task>,
    required: false,
    default: null
  },
  // 业务表单的业务键
  businessKey: {
    type: String,
    default: ''
  },
  // 流程定义中的任务表单配置列表
  userTaskForms: {
    type: Array as PropType<TaskForm[]>,
    default: () => []
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 流程变量
  formVariables: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({})
  }
})

// 表单相关数据
const formRef = ref()
const formData = ref({
  rule: [],
  option: {},
  value: {}
})

// 计算当前任务的表单配置
const taskForm = computed<TaskForm | null>(() => {
  if (!props.task || !props.userTaskForms || props.userTaskForms.length === 0) return null

  return (
    props.userTaskForms.find((form) => form.taskDefinitionKey === props.task.definitionKey) || null
  )
})

// 业务表单组件
const businessFormComponent = ref<any>(null)

// 初始化流程表单
const initFlowForm = async (formId) => {
  if (!formId) return

  try {
    // 获取表单详情
    const data = await FormApi.getForm(formId)
    // 设置conf表单数据
    let conf = JSON.parse(data.conf)
    conf.submitBtn.show = false

    // 使用流程变量填充表单
    setConfAndFields2(formData, JSON.stringify(conf), data.fields, props.formVariables || {})

    nextTick().then(() => {
      if (formRef.value?.fApi) {
        formRef.value.fApi.btn.show(false)
        formRef.value.fApi.resetBtn.show(false)
        formRef.value.fApi.disabled(true) // 已办任务表单默认禁用
        formRef.value.fApi.config.submitBtn.show = false
      }
    })
  } catch (error) {
    console.error('获取表单详情失败', error)
  }
}

// 监听taskForm变化，初始化表单
watch(
  taskForm,
  async (newVal) => {
    if (!newVal) return

    if (newVal.formType === 10) {
      await initFlowForm(newVal.formId)
    } else if (newVal.formType === 20 && newVal.formCustomCreatePath) {
      businessFormComponent.value = registerComponent(newVal.formCustomCreatePath)
    }
  },
  { immediate: true }
)
</script>
