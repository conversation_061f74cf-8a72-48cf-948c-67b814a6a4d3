<template>
  <el-dialog
    :title="oprateType === 'edit' ? '配置' : '查看'"
    v-model="show"
    width="860px"
    append-to-body
    destroy-on-close
    class="approval-edit-form"
    @close="close"
  >
    <el-form
      class="mb-16px mt-16px"
      :model="detailValue"
      ref="queryFormRef"
      label-width="68px"
      v-loading="formLoading"
    >
      <el-form-item label="应用名称">
        <el-input :value="name" disabled />
      </el-form-item>
      <el-form-item label="版本号">
        <el-input v-model="ver" :disabled="oprateType !== 'edit'" placeholder="" />
      </el-form-item>
      <el-form-item label="业务端" prop="clientId" :style="{ width: '100%' }">
        <el-select
          v-model="detailValue.clientId"
          @change="changeSizeId"
          :style="{ width: '100%' }"
          disabled
        >
          <el-option
            v-for="(item, index) in clientList"
            :key="index"
            :label="item.name + '(' + item.code + ')'"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="应用分配" v-show="detailValue.clientId">
        <div :style="{ maxWidth: '755px' }">
          <el-tabs v-model="activeTab" type="card" :style="{ width: '100%' }">
            <el-tab-pane
              v-for="item in serviceTabs"
              :key="item.id"
              :label="item.name"
              :name="item.id"
            />
          </el-tabs>
          <el-form-item label="菜单权限" prop="menuIds" :style="{ width: '100%' }">
            <el-card class="cardHeight">
              <template #header>
                全选/全不选:
                <el-switch
                  v-model="treeNodeAll"
                  active-text="是"
                  inactive-text="否"
                  inline-prompt
                  @change="handleCheckedTreeNodeAll"
                />
                全部展开/折叠:
                <el-switch
                  v-model="menuExpand"
                  active-text="展开"
                  inactive-text="折叠"
                  inline-prompt
                  @change="handleCheckedTreeExpand"
                />
                <span>父子联动(选中父节点，自动选择子节点):</span>
                <el-switch
                  v-model="checkStrictly"
                  active-text="是"
                  inactive-text="否"
                  inline-prompt
                />
              </template>
              <el-tree
                ref="treeRef"
                :data="menuOptions"
                :props="defaultProps"
                empty-text="暂无数据"
                node-key="id"
                :check-strictly="!checkStrictly"
                :default-checked-keys="defaultCheckedKeys"
                @check="checkChange"
                @check-change="beforeChange"
                show-checkbox
              >
                <template #default="{ node, data }">
                  <div class="w-full flex items-center">
                    <span :class="[data.isDelete && 'del']">{{ node.label }}</span>
                    <dict-tag
                      class="ml-2"
                      v-if="data?.scene"
                      type="system_business_scene"
                      :value="data?.scene"
                    />
                    <!--   <el-tag type="danger" effect="plain" round class="ml-3" v-if="data.ifNew">
                    新增
                  </el-tag> -->
                    <el-button
                      v-if="oprateType === 'check'"
                      class="absolute right-0"
                      link
                      type="primary"
                      size="small"
                      @click.stop="updateScene(data.id)"
                    >
                      业务场景设置
                    </el-button>
                  </div>
                </template>
              </el-tree>
            </el-card>
          </el-form-item>
          <!-- <div
            :gutter="16"
            v-if="detailValue?.service && detailValue?.service[activeTabIndex]"
            class="mt-16px"
          >
            <div>
              <el-checkbox v-model="detailValue.service[activeTabIndex].hasModule"
                >是否需要客户专属服务</el-checkbox
              >
            </div>
            <div v-if="detailValue.service[activeTabIndex].hasModule" class="pl-18px">
              <el-row>
                <el-input
                  placeholder="请输入镜像"
                  v-model="detailValue.service[activeTabIndex].moduleImage"
                />
              </el-row>
              <el-row class="mt-8px">
                <el-input
                  placeholder="请输入服务"
                  v-model="detailValue.service[activeTabIndex].moduleService"
                />
              </el-row>
            </div>
          </div> -->
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close" v-if="oprateType !== 'edit'">关 闭</el-button>
      <el-button @click="close" v-if="oprateType === 'edit'">取 消</el-button>
      <el-button
        type="primary"
        @click="submit"
        :loading="submitLoading"
        v-if="oprateType === 'edit'"
      >
        确 定
      </el-button>
    </template>
  </el-dialog>

  <updateSceneDialog ref="updateSceneDialogRef" @success="updateSceneSuccess" />
</template>
<script setup lang="ts">
defineOptions({
  name: 'ApprovalEditForm'
})

import { defaultProps, handleTree } from '@/utils/tree'
import { deepClone } from '@/utils/deep'

import { getMenuList } from '@/api/system/menu'

import { getTenantAppListFilter } from '@/api/system/apply'

import { getPackageMenus, setTenantPackage } from '@/api/system/application/index'
const emit = defineEmits(['success'])
const show = ref(false)
const detailValue: any = ref({
  clientId: '',
  service: []
})

const menuMap: Ref<any> = ref({})

const checkStrictly = ref(false) // 是否严格模式，即父子不关联

const submitLoading = ref(false)

const queryFormRef = ref()

const message = useMessage()

// 提交
const submit = async () => {
  try {
    await message.confirm('确认配置？')
    await queryFormRef?.value.validate()
    submitLoading.value = true

    // const arr = [...detailValue.value.service].filter((el) => {
    //   // 如果没有做删除和添加操作就过滤掉
    //   if (el.addMenuIdList.length > 0 || el.removeMenuIdList.length > 0) {
    //     return true
    //   } else {
    //     return false
    //   }
    // })

    let menuList: any[] = []
    console.log('detailValue.value.service', detailValue.value.service)
    console.log('menuMap', menuMap.value)
    detailValue.value.service.forEach((el) => {
      if (el.serviceIds.length > 0) {
        el.serviceIds.forEach((item) => {
          console.log('----------------------------------------------------------')
          console.log('menuMap.value[el.applicationId]', menuMap.value[el.applicationId])
          console.log('el.applicationId', el.applicationId)
          console.log('item', item)
          if (menuMap.value[el.applicationId]) {
            if (menuMap.value[el.applicationId].hasOwnProperty(item)) {
              menuList.push(menuMap.value[el.applicationId][item])
            }
          }
        })
      }
    })

    const resData = {
      packageId: rowData.value.id,
      menuList,
      ver: ver.value
    }

    console.log('resData', resData)

    await setTenantPackage(resData)
    detailValue.value = {
      clientId: '',
      service: []
    }
    emit('success')
    show.value = false
    ElMessage.success('修改成功')
  } finally {
    submitLoading.value = false
  }
}

// 关闭
const close = () => {
  menuOptions.value = []
  detailValue.value = {
    clientId: ''
  }
  show.value = false
  activeTab.value = undefined
  activeTabIndex.value = 0
}

const activeTab = ref()

const activeTabIndex = ref(0)

const treeRef = ref()

// 是否展开全部
const menuExpand = ref(true)

/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}

// 是否选中全部
const treeNodeAll = ref(false)

// 全选/全不选
const handleCheckedTreeNodeAll = () => {
  if (newMenuIdArr.value.length > 0) {
    checkStrictly.value = false
    nextTick(() => {
      if (treeNodeAll.value) {
        newMenuIdArr.value.forEach((menuId: string) => {
          treeRef.value.setChecked(menuId, true, false)
        })
      } else {
        treeRef.value.setCheckedNodes([])
        // todo:后续菜单功能完善,要剔除 2024-09-30
        defaultCheckedKeys.value.forEach((menuId) => {
          treeRef.value.setChecked(menuId, true, false)
        })
      }

      activeService.value.serviceIds = [...treeRef.value.getCheckedKeys()]
    })
  } else {
    checkStrictly.value = true
    nextTick(() => {
      treeRef.value.setCheckedNodes(treeNodeAll.value ? menuOptions.value : [])
      // todo:后续菜单功能完善,要剔除 2024-09-30
      defaultCheckedKeys.value.forEach((menuId) => {
        treeRef.value.setChecked(menuId, true, false)
      })
      activeService.value.serviceIds = [...treeRef.value.getCheckedKeys()]
      checkStrictly.value = false
    })
  }
}

const activeService = ref()

const newMenuIdArr: Ref<string[]> = ref([])

//设置默认选中菜单(目前为demo菜单,作为菜单垫片,避免需要经常更新菜单)  todo:后续菜单功能完善,要剔除 2024-09-30
let defaultCheckedKeys = ref<number[]>([])
// const setDefaultCheckedMenu = (menuArr) => {
//   defaultCheckedKeys.value = []
//   let demoMenuParent = menuArr.find((item) => {
//     if (item.name === 'demo' && item.parentId === 0) {
//       item.disabled = true //设为不可点击
//       return true
//     }
//   })
//   if (demoMenuParent !== undefined) {
//     defaultCheckedKeys.value.push(demoMenuParent.id)
//     menuArr
//       .filter((item) => item.parentId === demoMenuParent.id)
//       .forEach((item) => {
//         item.disabled = true //设为不可点击
//         defaultCheckedKeys.value.push(item.id)
//       })
//   }
// }

// 监测服务tag切换
watch(
  () => activeTab.value,
  async (newVal) => {
    menuExpand.value = true
    if (newVal) {
      try {
        serviceTabs.value.forEach((el, index) => {
          if (el.id === activeTab.value) {
            activeTabIndex.value = index
          }
        })
        formLoading.value = true

        // 获取菜单和按钮信息
        const res = await getMenuList({
          applicationId: activeTab.value
        })
        const preHandleArr =
          // checkMap.value.length === 0
          oprateType.value === 'edit'
            ? res
            : res.map((el) => {
                return {
                  ...el,
                  disabled: true
                }
              })

        //默认选择菜单(demo菜单)  todo:后续菜单功能完善,要剔除
        // setDefaultCheckedMenu(preHandleArr)
        menuOptions.value = handleTree(deepClone(preHandleArr))

        if (!menuMap.value[activeTab.value]) {
          menuMap.value[activeTab.value] = deepClone(preHandleArr).reduce((a, b) => {
            return {
              ...a,
              [b.id]: {
                menuId: b.id,
                menuCode: b.code,
                appId: b.applicationId,
                menuName: b.name
              }
            }
          }, {})
        }

        // 清干净菜单树，防止取消后再点击带上之前的数据
        treeRef.value.setCheckedKeys([])
        // 切换tag时把当前tag已经选中的值赋值进菜单树
        detailValue.value.service.forEach((el) => {
          if (el.applicationId === newVal) {
            // 记录当前的tag并且绑定对应关系，在选中菜单时能记录进原数组(todo: activeService和detailValue.service 是浅拷贝)
            activeService.value = el
            nextTick(() => {
              let isTreeNodeAll = true
              el.serviceIds.forEach((menuId: number) => {
                treeRef.value.setChecked(menuId, true, false)
              })
              if (
                el.serviceIds.length !== treeRef.value.getCheckedKeys().length ||
                el.serviceIds.length === 0
              ) {
                isTreeNodeAll = false
              }
              treeNodeAll.value = isTreeNodeAll
            })
          }
        })
        if (checkMap.value.length > 0) {
          nextTick(() => {
            checkMap.value.map((el) => {
              treeRef.value.setChecked(el.menuId, true, false)
            })
          })
        }
      } finally {
        formLoading.value = false
        nextTick(() => {
          // 默认展开所有菜单
          handleCheckedTreeExpand()
        })
      }
    }
  },
  {
    immediate: true
  }
)

// 关闭父级时，清空子级，递归清空
const closeOptions = (arr) => {
  arr.forEach((el) => {
    if (el.children) {
      closeOptions(el.children)
    }
    if (activeService.value.serviceIds.indexOf(el.id) !== -1) {
      treeRef.value.setChecked(el.id, false, false)
    }
  })
}

const beforeChange = (_val, checked) => {
  if (checked) {
    //如果是勾上，检查父级有没有勾上，没有就要顺带一起勾上
    if (_val.parentId) {
      if (activeService.value.serviceIds.indexOf(_val.parentId) === -1) {
        treeRef.value.setChecked(_val.parentId, true, false)
      }
    }
  } else {
    //如果是取消选中，把子级全都清空
    if (_val.children && !checkStrictly.value) {
      closeOptions(_val.children)
    }
  }
}
//记录选择菜单id列表
const checkChange = (_val, checked) => {
  activeService.value.serviceIds = checked.checkedKeys
}

const formLoading = ref(false)

const menuOptions: Ref<Array<any>> = ref([])

// 初始选中的数据
const orignalAllCheckedIdList: Ref<String[] | number[]> = ref([])

// 切换业务端
const changeSizeId = async (val) => {
  try {
    formLoading.value = true
    orignalAllCheckedIdList.value = []
    detailValue.value.service = []
    activeTab.value = undefined

    // 获取当前交互端所有选中的权限，后端返回一个对象Map
    // const checkMap = await getSubscriptionMenu({
    //   tenantId: rowData.value.id,
    //   clientId: val
    // })

    // 把所有的选中的作为每个服务的初始选中（不同服务的菜单id不同）
    // orignalAllCheckedIdList.value = res.map((el) => el.id)

    // getSubscriptionGetByAppId

    // console.log('checkMap.value', checkMap.value)

    detailValue.value.service = serviceTabs.value.map((el) => {
      return {
        applicationId: el.id,
        // serviceIds: checkMap.value.length > 0 ? [...checkMap.value].map((el) => el.menuId) : [],
        serviceIds: oprateType.value === 'edit' ? [...checkMap.value].map((el) => el.menuId) : [],
        addMenuIdList: [],
        removeMenuIdList: []
        // moduleService: '',
        // moduleImage: '',
        // hasModule: false
      }
    })

    // const tenantId = authUtil.getTenantId()

    // detailValue.value.service.forEach(async (el) => {
    //   const res = await getSubscriptionGetByAppId({
    //     tenantId,
    //     appId: el.applicationId
    //   })
    //   el.moduleService = res.moduleService ? res.moduleService : ''
    //   el.moduleImage = res.moduleImage ? res.moduleImage : ''
    //   el.hasModule = res.moduleImage || res.moduleService ? true : false
    // })

    activeTab.value = serviceTabs.value[0]?.id
  } finally {
    formLoading.value = false
  }
}

interface serviceTabsVO {
  applicationCode: string
  name: string
  id: string
}

const serviceTabs: Ref<Array<serviceTabsVO | never>> = ref([])

const serviceMap = ref({})

const rowData = ref()

const oprateType = ref('')

//获取客户端列表
import { useHook } from '../common/useHook'
const { clientList, getClientList } = useHook()

const checkMap: Ref<any[]> = ref([])

const name = ref('')

const ver = ref('')

const open = async (row, type) => {
  name.value = row.name

  checkMap.value = []
  menuMap.value = {}
  newMenuIdArr.value = []
  getClientList()
  activeTab.value = undefined
  oprateType.value = type
  ver.value = undefined
  type === 'check' && (ver.value = row.ver)
  rowData.value = row
  // 申请的所有服务
  // const tenantId = authUtil.getTenantId()
  // const res = await getTenantApplicationService({ tenantId })

  checkMap.value = await getPackageMenus({
    packageId: rowData.value.id
  })

  let checkMapIds = []

  if (checkMap.value.length > 0) {
    checkMap.value.forEach((el) => {
      if (checkMapIds.indexOf(el.appId) === -1) {
        checkMapIds.push(el.appId)
      }
    })
  }

  // if (oprateType.value !== 'edit') {
  //   checkMap.value.forEach((el) => {
  //     if (checkMapIds.indexOf(el.appId) === -1) {
  //       checkMapIds.push(el.appId)
  //     }
  //   })
  // }

  const res = await getTenantAppListFilter()

  console.log('重排res', res)
  console.log('checkMapIds', checkMapIds)

  serviceTabs.value =
    // checkMap.value.length > 0
    oprateType.value !== 'edit'
      ? res.filter((el) => {
          return checkMapIds.indexOf(el.id) !== -1
        })
      : res.sort((a, b) => {
          const result = checkMapIds.indexOf(a.id) - checkMapIds.indexOf(b.id)
          if (result === 0) {
            return 0
          } else if (result < 0) {
            return 1
          } else if (result > 0) {
            return -1
          }
        })

  console.log('serviceTabs.value', serviceTabs.value)

  serviceMap.value = serviceTabs.value.reduce((a, b) => {
    return { ...a, [b.id]: b }
  }, {})

  detailValue.value.clientId = row.clientId

  show.value = true
  nextTick(async () => {
    changeSizeId(row.clientId)

    checkMapIds.forEach(async (_checkMapId) => {
      if (!menuMap.value[_checkMapId]) {
        // 获取菜单和按钮信息
        const preHandleArr = await getMenuList({
          applicationId: _checkMapId
        })
        menuMap.value[_checkMapId] = deepClone(preHandleArr).reduce((a, b) => {
          return {
            ...a,
            [b.id]: {
              menuId: b.id,
              menuCode: b.code,
              appId: b.applicationId
            }
          }
        }, {})
      }
    })
  })
}

defineExpose({
  open
})

//业务场景设置
import updateSceneDialog from './updateSceneDialog.vue'
const updateSceneDialogRef = ref()
const updateScene = (id) => {
  updateSceneDialogRef.value.open(id)
}

/**
 * 更新场景成功后的处理函数
 *
 * 该函数用于在场景更新成功后，将活动标签页重置为默认状态（标签页索引为0），
 * 并在下一个Vue tick之后，将标签页索引恢复为更新前的状态。
 */
const updateSceneSuccess = async () => {
  const cacheData = unref(activeTab.value)
  activeTab.value = 0
  nextTick(() => {
    activeTab.value = cacheData
  })
}
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
.del {
  text-decoration: line-through;
}
</style>
