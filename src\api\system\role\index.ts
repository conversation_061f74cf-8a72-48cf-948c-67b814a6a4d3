import request from '@/config/axios'
import { deepClone } from '@/utils/deep'

export interface RoleVO {
  id: number
  name: string
  code: string
  sort: number
  status: number
  type: number
  dataScope: number
  dataScopeDeptIds: number[]
  createTime: Date
  isStale: boolean
  applicationCode?: string
  pappId?: string
  autoAuthRuleIds?: number[]
}

export interface UpdateStatusReqVO {
  id: number
  status: number
}

// 查询角色列表
export const getRolePage = async (params: PageParam) => {
  if (params.types) {
    const obj = deepClone(params)
    let str = obj.types
      .split(',')
      .map((el) => `types=${el}`)
      .join('&')
    delete obj.types
    for (const key in obj) {
      if (obj[key]) {
        str += `&${key}=${obj[key]}`
      }
    }
    return await request.get({ url: `/system/role/page?${str}` })
  } else {
    return await request.get({ url: '/system/role/page', params })
  }
}

// 查询角色（精简)列表
export const getSimpleRoleList = async (): Promise<RoleVO[]> => {
  return await request.get({ url: '/system/role/list-all-simple' })
}

// 查询角色详情
export const getRole = async (id: number) => {
  return await request.get({ url: '/system/role/get?id=' + id })
}

// 新增角色
export const createRole = async (data: RoleVO) => {
  return await request.post({ url: '/system/role/create', data })
}

// 修改角色
export const updateRole = async (data: RoleVO) => {
  return await request.put({ url: '/system/role/update', data })
}

// 修改角色状态
export const updateRoleStatus = async (data: UpdateStatusReqVO) => {
  return await request.put({ url: '/system/role/update-status', data })
}

// 删除角色
export const deleteRole = async (id: number) => {
  return await request.delete({ url: '/system/role/delete?id=' + id })
}

// 导出角色
export const exportRole = (params) => {
  return request.download({
    url: '/system/role/export-excel',
    params
  })
}

// 更新角色标签
export const updateTag = async (data: RoleVO) => {
  return await request.post({ url: '/system/role/update-tag', data })
}

// 获取角色标签回显
export const getRoleListAssignedTags = async (id: number) => {
  return await request.get({ url: '/system/role/list-assigned-tags/' + id })
}

// 租户暴露资源
export const resourceExpose = async (data: RoleVO) => {
  return await request.post({ url: '/system/resource/expose', data })
}

// 获取已暴露的资源列表
export const getListResourceExposes = async (params) => {
  return await request.get({ url: '/system/resource/listResourceExposes', params })
}

/**
 * 获取租户场景树列表
 *
 * @param id 租户ID
 * @param params 请求参数，可选
 * @returns 返回租户场景树列表
 */
export const getListResourceListTenantSceneTrees = async (id, params?) => {
  return await request.get({ url: `/system/resource/listTenantSceneTrees/${id}`, params })
}

// 获取租户业务场景树列表
export const getListResourceListTenantSceneTreesProxy = async (id, tenantIdList?) => {
  // return await request.get({ url: `/system/resource/listTenantSceneTrees/${id}` })
  if (tenantIdList && tenantIdList.length > 0) {
    // const url = '?tenantIds=' + tenantIdList.join('&tenantIds=')
    // return await request.get({ url: `/system/resource/listTenantSceneTrees/${id}${url}` })
    return await request.post({
      url: `/system/resource/listTenantSceneTrees/${id}`,
      data: tenantIdList
    })
  } else {
    return await request.get({ url: `/system/resource/listTenantSceneTrees/${id}` })
  }
}

// 分配资源数据权限（仅金邦达使用）
export const assignRoleResourceScope = async (data: RoleVO) => {
  return await request.post({ url: '/system/resource/assignRoleResourceScope', data })
}

// 精简匹配规则列表接口
export const getResourceAutomaticAuthorizationRule = async (params?) => {
  return await request.get({
    url: `/system/resource-automatic-authorization-rule/list-all-simple`,
    params
  })
}

/**
 * 为指定角色分配资源授权规则
 *
 * @param data 包含角色信息的对象
 * @returns 请求结果
 */
export const assignResourceAuthorizationRule = async (data: {
  roleCode: string
  autoAuthRuleIds: string[]
}) => {
  return await request.post({ url: '/system/resource/assignResourceAuthorizationRule', data })
}

/**
 * 给单个资源分配角色权限范围
 *
 * @param data 请求参数
 * @param data.roleCode 角色编码
 * @param data.resource 资源对象
 * @param data.resource.resourceId 资源ID
 * @param data.resource.scopes 权限范围数组
 * @param data.resource.scopes.scene 场景
 * @param data.resource.scopes.permissionType 权限类型
 * @param data.resource.scopes.resourceType 资源类型
 * @returns 返回请求结果
 */
export const assignRoleSingleResourceScope = async (data: {
  roleCode: string
  resource: {
    resourceId: number | string
    scopes: {
      scene: string
      permissionType: string | number
      resourceType: number
    }[]
  }
}) => {
  return await request.post({ url: '/system/resource/assignRoleSingleResourceScope', data })
}
