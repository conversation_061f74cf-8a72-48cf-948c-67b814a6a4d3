import request from '@/config/axios'

export interface TenantPackageVO {
  id: number
  name: string
  status: number
  remark: string
  creator: string
  updater: string
  updateTime: string
  menuIds: number[]
  createTime: Date
  menus: string[] | number[]
  code?: string
}

// 查询租户套餐列表
export const getTenantPackagePage = (params: PageParam) => {
  return request.get({ url: '/system/tenant-package/page', params })
}

// 获得租户
export const getTenantPackage = (id: number) => {
  return request.get({ url: '/system/tenant-package/get?id=' + id })
  // return request.get({ url: '/system/menu/get?applicationId=' + id })
}

// 新增租户套餐
export const createTenantPackage = (data: TenantPackageVO) => {
  return request.post({ url: '/system/tenant-package/create', data })
}

// 修改租户套餐
export const updateTenantPackage = (data: TenantPackageVO) => {
  return request.put({ url: '/system/tenant-package/update', data })
}

// 删除租户套餐
export const deleteTenantPackage = (id: number) => {
  return request.delete({ url: '/system/tenant-package/delete?id=' + id })
}
// 获取租户套餐精简信息列表
export const getTenantPackageList = () => {
  return request.get({ url: '/system/tenant-package/get-simple-list' })
}

export interface applicationVO {
  id: number // 服务id
  name: String // 应用名称
  code: String // 应用编码
  ver: String // 应用版本
  type: Number // 应用类型 "应用类型: 0-内置应用, 1-接入应用, 2-租户应用
  applicationOwner: String // 租户名称
  status: Number // 应用状态 "应用状态:  0-发布, 1-草稿
  description: String // 应用描述
  url: String // 应用地址
  oauthClient: String // 应用授权客户端
  homePageMenuId: Number // 应用首页菜单ID
  sort: Number // 应用排序
}
//获取租户对应端的服务列表
export const getClientApplicationList = (clientId): Promise<applicationVO[]> => {
  return request.get({
    url: '/system/tenant-application-subscription/client-application',
    params: { clientId }
  })
}
/**
 * 更新客户端应用信息(设置首页和排序)
 *
 * @param data 更新应用信息的参数
 * @returns 返回更新后的应用信息
 */
export const updateClientApplication = (data) => {
  return request.post({
    url: '/system/tenant-application-subscription/updateData',
    data
  })
}

// 更改服务排序
export const updateSort = (data) => {
  return request.post({ url: '/system/tenant-application/updateSort', data })
}
