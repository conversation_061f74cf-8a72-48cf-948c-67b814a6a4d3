/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-08-10 11:39:21
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-08-10 15:47:08
 * @Description:
 */
import request from '@/config/axios'

// 创建示例订单
export function createDemoOrder(data) {
  return request.post({
    url: '/pay/demo-order/create',
    data: data
  })
}

// 获得示例订单
export function getDemoOrder(id) {
  return request.get({
    url: '/pay/demo-order/get?id=' + id
  })
}

// 获得示例订单分页
export function getDemoOrderPage(query) {
  return request.get({
    url: '/pay/demo-order/page',
    params: query
  })
}

// 退款示例订单
export function refundDemoOrder(id) {
  return request.put({
    url: '/pay/demo-order/refund?id=' + id
  })
}
