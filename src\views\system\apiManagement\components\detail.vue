<template>
  <div class="detail-wrap" v-loading="loading">
    <section class="top-row">
      <el-row align="middle">
        <el-tag :type="true ? '' : 'success'" effect="dark" class="api-method">POST</el-tag>
        <div style="margin-left: 10px">
          <span class="api-url">{{ '/api/user/getUserInfo' }}</span>
          <el-icon class="copy-icon" @click="copyContent('/api/user/getUserInfo')">
            <CopyDocument />
          </el-icon>
        </div>
      </el-row>
      <div class="data-info">
        <div class="data-info-item">
          <span class="label">创建时间：</span>
          <span>2024/01/23</span>
        </div>

        <div class="data-info-item">
          <span class="label">修改时间：</span>
          <span>2024/01/23</span>
        </div>

        <div class="data-info-item">
          <span class="label">修改者：</span>
          <span>张三</span>
        </div>

        <div class="data-info-item">
          <span class="label">创建者：</span>
          <span>李四</span>
        </div>

        <div class="data-info-item">
          <span class="label">责任人：</span>
          <span>王五</span>
        </div>

        <div class="data-info-item">
          <span class="label">目录：</span>
          <span>目录1</span>
        </div>
      </div>
    </section>
    <section class="content-row">
      <div class="api-description">
        <div class="sub-title">接口说明</div>
        <div>这是用于登录的接口</div>
      </div>

      <div class="api-params">
        <div class="api-params-item">
          <div class="sub-title">Header 参数</div>
          <el-table :data="headerObj.tableData" border>
            <el-table-column prop="key" label="参数名称" width="200" />
            <el-table-column prop="type" label="参数类型" width="200" />
            <el-table-column prop="require" label="必填" width="100" align="center">
              <template #default="scope">
                <span>{{ scope.row.require ? '是' : '否' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="example" label="示例" width="200" />
            <el-table-column prop="mark" label="备注" />
          </el-table>
        </div>

        <el-row :gutter="20" class="api-params-item">
          <el-col :span="14" class="left-table">
            <div class="sub-title">Body 参数</div>
            <el-table :data="bodyObj.tableData" border ref="bodyTableRef">
              <el-table-column prop="key" label="参数名称" width="200" />
              <el-table-column prop="type" label="参数类型" width="200" />
              <el-table-column prop="require" label="必填" width="100" align="center">
                <template #default="scope">
                  <span>{{ scope.row.require ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="example" label="示例" width="200" />
              <el-table-column prop="mark" label="备注" />
            </el-table>
          </el-col>
          <el-col :span="10" class="right-example">
            <div class="sub-title">
              <span>示例</span>
              <el-button
                class="copy-btn"
                :icon="CopyDocument"
                size="small"
                type="info"
                plain
                @click="copyContent(bodyObj.exampleCode)"
              >
                复制
              </el-button>
            </div>
            <div
              class="example"
              v-html="bodyObj.exampleCode"
              :style="{ height: `${bodyExampleHeight}px` }"
            ></div>
          </el-col>
        </el-row>

        <div class="api-params-item respone-item">
          <div class="sub-title">返回数据</div>
          <el-tabs v-model="activeResponeTab" type="border-card">
            <el-tab-pane label="成功" name="success">
              <el-row class="respone-info">
                <el-col :span="4">
                  <span class="label">HTTP状态码：</span>
                  <span>{{ 200 }}</span>
                </el-col>
                <el-col :span="6">
                  <span class="label">内容格式：</span>
                  <span>{{ 'JSON' }}</span>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="14">
                  <div class="sub-title">数据结构</div>
                  <el-table
                    :data="responeObj.tableData"
                    border
                    :tree-props="{ children: 'children' }"
                    row-key="key"
                    default-expand-all
                    ref="responeTableRef"
                  >
                    <el-table-column prop="key" label="参数名称" width="200" />
                    <el-table-column prop="type" label="参数类型" width="200" />
                    <el-table-column prop="require" label="是否必须" width="100" align="center">
                      <template #default="scope">
                        <span>{{ scope.row.require ? '是' : '否' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="example" label="示例" width="200" />
                    <el-table-column prop="mark" label="备注" /> </el-table
                ></el-col>
                <el-col :span="10" class="right-example">
                  <div class="sub-title">
                    <span>示例</span>
                    <el-button
                      class="copy-btn"
                      :icon="CopyDocument"
                      size="small"
                      type="info"
                      plain
                      @click="copyContent(responeObj.exampleCode)"
                      >复制</el-button
                    >
                  </div>
                  <div
                    class="example"
                    v-html="responeObj.exampleCode"
                    :style="{ height: `${responeExampleHeight}px` }"
                  ></div>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="失败" name="fail">
              <el-row class="respone-info">
                <el-col :span="4">
                  <span class="label">HTTP状态码：</span>
                  <span>{{ 0 }}</span>
                </el-col>
                <el-col :span="6">
                  <span class="label">内容格式：</span>
                  <span>{{ 'JSON' }}</span>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { DocumentCopy, CopyDocument } from '@element-plus/icons-vue'
import { useClipboard } from '@vueuse/core'
const props = defineProps({
  detailInfo: {
    type: [Object, String],
    default: () => {}
  }
})
const { detailInfo } = toRefs(props)
onMounted(() => {
  console.log('detailInfo === ', detailInfo.value)
  mockData()
})

const loading = ref(false)

// -----------Header参数
const headerObj = ref<any>({
  tableData: []
})

// -----------Body参数
const bodyTableRef = ref<any>()
const bodyExampleHeight = ref('aoto') // 示例代码高度，和左侧表格高度一致
const bodyObj = ref<any>({
  tableData: [],
  exampleCode: ''
})

// -----------返回数据
const responeTableRef = ref<any>()
const responeExampleHeight = ref('auto') // 示例代码高度，和左侧表格高度一致
const responeObj = ref<any>({
  tableData: [],
  exampleCode: ''
})

const activeResponeTab = ref('success')

const mockData = () => {
  let list = [
    {
      key: 'Content-Type',
      type: 'application/json',
      require: true
    },
    {
      key: 'Content-Type',
      type: 'application/json',
      require: true
    },
    {
      key: 'data',
      type: 'object',
      require: true,
      children: [
        {
          key: 'userInfo',
          type: 'object',
          require: true,
          children: [
            {
              key: 'userName',
              type: 'string',
              require: true
            }
          ]
        },
        {
          key: 'age',
          type: 'string',
          require: true
        }
      ]
    }
  ]
  loading.value = true
  setTimeout(() => {
    headerObj.value.tableData = [...list]
    bodyObj.value.tableData = [...list]
    responeObj.value.tableData = [...list]
    bodyObj.value.exampleCode = JSON.stringify([...list])
    responeObj.value.exampleCode = JSON.stringify([...list])
    loading.value = false
    nextTick(() => {
      try {
        bodyExampleHeight.value = bodyTableRef?.value?.$el?.offsetHeight || 'auto'
        responeExampleHeight.value = responeTableRef?.value?.$el?.offsetHeight || 'auto'
      } catch (error) {}
    })
  }, 800)
}

const copyContent = async (content) => {
  const { copy } = useClipboard()
  try {
    await copy(content)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败！')
    console.error('复制失败', error)
  }
}

defineExpose({
  mockData
})
</script>

<style scoped>
.detail-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .top-row {
    padding: 15px 10px;
    border-radius: 6px;
    border: 1px solid #cdd0d6;
    color: #606266;
    .api-method {
      font-size: 14px;
    }
    .api-url:hover {
      text-decoration: underline;
      cursor: pointer;
    }
    .copy-icon {
      position: relative;
      top: 2px;
      margin-left: 6px;
      cursor: pointer;
    }
    .copy-icon:hover {
      color: #d5a147;
    }
    .data-info {
      margin-top: 14px;
      display: flex;
      .data-info-item {
        margin-right: 40px;
        .label {
          color: #606266;
        }
      }
    }
  }
  .content-row {
    padding: 20px 10px;
    flex: 1;
    overflow: auto;
    .api-description {
      color: #606266;
      .sub-title {
        margin-bottom: 6px;
      }
    }
    .api-params {
      .api-params-item {
        margin-top: 40px;
        .sub-title {
          margin-bottom: 10px;
          position: relative;
        }
        .left-table {
        }
        .right-example {
          display: flex;
          flex-direction: column;
          .copy-btn {
            position: absolute;
            right: 0;
          }
          .example {
            border: 1px solid #e5e7eb;
            padding: 10px;
            overflow: auto;
          }
        }
      }
      .respone-item {
        .respone-info {
          font-size: 14px;
          padding: 10px 10px 20px;
          .label {
            color: #606266;
          }
        }
      }
    }
  }
  .sub-title {
    color: #333333;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
  }
  .sub-title::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: #d5a147;
    margin-right: 4px;
  }
}
</style>
