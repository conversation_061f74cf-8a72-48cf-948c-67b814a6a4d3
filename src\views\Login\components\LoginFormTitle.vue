<template>
  <div class="title-warp">
    <LocaleDropdown :is-get-menu="false" />
    <h2 class="mb-3 text-2xl font-bold text-center xl:text-3xl enter-x xl:text-center">
      {{ getFormTitle }}
    </h2>
    <div></div>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'LoginFormTitle'
})

import { LOGIN_TYPE, useLoginState } from './useLogin'
import { LocaleDropdown } from '@/layout/components/LocaleDropdown/index'

const { t } = useI18n()

const { getLoginState } = useLoginState()

const getFormTitle = computed(() => {
  const titleObj = {
    [LOGIN_TYPE.RESET_PASSWORD]: t('sys.login.forgetFormTitle'),
    [LOGIN_TYPE.LOGIN]: t('sys.login.signInFormTitle'),
    [LOGIN_TYPE.REGISTER]: t('sys.login.signUpFormTitle'),
    [LOGIN_TYPE.MOBILE]: t('sys.login.mobileSignInFormTitle'),
    [LOGIN_TYPE.QR_CODE]: t('sys.login.qrSignInFormTitle'),
    [LOGIN_TYPE.SSO]: t('sys.login.ssoFormTitle')
  }
  return titleObj[unref(getLoginState)]
})
</script>
<style lang="scss" scoped>
.title-warp {
  display: flex;
  justify-content: space-between;
  // align-items: center;
}
</style>
