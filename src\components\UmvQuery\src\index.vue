<template>
  <el-form
    id="t_query_condition"
    ref="formRef"
    v-bind="$attrs"
    :label-width="labelWidth"
    :model="queryState"
    :rules="props.rules"
    inline-message
    size="default"
    class="t-query-condition"
    :style="{
      'grid-template-areas': gridAreas,
      'grid-template-columns': `repeat(${colLength}, minmax(0px, ${100 / colLength}%))`
    }"
    @submit.prevent
  >
    <el-form-item
      v-for="(opt, i) in cOpts"
      :key="i"
      :label="opt.hideLabel ? '' : opt.label"
      :label-width="opt.hideLabel ? '0' : opt.labelWidth"
      v-bind="$attrs"
      :style="{ gridArea: i }"
      :class="[opt.className, { render_label: opt.labelRender }]"
      :prop="opt.dataIndex || i.toString()"
    >
      <!-- 自定义label -->
      <template #label v-if="opt.labelRender">
        <render-comp :form="queryState" :render="opt.labelRender" />
      </template>
      <!-- 自定义控件渲染 -->
      <template v-if="opt.controlRender">
        <render-comp :form="queryState" :render="opt.controlRender" />
      </template>
    </el-form-item>
    <!-- 按钮区域 -->
    <div
      v-if="Object.keys(cOpts).length > 0 && isFooter"
      class="button-area"
      style="grid-area: submit_btn"
    >
      <slot name="footerBtn" :check="checkHandle" :reset="resetHandle"></slot>
      <template v-if="!slots.footerBtn || showDefaultButtons">
        <el-button
          v-if="check"
          class="relative -top-px"
          @click="checkHandle"
          v-bind="{ type: 'primary', ...queryAttrs }"
          :loading="loading"
        >
          {{ queryAttrs.btnTxt }}
        </el-button>
        <el-button v-if="reset" class="relative -top-px" v-bind="resetAttrs" @click="resetHandle">
          {{ resetAttrs.btnTxt }}
        </el-button>
        <slot name="querybar"></slot>
        <el-button v-if="needShowExpandButton && showOpen" @click="open = !open" link>
          {{ open ? translatedPackUpTxt : translatedUnfoldTxt }}
          <el-icon v-if="open"><ArrowUp /></el-icon>
          <el-icon v-else><ArrowDown /></el-icon>
        </el-button>
        <el-tooltip v-if="isShowColumn" :content="t('UmvQuery.query.columnAdjust')" placement="top">
          <el-dropdown @command="handleColumnChange" class="!align-middle">
            <el-button link type="info" size="large">
              <el-icon size="20px"><Grid /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="1">
                  {{ t('UmvQuery.query.columnLayout.oneColumn') }}
                </el-dropdown-item>
                <el-dropdown-item :command="2">
                  {{ t('UmvQuery.query.columnLayout.twoColumns') }}
                </el-dropdown-item>
                <el-dropdown-item :command="3">
                  {{ t('UmvQuery.query.columnLayout.threeColumns') }}
                </el-dropdown-item>
                <el-dropdown-item :command="4">
                  {{ t('UmvQuery.query.columnLayout.fourColumns') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
        <more-choose
          :isDropDownSelectMore="isDropDownSelectMore"
          :moreCheckList="moreCheckList"
          :popoverAttrsBind="popoverAttrsBind"
          class="cursor-pointer"
          @get-check-list="(event) => emits('getCheckList', event)"
        />
      </template>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import RenderComp from './renderComp.vue'
import MoreChoose from './moreChoose.vue'
import {
  computed,
  ref,
  watch,
  useSlots,
  onMounted,
  withDefaults,
  onBeforeUnmount,
  nextTick
} from 'vue'
import { ArrowUp, ArrowDown, Grid } from '@element-plus/icons-vue'
import { useComputed } from './useComputed'
import { debounce, isEqual } from 'lodash-es'
import type { UmvQueryProps, QueryOption, QueryForm } from '@/components/UmvQuery/src/type'

defineOptions({
  name: 'UmvQuery'
})
const { t } = useI18n()

const { getColLength } = useComputed()

/**
 * UmvQuery组件的属性
 */
const props = withDefaults(defineProps<UmvQueryProps>(), {
  labelWidth: '120px',
  btnCheckConfig: () => ({}),
  btnResetConfig: () => ({}),
  loading: false,
  reset: true,
  boolEnter: true,
  isShowOpen: true,
  isExpansion: false,
  maxVisibleRows: 2,
  packUpTxt: '收起',
  unfoldTxt: '展开',
  isFooter: true,
  configChangedReset: false,
  isShowWidthSize: false,
  widthSize: 4,
  isDropDownSelectMore: false,
  moreCheckList: () => [],
  popoverAttrs: () => ({}),
  isShowColumn: true,
  showDefaultButtons: true,
  check: true,
  rules: () => ({}),
  visibleSearchNum: undefined,
  colLengthMap: () => ({})
})

// 使用defineModel，返回一个响应式的ref对象
const model = defineModel<Record<string, any>>({ required: true })

const slots = useSlots()

/**
 * 判断是否使用了某个插槽
 * @param name 插槽名称
 * @returns 是否使用了该插槽
 */
const isShow = (name: string): boolean => {
  return Object.keys(slots).includes(name)
}

/**
 * 弹出框属性计算属性
 */
const popoverAttrsBind = computed(() => {
  return {
    showTxt: t('UmvQuery.moreChoose.showTxt'),
    title: t('UmvQuery.moreChoose.title'),
    allTxt: t('UmvQuery.moreChoose.allTxt'),
    reverseTxt: t('UmvQuery.moreChoose.reverseTxt'),
    clearTxt: t('UmvQuery.moreChoose.clearTxt'),
    ...props.popoverAttrs
  }
})

/**
 * 查询表单数据
 */
const queryState = ref<QueryForm>(initializeFormData())

/**
 * 初始化表单数据
 */
function initializeFormData(): QueryForm {
  if (model.value) {
    return { ...model.value }
  }

  if (!props.opts) {
    return {}
  }

  return Object.keys(props.opts).reduce((acc: QueryForm, field: string) => {
    const opt = props.opts[field]
    if (!opt) return acc

    const key = opt.dataIndex || field
    acc[key] = opt.defaultVal ?? null
    return acc
  }, {})
}

// 当queryState变化时，更新model (负责把【组件内部的交互修改】→ 同步给外部)
let isSyncing = false // 同步标志，防止 queryState 和 model 互相递归触发

watch(
  queryState,
  (newVal) => {
    if (isSyncing) return
    if (!isEqual(model.value, newVal)) {
      isSyncing = true
      // 合并 queryState 到 model.value，而不是直接替换，以保留 pageNo 等参数
      model.value = { ...model.value, ...newVal }
      nextTick(() => {
        isSyncing = false
      })
    }
  },
  { deep: true, flush: 'post' }
)

// 当model变化时，更新queryState (负责把【外部的交互修改】→ 同步给组件内部)
watch(
  () => model.value,
  (newVal) => {
    if (isSyncing) return
    if (newVal === null || newVal === undefined) {
      if (!isEqual(queryState.value, initializeFormData())) {
        isSyncing = true
        queryState.value = initializeFormData()
        nextTick(() => {
          isSyncing = false
        })
      }
    } else if (!isEqual(queryState.value, newVal)) {
      isSyncing = true
      queryState.value = { ...newVal }
      nextTick(() => {
        isSyncing = false
      })
    }
  },
  { deep: true, flush: 'post' }
)

/**
 * 列数
 */
const colLength = ref<number>(4)

/**
 * 是否显示展开按钮
 */
const showOpen = ref<boolean>(false)

/**
 * 是否展开查询条件
 */
const open = ref<boolean>(false)

/**
 * 查询按钮配置
 */
const queryAttrs = computed(() => {
  return {
    btnTxt: t('UmvQuery.query.btnTxt'),
    ...props.btnCheckConfig
  }
})

/**
 * 重置按钮配置
 */
const resetAttrs = computed(() => {
  return { btnTxt: t('UmvQuery.query.resetTxt'), ...props.btnResetConfig }
})

/**
 * 处理后的查询表单项配置
 */
const cOpts = computed(() => {
  if (!props.opts) return {}

  // 首先过滤掉不需要显示的项（基于visible属性）
  const visibleOpts = Object.keys(props.opts).reduce((acc, field) => {
    const opt = { ...props.opts[field] }
    opt.dataIndex = opt.dataIndex || field

    // 检查visible属性
    const isVisible =
      typeof opt.visible === 'function' ? opt.visible(queryState.value) : opt.visible !== false // 默认为true

    if (isVisible) {
      acc[field] = opt
    }

    return acc
  }, {} as Record<string, QueryOption>)

  // 如果已展开或没有启用展开功能，直接返回所有可见配置
  if (open.value || !showOpen.value) {
    return visibleOpts
  }

  // 未展开状态下的过滤逻辑
  let itemCount = 0
  let rowCount = 0
  let currentRowSpan = 0
  const buttonAreaSpan = 1 // 默认按钮区域占用1个单元格The default button area takes up 1 cell

  return Object.keys(visibleOpts).reduce((acc: Record<string, QueryOption>, field: string) => {
    const opt = { ...visibleOpts[field] }
    const span = opt.span ?? 1

    // 检查是否需要换行
    if (currentRowSpan + span > colLength.value) {
      rowCount++
      currentRowSpan = 0
    }

    currentRowSpan += span

    // 基于visibleSearchNum或maxVisibleRows进行过滤
    if (props.visibleSearchNum !== undefined) {
      // 基于条件数量控制
      if (itemCount >= props.visibleSearchNum) {
        return acc
      }
      itemCount++
    } else {
      // 基于行数控制
      if (rowCount >= props.maxVisibleRows) {
        return acc
      }

      // 检查添加当前项后是否会导致按钮区域被挤到下一行，且已经达到最大可见行数
      if (
        rowCount === props.maxVisibleRows - 1 &&
        currentRowSpan + buttonAreaSpan > colLength.value
      ) {
        return acc
      }
    }

    // 添加到结果中
    acc[field] = opt
    return acc
  }, {})
})

/**
 * 是否有被过滤的条件
 */
const hasFilteredItems = computed(() => {
  // 计算visible为true的条件数量
  const visibleCount = Object.keys(props.opts || {}).filter((field) => {
    const opt = props.opts[field]
    // 检查visible属性
    return typeof opt.visible === 'function' ? opt.visible(queryState.value) : opt.visible !== false // 默认为true
  }).length

  // 比较可见条件数量和实际渲染的条件数量
  return Object.keys(cOpts.value).length < visibleCount
})

/**
 * 是否需要显示展开/收起按钮
 */
const needShowExpandButton = computed(() => {
  // 如果不允许显示展开按钮或者使用下拉方式展示更多条件，则不显示
  if (!props.isShowOpen || props.isDropDownSelectMore) {
    return false
  }

  // 已展开状态下，只要有条件就显示收起按钮
  if (open.value) {
    return Object.keys(props.opts || {}).length > 0
  }

  // 未展开状态下，只有在有被过滤的条件时才显示展开按钮
  return hasFilteredItems.value
})

/**
 * 栅格布局区域
 */
const gridAreas = computed(() => {
  const fields = Object.keys(cOpts.value)
  let rowIndex = 0
  let rowSpan = 0
  const areas: string[][] = [[]]
  let totalRows = 0

  // 计算表单项布局
  for (const field of fields) {
    const opt = cOpts.value[field]
    if (!opt) continue

    const span = Math.min(opt.span ?? 1, colLength.value, 4) // 最大4

    // 检查是否需要换行
    if (rowSpan + span > colLength.value) {
      // 填充当前行剩余空间
      while (rowSpan < colLength.value) {
        areas[rowIndex].push('.')
        rowSpan += 1
      }
      rowSpan = 0
      areas[++rowIndex] = []
      totalRows = rowIndex + 1
    }

    // 添加当前字段到布局
    rowSpan += span
    for (let i = 0; i < span; i++) {
      areas[rowIndex].push(field)
    }
  }

  // 处理按钮区域
  const addButtonArea = () => {
    if (rowSpan < colLength.value) {
      // 当前行有空间，将按钮放在当前行
      while (rowSpan < colLength.value) {
        areas[rowIndex].push('submit_btn')
        rowSpan += 1
      }
    } else {
      // 当前行已满，添加新行放置按钮
      areas.push(Array(colLength.value).fill('submit_btn'))
      totalRows += 1
    }
  }

  addButtonArea()

  // 未展开状态下，确保总行数不超过maxVisibleRows
  if (!open.value && props.maxVisibleRows > 0 && totalRows > props.maxVisibleRows) {
    areas.splice(props.maxVisibleRows)
    // 最后一行全部设为按钮区域
    areas[props.maxVisibleRows - 1] = Array(colLength.value).fill('submit_btn')
  }

  // 确保按钮区域始终存在
  if (!areas.some((row) => row.includes('submit_btn'))) {
    areas.push(Array(colLength.value).fill('submit_btn'))
  }

  // 转换为CSS grid-template-areas格式
  return areas.reduce((acc, row) => acc + `'${row.join(' ')}'\n`, '')
})

/**
 * 初始化表单数据
 * @param {Record<string, QueryOption>} opts 查询表单项配置
 * @param {boolean} keepVal 是否保留原值
 * @returns {QueryForm} 初始化后的表单数据
 */
const initForm = (opts: Record<string, QueryOption>, keepVal = false): QueryForm => {
  if (!opts) return {}

  return Object.keys(opts).reduce((acc: QueryForm, field: string) => {
    const opt = opts[field]
    if (!opt) return acc

    const key = opt.dataIndex || field

    if (keepVal && queryState.value) {
      // 保留原值的情况
      acc[key] =
        queryState.value[key] !== undefined
          ? queryState.value[key]
          : 'defaultVal' in opt
          ? opt.defaultVal
          : null
    } else {
      // 重置值的情况
      acc[key] = 'defaultVal' in opt ? opt.defaultVal : null
    }

    return acc
  }, {})
}

const emits = defineEmits<{
  (e: 'check', form: QueryForm, flagText?: any): void
  (e: 'reset', form: QueryForm): void
  (e: 'handleEvent', type: string, val: any, form: QueryForm): void
  (e: 'getCheckList', event: any): void
  (e: 'getRefs', el: any, opt: any, index: number): void
  (e: 'columnChange', columns: number): void
}>()

/**
 * 重置按钮点击事件处理
 */
const resetHandle = (): void => {
  if (!props.opts) return
  // 重置表单数据
  resetFormData()

  // 重置后自动查询
  nextTick(() => {
    // 触发重置事件
    emits('reset', queryState.value)

    checkHandle('reset')
  })
}

/**
 * 重置表单数据
 */
const resetFormData = (): void => {
  if (!props.opts) return

  // 创建重置后的表单数据
  const formData = initForm(props.opts)

  // 保留v-model中不在opts中定义的字段
  if (model.value) {
    const newQueryState = { ...model.value }

    // 仅重置在表单配置中定义的字段
    Object.keys(props.opts).forEach((field) => {
      const opt = props.opts[field]
      if (opt) {
        const key = opt.dataIndex || field
        newQueryState[key] = formData[key]
      }
    })

    queryState.value = newQueryState
  } else {
    queryState.value = formData
  }

  // 原clearTSelectTable函数调用已移除
}

// clearTSelectTable函数已移除，因为不再需要

/**
 * 查询
 * @param {any} flagText 标记文本
 */
const checkHandle = (flagText: any = false): void => {
  emits('check', queryState.value, flagText)
}

/**
 * 处理列数变更
 * @param {number} columns 列数
 */
const handleColumnChange = (columns: number): void => {
  // 更新列数
  colLength.value = columns
  // 确保切换列数后表单项能够正确显示
  if (!open.value) {
    open.value = true
  }
  // 发出列数变更事件
  emits('columnChange', columns)
}

onMounted(() => {
  // 设置初始展开状态
  open.value =
    props.isExpansion || isShow('footerBtn') || !props.isFooter || props.isDropDownSelectMore

  // 设置列数
  if (props.isShowWidthSize) {
    colLength.value = props.widthSize
  } else {
    colLength.value = getColLength(props.colLengthMap)
    // 添加窗口大小变化监听器
    window.addEventListener('resize', handleResize)
  }

  // 设置展开按钮显示状态
  showOpen.value = props.isShowOpen && !props.isDropDownSelectMore

  // 启用回车键查询功能
  setupEnterKeyHandler()
})

/**
 * 设置回车键查询处理函数
 */
const setupEnterKeyHandler = () => {
  if (!props.boolEnter) return

  document.onkeyup = (e: KeyboardEvent) => {
    // 使用event.key代替keyCode（keyCode已废弃）
    if (e.key !== 'Enter') return

    // 检查是否有分页器输入框获得焦点
    const pagination = document.querySelectorAll('.el-pagination')
    let isPaginationInputFocus = false

    pagination.forEach((ele) => {
      const inputs = ele.getElementsByTagName('input')
      for (let i = 0; i < inputs.length; i++) {
        if (inputs[i] === document.activeElement) {
          isPaginationInputFocus = true
          break
        }
      }
    })

    // 如果分页器输入框获得焦点，则不触发查询操作
    if (!isPaginationInputFocus) {
      checkHandle()
    }
  }
}

// 添加 beforeUnmount 生命周期钩子
onBeforeUnmount(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize)
})

// 添加 resize 处理函数
const handleResize = debounce(() => {
  if (!props.isShowWidthSize) {
    colLength.value = getColLength(props.colLengthMap)
  }
}, 200)

watch(
  () => props.widthSize,
  (val) => {
    colLength.value = val
  }
)

watch(
  () => props.opts,
  (opts) => {
    queryState.value = initForm(opts, !props.configChangedReset)
  },
  { deep: true }
)

/**
 * 展开文本翻译
 */
const translatedUnfoldTxt = computed(() => {
  return props.unfoldTxt || t('UmvQuery.query.unfoldTxt')
})

/**
 * 收起文本翻译
 */
const translatedPackUpTxt = computed(() => {
  return props.packUpTxt || t('UmvQuery.query.packUpTxt')
})

// 添加form引用
const formRef = ref()

/**
 * 查询条件change事件
 * @param {Object} params 事件参数
 * @param {boolean} params.isChange 是否为变更事件
 * @param {string} params.type 事件类型
 * @param {any} params.val 事件值
 * @param {string} dataIndex 数据索引
 */
const handleEvent = (
  { isChange = false, type, val }: { isChange?: boolean; type?: string; val: any },
  dataIndex?: string
): void => {
  if (!isChange && type) {
    emits('handleEvent', type, val, queryState.value)
  } else if (dataIndex) {
    queryState.value[dataIndex] = val
  }
}

// 暴露方法出去
defineExpose({
  queryState,
  props,
  colLength,
  resetData: resetFormData,
  resetHandle,
  checkHandle,
  handleEvent,
  // 暴露El-Form组件的所有方法
  validate: (...args: any[]) => formRef.value?.validate?.(...args),
  validateField: (...args: any[]) => formRef.value?.validateField?.(...args),
  resetFields: () => {
    // 然后调用el-form的resetFields方法重置表单状态
    if (formRef.value?.resetFields) {
      formRef.value.resetFields()
    }
  },
  scrollToField: (...args: any[]) => formRef.value?.scrollToField?.(...args),
  clearValidate: (...args: any[]) => formRef.value?.clearValidate?.(...args),
  // 添加formRef引用，方便直接访问
  formRef
})
</script>

<style lang="scss" scoped>
.t-query-condition.el-form {
  position: relative;
  display: grid;
  gap: 2px 8px;
  margin-bottom: 10px;
  text-align: left;

  .el-select,
  .el-date-editor,
  .ant-calendar-picker {
    width: 100%;
  }

  .el-form-item {
    display: flex;
    margin-bottom: 6px;

    .el-form-item__label {
      flex-shrink: 0;
      min-width: 60px;
      padding-left: 8px;
    }

    .el-form-item__content {
      flex-grow: 1;
      margin-left: 0 !important;

      & > .el-select {
        min-width: auto;
      }
    }
  }

  .render_label {
    .el-form-item__label {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      &::before {
        margin-top: 1px;
      }
    }
  }

  // 按钮区域样式 - 实现整体换行效果
  .button-area {
    display: flex;
    align-items: center;
    flex-wrap: nowrap; // 防止按钮区域内的元素换行
    gap: 4px; // 统一按钮间距
    min-width: 0; // 允许容器收缩
    margin-bottom: 6px; // 保持和表单项一致的下边距
    // 确保所有直接子元素不换行
    > * {
      flex-shrink: 0; // 防止按钮被压缩
      white-space: nowrap; // 防止按钮内文本换行
    }

    // 移除原有的margin类样式，使用gap统一管理间距
    .el-button {
      margin-left: 0 !important;
    }

    .el-dropdown {
      margin-left: 0 !important;
    }

    .more-choose {
      margin-left: 0 !important;
    }
  }
}
</style>
