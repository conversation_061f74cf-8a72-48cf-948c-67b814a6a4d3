<template>
  <UmvContent>
    <UmvQuery
      ref="queryFormRef"
      v-model="queryParams"
      :opts="queryOpts"
      :loading="loading"
      @reset="handleQuery"
      @check="handleQuery"
    />

    <UmvTable
      ref="tableRef"
      v-loading="loading"
      :data="list"
      :columns="columns"
      @refresh="getList"
      show-overflow-tooltip
    >
      <template #tools>
        <el-button
          type="primary"
          plain
          size="small"
          @click="openForm('create')"
          v-hasPermi="['system:tenant:create']"
        >
          <icon-ep-plus style="font-size: 12px" class="mr-5px" />
          新增
        </el-button>
        <el-button
          type="success"
          plain
          size="small"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:tenant:export']"
        >
          <icon-ep-download style="font-size: 12px" class="mr-5px" />
          导出
        </el-button>
      </template>
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>
    <!-- 表单弹窗：添加/修改 -->
    <TenantForm ref="formRef" @success="getList" />
  </UmvContent>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'SystemTenant'
})

import { ref, type Ref } from 'vue'
import { ElButton, ElTag } from 'element-plus'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { checkPermi } from '@/utils/permission'
import download from '@/utils/download'
import * as TenantApi from '@/api/system/tenant'
// import * as TenantPackageApi from '@/api/system/tenantPackage' // 暂未使用
import TenantForm from './TenantForm.vue'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import UmvContent from '@/components/UmvContent'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// interface PACKAGE_TYPE { // 暂未使用
//   name: string
//   id: string | number
// }

// interface SET_MEAL_TYPE { // 暂未使用
//   label: string
//   value: string | number
// }

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<TenantApi.TenantVO[]>([]) // 列表的数据

// 查询参数
const queryParams: Ref<TenantApi.TenantPageReqVO> = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  contactName: undefined,
  contactMobile: undefined,
  status: undefined,
  createTime: undefined // 初始化为 undefined 以匹配API类型 Date[] | undefined
})

const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '租户名',
    defaultVal: undefined,
    controlRender: (form) => <el-input v-model={form.name} placeholder="请输入租户名" clearable />
  },
  contactName: {
    label: '联系人',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-input v-model={form.contactName} placeholder="请输入联系人" clearable />
    )
  },
  contactMobile: {
    label: '联系手机',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-input v-model={form.contactMobile} placeholder="请输入联系手机" clearable />
    )
  },
  status: {
    label: '租户状态',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.status} placeholder="请选择租户状态" clearable class="!w-240px">
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  createTime: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form) => (
      <el-date-picker
        v-model={form.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
      />
    )
  }
})

const columns = ref<TableColumn[]>([
  { label: '租户编号', prop: 'id', align: 'center' },
  { label: '租户名', prop: 'name', align: 'center' },
  { label: '联系人', prop: 'contactName', align: 'center' },
  { label: '联系手机', prop: 'contactMobile', align: 'center' },
  {
    label: '过期时间',
    prop: 'expireTime',
    align: 'center',
    width: '180px',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.expireTime)}</span>
    )
  },
  {
    label: '租户状态',
    prop: 'status',
    align: 'center',
    renderTemplate: (scope) => <dict-tag type={DICT_TYPE.COMMON_STATUS} value={scope.row.status} />
  },
  {
    label: '创建时间',
    prop: 'createTime',
    align: 'center',
    width: '180px',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    label: '操作',
    prop: 'operation', // 添加 prop
    align: 'center',
    minWidth: '110px',
    fixed: 'right',
    renderTemplate: (scope) => (
      <div>
        {checkPermi(['system:tenant:update']) && (
          <ElButton link type="primary" onClick={() => openForm('update', scope.row.id)}>
            编辑
          </ElButton>
        )}
        {checkPermi(['system:tenant:delete']) && (
          <ElButton link type="danger" onClick={() => handleDelete(scope.row.id)}>
            删除
          </ElButton>
        )}
      </div>
    )
  }
])

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const apiParams: TenantApi.TenantPageReqVO = {
      ...queryParams.value
    }
    const data = await TenantApi.getTenantPage(apiParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 - UmvQuery @reset 默认会调用查询，这里handleQuery已经包含了设置pageNo=1和getList */
// const resetQuery = () => {
//   queryFormRef.value.resetFields() // UmvQuery 内部会处理重置，并通过 v-model 更新 queryParams
//   handleQuery() // @reset 事件会自动触发 handleQuery
// }

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TenantApi.deleteTenant(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const apiParams: TenantApi.TenantExportReqVO = {
      ...queryParams.value // pageNo 和 pageSize 可能对导出不是必需的，但 TenantExportReqVO 可能包含它们
    }
    const data = await TenantApi.exportTenant(apiParams)
    download.excel(data.data, '租户列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  await getList()
})
</script>
<style lang="scss" scoped></style>
