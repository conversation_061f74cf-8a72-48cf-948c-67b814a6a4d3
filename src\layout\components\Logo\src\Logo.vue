<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 08:59:44
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-09 16:41:10
 * @Description: 
-->
<script setup lang="ts">
import { ref, watch, computed, onMounted, unref } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'

import envController from '@/controller/envController'

// import { useLocaleStore } from '@/store/modules/locale'
// const localeStore = useLocaleStore()
import { useSsoStore } from '@/store/modules/sso'
const ssoStore = useSsoStore()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()

const show = ref(true)

// const client = computed(() => appStore.getClient)

const layout = computed(() => appStore.getLayout)

const collapse = computed(() => appStore.getCollapse)
//代客操作
import { useAgentOperationStore } from '@/store/modules/agentOperation'
const agentOperationStore = useAgentOperationStore()

// import { getCompleteToken, getTenantId, setToken } from '@/utils/auth'
// import { refreshToken } from '@/config/axios/service'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
const clientList = computed(() => userStore.getClients || []) //过期角色不可见
const clientName = computed(
  () => userStore.clients.find((item) => item.id === userStore.getCurrentClientId)?.name
)
/**
 * 设置当前客户端,跳转至端重新授权,
 *
 * @param val 当前客户端对象
 * @returns 无返回值
 */
const setCurrentClient = async (val) => {
  const clientId = val.id
  let redirectUri = val.url as string
  const oauthClient = val.oauthClient

  if (clientId === userStore.getCurrentClientId) return

  // 检查是否为专线IP模式，如果是则调整redirectUri
  if (envController.isDirectIPMode && envController.isDirectIPMode()) {
    if (oauthClient === 'umv-front-client') {
      // 客户端
      redirectUri = envController.getClientUrl() as string
    }
  }

  ssoStore.ssoAuth(oauthClient, redirectUri)
}

onMounted(() => {
  if (unref(collapse)) show.value = false
})

watch(
  () => collapse.value,
  (collapse: boolean) => {
    if (unref(layout) === 'topLeft' || unref(layout) === 'cutMenu') {
      show.value = true
      return
    }
    if (!collapse) {
      setTimeout(() => {
        show.value = !collapse
      }, 400)
    } else {
      show.value = !collapse
    }
  }
)

watch(
  () => layout.value,
  (layout) => {
    if (layout === 'top' || layout === 'cutMenu') {
      show.value = true
    } else {
      if (unref(collapse)) {
        show.value = false
      } else {
        show.value = true
      }
    }
  }
)
</script>

<template>
  <ElDropdown
    trigger="hover"
    @command="setCurrentClient"
    :disabled="agentOperationStore.agentOperationMode"
  >
    <div class="flex justify-items-center items-center outline-none">
      <img
        src="@/assets/imgs/umvLogo.svg"
        class="w-[calc(var(--logo-height)+40px)] h-[calc(var(--logo-height)-25px)]"
      />
      <div
        v-if="show"
        :class="[
          'ml-10px text-16px font-700',
          {
            'text-[var(--logo-title-text-color)]': layout === 'classic',
            'text-[var(--top-header-text-color)]':
              layout === 'topLeft' || layout === 'top' || layout === 'cutMenu'
          }
        ]"
      >
        {{ clientName ? clientName : '当前无交互端' }}
      </div>
    </div>
    <template #dropdown v-if="clientList.length > 1">
      <ElDropdownMenu>
        <ElDropdownItem v-for="item in clientList" :key="item.id" :command="item">
          <span :class="[userStore.getCurrentClientId === item.id && 'activeClient']">{{
            item.name
          }}</span>
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>
</template>
<style lang="scss" scoped>
.activeClient {
  color: var(--el-color-primary);
}
</style>
