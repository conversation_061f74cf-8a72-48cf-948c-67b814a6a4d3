<!--
 * @Author: hao-jie.chen <EMAIL>
 * @Date: 2024-04-30 14:05:04
 * @LastEditors: hao-jie.chen <EMAIL>
 * @LastEditTime: 2025-02-28 15:33:30
 * @Description: 
-->
<template>
  <Dialog :title="t('system.subscribe.desc6')" width="800" v-model="detailVisible" @close="close">
    <el-form-item :label="t('system.subscribe.menuPerm')" v-loading="loading">
      <el-card class="cardHeight">
        全部展开/折叠:
        <el-switch
          v-model="menuExpand"
          active-text="展开"
          inactive-text="折叠"
          inline-prompt
          @change="handleCheckedTreeExpand"
        />
        <el-tree
          ref="treeRef"
          default-expand-all
          :data="menuOptions"
          :props="defaultProps"
          :empty-text="t('common.emptyText')"
          node-key="id"
        >
          <template #default="{ node, data }">
            <span>
              <span>{{ node.label }}</span>
              <span>
                <el-tag
                  class="ml-10"
                  size="small"
                  type="success"
                  v-if="data.id === homePageService?.homePageMenuId"
                >
                  {{ t('system.subscribe.home') }}
                </el-tag>
                <el-button
                  class="absolute right-0"
                  size="small"
                  v-if="
                    data.component && data.id !== homePageService?.homePageMenuId && data.visible
                  "
                  type="primary"
                  plain
                  @click.stop="setHomePage(data)"
                >
                  {{ t('system.subscribe.setHome') }}
                </el-button>
              </span>
            </span>
          </template>
        </el-tree>
      </el-card>
    </el-form-item>

    <template #footer>
      <el-button @click="close">{{ t('common.close') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'settingDailog'
})

import { defaultProps } from '@/utils/tree'
// import { getTenantMenu } from '@/api/system/apply'
const { t } = useI18n() // 国际化

const message = useMessage()

const detailVisible = ref(false)
const detailData = ref()
const emit = defineEmits(['success'])
const menuOptions = ref<any[]>([]) // 树形结构数据

const loading = ref(false)
import { routeDuplication, filterRouteByCondition, getHandleRoute } from '@/utils/routerHelper'
import { getClientApplicationList } from '@/api/system/tenantPackage'
import { getRouteAndPermissionRenovation } from '@/api/login'

let homePageService = ref(undefined) //保存已设首页的服务信息
let applications = ref([])
const open = async (row) => {
  if (loading.value) return
  try {
    loading.value = true
    detailData.value = row

    //获取服务列表,并找出已设首页的服务
    applications.value = await getClientApplicationList(row.id)
    console.log('applications', applications)
    homePageService.value = applications.value?.find((item) => !!item.homePageMenuId)
    console.log('homePageService', homePageService.value)
    // const res = await getTenantMenu({
    //   clientId: row.id
    // })
    const res = await getRouteAndPermissionRenovation({
      clientId: row.id
    })
    console.log('res', res)

    let handledRoute = getHandleRoute(res.menus)
    console.log('handledRoute', handledRoute)
    let resRoute = filterRouteByCondition(handledRoute)

    menuOptions.value = routeDuplication(resRoute, res.menus)
    detailVisible.value = true
  } finally {
    loading.value = false
  }
}
defineExpose({ open })

const close = () => {
  detailVisible.value = false
  emit('success')
}

//首页设置
import { updateClientApplication } from '@/api/system/tenantPackage'

const setHomePage = async (data) => {
  try {
    if (!!homePageService.value) {
      await message.delConfirm(t('system.subscribe.desc7'))
      //重置首页
      await updateClientApplication({
        id: homePageService.value.id,
        homePageMenuId: 0
      })
    }
    //当前租户对应服务的id
    let currentService = applications.value?.find(
      (item) => item.applicationId === data.applicationId
    )
    console.log('currentService', currentService)
    if (!currentService) {
      throw '当前服务不存在'
    }

    await updateClientApplication({
      id: currentService.id,
      homePageMenuId: data.id
    })
    console.log(data)

    open(detailData.value)
  } catch (error) {
    message.error(error)
  }
}

// 是否展开全部
const menuExpand = ref(true)
const treeRef = ref()
/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}
onUnmounted(() => {
  console.log('umounted')
})
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
</style>
