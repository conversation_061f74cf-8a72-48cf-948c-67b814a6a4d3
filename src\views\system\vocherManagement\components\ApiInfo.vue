<template>
  <div class="api-info-wrap" v-loading="loading">
    <section class="top-row">
      <el-row align="middle">
        <el-tag
          :type="summaryInfo.apiDocMethod == 'POST' ? 'success' : 'warning'"
          effect="dark"
          class="api-method"
          >{{ summaryInfo.apiDocMethod }}</el-tag
        >
        <div style="margin-left: 10px">
          <span class="api-url">{{ summaryInfo.onlyPathKey }}</span>
          <el-icon class="copy-icon" @click="copyContent(summaryInfo.onlyPathKey)">
            <CopyDocument />
          </el-icon>
        </div>
      </el-row>
      <div class="data-info">
        <div class="data-info-item">
          <span class="label">创建时间：</span>
          <span>{{ summaryInfo.createTime }}</span>
        </div>
      </div>
    </section>
    <section class="content-row">
      <div class="api-description">
        <div class="sub-title">接口说明</div>
        <div>{{ summaryInfo.apiDocDescribe }}</div>
      </div>

      <div class="api-params">
        <div class="api-params-item">
          <div class="sub-title">Header 参数</div>
          <el-table
            :data="headerObj.tableData"
            border
            :tree-props="{ children: 'children' }"
            row-key="createTime"
            default-expand-all
          >
            <el-table-column prop="paramName" label="参数名称" width="200" />
            <el-table-column prop="paramType" label="参数类型" />
            <el-table-column prop="paramRequired" label="是否必填" align="center">
              <template #default="scope">
                <span>{{ scope.row.paramRequired ? '是' : '否' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="paramValue" label="默认值" />
            <el-table-column prop="paramRemark" label="备注" />
          </el-table>
        </div>

        <el-row :gutter="20" class="api-params-item">
          <el-col :span="14" class="left-table">
            <div class="sub-title">Body 参数</div>
            <el-table
              :data="bodyObj.tableData"
              border
              ref="bodyTableRef"
              :tree-props="{ children: 'children' }"
              row-key="createTime"
              default-expand-all
            >
              <el-table-column prop="paramName" label="参数名称" width="200" />
              <el-table-column prop="paramType" label="参数类型" />
              <el-table-column prop="paramRequired" label="是否必填" width="100" align="center">
                <template #default="scope">
                  <span>{{ scope.row.paramRequired ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="paramValue" label="默认值" />
              <el-table-column prop="paramRemark" label="备注" />
            </el-table>
          </el-col>
          <el-col :span="10" class="right-example">
            <div class="sub-title">
              <span>示例</span>
              <el-button
                class="copy-btn"
                :icon="CopyDocument"
                size="small"
                type="info"
                plain
                @click="copyContent(bodyObj.exampleCode)"
              >
                复制
              </el-button>
            </div>
            <!-- <div
              class="example"
              v-html="bodyObj.exampleCode"
              :style="{ height: `${bodyExampleHeight}px` }"
            ></div> -->
            <VAceEditor
              key="exampleCodeFailed"
              class="example"
              :style="{ height: `${bodyExampleHeight}px `, 'min-height': '50px' }"
              v-model:value="bodyObj.exampleCode"
              :options="aceConfig.options"
              :readonly="true"
            />
          </el-col>
        </el-row>

        <div class="api-params-item respone-item">
          <div class="sub-title">返回数据</div>
          <el-tabs v-model="activeResponeTab" type="border-card" @tab-change="changeTab">
            <el-tab-pane label="成功" name="success">
              <el-row class="respone-info">
                <el-col :span="4">
                  <span class="label">HTTP状态码：</span>
                  <span>{{ responeObj.apiDocSuccessCode }}</span>
                </el-col>
                <el-col :span="6">
                  <span class="label">内容格式：</span>
                  <span>{{ responeObj.apiDocSuccessFormat }}</span>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="14">
                  <div class="sub-title">数据结构</div>
                  <el-table
                    :data="responeObj.tableDataSuccess"
                    border
                    :tree-props="{ children: 'children' }"
                    row-key="createTime"
                    default-expand-all
                    ref="responeTableRef"
                  >
                    <el-table-column prop="paramName" label="参数名称" width="200" />
                    <el-table-column prop="paramType" label="参数类型" />
                    <el-table-column
                      prop="paramRequired"
                      label="是否必填"
                      width="100"
                      align="center"
                    >
                      <template #default="scope">
                        <span>{{ scope.row.paramRequired ? '是' : '否' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="paramValue" label="默认值" />
                    <el-table-column prop="paramRemark" label="备注" /> </el-table
                ></el-col>
                <el-col :span="10" class="right-example">
                  <div class="sub-title">
                    <span>示例</span>
                    <el-button
                      class="copy-btn"
                      :icon="CopyDocument"
                      size="small"
                      type="info"
                      plain
                      @click="copyContent(responeObj.exampleCodeSuccess)"
                      >复制</el-button
                    >
                  </div>
                  <!-- <div
                    class="example"
                    v-html="responeObj.exampleCodeSuccess"
                    :style="{ height: `${responeExampleHeight}px` }"
                  ></div> -->
                  <VAceEditor
                    key="exampleCodeSuccess"
                    class="example"
                    :style="{ height: `${responeExampleHeight}px `, 'min-height': '50px' }"
                    v-model:value="responeObj.exampleCodeSuccess"
                    :options="aceConfig.options"
                    :readonly="true"
                  />
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="失败" name="fail">
              <el-row class="respone-info">
                <el-col :span="4">
                  <span class="label">HTTP状态码：</span>
                  <span>{{ responeObj.apiDocFailedCode }}</span>
                </el-col>
                <el-col :span="6">
                  <span class="label">内容格式：</span>
                  <span>{{ responeObj.apiDocFailedFormat }}</span>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="14">
                  <div class="sub-title">数据结构</div>
                  <el-table
                    :data="responeObj.tableDataFailed"
                    border
                    :tree-props="{ children: 'children' }"
                    row-key="paramName"
                    default-expand-all
                    ref="responeTableFailedRef"
                  >
                    <el-table-column prop="paramName" label="参数名称" width="200" />
                    <el-table-column prop="paramType" label="参数类型" />
                    <el-table-column
                      prop="paramRequired"
                      label="是否必填"
                      width="100"
                      align="center"
                    >
                      <template #default="scope">
                        <span>{{ scope.row.paramRequired ? '是' : '否' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="paramValue" label="默认值" />
                    <el-table-column prop="paramRemark" label="备注" /> </el-table
                ></el-col>
                <el-col :span="10" class="right-example">
                  <div class="sub-title">
                    <span>示例</span>
                    <el-button
                      class="copy-btn"
                      :icon="CopyDocument"
                      size="small"
                      type="info"
                      plain
                      @click="copyContent(responeObj.exampleCodeFailed)"
                      >复制</el-button
                    >
                  </div>
                  <!-- <div
                    class="example"
                    v-html="responeObj.exampleCodeFailed"
                    :style="{ height: `${responeExampleHeight}px` }"
                  ></div> -->
                  <VAceEditor
                    key="exampleCodeFailed"
                    class="example"
                    :style="{ height: `${responeExampleFailedHeight}px `, 'min-height': '20px' }"
                    v-model:value="responeObj.exampleCodeFailed"
                    :options="aceConfig.options"
                    :readonly="true"
                  />
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { VAceEditor } from 'vue3-ace-editor'

import { CopyDocument } from '@element-plus/icons-vue'
import { useClipboard } from '@vueuse/core'
import { formatDate } from '@/utils/formatTime'
const props = defineProps({
  apiInfoData: {
    type: Object,
    default: () => {}
  }
})
const { apiInfoData } = toRefs(props)

const aceConfig = reactive({
  options: {
    enableBasicAutocompletion: true,
    enableSnippest: true,
    enableLiveAutocompletion: true,
    tabSize: 2,
    showPrintMargin: false,
    fontSize: 14
  }
})

const loading = ref(false)

const summaryInfo = ref({
  apiDocMethod: '',
  onlyPathKey: '',
  createTime: '',
  apiDocDescribe: ''
})

// -----------Header参数
const headerObj = ref<any>({
  tableData: []
})

// -----------Body参数
const bodyTableRef = ref<any>()
const bodyExampleHeight = ref('auto') // 示例代码高度，和左侧表格高度一致
const bodyObj = ref<any>({
  tableData: [],
  exampleCode: ''
})

// -----------返回数据
const responeTableRef = ref<any>()
const responeTableFailedRef = ref<any>()
const responeExampleHeight = ref('auto') // 成功示例代码高度，和左侧成功表格高度一致
const responeExampleFailedHeight = ref('auto') // 成功示例代码高度，和左侧成功表格高度一致
const responeObj = ref<any>({
  tableDataSuccess: [],
  exampleCodeSuccess: '',
  apiDocSuccessCode: '',
  apiDocSuccessFormat: '',

  tableDataFailed: [],
  exampleCodeFailed: '',
  apiDocFailedCode: '',
  apiDocFailedFormat: ''
})

const activeResponeTab = ref('success')

const changeTab = () => {
  getTableHeight()
}

watch(apiInfoData, () => {
  dealInfo()
  getTableHeight()
})
onMounted(() => {
  // dealInfo()
})

const dealInfo = () => {
  let info = apiInfoData.value
  console.log('api info ==== ', info)

  // 简介、接口说明
  summaryInfo.value.apiDocMethod = info.apiDocMethod
  summaryInfo.value.onlyPathKey = info.onlyPathKey
  summaryInfo.value.createTime = info.createTime
    ? formatDate(info.createTime, 'YYYY-MM-DD HH:mm:ss')
    : ''
  summaryInfo.value.apiDocDescribe = info.apiDocDescribe

  // header处理
  if (info.apiDocHeader) {
    let apiDocHeader = JSON.parse(info.apiDocHeader) || []
    headerObj.value.tableData = apiDocHeader
  }

  // body-参数结构
  if (info.apiDocBody) {
    let apiDocBody = JSON.parse(info.apiDocBody) || []
    bodyObj.value.tableData = apiDocBody
  }

  // body-参数示例
  if (info.apiDocBodySample) {
    // let apiDocBodySample = JSON.parse(info.apiDocBodySample) || {}
    bodyObj.value.exampleCode = info.apiDocBodySample
  }

  // 成功返回-结构
  if (info.apiDocSuccessStruct) {
    let apiDocSuccessStruct = JSON.parse(info.apiDocSuccessStruct) || {}
    responeObj.value.tableDataSuccess = apiDocSuccessStruct
  }

  responeObj.value.apiDocSuccessCode = info.apiDocSuccessCode || ''
  responeObj.value.apiDocSuccessFormat = info.apiDocSuccessFormat || ''
  responeObj.value.apiDocFailedCode = info.apiDocFailedCode || ''
  responeObj.value.apiDocFailedFormat = info.apiDocFailedFormat || ''

  // 成功返回-示例
  if (info.apiDocSuccessStructSample) {
    responeObj.value.exampleCodeSuccess = info.apiDocSuccessStructSample
    // responeObj.value.exampleCodeSuccess = JSON.stringify({ name: '+++++', age: 14 })
  }

  // 失败返回-结构
  if (info.apiDocFailedStruct) {
    let apiDocFailedStruct = JSON.parse(info.apiDocFailedStruct) || {}
    responeObj.value.tableDataFailed = apiDocFailedStruct
  }

  // 失败返回-示例
  if (info.apiDocFailedStructSample) {
    responeObj.value.exampleCodeFailed = info.apiDocFailedStructSample
  }
}

const getTableHeight = () => {
  setTimeout(() => {
    nextTick(() => {
      try {
        bodyExampleHeight.value = bodyTableRef?.value?.$el?.offsetHeight || 'auto'
        responeExampleHeight.value = responeTableRef?.value?.$el?.offsetHeight || 'auto'
        responeExampleFailedHeight.value = responeTableFailedRef?.value?.$el?.offsetHeight || 'auto'
      } catch (error) {}
    })
  }, 0)
}

const copyContent = async (content) => {
  const { copy } = useClipboard()
  try {
    await copy(content)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败！')
    console.error('复制失败', error)
  }
}
</script>

<style scoped>
.api-info-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .top-row {
    padding: 15px 10px;
    border-radius: 6px;
    border: 1px solid #cdd0d6;
    color: #606266;
    .api-method {
      font-size: 14px;
    }
    .api-url:hover {
      text-decoration: underline;
      cursor: pointer;
    }
    .copy-icon {
      position: relative;
      top: 2px;
      margin-left: 6px;
      cursor: pointer;
    }
    .copy-icon:hover {
      color: #d5a147;
    }
    .data-info {
      margin-top: 14px;
      display: flex;
      .data-info-item {
        margin-right: 40px;
        .label {
          color: #606266;
        }
      }
    }
  }
  .content-row {
    padding: 20px 10px;
    flex: 1;
    overflow: auto;
    .api-description {
      color: #606266;
      .sub-title {
        margin-bottom: 6px;
      }
    }
    .api-params {
      .api-params-item {
        margin-top: 40px;
        .sub-title {
          margin-bottom: 10px;
          position: relative;
        }
        .left-table {
        }
        .right-example {
          display: flex;
          flex-direction: column;
          .copy-btn {
            position: absolute;
            right: 0;
          }
          .example {
            border: 1px solid #e5e7eb;
            padding: 10px;
            overflow: auto;
          }
        }
      }
      .respone-item {
        .respone-info {
          font-size: 14px;
          padding: 10px 10px 20px;
          .label {
            color: #606266;
          }
        }
      }
    }
  }
  .sub-title {
    color: #333333;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
  }
  .sub-title::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: #d5a147;
    margin-right: 4px;
  }
}
</style>
