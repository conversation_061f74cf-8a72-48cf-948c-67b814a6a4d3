import { useI18n } from '@/hooks/web/useI18n'

// 使用动态导入方式
const Layout = () => import('@/layout/Layout.vue')

const { t } = useI18n()

// 开发环境路由配置
const devRoutes: AppRouteRecordRaw[] = [
  {
    path: '/devDemo',
    component: Layout,
    name: 'DevDEmo',
    meta: {
      hidden: false,
      title: '开发路由测试',
      icon: 'ep:tools',
      noCache: true
    },
    children: [
      {
        path: '/demo1',
        component: () => import('@/views/demo/UmvQueryDemo.vue'),
        name: 'demo1',
        meta: {
          canTo: true,
          noTagsView: false,
          icon: 'ep:user',
          title: 'UmvQueryDemo'
        }
      },
      {
        path: '/demo2',
        component: () => import('@/views/demo/UmvTableDemo.vue'),
        name: 'demo2',
        meta: {
          canTo: true,
          noTagsView: false,
          icon: 'ep:user',
          title: 'UmvTableDemo'
        }
      }
    ]
  }
]

export default devRoutes
