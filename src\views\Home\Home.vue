<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-25 11:47:52
 * @LastEditors: <PERSON>J<PERSON>
 * @LastEditTime: 2023-06-09 09:55:06
 * @Description: 
-->
<template>
  <el-container :class="prefixCls">
    <el-header>
      <div
        :class="`${prefixCls}__top w-full h-full flex items-center justify-between px-16 bg-white <sm:px-5`"
      >
        <div class="flex flex-row items-center">
          <img
            class="mr-10 cursor-pointer <2xl:(max-w-105px)"
            src="/umvLogo.svg"
            @click="
              () => {
                tabIndex = 0
                scrollTop()
              }
            "
          />
          <!-- tab -->
          <Tab ref="tabRef" @scroll-top="scrollTop" />
        </div>

        <GoldButton
          :title="t('common.login')"
          class="w-80px login-btn"
          @click="showLoginForm"
          v-if="!isExhibit"
        />
      </div>
    </el-header>
    <el-main>
      <el-scrollbar :height="mainHeight" ref="scrollbarRef">
        <Transition name="bounce">
          <UMVCard v-if="tabIndex == 0" />
          <CoreIdea v-else-if="tabIndex == 1" />
          <OnlineService v-else-if="tabIndex == 2" />
          <InnovateProduct v-else-if="tabIndex == 3" />
          <AIDesign v-else-if="tabIndex == 4" />
          <AcquireCustomers v-else-if="tabIndex == 5" />
          <AboutMe v-else-if="tabIndex == 6" />
          <Agreement class="pt-50px xl:px-250px lg:px-200px" v-else-if="tabIndex == 11 && !ifEn" />
          <!--Agreement 英文版 -->
          <Agreement_en_US
            class="pt-50px xl:px-250px lg:px-200px"
            v-else-if="tabIndex == 11 && ifEn"
          />
          <Agreement2
            class="pt-50px xl:px-250px lg:px-200px"
            v-else-if="tabIndex == 22 && !ifEn"
            @tab-click="tabClick"
          />
          <!--Agreement2 英文版 -->
          <Agreement2_en_US
            class="pt-50px xl:px-250px lg:px-200px"
            v-else-if="tabIndex == 22 && ifEn"
            @tab-click="tabClick"
          />

          <SDK class="pt-50px xl:px-250px lg:px-200px" v-else-if="tabIndex == 33 && !ifEn" />
          <!--SDK 英文版 -->
          <SDK_en_US class="pt-50px xl:px-250px lg:px-200px" v-else-if="tabIndex == 33 && ifEn" />

          <UMVCard v-else />
        </Transition>

        <Footer
          @change-tab-index="
            (val) => {
              tabIndex = val
              scrollTop()
            }
          "
        />
      </el-scrollbar>
    </el-main>
    <!-- 登录表单 -->
    <LoginForm ref="loginFormRef" v-if="!isExhibit" />
  </el-container>
</template>
<script setup lang="ts">
defineOptions({
  name: 'Home'
})

// import { underlineToHump } from '@/utils'

import { useDesign } from '@/hooks/web/useDesign'
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('home')

const { t, ifEn } = useI18n()
import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

//滚动条
const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>()
//滚动到顶部
const scrollTop = () => {
  scrollbarRef.value!.setScrollTop(0)
}
//tab
import Tab from './components/Tab.vue'
let tabRef = ref()
let tabIndex = computed({
  get: () => tabRef?.value?.tabIndex,
  set: (val) => {
    tabRef.value.tabIndex = val - 0
  }
})
// 切换选项卡
const tabClick = (index) => {
  console.log(index)
  tabIndex.value = index - 0
}

//登录表单
import LoginForm from '../Login/Login.vue'
let loginFormRef = ref()
const showLoginForm = () => {
  loginFormRef.value.showLoginForm()
}

//首页
import UMVCard from './components/UMVCard.vue'
//核心理念
import CoreIdea from './components/CoreIdea.vue'
//在线服务
import OnlineService from './components/OnlineService.vue'
//创新产品
import InnovateProduct from './components/InnovateProduct.vue'
//AI设计
import AIDesign from './components/AIDesign.vue'
//获客引流
import AcquireCustomers from './components/AcquireCustomers.vue'

//关于我们
import AboutMe from './components/AboutMe.vue'

//平台协议
import Agreement from './components/Agreement.vue'

//平台协议 英文
import Agreement_en_US from './components/Agreement_en_US.vue'
//隐私政策
import Agreement2 from './components/Agreement2.vue'
//隐私政策-英文
import Agreement2_en_US from './components/Agreement2_en_US.vue'
//Agreement2
import SDK from './components/SDK.vue'
//Agreement2-英文
import SDK_en_US from './components/SDK_en_US.vue'

//footer
import Footer from './components/Footer.vue'

//计算main高度
const headerHeight = ref(60)
let mainHeight = ref(0)
nextTick(() => {
  setMainHeight()
})
const setMainHeight = () => {
  headerHeight.value = document?.querySelector('.el-header')?.offsetHeight
  mainHeight.value = document?.querySelector('#app')?.offsetHeight - headerHeight.value
}
window.onresize = () => {
  setMainHeight()
}

const { query } = useRoute() // 查询参数

// 是否是展示模式，如果是，就没有登录按钮，目前只用于客户端的关于板块
const isExhibit = computed(() => {
  return query?.isExhibit
})

onBeforeMount(() => {
  if (query?.l === '1') {
    //带有login参数就显示登录弹窗
    nextTick(() => {
      showLoginForm()
    })
  }
})
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-home;

.#{$prefix-cls} {
  .el-main {
    padding: 0px !important;
  }

  &__main {
    background-color: aquamarine !important;
  }
}
.font-color {
  color: #7d8291;
}
.dotted::before {
  content: '•';
  margin-right: 10px;
}

.bg-white {
  background-color: white;
}
// 蒙板
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.el-button) {
  &:hover {
    // border-color: rgba(213, 161, 71, 0.7);
    // background-color: rgba(213, 161, 71, 0.1);
    // background: linear-gradient(114deg, #eecd91, #d5a147);
    border-color: var(--client-color-primary-light-3);
    background-color: var(--client-color-primary-light-7);
  }
}

/**动画demo */
// .bounce-enter-active {
//   animation: bounce-in 0.1s;
// }
// .bounce-leave-active {
//   animation: bounce-in 1s reverse;
// }

// @keyframes bounce-in {
// 0% {
//   transform: scale(1.25);
// }
// 50% {
//   transform: scale(1.25);
// }
// 100% {
//   transform: scale(1);
// }
// }

.login-btn {
  background: linear-gradient(114deg, #eecd91, #d5a147);
  &:hover {
    border-color: rgba(213, 161, 71, 0.7);
    background-color: rgba(213, 161, 71, 0.1);
  }
}
</style>
