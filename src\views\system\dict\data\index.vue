<template>
  <!-- 列表 -->
  <UmvContent>
    <UmvQuery v-model="queryParams" :opts="queryOpts" @check="handleQuery" @reset="handleQuery" />

    <UmvTable v-loading="loading" :data="list" :columns="columns" ref="tableRef" @refresh="getList">
      <template #tools>
        <el-button
          type="primary"
          plain
          size="small"
          @click="openForm('create')"
          v-hasPermi="['system:dict:create']"
        >
          <icon-ep-plus style="font-size: 12px" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          size="small"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:dict:export']"
        >
          <icon-ep-download style="font-size: 12px" class="mr-5px" /> 导出
        </el-button>
      </template>

      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>
    <!-- 表单弹窗：添加/修改 -->
    <DictDataForm ref="formRef" @success="getList" />
  </UmvContent>
</template>
<script lang="tsx" setup>
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as DictDataApi from '@/api/system/dict/dict.data'
import * as DictTypeApi from '@/api/system/dict/dict.type'
import DictDataForm from './DictDataForm.vue'
import { useDictStoreWithOut } from '@/store/modules/dict'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { checkPermi } from '@/utils/permission'
import UmvContent from '@/components/UmvContent'

defineOptions({ name: 'SystemDictData' })

const dictStore = useDictStoreWithOut()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute() // 路由

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  label: '',
  status: undefined,
  dictType: route.params.dictType
})
const dictTypeList = ref<DictTypeApi.DictTypeVO[]>([]) // 字典类型的列表

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  dictType: {
    label: '字典名称',
    defaultVal: route.params.dictType,
    controlRender: (form) => (
      <el-select v-model={form.dictType}>
        {dictTypeList.value.map((item) => (
          <el-option key={item.type} label={item.name} value={item.type} />
        ))}
      </el-select>
    )
  },
  label: {
    label: '字典标签',
    defaultVal: '',
    controlRender: (form) => (
      <el-input v-model={form.label} placeholder="请输入字典标签" clearable />
    )
  },
  status: {
    label: '状态',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.status} placeholder="数据状态" clearable>
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: '字典编码', align: 'center' },
  { prop: 'label', label: '字典标签', align: 'center' },
  { prop: 'value', label: '字典键值', align: 'center' },
  { prop: 'sort', label: '字典排序', align: 'center' },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    renderTemplate: (scope) => <dict-tag type={DICT_TYPE.COMMON_STATUS} value={scope.row.status} />
  },
  { prop: 'colorType', label: '颜色类型', align: 'center' },
  { prop: 'cssClass', label: 'CSS Class', align: 'center' },
  { prop: 'remark', label: '备注', align: 'center', showOverflowTooltip: true },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        {checkPermi(['system:dict:update']) && (
          <el-button link type="primary" onClick={() => openForm('update', scope.row.id)}>
            修改
          </el-button>
        )}
        {checkPermi(['system:dict:delete']) && (
          <el-button link type="danger" onClick={() => handleDelete(scope.row.id)}>
            删除
          </el-button>
        )}
      </div>
    )
  }
])

const tableRef = ref() // 表格引用
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DictDataApi.getDictDataPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id, queryParams.value.dictType)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DictDataApi.deleteDictData(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await dictStore.resetDict()
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DictDataApi.exportDictData(queryParams.value)
    download.excel(data.data, '字典数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  // 查询字典（精简)列表
  dictTypeList.value = await DictTypeApi.getSimpleDictTypeList()
  await getList()
})
</script>
