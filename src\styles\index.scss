@import './var.css';
@import 'element-plus/theme-chalk/dark/css-vars.css';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow: 0 0 10px var(--el-color-primary), 0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}
// 解决element-plus 组件默认宽度为100%，行内表单会造成组件宽度无法自适应，手动设置固定宽度样式
.el-form--inline {
  .el-form-item {
    & > .el-input,
    .el-cascader,
    .el-select,
    .el-date-editor,
    .el-autocomplete {
      width: 200px;
    }
  }
}

// /* ---el-table滚动条公共样式--- */
.el-scrollbar {
  // 横向滚动条
  .el-scrollbar__bar.is-horizontal .el-scrollbar__thumb {
    opacity: 0.8; // 默认滚动条自带透明度
    height: 12px; // 横向滑块的宽度
    border-radius: 2px; // 圆角度数
    // background: linear-gradient(114deg, #f9e0b3, #eecd91);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.15); // 滑块阴影
  }

  .el-scrollbar__bar.is-horizontal {
    height: 12px;
  }
  // 纵向滚动条
  // .el-scrollbar__bar.is-vertical .el-scrollbar__thumb {
  //   opacity: 1;
  //   width: 8px; // 纵向滑块的宽度
  //   border-radius: 2px;
  //   background: linear-gradient(114deg, #f9e0b3, #eecd91);
  //   box-shadow: 0 0 6px rgba(0, 0, 0, 0.15);
  // }
}

.icons {
  font-size: 14px;
  outline: white;
}

/* 选择具有v-content-wrap类的第二个div元素 */
div:nth-child(2).v-content-wrap {
  margin-top: 10px;
}

//鼠标悬浮变大
.hover-scale {
  transition: all 0.3s;
  &:hover {
    transform: scale(1.1);
  }
}
