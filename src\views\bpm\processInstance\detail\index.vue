<template>
  <ContentWrap>
    <!-- 表单信息 -->
    <ProcessInstanceFormInfo
      :process-instance="processInstance"
      :loading="processInstanceLoading"
    />

    <!-- 已办任务信息 -->
    <ProcessInstanceDoneInfo
      v-if="doneTask"
      :task="doneTask"
      :business-key="processInstance?.businessKey"
      :user-task-forms="processInstance?.processDefinition?.userTaskForms"
      :form-variables="processInstance?.formVariables"
      :loading="processInstanceLoading"
    />

    <!-- 审批信息 -->
    <el-card
      v-for="(item, index) in runningTasks"
      :key="index"
      v-loading="processInstanceLoading"
      class="box-card"
    >
      <template #header>
        <span class="el-icon-picture-outline">审批任务【{{ item.name }}】</span>
      </template>
      <el-col :offset="6" :span="16">
        <el-form
          :ref="'form' + index"
          :model="auditForms[index]"
          :rules="auditRule"
          label-width="100px"
        >
          <!-- 委派任务 -->
          <el-form-item v-if="getCurrentDelegateTask(item)" label="委派任务">
            <span class="text-sm mr-4 p-1 px-2 border-1 border-[#d3d4d6] rounded">
              {{ getCurrentDelegateTask(item)?.ownerUserNickname }}
            </span>
            <el-tag>委派给</el-tag>
            <span class="text-sm ml-4 p-1 px-2 border-1 border-[#d3d4d6] rounded">
              {{ getCurrentDelegateTask(item)?.assigneeUserNickname }}
            </span>
          </el-form-item>

          <el-form-item v-if="processInstance && processInstance.name" label="流程名">
            {{ processInstance.name }}
          </el-form-item>
          <el-form-item v-if="processInstance && processInstance.startUser" label="流程发起人">
            {{ processInstance.startUser.nickname }}
            <el-tag size="small" type="info">{{ processInstance.startUser.deptName }}</el-tag>
          </el-form-item>

          <!-- 任务表单 -->
          <ProcessInstanceTaskForm
            ref="taskFormRef"
            :task="item"
            :business-key="processInstance?.businessKey"
            :user-task-forms="processInstance?.processDefinition?.userTaskForms"
          />

          <el-form-item label="审批建议" prop="reason">
            <el-input
              v-model="auditForms[index].reason"
              placeholder="请输入审批建议"
              type="textarea"
            />
          </el-form-item>
          <el-form-item label="下节点审批人">
            <ElButton
              class="mr-5"
              type="primary"
              :icon="Plus"
              circle
              @click="
                () => {
                  handleType = handleTypeEnum.CC
                  openTaskAssignRuleForm(index)
                }
              "
            />
            <el-tag size="large" type="info" v-if="auditForms[index].assigneeType">
              {{ bpmTaskAssignRuleType[auditForms[index].assigneeType].zh }}
            </el-tag>
            <el-tag
              v-for="assignItem in auditForms[index].assigneeOptions"
              :key="assignItem.id"
              class="ml-2"
              size="large"
              closable
              @close="deleteAssignData(assignItem.id, index)"
              >{{ assignItem.name }}</el-tag
            >
          </el-form-item>
          <el-form-item label="抄送人">
            <ElButton
              class="mr-5"
              type="primary"
              :icon="Plus"
              circle
              @click="
                () => {
                  handleType = handleTypeEnum.CC
                  openTaskSelectUserForm(item.id, index)
                }
              "
            />

            <el-tag
              v-for="userItem in auditForms[index].CCUserList"
              :key="userItem.id"
              class="mr-2"
              size="large"
              closable
              @close="deleteCCUser(userItem.id, index)"
              >{{ userItem.nickname }}</el-tag
            >
          </el-form-item>
        </el-form>

        <div class="ml-10% mb-20px text-14px">
          <el-button type="success" @click="handleAudit(item, true)">
            <icon-ep-select style="font-size: 12px" class="mr-2px" />
            通过
          </el-button>
          <el-button type="danger" @click="handleAudit(item, false)">
            <icon-ep-close style="font-size: 12px" class="mr-2px" />
            不通过
          </el-button>
          <el-button
            type="primary"
            @click="
              () => {
                handleType = handleTypeEnum.Forward
                openTaskSelectUserForm(item.id, index)
              }
            "
          >
            <icon-ep-edit style="font-size: 12px" class="mr-2px" />
            转办
          </el-button>
          <el-button
            type="primary"
            @click="
              () => {
                handleType = handleTypeEnum.Delegate
                openTaskSelectUserForm(item.id, index)
              }
            "
          >
            <icon-ep-position icon="ep:position" />
            委派
          </el-button>
          <el-button type="warning" @click="handleBack(item)">
            <icon-ep-back style="font-size: 12px" class="mr-5px" />
            回退
          </el-button>
        </div>
      </el-col>
    </el-card>

    <!-- 审批记录 -->
    <ProcessInstanceTaskList :loading="tasksLoad" :tasks="tasks" />

    <!-- 高亮流程图 -->
    <ProcessInstanceBpmnViewer
      :id="`${id}`"
      :bpmn-xml="bpmnXML"
      :loading="processInstanceLoading"
      :process-instance="processInstance"
      :tasks="tasks"
    />

    <!-- 弹窗：转派审批人 -->
    <TaskSelectUserForm
      ref="taskSelectUserFormRef"
      :handleType="handleType"
      @success="getDetail"
      @handle-forward="handleForward"
      @handle-delegate="handleDelegate"
      @set-cc-user-list="setCcUserList"
    />
    <!-- 弹窗: 下节点审批人 -->
    <TaskAssignRuleForm ref="taskAssignRuleForm" :handleType="2" @success="setAssignee" />
  </ContentWrap>
</template>
<script setup lang="ts">
defineOptions({
  name: 'BpmProcessInstanceDetail'
})

import { useUserStore } from '@/store/modules/user'
import * as DefinitionApi from '@/api/bpm/definition'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as TaskApi from '@/api/bpm/task'

import TaskSelectUserForm from './TaskSelectUserForm.vue'
import ProcessInstanceBpmnViewer from './ProcessInstanceBpmnViewer.vue'
import ProcessInstanceTaskList from './ProcessInstanceTaskList.vue'
import ProcessInstanceFormInfo from './ProcessInstanceFormInfo.vue'
import { Plus } from '@element-plus/icons-vue'
import ProcessInstanceTaskForm from './ProcessInstanceTaskForm.vue'
import ProcessInstanceDoneInfo from './ProcessInstanceDoneInfo.vue'
const { query } = useRoute() // 查询参数
const message = useMessage() // 消息弹窗
const { proxy } = getCurrentInstance() as any

const userId = useUserStore().getUser.id // 当前登录的编号
const id = query.id as unknown as number // 流程实例的编号
const taskId = query.taskId as string // 已办任务的编号（从已办任务列表进入时）
const processInstanceLoading = ref(false) // 流程实例的加载中
const processInstance = ref<ProcessInstanceApi.ProcessInstanceType>() // 流程实例
const bpmnXML = ref('') // BPMN XML
const tasksLoad = ref(true) // 任务的加载中
const tasks = ref<any[]>([]) // 任务列表
const doneTask = ref<any>(null) // 已办任务详情

// ========== 审批信息 ==========
const runningTasks = ref<any[]>([]) // 运行中的任务
const auditForms = ref<any[]>([]) // 审批任务的表单
const auditRule = reactive({
  reason: [{ required: true, message: '审批建议不能为空', trigger: 'blur' }]
})

//审批人选择弹出框
enum handleTypeEnum {
  /** 转办*/
  Forward = 'Forward',
  /** 委派*/
  Delegate = 'Delegate',
  /**抄送3 */
  CC = 'CC'
}
let handleType = ref<string>('')

const deleteCCUser = (val, auditFormsIndex) => {
  //删除抄送人
  auditForms.value[auditFormsIndex].CCUserList = auditForms.value[
    auditFormsIndex
  ]?.CCUserList.filter((item) => item.id != val)
  //子组件CCUserList数据同步
  taskSelectUserFormRef.value.CCUserList = taskSelectUserFormRef.value.CCUserList.filter(
    (item) => item.id != val
  )
  //子组件assigneeUserId数据同步
  taskSelectUserFormRef.value.formData.assigneeUserId =
    taskSelectUserFormRef.value.formData.assigneeUserId.filter((item) => item != val)
}

/** 处理审批通过和不通过的操作 */
const handleAudit = async (task, pass) => {
  // 1.1 获得对应表单
  const index = runningTasks.value.indexOf(task)
  const auditFormRef = proxy.$refs['form' + index][0]
  // 1.2 校验表单
  const elForm = unref(auditFormRef)
  if (!elForm) return
  const valid = await elForm.validate()
  if (!valid) return

  // 获取任务表单数据
  const taskFormData = proxy.$refs.taskFormRef ? proxy.$refs.taskFormRef[index]?.getFormData() : {}

  // 2.1 提交审批
  const data = {
    id: task.id,
    reason: auditForms.value[index].reason,
    ccUserIds: auditForms.value[index]?.CCUserList?.map((item) => item.id),
    assigneeType: auditForms.value[index]?.assigneeType,
    assigneeOptions: auditForms.value[index]?.assigneeOptions?.map((item) => item.id),
    variables: taskFormData || {}
  }

  if (pass) {
    await TaskApi.approveTask(data)
    message.success('审批通过成功')
  } else {
    await TaskApi.rejectTask(data)
    message.success('审批不通过成功')
  }
  // 2.2 加载最新数据
  getDetail()
}

//审批人弹出框
const taskSelectUserFormRef = ref()

/**
 *打开弹窗
 * @param id  任务id
 * @param index  并行任务索引
 */
const openTaskSelectUserForm = (id: string, auditFormsIndex: number) => {
  taskSelectUserFormRef.value.open(id, auditFormsIndex)
}
/** 抄送 */
// const CCUserList = computed(() => taskSelectUserFormRef.value.CCUserList)
const setCcUserList = ({ CCUserList, auditFormsIndex }) => {
  console.log(CCUserList, auditFormsIndex)

  auditForms.value[auditFormsIndex].CCUserList = CCUserList
  console.log(auditForms.value)
}

/** 转办 */
const handleForward = async () => {
  await TaskApi.updateTaskAssignee(taskSelectUserFormRef.value.formData)
}

/** 委派 */
const handleDelegate = async ({ id, userId, auditFormsIndex }) => {
  console.log(id, userId, auditFormsIndex)
  await TaskApi.delegate({
    id,
    userId,
    delegateReason: auditForms.value[auditFormsIndex].reason
  })
  message.success('委派成功')
  //  加载最新数据
  getDetail()
}

/** 处理审批退回的操作 */
const handleBack = async (task) => {
  message.error('暂不支持【退回】功能！')
  console.log(task)
}

/** 获得详情 */
const getDetail = () => {
  // 1. 获得流程实例相关
  getProcessInstance()
  // 2. 获得流程任务列表（审批记录）
  getTaskList()
}
/** 初始化 */
onMounted(() => {
  getDetail()
})
/** 加载流程实例 */
const getProcessInstance = async () => {
  try {
    processInstanceLoading.value = true
    const data = await ProcessInstanceApi.getProcessInstance(id)
    if (!data) {
      message.error('查询不到流程信息！')
      return
    }
    processInstance.value = data

    // 加载流程图
    bpmnXML.value = await DefinitionApi.getProcessDefinitionBpmnXML(
      data.processDefinition.id as unknown as number
    )
  } finally {
    processInstanceLoading.value = false
  }
}

/** 加载任务列表 */
const getTaskList = async () => {
  try {
    // 获得未取消的任务
    tasksLoad.value = true
    const data = await TaskApi.getTaskListByProcessInstanceId(id)
    tasks.value = []
    // 1.1 移除已取消的审批
    data.forEach((task) => {
      if (task.result !== 4) {
        tasks.value.push(task)
      }
    })
    // 1.2 排序，将未完成的排在前面，已完成的排在后面；
    tasks.value.sort((a, b) => {
      // 有已完成的情况，按照完成时间倒序
      if (a.endTime && b.endTime) {
        return b.endTime - a.endTime
      } else if (a.endTime) {
        return 1
      } else if (b.endTime) {
        return -1
        // 都是未完成，按照创建时间倒序
      } else {
        return b.createTime - a.createTime
      }
    })

    // 获得需要自己审批的任务
    runningTasks.value = []
    auditForms.value = []
    tasks.value.forEach((task) => {
      // 2.1 只有待处理才需要
      if (task.result !== 1) {
        return
      }
      // 2.2 自己不是处理人
      if (!task.assigneeUser || task.assigneeUser.id !== userId) {
        return
      }
      // 2.3 添加到处理任务
      runningTasks.value.push({ ...task })
      auditForms.value.push({
        reason: ''
      })
    })

    // 如果是从已办任务打开，找到对应的任务详情
    if (taskId) {
      doneTask.value = tasks.value.find((task) => task.id === taskId)
    }
  } finally {
    tasksLoad.value = false
  }
}

//判断当前任务是否为委派任务并返回委派任务详情
const getCurrentDelegateTask = (task) => {
  if (!task.delegateList || task.delegateList?.length < 0) return
  // endTime有数据则已经审批过的要忽略
  let target = task.delegateList.find((item) => !item?.endTime)
  return target
}

/**下节点审批人 */
import { bpmTaskAssignRuleType } from '@/utils/constants'

import TaskAssignRuleForm from '@/views/bpm/taskAssignRule/TaskAssignRuleForm.vue'
const taskAssignRuleForm = ref()

/**
 * 打开弹出框
 * @param auditFormsIndex 并行任务索引
 */
const openTaskAssignRuleForm = (auditFormsIndex: number) => {
  taskAssignRuleForm.value.open('', '', auditFormsIndex)
}

const setAssignee = ({ assigneeOptions, assigneeType, auditFormsIndex }) => {
  auditForms.value[auditFormsIndex].assigneeOptions = assigneeOptions
  auditForms.value[auditFormsIndex].assigneeType = assigneeType
}

const deleteAssignData = (val, auditFormsIndex) => {
  auditForms.value[auditFormsIndex].assigneeOptions = auditForms.value[
    auditFormsIndex
  ].assigneeOptions.filter((item) => item.id != val)
  if (!auditForms.value[auditFormsIndex].assigneeOptions.length) {
    auditForms.value[auditFormsIndex].assigneeType = undefined
  }
}
</script>
