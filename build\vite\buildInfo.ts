/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-27 15:19:27
 * @LastEditors: HoJack
 * @LastEditTime: 2023-06-27 15:19:47
 * @Description: 构建信息和版本文件生成
 */
import dayjs from 'dayjs'
import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'
const __APP_INFO__ = {
  buildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  // 提交时间
  GIT_COMMIT_DATE: execSync('git log -1 --format=%cI').toString().trim(),
  // HASH
  GIT_HASH: execSync('git rev-parse HEAD').toString().trim(),
  // 最后提交 message
  GIT_LAST_COMMIT_MESSAGE: execSync('git show -s --format=%s')
    .toString()
    .trim()
    .replace(/\>/g, '➡'),
  // 最近打过的标签
  GIT_TAG: execSync('git describe --tags --abbrev=0').toString().trim()
}
const env = process.env
export const createBuildInfo = () => {
  return {
    __APP_INFO__: JSON.stringify(__APP_INFO__),
    __GLOBAL_ENV__: env // 环境变量
  }
}

/**
 * 生成git版本信息的插件
 *
 * @returns 返回含git版本信息的HTML
 */

/**
 * 生成版本文件
 */
function generateVersionFile() {
  try {
    const versionInfo = {
      buildTime: __APP_INFO__.buildTime,
      gitCommitDate: __APP_INFO__.GIT_COMMIT_DATE,
      gitHash: __APP_INFO__.GIT_HASH,
      gitTag: __APP_INFO__.GIT_TAG,
      lastCommitMessage: __APP_INFO__.GIT_LAST_COMMIT_MESSAGE,
      timestamp: Date.now()
    }

    const distPath = path.join(process.cwd(), 'dist')
    const versionFilePath = path.join(distPath, 'version.json')

    // 确保dist目录存在
    if (!fs.existsSync(distPath)) {
      fs.mkdirSync(distPath, { recursive: true })
    }

    // 写入版本文件
    fs.writeFileSync(versionFilePath, JSON.stringify(versionInfo, null, 2))

    console.log('✅ 版本文件生成成功:', versionFilePath)
    console.log('📦 版本信息:', versionInfo)
  } catch (error) {
    console.error('❌ 生成版本文件失败:', error)

    // 如果出错，创建一个基本的版本文件
    const fallbackVersion = {
      buildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      gitCommitDate: 'unknown',
      gitHash: 'unknown',
      gitTag: 'unknown',
      lastCommitMessage: 'unknown',
      timestamp: Date.now()
    }

    const distPath = path.join(process.cwd(), 'dist')
    const versionFilePath = path.join(distPath, 'version.json')

    if (!fs.existsSync(distPath)) {
      fs.mkdirSync(distPath, { recursive: true })
    }

    fs.writeFileSync(versionFilePath, JSON.stringify(fallbackVersion, null, 2))
    console.log('⚠️  使用备用版本信息:', fallbackVersion)
  }
}

export function gitVersionPlugin() {
  return {
    name: 'git-version-plugin',
    transformIndexHtml(html: string) {
      // 在这里对 HTML 进行处理，生成自定义标签
      const metaTag = `<meta name="gitMessage" content="${JSON.stringify(__APP_INFO__)}">`
      // 将模板中的占位符替换为生成的自定义标签
      const transformedHtml = html.replace('<!--__VERSION_TAG__-->', metaTag)
      return transformedHtml
    },
    writeBundle() {
      // 构建完成后生成版本文件
      generateVersionFile()
    }
  }
}
