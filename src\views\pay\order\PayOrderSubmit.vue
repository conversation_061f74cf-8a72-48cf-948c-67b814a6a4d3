<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-08-18 14:41:03
 * @LastEditors: HoJack
 * @LastEditTime: 2023-10-09 16:48:26
 * @Description: 支持所有支付方式,主要用于调试和展示(不是实际使用页面)
-->
<template>
  <div class="app-container">
    <!-- 支付信息 -->
    <el-card v-loading="loading">
      <el-descriptions :title="t('pay.order.payInfo')" :column="3" border>
        <el-descriptions-item :label="t('pay.order.orderId')">{{
          payOrder.id
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('pay.order.subject')">{{
          payOrder.subject
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('pay.order.body2')">{{
          payOrder.body
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('pay.order.amount')"
          >￥{{ (payOrder.price / 100.0).toFixed(2) }}</el-descriptions-item
        >
        <el-descriptions-item :label="t('common.createTime')">{{
          utils.formatTime(payOrder.createTime, 'yyyy-MM-dd HH:mm:ss')
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('pay.order.expireTime2')">{{
          utils.formatTime(payOrder.expireTime, 'yyyy-MM-dd HH:mm:ss')
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    <div class="mb-4"> {{ t('pay.order.returnUrl') }}: <ElInput v-model="returnUrl" /> </div>

    <el-tabs type="border-card" v-model="appCode" @tab-click="tabClick">
      <el-tab-pane
        v-for="item in appList"
        :key="item.id"
        :label="item.name"
        :name="item.code"
        :value="item.code"
      />
      <!-- 支付选择框 -->

      <!-- 支付宝 -->
      <div v-if="payType === 1">
        <el-descriptions :title="t('pay.order.desc2')" />
        <div
          class="pay-channel-container"
          v-loading="submitLoading"
          :element-loading-text="t('pay.order.desc3')"
        >
          <div
            class="box"
            v-for="channel in aliPayChannels"
            :key="channel.code"
            @click="submit(channel.code)"
          >
            <Icon :icon="`svg-icon:pay-${[channel.code]}`" class="!text-6xl" />
            <div class="title">{{ channel.name }}</div>
          </div>
        </div>
      </div>
      <!-- 微信支付 -->
      <div v-else>
        <el-descriptions :title="t('pay.order.desc4')" style="margin-top: 20px" />
        <div
          class="pay-channel-container"
          v-loading="submitLoading"
          :element-loading-text="t('pay.order.desc3')"
        >
          <div
            class="box"
            v-for="channel in wxPayChannels"
            :key="channel.code"
            @click="submit(channel.code)"
          >
            <Icon :icon="`svg-icon:pay-${[channel.code]}`" class="!text-6xl" />
            <div class="title">{{ channel.name }}</div>
          </div>
        </div>
      </div>
      <!-- 其它支付 -->
      <!-- <el-descriptions title="选择其它支付" style="margin-top: 20px" />
      <div class="pay-channel-container">
        <div
          class="box"
          v-for="channel in otherPayChannels"
          :key="channel.code"
          @click="submit(channel.code)"
        >
          <Icon :icon="`svg-icon:pay-${[channel.code]}`" class="!text-6xl" />
          <div class="title">{{ channel.name }}</div>
        </div>
      </div> -->
    </el-tabs>

    <!-- 展示形式：二维码 URL -->
    <el-dialog
      :title="qrCode.title"
      v-model="qrCode.visible"
      width="350px"
      append-to-body
      :close-on-press-escape="false"
    >
      <!-- <qrcode-vue :value="qrCode.url" size="310" level="L" /> -->
      <Qrcode :text="qrCode.url" :width="310" />
    </el-dialog>

    <!-- 展示形式：IFrame -->
    <el-dialog
      :title="iframe.title"
      v-model:visible="iframe.visible"
      width="800px"
      height="800px"
      append-to-body
      :close-on-press-escape="false"
    >
      <iframe :src="iframe.url" width="100%"></iframe>
    </el-dialog>

    <!-- 展示形式：Form -->
    <div ref="formRef" v-html="form.value"></div>

    <!-- 展示形式：BarCode 条形码 -->
    <el-dialog
      :title="barCode.title"
      v-model="barCode.visible"
      width="500px"
      append-to-body
      :close-on-press-escape="false"
    >
      <el-form ref="form" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('pay.order.name')" prop="name">
              <el-input
                v-model="barCode.value"
                :placeholder="t('pay.order.valuePlaceholder')"
                required
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div style="text-align: right">
              {{ t('pay.order.desc5') }}
              <el-link
                type="danger"
                target="_blank"
                href="https://baike.baidu.com/item/条码支付/10711903"
                >{{ t('pay.order.desc6') }}</el-link
              >
              {{ t('pay.order.desc7') }}
            </div>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="submit0(barCode.channelCode)"
            :disabled="barCode.value.length === 0"
            >{{ t('pay.order.paySure') }}</el-button
          >
          <el-button @click="barCode.visible = false">{{ t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'PayOrderSubmit'
})

import * as utils from '@/utils/index'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { getOrder, submitOrder } from '@/api/pay/order'
import { PayChannelEnum, PayDisplayModeEnum, PayOrderStatusEnum } from '@/utils/constants'
import { useRoute, useRouter } from 'vue-router'
const { t } = useI18n() // 国际化
const route = useRoute()
const router = useRouter()

const message = useMessage()
let loading = ref(false) // 支付信息的 loading

let form = ref({
  // 展示形式：form
  html: ''
})

let payOrderId = ref() // 支付订单id

onMounted(() => {
  payOrderId.value = route.query.payOrderId
  getDetail()
  initPayChannels()
})

/** 初始化支付渠道 */

type channelType = {
  name: string
  code: string | number | boolean
}
let aliPayChannels = ref<channelType[]>([]) // 阿里支付的渠道
let wxPayChannels = ref<channelType[]>([]) // 微信支付的渠道
let otherPayChannels = ref<channelType[]>([]) // 其它的支付渠道

const initPayChannels = () => {
  // 微信支付
  console.log(getDictOptions(DICT_TYPE.PAY_CHANNEL_CODE_TYPE))

  for (const dict of getDictOptions(DICT_TYPE.PAY_CHANNEL_CODE_TYPE)) {
    const payChannel = {
      name: dict.label,
      code: dict.value
    }

    let dictValue = dict.value as string

    if (dictValue.indexOf('wx_') === 0) {
      wxPayChannels.value.push(payChannel)
    } else if (dictValue.indexOf('alipay_') === 0) {
      aliPayChannels.value.push(payChannel)
    } else {
      otherPayChannels.value.push(payChannel)
    }
  }
}

/** 获得支付信息 */
let payOrder = ref({}) as any // 支付信息

const getDetail = () => {
  console.log('订单id' + payOrderId.value)

  // 1.1 未传递订单编号
  if (!payOrderId.value) {
    message.error(t('pay.order.desc8'))
    goBackToList()
    return
  }
  getOrder(payOrderId.value).then((response) => {
    // 1.2 无法查询到支付信息
    if (!response) {
      message.error(t('pay.order.desc9'))
      goBackToList()
      return
    }
    // 1.3 订单已支付
    if (response.status !== PayOrderStatusEnum.WAITING.status) {
      message.error(t('pay.order.desc10'))
      goBackToList()
      return
    }

    // 2. 可以展示
    payOrder.value = response
  })
}

/** 提交支付 */
let submitLoading = ref(false) // 提交支付的 loading

let barCode = ref({
  // 展示形式：条形码
  channelCode: '',
  value: '',
  title: '',
  visible: false
})
const submit = (channelCode) => {
  // 条形码支付，需要特殊处理
  if (channelCode === PayChannelEnum.ALIPAY_BAR.code) {
    barCode.value = {
      channelCode: channelCode,
      value: '',
      title: t('pay.order.desc11'),
      visible: true
    }
    return
  }

  // 默认的提交处理
  submit0(channelCode)
}

let returnUrl = ref(window.location.origin + '/#/pay/demo-order')
const submit0 = (channelCode) => {
  submitLoading.value = true

  submitOrder({
    id: payOrderId.value,
    returnUrl: returnUrl.value,
    channelCode: channelCode,
    appCode: appCode.value, //应用编码 等于商户id
    ...buildSubmitParam(channelCode)
  })
    .then((response) => {
      const data = response
      console.log(PayDisplayModeEnum.URL.mode)
      console.log(data.displayMode)

      if (data.displayMode === PayDisplayModeEnum.IFRAME.mode) {
        displayIFrame(channelCode, data)
      } else if (data.displayMode === PayDisplayModeEnum.URL.mode) {
        displayUrl(channelCode, data)
      } else if (data.displayMode === PayDisplayModeEnum.FORM.mode) {
        displayForm(channelCode, data)
      } else if (data.displayMode === PayDisplayModeEnum.QR_CODE.mode) {
        displayQrCode(channelCode, data)
      } else {
        message.info(t('pay.order.desc12') + data.displayMode)
        submitLoading.value = false

        return
      }

      // 打开轮询任务
      // createQueryInterval()
    })
    .catch(() => {
      submitLoading.value = false
    })
}
/** 构建提交支付的额外参数 */
const buildSubmitParam = (channelCode) => {
  // ① 支付宝 PC 支付时，有多种展示形态
  if (channelCode === PayChannelEnum.ALIPAY_PC.code) {
    // 情况【前置模式】：将二维码前置到商户的订单确认页的模式。需要商户在自己的页面中以 iframe 方式请求支付宝页面。具体支持的枚举值有以下几种：
    // 0：订单码-简约前置模式，对应 iframe 宽度不能小于 600px，高度不能小于 300px
    // return {
    //   "channelExtras": {
    //     "qr_pay_mode": "0"
    //   }
    // }
    // 1：订单码-前置模式，对应iframe 宽度不能小于 300px，高度不能小于 600px
    // return {
    //   "channelExtras": {
    //     "qr_pay_mode": "1"
    //   }
    // }
    // 3：订单码-迷你前置模式，对应 iframe 宽度不能小于 75px，高度不能小于 75px
    // return {
    //   "channelExtras": {
    //     "qr_pay_mode": "3"
    //   }
    // }
    // 4：订单码-可定义宽度的嵌入式二维码，商户可根据需要设定二维码的大小
    // return {
    //   "channelExtras": {
    //     "qr_pay_mode": "4"
    //   }
    // }
    // 情况【跳转模式】：跳转模式下，用户的扫码界面是由支付宝生成的，不在商户的域名下。支持传入的枚举值有
    return {
      channelExtras: {
        qr_pay_mode: '2'
      }
    }
    // 情况【表单模式】：直接提交当前页面到支付宝
    // return {
    //   displayMode: PayDisplayModeEnum.FORM.mode
    // }
  }

  // ② 支付宝 Wap 支付时，引导手机扫码支付
  if (channelCode === PayChannelEnum.ALIPAY_WAP.code) {
    return {
      displayMode: PayDisplayModeEnum.QR_CODE.mode
    }
  }

  // ③ 支付宝 BarCode 支付时，需要传递 authCode 条形码
  if (channelCode === PayChannelEnum.ALIPAY_BAR.code) {
    return {
      channelExtras: {
        auth_code: barCode.value.value
      }
    }
  }
  //  微信支付  channelExtras传入productId 即支付订单编号id
  if (channelCode === PayChannelEnum.WX_H5.code || channelCode === PayChannelEnum.WX_NATIVE.code) {
    return {
      channelExtras: {
        productId: payOrder.value.id
      }
    }
  }
  return {}
}

/** 提交支付后，IFrame 内置 URL 的展示形式 */
let iframe = ref({
  // 展示形式：iframe
  url: '',
  title: '',
  visible: false
})

const displayIFrame = (channelCode, data) => {
  // TODO 芋艿：目前有点奇怪，支付宝总是会显示“支付环境存在风险”
  iframe.value = {
    title: t('pay.order.payWin'),
    url: data.displayContent,
    visible: true
  }
  submitLoading.value = false
}
/** 提交支付后，URL 的展示形式 */
const displayUrl = (channelCode, data) => {
  console.log(data)

  window.open(data.displayContent)
  submitLoading.value = false
}
/** 提交支付后，Form 的展示形式 */
const displayForm = (channelCode, data) => {
  // 渲染支付页面
  form.value = {
    value: data.displayContent
  }
  // 防抖避免重复支付
  nextTick(() => {
    // 提交支付表单
    this.$refs.formRef.children[0].submit()
    setTimeout(() => {
      submitLoading.value = false
    }, 1000)
  })
}

/** 提交支付后（支付宝扫码支付） */
let qrCode = ref({
  // 展示形式：二维码
  url: '',
  title: '',
  visible: false
})

const displayQrCode = (channelCode, data) => {
  let title = t('pay.order.desc13')
  if (channelCode === PayChannelEnum.ALIPAY_WAP.code) {
    // 考虑到 WAP 测试，所以引导手机浏览器搞
  } else if (channelCode.indexOf('alipay_') === 0) {
    title = t('pay.order.desc14')
  } else if (channelCode.indexOf('wx_') === 0) {
    title = t('pay.order.desc15')
  }
  qrCode.value = {
    title: title,
    url: data.displayContent,
    visible: true
  }
  submitLoading.value = false
}
/** 轮询查询任务 */
let interval = ref() // 定时任务，轮询是否完成支付

const createQueryInterval = () => {
  if (interval.value) {
    return
  }
  interval.value = setInterval(() => {
    getOrder(payOrderId.value).then((response) => {
      // 已支付
      if (response.status === PayOrderStatusEnum.SUCCESS.status) {
        clearQueryInterval()
        message.success(t('pay.order.desc16'))
        goBackToList()
      }
      // 已取消
      if (response.status === PayOrderStatusEnum.CLOSED.status) {
        clearQueryInterval()
        message.error(t('pay.order.desc17'))
        goBackToList()
      }
    })
  }, 1000 * 2)
}
/** 清空查询任务 */
const clearQueryInterval = () => {
  // 清空各种弹窗
  qrCode.value = {
    title: '',
    url: '',
    visible: false
  }
  // 清空任务
  clearInterval(interval.value)
  interval.value = undefined
}
//避免内存溢出,清空查询任务
onBeforeRouteLeave(() => {
  clearQueryInterval()
})

/** 回到列表 **/
import { useTagsViewStore } from '@/store/modules/tagsView'
const tagsViewStore = useTagsViewStore()
const goBackToList = () => {
  tagsViewStore.delCachedView()
  router.go(-1)
}

//获取应用信息
let appCode = ref<any>()

//支付类型: 支付宝0  微信1
let payType = ref<number>(0)
import * as AppApi from '@/api/pay/app'
let appList = ref<any>([])
const getAppList = async () => {
  let res = await AppApi.getAppPage({ pageNo: 1, pageSize: 100 })
  appList.value = res.list

  //如果list不为空,为appCode设置默认值
  if (appList.value.length > 0) {
    appCode.value = appList.value[0].code
    payType.value = appList.value[0].type
  }
}
onMounted(() => {
  getAppList()
})

const tabClick = ({ props }) => {
  console.log(props)
  appList.value.find((item) => {
    if (item.code === props.name) {
      payType.value = item.type
    }
  })
}
</script>
<style lang="scss" scoped>
.pay-channel-container {
  display: flex;
  margin-top: -10px;
  .box {
    width: 130px;
    border: 1px solid #e6ebf5;
    cursor: pointer;
    text-align: center;
    padding-top: 10px;
    padding-bottom: 5px;
    margin-right: 10px;
    img {
      width: 40px;
      height: 40px;
    }
    .title {
      padding-top: 5px;
    }
  }
}
</style>
