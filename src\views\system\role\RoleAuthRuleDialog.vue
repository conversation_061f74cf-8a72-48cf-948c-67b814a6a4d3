<!-- 授权规则弹窗 -->
<template>
  <el-dialog
    :title="t('system.role.autoAuthRule')"
    v-model="dialogVisible"
    width="500px"
    append-to-body
    destroy-on-close
  >
    <el-form ref="formRef" v-loading="formLoading" :model="formData" label-width="140px">
      <el-form-item :label="t('system.role.name')">
        <el-tag>{{ formData.name }}</el-tag>
      </el-form-item>
      <el-form-item :label="t('system.role.code')">
        <el-tag>{{ formData.code }}</el-tag>
      </el-form-item>
      <el-form-item :label="t('system.role.autoAuthRule')" prop="autoAuthRuleIds">
        <el-select v-model="formData.autoAuthRuleIds" multiple class="w-full">
          <el-option
            v-for="(item, index) in rulesList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
      <el-button type="primary" @click="submitForm" :disabled="formLoading">
        {{ t('common.submit') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import * as RoleApi from '@/api/system/role'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const emit = defineEmits(['success']) // 定义emit

const dialogVisible = ref(false) // 弹出层是否显示
const formLoading = ref(false) // 表单的加载中

const formData = reactive({
  id: 0,
  name: '',
  code: '',
  autoAuthRuleIds: [] as string[]
})

const rulesList = ref<any[]>([])

// 获取授权规则列表
const getRules = async () => {
  try {
    const rules = await RoleApi.getResourceAutomaticAuthorizationRule()
    rulesList.value = rules.map((rule) => ({
      ...rule,
      id: String(rule.id)
    }))
  } catch (error) {
    console.error('获取授权规则列表失败', error)
  }
}

// 打开弹窗
const open = async (row?: RoleApi.RoleVO) => {
  dialogVisible.value = true
  formLoading.value = true

  try {
    await getRules()

    // 设置表单数据
    if (row) {
      formData.id = row.id
      formData.name = row.name
      formData.code = row.code
      // 修复类型错误，将split改为处理字符串或数组
      if (row.autoAuthRuleIds) {
        formData.autoAuthRuleIds =
          typeof row.autoAuthRuleIds === 'string'
            ? row.autoAuthRuleIds.split(',')
            : row.autoAuthRuleIds.map((id) => String(id))
      } else {
        formData.autoAuthRuleIds = []
      }
    }
  } catch (error) {
    console.error('打开授权规则弹窗失败', error)
  } finally {
    formLoading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  //   if (formData.autoAuthRuleIds.length === 0) {
  //     ElMessage.warning(t('system.role.ruleRequired'))
  //     return
  //   }

  try {
    formLoading.value = true

    await RoleApi.assignResourceAuthorizationRule({
      roleCode: formData.code,
      autoAuthRuleIds: formData.autoAuthRuleIds
    })

    ElMessage.success(t('common.handleSuccess'))
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交授权规则失败', error)
  } finally {
    formLoading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>
