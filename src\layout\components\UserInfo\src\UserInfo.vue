<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 13:59:22
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-16 09:37:53
 * @Description: 
-->
<script setup lang="ts">
defineOptions({
  name: 'UserInfo'
})

import { getTenantId } from '@/utils/auth'
import { checkPermi } from '@/utils/permission' // 权限判断函数

import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { useDesign } from '@/hooks/web/useDesign'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'

import { useTagsViewStore } from '@/store/modules/tagsView'

const { t } = useI18n()

const { wsCache } = useCache()

const { push } = useRouter()

const userStore = useUserStore()
const appStore = useAppStore()

const tagsViewStore = useTagsViewStore()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('user-info')

const user = wsCache.get(CACHE_KEY.USER)

const userName = user.user.nickname ? user.user.nickname : 'Admin'

const message = useMessage()

const toProfile = async () => {
  if (agentOperationStore.getCurrentCustomer.customerName) {
    message.error('代客操作模式下不支持查看个人相关信息')
    return
  } else {
    push('/user/profile')
  }
}
const showSettingTool = () => {
  appStore.setShowSettingTool(true)
}

//代客操作
import { useAgentOperationStore } from '@/store/modules/agentOperation'
const agentOperationStore = useAgentOperationStore()
</script>

<template>
  <ElDropdown :class="prefixCls" trigger="click">
    <div class="flex items-center">
      <img
        v-if="user.user.avatar"
        :src="user.user.avatar"
        alt=""
        class="w-[calc(var(--logo-height)-25px)] rounded-[50%]"
      />
      <img
        v-else
        src="@/assets/imgs/avatar.gif"
        alt=""
        class="w-[calc(var(--logo-height)-25px)] rounded-[50%]"
      />

      <span class="<lg:hidden text-14px pl-[5px] text-[var(--top-header-text-color)]">
        {{ userName }}
      </span>
    </div>
    <template #dropdown>
      <ElDropdownMenu>
        <ElDropdownItem
          v-if="getTenantId() == 'goldpac.com' && checkPermi(['system:oauth2-token:page'])"
        >
          <icon-ep-tools />
          <div
            @click="
              () => {
                agentOperationStore.openAgentOperationMode()
                agentOperationStore.getTokenList({ pageNo: 1, pageSize: 10 })
              }
            "
          >
            代客操作
          </div>
        </ElDropdownItem>
        <ElDropdownItem>
          <icon-ep-tools />
          <div @click="toProfile">{{ t('common.profile') }}</div>
        </ElDropdownItem>
        <ElDropdownItem>
          <icon-ep-menu icon="ep:menu" />
          <div @click="showSettingTool">{{ t('common.setting') }}</div>
        </ElDropdownItem>
        <ElDropdownItem divided @click="userStore.loginOut()">
          <icon-ep-switch-button icon="ep:switch-button" />
          <div>{{ t('common.loginOut') }}</div>
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>
</template>
