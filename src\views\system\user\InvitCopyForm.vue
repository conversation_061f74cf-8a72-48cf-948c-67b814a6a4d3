<template>
  <Dialog v-model="dialogVisible" :title="t('sys.user.loginVer')" width="800" @close="close">
    <div class="mt-16px mb-16px ml-16px mr-16px">
      <el-form>
        <el-form-item :label="t('sys.user.linkValidTerm')"
          >{{ formData.effectiveMillisTime / 60 }}{{ t('sys.user.min') }}</el-form-item
        >
      </el-form>
      <el-form>
        <el-form-item :label="t('sys.user.phoneLink')"
          >{{ formData.phoneUrl }}
          <el-button @click="copyInvit('phoneUrl')" link class="ml-16px">{{
            t('sys.user.copy')
          }}</el-button></el-form-item
        >
      </el-form>
      <el-form>
        <el-form-item :label="t('sys.user.emailLink')"
          >{{ formData.mailUrl }}
          <el-button @click="copyInvit('mailUrl')" link class="ml-16px">{{
            t('sys.user.copy')
          }}</el-button></el-form-item
        >
      </el-form>
    </div>

    <template #footer>
      <el-button type="primary" @click="copyAll()">{{ t('sys.user.copyAll') }}</el-button>
      <el-button @click="close">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'InvitCopyForm'
})

import { useClipboard } from '@vueuse/core'
const { t } = useI18n() // 国际化

const formData = ref({
  effectiveMillisTime: 0,
  phoneUrl: '',
  mailUrl: ''
})

const close = () => {
  formData.value = {
    effectiveMillisTime: 0,
    phoneUrl: '',
    mailUrl: ''
  }
  dialogVisible.value = false
}

const open = (phoneData, mailData) => {
  formData.value.phoneUrl = phoneData.link
  formData.value.effectiveMillisTime = phoneData.effectiveMillisTime / 1000
  formData.value.mailUrl = mailData.link
  dialogVisible.value = true
}

const copyInvit = async (key) => {
  const { copy, copied, isSupported } = useClipboard({ source: formData.value[key] })
  if (!isSupported) {
    ElMessage.error(t('common.copyError'))
  } else {
    await copy()
    if (unref(copied)) {
      ElMessage.success(t('common.copySuccess'))
    }
  }
}

const copyAll = async () => {
  const { copy, copied, isSupported } = useClipboard({
    source: `${t('sys.user.linkValidTerm')}${formData.value.effectiveMillisTime / 60}${t(
      'sys.user.min'
    )}\n${t('sys.user.phoneLink')}${formData.value.phoneUrl}\n${t('sys.user.emailLink')}${
      formData.value.mailUrl
    }`
  })
  if (!isSupported) {
    ElMessage.error(t('common.copyError'))
  } else {
    await copy()
    if (unref(copied)) {
      ElMessage.success(t('common.copySuccess'))
    }
  }
}

defineExpose({
  open
})

const dialogVisible = ref(false)
</script>
