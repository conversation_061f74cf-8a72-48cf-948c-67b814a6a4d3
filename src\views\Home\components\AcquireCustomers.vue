<template>
  <!-- 获客引流 -->
  <div class="home__acquire-customers">
    <div class="header_title">
      <div class="title-text">{{ t('home.AcquireCustomers.title') }}</div>
    </div>
    <div :class="ifEn ? 'content_flow_en_US' : 'content_flow'"></div>
  </div>
</template>

<script setup lang="ts">
const { t, ifEn } = useI18n()
const basePath = import.meta.env.BASE_URL
</script>

<style lang="scss" scoped>
.home__acquire-customers {
  display: flex;
  align-items: center;
  flex-direction: column;
  min-height: 1279px;
  background: url('/Home/AcquireCustomers/AcquireCustomers_bg.webp') center/100% 100% no-repeat;
}

.header_title {
  position: relative;
  margin-top: 146px;
  height: 33px;
  background: url('/Home/AcquireCustomers/AcquireCustomers-resource sharing.png') center/100% 100%
    no-repeat;
  cursor: default;
  user-select: none;

  .title-text {
    font-family: PingFang SC;
    font-size: 40px;
    color: #000000;
    line-height: 60px;
  }
}

.content_flow {
  margin-top: 64px;
  width: 1411px;
  height: 876px;
  background: url('/Home/AcquireCustomers/AcquireCustomers-flowbg.webp') center/100% 100% no-repeat;
}
.content_flow_en_US {
  margin-top: 64px;
  width: 1411px;
  height: 876px;
  background: url('/Home/AcquireCustomers/en_US/AcquireCustomers-flowbg.webp') center/100% 100%
    no-repeat;
}

@media (max-width: 1366px) {
  .home__acquire-customers {
    min-height: 910px;
  }

  .header_title {
    margin-top: 104px;
    height: 23px;

    .title-text {
      font-size: 28px;
      color: #000000;
      line-height: 43px;
    }
  }

  .content_flow {
    margin-top: 46px;
    width: 1006px;
    height: 623px;
  }
  .content_flow_en_US {
    margin-top: 46px;
    width: 1006px;
    height: 623px;
  }
}
</style>
