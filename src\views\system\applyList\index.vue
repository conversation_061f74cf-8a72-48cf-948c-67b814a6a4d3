<!-- 应用发布管理 -->
<template>
  <!-- 列表 -->
  <UmvContent>
    <template #search>
      <UmvQuery
        labelWidth="100px"
        v-model="queryParams"
        :opts="queryOpts"
        :loading="loading"
        @check="handleQuery"
        @reset="resetQuery"
      />
    </template>
    <UmvTable v-loading="loading" :data="list" :columns="tableColumns" @refresh="getList">
      <template #tools>
        <el-button type="primary" plain @click="openForm('create')">
          <icon-ep-plus style="font-size: 12px" class="mr-5px" />
          新增
        </el-button>
        <span
          class="ml-5 text-gold"
          v-if="envController.getEnvironment() == 'uat' || envController.getEnvironment() == 'sit'"
        >
          修改菜单需要联系管理员,请不要随意修改,以免造成系统异常
        </span>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单弹窗：添加/修改 -->
    <ApplyForm ref="formRef" @success="getList" />
  </UmvContent>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'SystemApplyList'
})

import envController from '@/controller/envController'
import { dateFormatter } from '@/utils/formatTime'
import * as TenantPackageApi from '@/api/system/tenantPackage'
import { getTenantAppList } from '@/api/system/apply'
import ApplyForm from './applyForm.vue'
import { updateApplicationCreate } from '@/api/system/apply'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import type { DateModelType } from 'element-plus'
import { ElSwitch, ElButton, ElInput } from 'element-plus'
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import UmvContent from '@/components/UmvContent'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由
const route = useRoute()

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<any[]>([]) // 列表的数据

interface QueryParamsType {
  pageNo: number
  pageSize: number
  name: string | null
  status: string | null
  remark: string | null
  createTime: [DateModelType?, DateModelType?]
}

const queryParams = ref<QueryParamsType>({
  pageNo: 1,
  pageSize: 10,
  name: null,
  status: null,
  remark: null,
  createTime: [] as unknown as [DateModelType?, DateModelType?]
}) as any // 临时使用any解决类型问题
const queryFormRef = ref() // 搜索的表单

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '服务名',
    defaultVal: '',
    controlRender: (form) => <el-input v-model={form.name} placeholder="请输入服务名" clearable />
  },
  createTime: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form) => (
      <el-date-picker
        v-model={form.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    )
  }
})

// 当前用户是否是管理员
const ifAdmin = computed(() => {
  return route.query.ifAdmin
})

// 表格列定义
const tableColumns = ref<TableColumn[]>([
  { prop: 'name', label: '服务名', align: 'center' },
  {
    prop: 'sort',
    label: '排序',
    align: 'center',
    width: '120px',
    renderTemplate: (scope) => (
      <ElInput
        style="text-align: center"
        type="number"
        v-model={scope.row.sort}
        onBlur={() => changeSort(scope.row)}
        min={0}
      />
    )
  },
  { prop: 'ver', label: '版本号', align: 'center' },
  { prop: 'code', label: '服务编号', align: 'center' },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    renderTemplate: (scope) => {
      return <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    }
  },
  {
    prop: 'status',
    label: '是否发布',
    align: 'center',
    renderTemplate: (scope) => (
      <ElSwitch
        modelValue={scope.row.status === 0}
        disabled={scope.row.status === 0 && !ifAdmin.value}
        onClick={() => chagePubilsh(scope.row)}
      />
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        {scope.row.status === 1 ? (
          <ElButton link type="primary" onClick={() => openForm('update', scope.row)}>
            修改
          </ElButton>
        ) : (
          <ElButton link type="primary" onClick={() => openForm('preview', scope.row)}>
            查看
          </ElButton>
        )}
        <ElButton link type="primary" onClick={() => toDetial(scope.row.code)}>
          详情
        </ElButton>
      </div>
    )
  }
])

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getTenantAppList(queryParams.value)
    // 修改以适应API返回格式
    list.value = Array.isArray(data) ? data : (data as any).list || []
    list.value = list.value.map((el: any) => {
      el.originSort = el.sort
      return el
    })
    total.value = Array.isArray(data) ? list.value.length : (data as any).total || 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = (form: any) => {
  // 将UmvQuery表单数据合并到查询参数
  // Object.assign(queryParams, form)
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, row?) => {
  formRef.value.open(type, row)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TenantPackageApi.deleteTenantPackage(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

// 打开详情页
const toDetial = (code: string) => {
  push({
    name: 'SystemApplyVersion',
    query: {
      code: code
    }
  })
}

// 点击发布按钮
const chagePubilsh = async (row) => {
  if (row.status !== 0 || ifAdmin.value) {
    loading.value = true
    await updateApplicationCreate({
      ...row,
      status: row.status !== 0 ? 0 : 1
    })
    getList()
  }
}

// 更改排序
const changeSort = async (_row) => {
  if (!Number.isNaN(parseInt(_row.sort))) {
    if (_row.originSort == parseInt(_row.sort)) {
      return
    } else {
      try {
        loading.value = true
        await TenantPackageApi.updateSort({
          id: _row.id,
          sort: parseInt(_row.sort)
        })
        message.success('更新成功')
        _row.originSort = parseInt(_row.sort)
      } catch (_e) {
        _row.sort = _row.originSort
      } finally {
        loading.value = false
      }
    }
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
