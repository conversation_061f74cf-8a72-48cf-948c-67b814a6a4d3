<template>
  <el-dialog
    v-model="dialogVisible"
    :title="formType === 'create' ? '创建徽商银行支付渠道' : '编辑徽商银行支付渠道'"
    @closed="close"
    append-to-body
    destroy-on-close
    scroll
    width="800px"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="250px"
      v-loading="formLoading"
    >
      <el-form-item :label="'渠道类型'" prop="code">
        <el-select v-model="formData.code" :disabled="formType !== 'create'">
          <el-option
            v-for="(item, index) in codeMap"
            :label="item.name"
            :value="item.code"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('pay.app.feeRate')" prop="feeRate">
        <el-input
          v-model="formData.feeRate"
          :placeholder="t('pay.app.feeRatePlaceholder')"
          clearable
          type="number"
          :style="{ width: '100%' }"
        >
          <template #append>%</template>
        </el-input>
      </el-form-item>
      <!-- <el-form-item  :label="t('pay.app.zfbConfig.appId')" prop="config.appId">
          <el-input
            v-model="formData.config.appId"
            :placeholder="t('pay.app.zfbConfig.appIdPlaceholder')"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item> -->

      <el-form-item :label="t('pay.app.config.status')" prop="status">
        <el-radio-group v-model="formData.status" size="default">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="'开放银行APPID(APP_KEY)'" prop="config.appKey">
        <el-input
          v-model="formData.config.appKey"
          :placeholder="'请输入开放银行APPID(APP_KEY)'"
          clearable
          :style="{ width: '100%' }"
        />
      </el-form-item>
      <el-form-item label="商户号(merNo)" prop="config.mchId">
        <el-input
          v-model="formData.config.mchId"
          :style="{ width: '100%' }"
          :placeholder="'请输入商户号(merNo)'"
        />
      </el-form-item>
      <el-form-item label="微信appId" prop="config.wxAppId">
        <el-input
          v-model="formData.config.wxAppId"
          :style="{ width: '100%' }"
          :placeholder="'请输入微信appId'"
        />
      </el-form-item>
      <el-form-item :label="'商户公钥证书(APP_CERT)'" prop="config.appCert">
        <el-input
          v-model="formData.config.appCert"
          type="textarea"
          :placeholder="'请输入商户公钥证书(APP_CERT)'"
          :autosize="{ minRows: 8, maxRows: 8 }"
          :style="{ width: '100%' }"
        />
      </el-form-item>
      <el-form-item label="">
        <el-upload
          action=""
          ref="privateKeyContentFile"
          :limit="1"
          :accept="fileAccept"
          :http-request="appCertUpload"
          :before-upload="fileBeforeUpload"
        >
          <el-button
            size="small"
            type="primary"
            icon="el-icon-upload"
            @click="changeFileAccept('.cer')"
            >{{ t('pay.app.clickUpload') }}</el-button
          >
        </el-upload>
      </el-form-item>

      <el-form-item
        :label="'开放银行公网地址(PUBLIC_URL)'"
        prop="config.publicUrl"
        :autosize="{ minRows: 8, maxRows: 8 }"
      >
        <el-input
          v-model="formData.config.publicUrl"
          :style="{ width: '100%' }"
          :placeholder="'请输入开放银行公网地址(PUBLIC_URL)'"
        />
      </el-form-item>

      <el-form-item :label="'开放银行公钥证书(opencert.cer)'" prop="config.hsbPubliceKeyContent">
        <el-input
          v-model="formData.config.hsbPubliceKeyContent"
          type="textarea"
          :placeholder="'请上传开放银行公钥证书(opencert.cer)'"
          :autosize="{ minRows: 8, maxRows: 8 }"
          :style="{ width: '100%' }"
        />
      </el-form-item>
      <el-form-item label="">
        <el-upload
          action=""
          ref="privateKeyContentFile"
          :limit="1"
          :accept="fileAccept"
          :http-request="hsbPubliceKeyUpload"
          :before-upload="fileBeforeUpload"
        >
          <el-button
            size="small"
            type="primary"
            icon="el-icon-upload"
            @click="changeFileAccept('.cer')"
            >{{ t('pay.app.clickUpload') }}</el-button
          >
        </el-upload>
      </el-form-item>
      <el-form-item :label="'商户私钥证书(merchant.pem)'" prop="config.mchPrivateKeyContent">
        <el-input
          v-model="formData.config.mchPrivateKeyContent"
          type="textarea"
          :placeholder="'请上传商户私钥证书(merchant.pem)'"
          :autosize="{ minRows: 8, maxRows: 8 }"
          :style="{ width: '100%' }"
        />
      </el-form-item>
      <el-form-item label="">
        <el-upload
          action=""
          ref="privateKeyContentFile"
          :limit="1"
          :accept="fileAccept"
          :http-request="mchPrivateKeyUpload"
          :before-upload="fileBeforeUpload"
        >
          <el-button
            size="small"
            type="primary"
            icon="el-icon-upload"
            @click="changeFileAccept('.pem')"
            >{{ t('pay.app.clickUpload') }}</el-button
          >
        </el-upload>
      </el-form-item>
      <el-form-item
        :label="'开放银行h5公钥证书(openhtml.cer)'"
        prop="config.hsbH5PubliceKeyContent"
      >
        <el-input
          v-model="formData.config.hsbH5PubliceKeyContent"
          type="textarea"
          :placeholder="'请上传开放银行h5公钥证书(openhtml.cer)'"
          :autosize="{ minRows: 8, maxRows: 8 }"
          :style="{ width: '100%' }"
        />
      </el-form-item>
      <el-form-item label="">
        <el-upload
          action=""
          ref="privateKeyContentFile"
          :limit="1"
          :accept="fileAccept"
          :http-request="hsbH5PubliceKeyUpload"
          :before-upload="fileBeforeUpload"
        >
          <el-button
            size="small"
            type="primary"
            icon="el-icon-upload"
            @click="changeFileAccept('.cer')"
            >{{ t('pay.app.clickUpload') }}</el-button
          >
        </el-upload>
      </el-form-item>

      <el-form-item
        :label="'OutPrivateKey'"
        prop="config.outPrivateKey"
        :autosize="{ minRows: 8, maxRows: 8 }"
      >
        <el-input
          v-model="formData.config.outPrivateKey"
          :style="{ width: '100%' }"
          :placeholder="'请输入OutPrivateKey'"
        />
      </el-form-item>

      <el-form-item :label="'ca证书'" prop="config.caContent">
        <el-input
          v-model="formData.config.caContent"
          type="textarea"
          :placeholder="'请上传ca证书'"
          :autosize="{ minRows: 8, maxRows: 8 }"
          :style="{ width: '100%' }"
        />
      </el-form-item>
      <el-form-item label="">
        <el-upload
          action=""
          ref="privateKeyContentFile"
          :limit="1"
          :http-request="caContentUpload"
          :before-upload="fileBeforeUpload"
        >
          <el-button
            size="small"
            type="primary"
            icon="el-icon-upload"
            @click="changeFileAccept(undefined)"
            >{{ t('pay.app.clickUpload') }}</el-button
          >
        </el-upload>
      </el-form-item>

      <el-form-item :label="'key密码'" prop="config.keyPwd">
        <el-input
          v-model="formData.config.keyPwd"
          :style="{ width: '100%' }"
          :placeholder="'请输入key密码'"
        />
      </el-form-item>
      <el-form-item :label="'类型'" prop="config.type">
        <el-input
          v-model="formData.config.type"
          :style="{ width: '100%' }"
          :placeholder="'请输入类型'"
        />
      </el-form-item>
      <el-form-item
        :label="'key内容'"
        prop="config.keyContent"
        :autosize="{ minRows: 8, maxRows: 8 }"
      >
        <el-input
          v-model="formData.config.keyContent"
          :style="{ width: '100%' }"
          :placeholder="'请输入key内容'"
        />
      </el-form-item>
      <el-form-item :label="'文件路径'" prop="config.fileUrl">
        <el-input
          v-model="formData.config.fileUrl"
          :style="{ width: '100%' }"
          :placeholder="'请输入文件路径'"
        />
      </el-form-item>
      <el-form-item :label="'rsa私钥'" prop="config.rsaPrivateKey">
        <el-input
          v-model="formData.config.rsaPrivateKey"
          :style="{ width: '100%' }"
          :placeholder="'请输入rsa私钥'"
        />
      </el-form-item>
      <el-form-item :label="'平台ID'" prop="config.platFormId">
        <el-input
          v-model="formData.config.platFormId"
          :style="{ width: '100%' }"
          :placeholder="'请输入平台ID'"
        />
      </el-form-item>
    </el-form>
    <div class="flex justify-end">
      <el-button @click="close">{{ t('common.cancel') }}</el-button>
      <el-button type="primary" @click="submitForm">{{ t('common.ok') }}</el-button>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'HsChannelForm'
})

const { t } = useI18n() // 国际化

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import * as ChannelApi from '@/api/pay/channel'
import type { FormInstance } from 'element-plus'

const message = useMessage() // 消息弹窗
let formRef = ref<FormInstance>()

let dialogVisible = ref<boolean>(false)
let formLoading = ref<boolean>(false)

//提交表单
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

let formData = ref<any>({
  id: '',
  appId: '',
  merchantId: '', //商户id
  code: '',
  status: 0,
  feeRate: undefined,
  remark: '',
  config: {
    mchId: undefined,
    wxAppId: undefined,
    appKey: undefined,
    appCert: undefined,
    publicUrl: undefined,
    hsbPubliceKeyContent: undefined,
    mchPrivateKeyContent: undefined,
    hsbH5PubliceKeyContent: undefined,
    outPrivateKey: undefined,
    caContent: undefined,
    keyPwd: undefined,
    type: undefined,
    keyContent: undefined,
    fileUrl: undefined,
    rsaPrivateKey: undefined,
    platFormId: undefined
  }
})

let rules = reactive({
  code: [{ required: true, message: '请选择渠道类型', trigger: 'blur' }],
  feeRate: [{ required: true, message: t('pay.app.rzfb1'), trigger: 'blur' }],
  status: [{ required: true, message: t('pay.app.rzfb2'), trigger: 'blur' }],
  'config.appId': [{ required: true, message: t('pay.app.rzfb3'), trigger: 'blur' }],
  'config.mchId': [{ required: true, message: t('pay.app.rzfb4'), trigger: 'blur' }],
  'config.wxAppId': [{ required: true, message: '请输入微信AppId', trigger: 'blur' }],
  'config.appKey': [{ required: true, message: '请输入应用的key', trigger: 'blur' }],
  'config.appCert': [{ required: true, message: t('pay.app.rzfb10'), trigger: 'blur' }],
  'config.publicUrl': [
    { required: true, message: '请输入开放平台提供的请求公网地址', trigger: 'blur' }
  ],
  'config.hsbPubliceKeyContent': [
    { required: true, message: '请输入开放平台公钥证书内容', trigger: 'blur' }
  ],
  'config.mchPrivateKeyContent': [
    { required: true, message: '请输入商户私钥证书内容', trigger: 'blur' }
  ],
  'config.serverUrl': [{ required: true, message: t('pay.app.rzfb5'), trigger: 'blur' }],
  'config.signType': [{ required: true, message: t('pay.app.rzfb6'), trigger: 'blur' }],
  'config.mode': [{ required: true, message: t('pay.app.rzfb7'), trigger: 'blur' }],
  'config.privateKey': [{ required: true, message: t('pay.app.rzfb8'), trigger: 'blur' }],
  'config.alipayPublicKey': [{ required: true, message: t('pay.app.rzfb9'), trigger: 'blur' }],

  'config.alipayPublicCertContent': [
    { required: true, message: t('pay.app.rzfb11'), trigger: 'blur' }
  ],
  'config.rootCertContent': [{ required: true, message: t('pay.app.rzfb12'), trigger: 'blur' }]
})

/** 打开弹窗 */
let formType = ref('create')

const codeMap: Ref<any[]> = ref([]) //渠道类型

const open = async (row, code?) => {
  dialogVisible.value = true
  formLoading.value = true
  console.log(row, code)

  resetForm(row, code)
  try {
    const res = await ChannelApi.getChannelListChannelTypesByAppType(3) //3为徽商银行
    codeMap.value = res
    if (code) {
      let response = await ChannelApi.getChannel(row.merchantId, row.id, code)

      if (response && response?.id) {
        formData.value = response
        console.log('response', response)
        formData.value.config = JSON.parse(response?.config)
      }
      formType.value = !response?.id ? 'create' : 'edit'
    } else {
      formType.value = 'create'
    }
  } catch (error) {
    console.log(error)
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 重置表单 */
let resetForm = (row, code?) => {
  formData.value = {
    id: row?.id,
    appId: row?.id,
    merchantId: row?.merchantId,
    code: code,
    status: CommonStatusEnum.ENABLE,
    feeRate: undefined,
    remark: '',
    config: {
      mchId: undefined,
      appKey: undefined,
      appCert: undefined,
      publicUrl: undefined,
      hsbPubliceKeyContent: undefined,
      mchPrivateKeyContent: undefined,
      hsbH5PubliceKeyContent: undefined,
      outPrivateKey: undefined,
      caContent: undefined,
      keyPwd: undefined,
      type: undefined,
      keyContent: undefined,
      fileUrl: undefined,
      rsaPrivateKey: undefined,
      platFormId: undefined
    }
  }
  formRef.value?.resetFields()
}

const close = () => {
  console.log('close')

  dialogVisible.value = false
  resetForm(undefined, undefined)
}

const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value?.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    console.log('formData.value', formData.value)
    const data = { ...formData.value } as unknown as ChannelApi.ChannelVO
    data.config = JSON.stringify(formData.value.config)

    if (formType.value === 'create') {
      await ChannelApi.createChannel(data)
      message.success(t('common.createSuccess'))
    } else {
      await ChannelApi.updateChannel(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (e) {
  } finally {
    formLoading.value = false
  }
}

let fileAccept = ref('.cer')

const fileBeforeUpload = (file) => {
  let format = '.' + file.name.split('.')[1]
  if (fileAccept.value && format !== fileAccept.value) {
    const str = t('pay.app.desc2', {
      fileAccept: fileAccept.value
    })
    message.error(str)
    return false
  }
  let isRightSize = file.size / 1024 / 1024 < 2
  if (!isRightSize) {
    message.error(t('pay.app.desc3'))
  }
  return isRightSize
}

/******************************************* 文件上传 *******************************************************/

const changeFileAccept = (type) => {
  fileAccept.value = type
}

const appCertUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.appCert = e?.target.result
  }
  readFile.readAsText(event.file)
}

const hsbPubliceKeyUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.hsbPubliceKeyContent = e?.target.result
  }
  readFile.readAsText(event.file)
}

const mchPrivateKeyUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.mchPrivateKeyContent = e?.target.result
  }
  readFile.readAsText(event.file)
}

const hsbH5PubliceKeyUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.hsbH5PubliceKeyContent = e?.target.result
  }
  readFile.readAsText(event.file)
}

const caContentUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.caContent = e?.target.result
  }
  readFile.readAsText(event.file)
}
</script>
