<template>
  <div class="resource-approval-approval">
    <!-- <ContentWrap>
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item :label="t('res.approval.name')" prop="name">
          <el-input
            v-model="queryParams.code"
            class="!w-240px"
            clearable
            :placeholder="t('res.approval.codePlaceholder')"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <icon-ep-search class="mr-5px" />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <icon-ep-refresh class="mr-5px" style="font-size: 12px" />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap> -->
    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list">
        <el-table-column type="expand">
          <template #default="props">
            <el-table :data="props.row.showResources" :border="true">
              <el-table-column align="center" :label="t('res.approval.scene')" prop="scene">
                <template #default="{ row }">
                  {{ systemBusinessSceneMap[row.scene] }}
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                :label="t('res.approval.permissionTypes')"
                prop="permissionTypes"
              >
                <template #default="{ row }">
                  {{ row.permissionTypes.map((el) => permissionPointMap[el]).join() }}
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="t('res.approval.applicationNo')"
          prop="applicationNo"
        />
        <el-table-column align="center" :label="'资源编号'" prop="exposeNo" />
        <el-table-column
          align="center"
          :label="t('res.approval.roleName')"
          prop="roleExpose.name"
        />
        <el-table-column align="center" :label="'申请方'" prop="applicantName" />
        <el-table-column align="center" :label="'授权方'" prop="authorizerName" />

        <el-table-column
          align="center"
          :label="t('res.approval.applicationReason')"
          prop="applicationReason"
        />
        <el-table-column align="center" :label="'授权意见'" width="100px" prop="approvalOpinion" />
        <el-table-column
          align="center"
          :label="t('res.approval.authStatusName')"
          prop="authStatusName"
        />
        <el-table-column align="center" :label="'操作'" prop="exposeNo">
          <template #default="{ row }">
            <el-button link type="primary" @click="showApproval(row)" v-if="row.authStatus === 1">
              {{ t('res.approval.authStatus1') }}
            </el-button>
            <el-button link type="primary" @click="withdraw(row)" v-if="row.authStatus === 2">
              {{ t('res.approval.authStatus2') }}
            </el-button>
            <span> </span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <!-- <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      /> -->
    </ContentWrap>
    <Dialog
      :title="t('res.approval.applicationReason')"
      v-model="visible"
      width="600"
      class="mt-16px"
      @close="close"
    >
      <el-form label-width="90px" :model="formData" ref="formRef">
        <el-form-item
          :label="t('res.approval.result')"
          prop="result"
          :rules="{
            required: true,
            trigger: ['blur', 'change'],
            message: t('res.approval.resultPlaceholder')
          }"
        >
          <el-select v-model="formData.result" :placeholder="t('res.approval.resultPlaceholder')">
            <el-option :label="t('common.yes')" value="2" />
            <el-option :label="t('common.no')" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="t('res.approval.approvalOpinion')"
          prop="approvalOpinion"
          :rules="{
            required: true,
            trigger: ['blur', 'change'],
            message: t('res.approval.approvalOpinionPlaceholder')
          }"
        >
          <el-input
            v-model="formData.approvalOpinion"
            :placeholder="t('res.approval.approvalOpinionPlaceholder')"
            type="textarea"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button type="primary" @click="application" :loading="loading">{{
          t('common.ok')
        }}</el-button>
        <el-button @click="close">{{ t('common.cancel') }}</el-button>
      </template>
    </Dialog>
    <Dialog
      :title="t('res.approval.withdraw')"
      v-model="withdrawVisible"
      @close="withdrawClose"
      width="600"
    >
      <el-form label-width="90px" :model="withdrawFormData" ref="withdrawFormRef" class="mt-16px">
        <el-form-item
          :label="t('res.approval.reason')"
          prop="reason"
          :rules="{
            required: true,
            trigger: ['blur', 'change'],
            message: t('res.approval.reasonPlaceholder')
          }"
        >
          <el-input
            v-model="withdrawFormData.reason"
            :placeholder="t('res.approval.reasonPlaceholder')"
            type="textarea"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button type="primary" @click="confirmWithdraw" :loading="withdrawLoading">{{
          t('common.ok')
        }}</el-button>
        <el-button @click="withdrawClose">{{ t('common.cancel') }}</el-button>
      </template>
    </Dialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'resourceApprovalApproval'
})

import {
  resourceRowVO,
  resourceApproval,
  resourceWithdraw,
  getResourceApprovalOfTenantPage
} from '@/api/system/resource'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
const { t } = useI18n() // 国际化
const loading = ref(false)

const list: Ref<Array<resourceRowVO>> = ref([])

const queryParams = ref({
  // pageNo: 1,
  // pageSize: 10,
  code: undefined
})

// 转业务场景为map，查名字用
const systemBusinessSceneMap = computed(() => {
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

const permissionPointMap = computed(() => {
  return getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

const total = ref(0)

const handleQuery = async () => {
  try {
    loading.value = true
    const res = await getResourceApprovalOfTenantPage({ ...queryParams.value })
    const tempList = res.list
    total.value = res.total
    list.value = tempList.map((el) => {
      let resourcesMap = {}
      let showResources = []
      el.resourceExposes.forEach((e) => {
        if (!resourcesMap[e.scene]) {
          resourcesMap[e.scene] = [e.permissionType]
        } else {
          resourcesMap[e.scene].push(e.permissionType)
        }
      })
      for (let key in resourcesMap) {
        showResources.push({
          scene: key,
          permissionTypes: resourcesMap[key]
        })
      }
      return {
        ...el,
        showResources
      }
    })
  } catch (e) {
  } finally {
    loading.value = false
  }
}

const resetQuery = () => {
  queryParams.value = {
    // pageNo: 1,
    // pageSize: 10,
    code: undefined
  }
}

const visible = ref(false)

const activeRow = ref()

const formData = ref({
  result: undefined,
  approvalOpinion: undefined
})

// 用户自身的权限信息
const ownDataPermissionScope = ref()

// 打开审批窗口
const showApproval = (row) => {
  activeRow.value = row
  visible.value = true
}

const withdrawVisible = ref()

const withdrawFormRef = ref()

const withdrawFormData = ref({
  reason: undefined
})

// 撤回
const withdraw = (row) => {
  activeRow.value = row
  withdrawVisible.value = true
}

const withdrawLoading = ref(false)

const withdrawClose = () => {
  withdrawFormData.value = {
    reason: undefined
  }
  withdrawVisible.value = false
}

const confirmWithdraw = async () => {
  await withdrawFormRef.value.validate()
  try {
    withdrawLoading.value = true
    await resourceWithdraw({
      applicationNo: activeRow.value.applicationNo,
      ...withdrawFormData.value
    })
    ElMessage.success(t('common.handleSuccess'))
    handleQuery()
    withdrawClose()
  } finally {
    withdrawLoading.value = false
  }
}

const close = () => {
  formData.value = {
    result: undefined,
    approvalOpinion: undefined
  }
  visible.value = false
}

const formRef = ref()

// 申请资源
const application = async () => {
  await formRef.value.validate()
  try {
    loading.value = true
    await resourceApproval({
      applicationNo: activeRow.value.applicationNo,
      ...formData.value
    })
    ElMessage.success(t('common.handleSuccess'))
    handleQuery()
    close()
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  handleQuery()
})
</script>
