import request from '@/config/axios'

export interface tenantClientDelVO {
  id: string | number
}

export interface tenantClientVO {
  name: string
  code: string
  oauthClient: string
  url: string
  description: string
  id?: string | number
}

export interface tenantClientRowVO extends tenantClientVO {
  id: string | number
  createTime: string
}

export interface tableResVO<T> {
  list: Array<T>
  total: number
}

// 查询交互端列表
export const getTenantClientPage = async (params: PageParam) => {
  return await request.get({ url: '/system/tenant-client/page', params })
}

// 新增交互端列表
export const addTenantClient = async (data: tenantClientVO) => {
  return await request.post({ url: '/system/tenant-client/create', data })
}

// 更新交互端列表
export const updateTenantClient = async (data: tenantClientVO) => {
  return await request.put({ url: '/system/tenant-client/update', data })
}

// 删除交互端列表
export const deleteTenantClient = async (params: tenantClientDelVO) => {
  return await request.delete({ url: '/system/tenant-client/delete', params })
}
