<template>
  <div class="resource-approval">
    <el-tabs v-model="activeName">
      <el-tab-pane :label="t('res.approval.apply')" name="apply"><Apply /></el-tab-pane>
      <el-tab-pane :label="t('res.approval.approval')" name="approval"><Approval /></el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ResourceApproval'
})

import Apply from './Apply.vue'
import Approval from './Approval.vue'
const { t } = useI18n() // 国际化
const activeName = ref('apply')
</script>
