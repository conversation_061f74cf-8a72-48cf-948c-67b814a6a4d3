import request from '@/config/axios'

// 新增租户升级策略
export const createTenantUpgradeRule = (data) => {
  return request.post({ url: '/system/tenantUpgradeRule/add', data })
}

// 更新租户升级策略
export const updateTenantUpgradeRule = (data) => {
  return request.put({ url: '/system/tenantUpgradeRule/update', data })
}

// 删除租户升级策略
export const deleteTenantUpgradeRule = (id?) => {
  return request.delete({ url: `/system/tenantUpgradeRule/delete?id=${id}` })
}

// 获取租户升级策略分页列表
export const getTenantUpgradeRulePage = (params) => {
  return request.get({ url: '/system/tenantUpgradeRule/page', params })
}

// 获取应用列表
export const getTenantApplicationListAll = (params?) => {
  return request.get({ url: '/system/tenant-application/list-all', params })
}
