<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="服务名" prop="name">
        <el-input v-model="formData.name" placeholder="请输入服务名" :readonly="isPreview" />
      </el-form-item>
      <el-form-item label="服务编号" prop="code">
        <el-input
          v-model="formData.code"
          placeholder="请输入服务编号"
          :disabled="isEdit || isPreview"
        />
      </el-form-item>
      <el-form-item label="版本号" prop="ver">
        <el-input v-model="formData.ver" placeholder="请输入版本号" :readonly="isPreview" />
      </el-form-item>
      <!-- <el-form-item label="是否定制化">
        <el-select v-model="isCustom">
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="绑定租户" prop="applicationOwner">
        <el-select v-model="formData.applicationOwner" multiple :disabled="isEdit || isPreview">
          <el-option
            v-for="(item, index) in tenantList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="服务编号" prop="code"></el-form-item> -->
      <!-- <el-form-item label="服务地址" prop="url">
        <el-input v-model="formData.url" placeholder="请输入服务地址" :readonly="isPreview" />
      </el-form-item>
      <el-form-item label="OAuth客户端" prop="oauthClient">
        <el-input
          v-model="formData.oauthClient"
          placeholder="请输入OAuth客户端"
          :readonly="isPreview"
        />
      </el-form-item> -->
      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          placeholder="请输入描述"
          type="textarea"
          :readonly="isPreview"
        />
      </el-form-item>
      <el-form-item label="菜单详情" v-if="formType == 'preview'">
        <el-tree-v2 class="w-full" :data="menueList" :props="menueProps" :height="150" />
      </el-form-item>
    </el-form>
    <template #footer v-if="!isPreview">
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ApplyForm'
})

import { createApplicationCreate, updateApplicationCreate } from '@/api/system/apply'
import { getTenantAppList } from '@/api/system/apply'
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改；preview - 查看

const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const isEdit = ref(false)

// const isCustom = ref('0')

const formData = ref({
  name: null,
  code: null,
  ver: null,
  description: null,
  applicationOwner: []
  // url: null,
  // oauthClient: null
})

const formRef = ref() // 表单 Ref

const isPreview = computed(() => {
  return formType.value === 'preview'
})

// const formValue = ref({})

// let appId: Ref<string | number> = ref('')

const rgx = /^[A-Z]{5,8}$/

// 验证服务编号
const validatorCode = (_rule, value, callback) => {
  if (value === '' || value === null || value === undefined) {
    callback(new Error('服务编号不能为空'))
    return
  } else if (!rgx.test(value)) {
    callback(new Error('服务编号需要是5到8为大写英文字母组成'))
    return
  } else {
    callback()
  }
}

const formRules = reactive({
  name: [{ required: true, message: '服务名不能为空', trigger: 'blur' }],
  code: [{ required: true, validator: validatorCode }],
  ver: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
  applicationOwner: [{ required: false, message: '请绑定租户', trigger: 'blur' }]

  // url: [{ required: true, message: '服务地址不能为空', trigger: 'blur' }],
  // oauthClient: [{ required: true, message: 'OAuth客户端不能为空', trigger: 'blur' }]
})

const { t } = useI18n() // 国际化

const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  let data = formData.value

  console.log('data.code', data.code)
  if (formType.value === 'create') {
    const validRes = await getTenantAppList({
      pageNo: 1,
      pageSize: 100,
      code: data.code
    })
    if (validRes.total > 0) {
      // dialogVisible.value = false
      formLoading.value = false
      ElNotification.error('服务编号重复，请重新填写')
      return
    }
  }

  try {
    const fn = formType.value === 'create' ? createApplicationCreate : updateApplicationCreate
    await fn({
      ...data,
      applicationOwner: formData.value.applicationOwner?.toString()
    })
    message.success('操作成功')
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

//菜单列表
import { getMenuList } from '@/api/system/menu'
import { handleTree } from '@/utils/tree'
const menueProps = {
  value: 'id',
  label: 'name',
  children: 'children'
}
const menueList = ref<angy[]>()

/** 打开弹窗 */
const open = async (type: string, row?) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  //
  if (row) {
    formData.value = row
    formData.value.applicationOwner = row.applicationOwner?.split(',')

    //获取菜单列表
    if (type === 'preview') {
      const res = await getMenuList({ applicationId: row.id })
      menueList.value = handleTree(res)
    }
  } else {
    resetForm()
  }
  isEdit.value = row ? true : false
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 重置表单 */
const resetForm = () => {
  // 重置表单
  formData.value = {
    name: null,
    code: null,
    ver: null,
    description: null,
    applicationOwner: []
    // url: null,
    // oauthClient: null
  }
}

// 获取租户列表
import { getTenantList } from '@/api/system/tenant'

let tenantList = ref<any[]>([])
const getTenantSimpleList = async () => {
  try {
    let data = await getTenantList()
    tenantList.value = data
  } catch (error) {}
}
onMounted(() => {
  getTenantSimpleList()
})
</script>
