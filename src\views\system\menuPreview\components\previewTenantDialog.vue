<template>
  <el-dialog
    title="查看租户"
    v-model="show"
    width="860px"
    append-to-body
    destroy-on-close
    class="approval-edit-form"
    @close="close"
  >
    <el-card style="max-height: 400px; overflow: auto; margin: 16px 0" v-loading="loading">
      {{ tenantStr }}
    </el-card>

    <template #footer>
      <el-button @click="close">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'previewTenantDialog'
})

const show = ref(false)

const treeRef = ref()
const close = () => {
  show.value = false
}

const loading = ref(false)

const tenantStr = ref('')

import { pinyin } from 'pinyin-pro'

const open = (_tenantList) => {
  show.value = true
  tenantStr.value = _tenantList
    // .sort((a, b) => {
    //   const NumA = Number(a)
    //   const NumB = Number(b)
    //   if (!isNaN(NumA) && !isNaN(NumB)) {
    //     return NumA - NumB
    //   } else {
    //     return (a.tenantId + '').localeCompare(b.tenantId + '')
    //   }
    // })
    .map((el) => el.name)
    .sort((a, b) => {
      const posA = pinyin(a.trim(), { toneType: 'none' }).replaceAll(' ', '')
      const posB = pinyin(b.trim(), { toneType: 'none' }).replaceAll(' ', '')
      // return comparePinyin(posA, posB)
      return posA.localeCompare(posB)
    })
    .join('，')
}

defineExpose({
  open
})
</script>
