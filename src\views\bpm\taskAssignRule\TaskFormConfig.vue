<template>
  <Dialog v-model="visible" :title="title" width="650">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="150px">
      <el-form-item label="表单类型" prop="formType">
        <el-radio-group v-model="formData.formType">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_MODEL_FORM_TYPE)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="formData.formType === 10" label="流程表单" prop="formId">
        <el-select v-model="formData.formId" clearable style="width: 100%">
          <el-option v-for="form in formList" :key="form.id" :label="form.name" :value="form.id" />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="formData.formType === 20"
        label="编辑组件路径"
        prop="formCustomCreatePath"
      >
        <el-tooltip
          class="item"
          content="自定义业务编辑组件路径，使用项目scr/views下的相对路径，例如说：bpm/oa/leave/create"
          effect="light"
          placement="top"
        >
          <el-input
            v-model="formData.formCustomCreatePath"
            placeholder="请输入业务编辑组件"
            style="width: 100%"
          />
          <i class="el-icon-question" style="padding-left: 5px"></i>
        </el-tooltip>
      </el-form-item>

      <el-form-item v-if="formData.formType === 20" label="详情组件路径" prop="formCustomViewPath">
        <el-tooltip
          class="item"
          content="自定义业务详情组件路径，使用项目scr/views下的相对路径，例如说：bpm/oa/leave/detail"
          effect="light"
          placement="bottom"
        >
          <el-input
            v-model="formData.formCustomViewPath"
            placeholder="请输入业务详情组件路径"
            style="width: 100%"
          />
          <i class="el-icon-question" style="padding-left: 5px"></i>
        </el-tooltip>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="loading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { FormInstance } from 'element-plus'
import * as ModelApi from '@/api/bpm/model'
import * as FormApi from '@/api/bpm/form'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

const message = useMessage() // 消息弹窗

const visible = ref(false)
const title = ref('配置任务表单')
const loading = ref(false)
const formList = ref([])

const formData = reactive({
  taskDefinitionKey: '',
  taskName: '',
  formType: 10,
  formId: undefined,
  formCustomCreatePath: '',
  formCustomViewPath: ''
})

const formRules = reactive({
  formType: [{ required: true, message: '表单类型不能为空', trigger: 'change' }],
  formId: [{ required: false, message: '表单不能为空', trigger: 'change' }],
  formCustomCreatePath: [{ required: false, message: '编辑组件路径不能为空', trigger: 'blur' }],
  formCustomViewPath: [{ required: false, message: '详情组件路径不能为空', trigger: 'blur' }]
})

const formRef = ref<FormInstance>()
const modelId = ref<number>()
const modelData = ref<ModelApi.ModelCreateReqVO>()
const emit = defineEmits(['success'])

/** 打开弹窗 */
const open = async (id: number, taskKey: string, taskName: string) => {
  visible.value = true
  modelId.value = id
  title.value = `配置任务 [${taskName}] 的表单`

  // 重置表单
  formData.taskDefinitionKey = taskKey
  formData.taskName = taskName
  formData.formType = 10
  formData.formId = undefined
  formData.formCustomCreatePath = ''
  formData.formCustomViewPath = ''

  // 获取表单列表
  await getFormList()

  // 获取模型数据
  await getModelData()

  // 如果已有配置，则填充表单
  if (modelData.value?.userTaskForms) {
    const taskForm = modelData.value.userTaskForms.find(
      (item) => item.taskDefinitionKey === taskKey
    )
    if (taskForm) {
      formData.formType = taskForm.formType
      formData.formId = taskForm.formId
      formData.formCustomCreatePath = taskForm.formCustomCreatePath
      formData.formCustomViewPath = taskForm.formCustomViewPath
    }
  }
}

/** 获取表单列表 */
const getFormList = async () => {
  try {
    formList.value = await FormApi.getSimpleFormList()
  } catch (error) {
    console.error('获取表单列表失败', error)
  }
}

/** 获取模型数据 */
const getModelData = async () => {
  try {
    loading.value = true
    const res = await ModelApi.getModel(modelId.value!)
    modelData.value = res
  } catch (error) {
    console.error('获取模型数据失败', error)
  } finally {
    loading.value = false
  }
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  // 提交请求
  loading.value = true
  try {
    // 准备更新的数据
    const updateData = { ...modelData.value }

    // 初始化 userTaskForms 数组（如果不存在）
    if (!updateData.userTaskForms) {
      updateData.userTaskForms = []
    }

    // 判断是否需要过滤任务节点
    const shouldFilterTask =
      (formData.formType === 10 && !formData.formId) ||
      (formData.formType === 20 && !formData.formCustomCreatePath && !formData.formCustomViewPath)

    if (shouldFilterTask) {
      // 需要过滤掉这个任务节点
      // 查找是否已存在该任务的表单配置
      const taskFormIndex = updateData.userTaskForms.findIndex(
        (item) => item.taskDefinitionKey === formData.taskDefinitionKey
      )

      // 如果存在配置，删除它
      if (taskFormIndex >= 0) {
        updateData.userTaskForms.splice(taskFormIndex, 1)
      }

      // 调用更新接口，保存删除后的配置
      await ModelApi.updateModel(updateData)
      visible.value = false
      emit('success')
      message.success('已移除任务表单配置')
      return
    }

    // 查找是否已存在该任务的表单配置
    const taskFormIndex = updateData.userTaskForms.findIndex(
      (item) => item.taskDefinitionKey === formData.taskDefinitionKey
    )

    // 准备新的表单配置
    const taskFormConfig = {
      taskDefinitionKey: formData.taskDefinitionKey,
      taskName: formData.taskName,
      description: formData.taskName || '', // 添加description字段解决类型错误
      formType: formData.formType,
      formId: formData.formType === 10 ? formData.formId : null,
      formCustomCreatePath: formData.formType === 20 ? formData.formCustomCreatePath : null,
      formCustomViewPath: formData.formType === 20 ? formData.formCustomViewPath : null
    }

    // 更新或添加配置
    if (taskFormIndex >= 0) {
      updateData.userTaskForms[taskFormIndex] = taskFormConfig
    } else {
      updateData.userTaskForms.push(taskFormConfig)
    }

    // 调用更新接口
    await ModelApi.updateModel(updateData)

    // 关闭弹窗并通知父组件
    visible.value = false
    emit('success')
    message.success('配置成功')
  } catch (error) {
    console.error('保存配置失败', error)
    message.error('配置失败')
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>
