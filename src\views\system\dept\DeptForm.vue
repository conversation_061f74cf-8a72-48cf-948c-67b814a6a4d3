<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      :label-width="ifEn ? '160px' : '80px'"
    >
      <el-form-item :label="t('system.dept.parentId')" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="deptTree"
          :props="defaultProps"
          check-strictly
          default-expand-all
          :placeholder="t('system.dept.parentIdPlaceholder')"
          value-key="deptId"
        />
      </el-form-item>
      <el-form-item :label="t('system.dept.name')" prop="name">
        <el-input v-model="formData.name" :placeholder="t('system.dept.namePlaceholder')" />
      </el-form-item>
      <el-form-item :label="t('system.dept.sort')" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item :label="t('system.dept.code')" prop="code">
        <el-input
          v-model="formData.code"
          maxlength="255"
          :placeholder="t('system.dept.codePlaceholder')"
        />
      </el-form-item>
      <el-form-item :label="t('system.dept.leaderUserId')" prop="leaderUserId">
        <el-select
          v-model="formData.leaderUserId"
          clearable
          :placeholder="t('system.dept.leaderUserIdPlaceholder')"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('system.dept.phone')" prop="phone">
        <el-input
          v-model="formData.phone"
          maxlength="11"
          :placeholder="t('system.dept.phonePlaceholder')"
        />
      </el-form-item>
      <el-form-item :label="t('system.dept.email')" prop="email">
        <el-input
          v-model="formData.email"
          maxlength="50"
          :placeholder="t('system.dept.emailPlaceholder')"
        />
      </el-form-item>
      <el-form-item :label="t('system.dept.status')" prop="status">
        <el-select
          v-model="formData.status"
          clearable
          :placeholder="t('system.dept.statusPlaceholder')"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm">{{ t('common.ok') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemDeptForm'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import { CommonStatusEnum } from '@/utils/constants'

const { t, ifEn } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  title: '',
  parentId: undefined,
  name: undefined,
  sort: undefined,
  leaderUserId: undefined,
  phone: undefined,
  email: undefined,
  code: undefined,
  status: CommonStatusEnum.ENABLE
})
const formRules = reactive({
  parentId: [{ required: true, message: t('system.dept.rDesc1'), trigger: 'blur' }],
  name: [{ required: true, message: t('system.dept.rDesc2'), trigger: 'blur' }],
  sort: [{ required: true, message: t('system.dept.rDesc3'), trigger: 'blur' }],
  email: [{ type: 'email', message: t('system.dept.rDesc4'), trigger: ['blur', 'change'] }],
  phone: [
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: t('system.dept.rDesc5'), trigger: 'blur' }
  ],
  status: [{ required: true, message: t('system.dept.rDesc6'), trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const deptTree = ref() // 树形结构
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DeptApi.getDept(id)
    } finally {
      formLoading.value = false
    }
  }
  // 获得用户列表
  userList.value = await UserApi.getSimpleUserList()
  // 获得部门树
  await getTree()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DeptApi.DeptVO
    if (formType.value === 'create') {
      await DeptApi.createDept(data)
      message.success(t('common.createSuccess'))
    } else {
      await DeptApi.updateDept(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: '',
    parentId: undefined,
    name: undefined,
    sort: undefined,
    leaderUserId: undefined,
    phone: undefined,
    email: undefined,
    status: CommonStatusEnum.ENABLE
  }
  formRef.value?.resetFields()
}

/** 获得部门树 */
const getTree = async () => {
  deptTree.value = []
  const data = await DeptApi.getSimpleDeptList()
  let dept: Tree = { id: 0, name: t('system.dept.topDivision'), children: [] }
  dept.children = handleTree(data)
  deptTree.value.push(dept)
}
</script>
