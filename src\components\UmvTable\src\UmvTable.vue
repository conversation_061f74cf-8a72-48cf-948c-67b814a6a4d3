<template>
  <div
    class="umv-table flex flex-col !flex-1 overflow-hidden bg-el-bg-color"
    :class="{ 'umv-table--fullscreen': isFullscreen && !contentWrapFullscreen }"
  >
    <!-- 表格头部区域 -->
    <el-row justify="space-between" align="middle" class="mb-2.5">
      <!-- 左侧工具栏 -->
      <el-space wrap>
        <slot name="tools"></slot>
      </el-space>

      <!-- 中间标题区域 -->
      <el-space v-if="props.title" wrap>
        <div class="text-el-text-color-primary text-lg font-medium leading-normal">
          {{ props.title }}
        </div>
      </el-space>

      <!-- 右侧表格操作按钮组 -->
      <el-space wrap :size="14" class="ml-auto">
        <!-- 斑马纹开关 -->
        <el-tooltip :content="t('UmvTable.table.tooltip.stripe')" placement="top">
          <el-switch v-model="stripe" size="small" />
        </el-tooltip>

        <!-- 刷新按钮 -->
        <el-tooltip :content="t('UmvTable.table.tooltip.refresh')" placement="top">
          <el-button :icon="RefreshRight" size="small" text @click="handleRefresh" />
        </el-tooltip>

        <!-- 全屏按钮 -->
        <el-tooltip :content="t('UmvTable.table.tooltip.fullscreen')" placement="top">
          <el-button
            :icon="isFullscreen ? Crop : FullScreen"
            size="small"
            text
            @click="toggleFullscreen"
          />
        </el-tooltip>

        <!-- 边框显示按钮 -->
        <el-tooltip :content="t('UmvTable.table.tooltip.border')" placement="top">
          <el-button :icon="Grid" size="small" text @click="toggleBorder" />
        </el-tooltip>

        <!-- 表格尺寸设置 -->
        <el-tooltip :content="t('UmvTable.table.tooltip.size')" placement="top">
          <el-dropdown @command="handleSizeChange" trigger="click">
            <el-button :icon="ScaleToOriginal" size="small" text />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in TABLE_SIZE_OPTIONS"
                  :key="item.value"
                  :command="item.value"
                  :class="{ 'is-active': item.value === size }"
                >
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>

        <!-- 列设置按钮 -->
        <el-tooltip
          :content="t('UmvTable.table.tooltip.columnSetting')"
          placement="top"
          v-if="showSettingColumnBtn"
        >
          <el-dropdown trigger="click" placement="bottom-end">
            <el-button :icon="More" size="small" type="primary" text />

            <template #dropdown>
              <el-dropdown-menu class="column-setting-dropdown">
                <!-- 标题栏 -->
                <div class="column-setting-header">
                  <span>{{ t('UmvTable.table.setting.columnSetting') }}</span>
                </div>

                <!-- 列拖拽排序区域 -->
                <div class="umv-table__draggable">
                  <draggable v-model="settingColumnList" :animation="150" item-key="prop">
                    <template #item="{ element }">
                      <div
                        class="umv-table__draggable-item flex items-center cursor-pointer p-[6px_4px] transition-colors duration-300 hover:bg-el-fill-color-light"
                      >
                        <div
                          class="umv-table__draggable-item-move px-1.5 cursor-move text-el-text-color-secondary flex items-center"
                        >
                          <el-icon><Rank /></el-icon>
                        </div>
                        <el-checkbox v-model="element.show" :disabled="element.disabled">
                          {{ element.label }}
                        </el-checkbox>
                      </div>
                    </template>
                  </draggable>
                </div>

                <!-- 重置按钮 -->
                <div class="column-setting-footer">
                  <el-button
                    type="primary"
                    size="small"
                    @click="resetSettingColumns"
                    class="w-9/10"
                  >
                    <el-icon><RefreshRight /></el-icon>
                    <span>{{ t('UmvTable.table.setting.reset') }}</span>
                  </el-button>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
      </el-space>
    </el-row>

    <!-- 表格主体区域 -->
    <div class="max-h-full overflow-hidden" :style="tableContainerStyle">
      <el-table
        ref="tableRef"
        v-bind="tableProps"
        :stripe="stripe"
        :size="size"
        :border="isBordered"
        :data="data"
        :height="'100%'"
      >
        <!-- 优先使用默认插槽中的列定义 -->
        <template v-if="useSlotColumns">
          <slot></slot>
        </template>

        <!-- 或者使用通过columns属性配置的列 -->
        <template v-else-if="props.columns && props.columns.length">
          <el-table-column
            v-for="column in columnsToRender"
            :key="column.prop"
            :type="column.type"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
            :sortable="column.sortable"
            :show-overflow-tooltip="column.showOverflowTooltip"
            :align="column.align || 'center'"
            :header-align="column.headerAlign || 'center'"
          >
            <template v-if="column.renderTemplate" #default="scope">
              <component :is="column.renderTemplate(scope)" />
            </template>
            <template v-else-if="column.slot" #default="scope">
              <slot :name="column.slot" v-bind="scope"></slot>
            </template>
            <template v-if="column.headerSlot" #header="scope">
              <slot :name="column.headerSlot" v-bind="scope"></slot>
            </template>
          </el-table-column>
        </template>

        <!-- 动态插槽传递 -->
        <template v-for="key in Object.keys($slots)" :key="key" #[key]="scope">
          <slot :name="key" v-bind="scope"></slot>
        </template>
      </el-table>
    </div>

    <slot name="pagination"></slot>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  ref,
  watch,
  nextTick,
  onMounted,
  defineComponent,
  onUnmounted,
  getCurrentInstance,
  inject
} from 'vue'
import {
  ElTable,
  ElButton,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElTooltip,
  ElSwitch,
  ElRow,
  ElSpace,
  ElIcon,
  ElPopover,
  ElCheckbox,
  ElDivider
} from 'element-plus'
import draggable from 'vuedraggable'
import { omit } from 'lodash-es'
import {
  RefreshRight,
  FullScreen,
  Crop,
  Grid,
  More,
  Rank,
  ScaleToOriginal
} from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
// 从types.ts导入类型定义
import type { TableProps, TableColumn, Recordable } from '@/components/UmvTable/src/types'
defineOptions({ name: 'UmvTable' })

const { t } = useI18n()

// Props 默认值
/** Props 类型定义 */

interface Props extends /* @vue-ignore */ TableProps {
  /** 表格标题 */
  title?: string
  /** 禁止控制显示隐藏的列 */
  disabledColumnKeys?: string[]
  /** 表格数据 */
  data: Recordable[]
  /** 表格列配置 */
  columns?: TableColumn[]
  /** 边框默认值 */
  defaultBorder?: boolean
}
// Props 默认值
const props = withDefaults(defineProps<Props>(), {
  title: '',
  disabledColumnKeys: () => [],
  data: () => [],
  columns: () => [],
  defaultBorder: true
})

/** Emits 类型定义 */
const emit = defineEmits<{
  (e: 'refresh'): void
}>()

const $slots = useSlots()
const attrs = useAttrs()

/** 表格属性计算 */
const tableProps = computed(() => {
  return {
    ...attrs,
    ...omit(props, ['title', 'disabledColumnKeys', 'columns'])
  }
})

/** 组件状态 */
const tableRef = ref<InstanceType<typeof ElTable>>()
const stripe = ref(true)
const size = ref<'large' | 'default' | 'small'>('default')
const isBordered = ref(props.defaultBorder)
const isFullscreen = ref(false)

// 尝试获取ContentWrap的全屏功能
interface ContentWrapFullscreen {
  toggleFullscreen: () => void
  isFullscreen: Ref<boolean>
  onFullscreenChange?: (callback: (newVal: boolean) => void) => void
}

interface ContentHeight {
  height: Ref<number>
  isFullscreen: Ref<boolean>
  getContentHeight: () => number
}

const contentWrapFullscreen = inject<ContentWrapFullscreen | null>('contentWrapFullscreen', null)
const contentHeight = inject<ContentHeight | null>('contentHeight', null)

/** 表格尺寸选项 */
const TABLE_SIZE_OPTIONS = [
  { label: t('UmvTable.table.size.small'), value: 'small' },
  { label: t('UmvTable.table.size.default'), value: 'default' },
  { label: t('UmvTable.table.size.large'), value: 'large' }
] as const

/** 处理表格尺寸变更 */
const handleSizeChange = (value: string) => {
  if (value && ['small', 'default', 'large'].includes(value)) {
    size.value = value as 'large' | 'default' | 'small'
  }
}

/** 处理表格刷新 */
const handleRefresh = () => {
  emit('refresh')
}

/** 切换全屏状态 */
const toggleFullscreen = () => {
  // 检查是否在ContentWrap中
  if (contentWrapFullscreen && typeof contentWrapFullscreen.toggleFullscreen === 'function') {
    // 调用ContentWrap的全屏方法
    contentWrapFullscreen.toggleFullscreen()

    // 同步当前组件的全屏状态，使用nextTick确保状态更新
    nextTick(() => {
      isFullscreen.value = contentWrapFullscreen.isFullscreen.value || false
    })
  } else {
    // 原有的全屏逻辑，当不在ContentWrap中时使用
    nextTick(() => {
      isFullscreen.value = !isFullscreen.value

      // 处理进入全屏时的文档滚动行为
      if (isFullscreen.value) {
        // 阻止body滚动
        document.body.style.overflow = 'hidden'
        // 调整表格布局以适应新的容器大小
        tableRef.value?.doLayout()
      } else {
        // 恢复body滚动
        document.body.style.overflow = ''
        // 重新调整表格布局
        nextTick(() => {
          tableRef.value?.doLayout()
        })
      }
    })
  }
}

/** 切换边框显示 */
const toggleBorder = () => {
  isBordered.value = !isBordered.value
}

/** 列设置相关逻辑 */
const showSettingColumnBtn = computed(() => {
  return Boolean(props.columns?.length)
})

/** 列设置项类型 */
interface SettingColumnItem {
  /** 列标题 */
  label: string
  /** 列标识 */
  prop: string
  /** 是否显示 */
  show: boolean
  /** 是否禁用 */
  disabled: boolean
}

const settingColumnList = ref<SettingColumnItem[]>([])

/** 重置列设置 */
const resetSettingColumns = () => {
  if (!props.columns) {
    settingColumnList.value = []
    return
  }

  // 重置列设置，所有列默认设置为显示
  settingColumnList.value = props.columns.map((column) => {
    return {
      prop: column.prop,
      label: column.label || '',
      show: true, // 默认显示
      disabled: props.disabledColumnKeys.includes(column.prop)
    }
  })
}

/** 监听属性变化，重置列设置 */
watch(
  () => props.columns,
  () => resetSettingColumns(),
  { immediate: true }
)

/** 监听边框默认值变化 */
watch(
  () => props.defaultBorder,
  (newVal) => {
    isBordered.value = newVal
  }
)

/** 监听列设置变化 */
// watch(
//   settingColumnList,
//   (newVal) => {
//     console.log('列设置变化:', newVal)
//   },
//   { deep: true }
// )

/** 计算需要渲染的列 */
const columnsToRender = computed(() => {
  if (!props.columns || !props.columns.length) return []

  // 只根据列设置过滤显示/隐藏的列，不再尝试检测或过滤与插槽中的列冲突的情况
  const visibleColumns: TableColumn[] = []

  // 根据settingColumnList的顺序来排序和过滤columns
  for (const setting of settingColumnList.value) {
    // 只显示设置为显示的列
    if (setting.show) {
      // 找到对应的原始列配置
      const originalColumn = props.columns.find((col) => col.prop === setting.prop)
      if (originalColumn) {
        visibleColumns.push(originalColumn)
      }
    }
  }

  // console.log(
  //   '要渲染的列:',
  //   visibleColumns.map((col) => col.prop)
  // )
  return visibleColumns
})

/** 处理ESC键退出全屏 */
const handleEscKeydown = (e: KeyboardEvent) => {
  // 只有当不在ContentWrap中时，才处理ESC键事件
  if (e.key === 'Escape' && isFullscreen.value && !contentWrapFullscreen) {
    e.preventDefault()
    toggleFullscreen()
  }
}

/** 组件初始化 */
const initComponent = () => {
  nextTick(() => {
    try {
      if (props.columns?.length) {
        resetSettingColumns()
      }

      // 同步ContentWrap的全屏状态（如果在ContentWrap中）
      if (contentWrapFullscreen) {
        // 初始同步全屏状态
        isFullscreen.value = contentWrapFullscreen.isFullscreen.value

        // 监听ContentWrap的全屏状态变化
        if (typeof contentWrapFullscreen.onFullscreenChange === 'function') {
          contentWrapFullscreen.onFullscreenChange((newVal) => {
            isFullscreen.value = newVal
            // 全屏状态改变时需要重新布局
            nextTick(() => {
              tableRef.value?.doLayout()
            })
          })
        }

        // 如果在ContentWrap中，不添加ESC键监听，让ContentWrap处理
        // 添加窗口大小变化监听
        window.addEventListener('resize', handleWindowResize)
      } else {
        // 添加窗口大小变化监听
        window.addEventListener('resize', handleWindowResize)
        // 添加ESC键监听（只有在不在ContentWrap中时才添加）
        window.addEventListener('keydown', handleEscKeydown)
      }
    } catch (err) {
      console.error('初始化组件失败:', err)
    }
  })
}

/** 处理窗口大小变化 */
const handleWindowResize = () => {
  if (isFullscreen.value || (contentWrapFullscreen && contentWrapFullscreen.isFullscreen.value)) {
    // 在全屏模式下，窗口大小变化时重新布局表格
    nextTick(() => {
      tableRef.value?.doLayout() // 重新布局表格
    })
  }
}

onMounted(() => {
  initComponent()
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize)
  // 只有在不在ContentWrap中时才需要移除ESC键监听
  if (!contentWrapFullscreen) {
    window.removeEventListener('keydown', handleEscKeydown)
  }
})

// 检测是否使用了默认插槽中的列定义
const useSlotColumns = ref(false)

// 检测是否混用了两种方式
const checkColumnDefinitionMethod = () => {
  nextTick(() => {
    try {
      // 检测默认插槽中是否有el-table-column
      const hasSlotColumns = !!$slots.default?.().some((vnode) => {
        if (!vnode.type) return false
        const type = vnode.type as any
        return (
          (typeof type === 'object' && type.name === 'ElTableColumn') ||
          (typeof type === 'object' && type.__name === 'ElTableColumn') ||
          (typeof type === 'string' && /ElTableColumn/i.test(type)) ||
          (typeof type.name === 'string' && /ElTableColumn/i.test(type.name))
        )
      })

      // 检测是否同时使用了两种方式
      if (hasSlotColumns && props.columns?.length) {
        console.warn(
          '[UmvTable] 警告: 不建议同时使用columns属性和默认插槽中的el-table-column定义列，' +
            '这可能导致渲染问题。当检测到默认插槽中有el-table-column时，columns属性将被忽略。'
        )
      }

      // 更新状态
      useSlotColumns.value = hasSlotColumns
    } catch (error) {
      console.error('[UmvTable] 检测列定义方式出错:', error)
    }
  })
}

// 在挂载和columns变化时检测
onMounted(checkColumnDefinitionMethod)
watch(() => props.columns, checkColumnDefinitionMethod)
watch(() => $slots.default, checkColumnDefinitionMethod, { immediate: true })

/** 计算表格容器的样式 */
const tableContainerStyle = computed(() => {
  // 如果在 UmvContent 中，并且 UmvContent 组件提供了高度
  // if (contentHeight && contentHeight.height.value > 0) {
  //   // 根据是否全屏和内容组件提供的高度来计算表格容器样式
  //   if (contentHeight.isFullscreen.value) {
  //     // 全屏模式下，减去表格头部和分页区域的高度
  //     return { height: 'calc(100% - 40px)', overflow: 'auto' }
  //   } else {
  //     // 非全屏模式下，使用内容组件提供的高度值
  //     // 减去表格头部和分页的预估高度
  //     const headerHeight = 40 // 预估的表格头部高度
  //     const paginationHeight = $slots.pagination ? 45 : 0 // 如果有分页，预估高度
  //     const calculatedHeight = contentHeight.height.value - headerHeight - paginationHeight

  //     if (calculatedHeight > 100) {
  //       // 确保计算的高度合理
  //       return { height: `${calculatedHeight}px`, overflow: 'auto' }
  //     }
  //   }
  // }

  // 默认全屏样式或独立使用时
  return isFullscreen.value ? { height: 'calc(100% - 40px)', overflow: 'auto' } : {}
})

// 监听表格高度变化，并通知表格重新布局
if (contentHeight) {
  watch(
    () => contentHeight.height.value,
    () => {
      nextTick(() => {
        tableRef.value?.doLayout()
      })
    }
  )
}

// 暴露ElTable所有的方法和属性
defineExpose({
  // 直接暴露tableRef，让外部可以访问ElTable实例
  tableRef,

  // 转发ElTable实例的常用方法
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row: any, selected = false) =>
    tableRef.value?.toggleRowSelection(row, selected),
  toggleAllSelection: () => tableRef.value?.toggleAllSelection(),
  toggleRowExpansion: (row: any, expanded = false) =>
    tableRef.value?.toggleRowExpansion(row, expanded),
  setCurrentRow: (row: any) => tableRef.value?.setCurrentRow(row),
  clearSort: () => tableRef.value?.clearSort(),
  clearFilter: (columnKeys?: string[]) => tableRef.value?.clearFilter(columnKeys),
  doLayout: () => tableRef.value?.doLayout(),
  sort: (prop: string, order: string) => tableRef.value?.sort(prop, order),
  getSelectionRows: () => tableRef.value?.getSelectionRows?.() || [],

  // 转发UmvTable特有的方法
  toggleFullscreen,
  toggleBorder,
  setBorder: (value: boolean) => {
    isBordered.value = value
  },
  setSize: (value: 'small' | 'default' | 'large') => {
    size.value = value
  },
  resetSettingColumns
})
</script>

<style scoped>
/* 确保列设置弹出框正常显示 */
.column-setting-dropdown {
  width: 220px !important;
  max-height: 350px !important;
  padding: 0 !important;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex !important;
  flex-direction: column !important;
}

.column-setting-dropdown .el-dropdown-menu__item {
  padding: 0 !important;
  line-height: 1 !important;
}

.column-setting-header {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-fill-color-light);
  border-radius: 4px 4px 0 0;
}

.column-setting-footer {
  padding: 8px 12px;
  text-align: center;
  border-top: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-fill-color-light);
}

.umv-table__draggable {
  padding: 4px 0;
  max-height: 200px; /* 降低最大高度以确保按钮可见 */
  overflow-y: auto;
  margin: 0;
}

.umv-table__draggable-item {
  border-radius: 4px;
  margin: 2px 5px;
}

/* 全屏样式 */
.umv-table {
  transition: all 0.3s ease-in-out;
}

.umv-table--fullscreen {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2000;
  margin: 0;
  padding: 15px;
  width: 100vw;
  height: 100vh;
  background-color: var(--el-bg-color);
  overflow: auto;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-in-out;
}

.umv-table--fullscreen .max-h-full {
  flex: 1;
  overflow: auto !important;
}

/* 激活状态样式 */
.is-active {
  color: var(--el-color-primary);
  font-weight: bold;
}
</style>
