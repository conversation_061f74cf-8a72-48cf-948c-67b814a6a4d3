<template>
  <div class="detail-info-wrap">
    <!-- 左侧树形结构 -->
    <section class="left-wrap" v-loading="treeLoading">
      <div class="tree-block">
        <el-tree
          ref="treeRef"
          :data="treeDataList"
          :props="treeProps"
          :filter-node-method="filterNode"
          highlight-current
          check-strictly
          @node-click="handleTreeClick"
          :default-checked-keys="activeTreeKey"
          :default-expanded-keys="activeTreeKey"
          node-key="onlyPathKey"
        />
      </div>
    </section>

    <!-- 右侧内容 -->
    <section class="right-wrap">
      <div class="content-wrap">
        <ApiInfo :apiInfoData="apiInfoData" />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import ApiInfo from './ApiInfo.vue'
import { onMounted } from 'vue'

const props = defineProps({
  apiManagementList: {
    type: Array,
    default: () => []
  },
  detailInfoData: {
    type: Object,
    default: () => {}
  }
})
const { apiManagementList, detailInfoData } = toRefs(props)

const treeLoading = ref(false)
const filterText = ref('')
const treeRef = ref()
const treeDataList = ref<any>([])
const treeProps = ref({
  children: 'children',
  label: 'name'
})

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

onMounted(() => {
  // mockTree()
  filterApiList() // 过滤树型选择
})

const filterApiList = () => {
  try {
    let list: any = apiManagementList.value
    let apiListData = detailInfoData.value.apiListData
    console.log('apiListData ==== ', apiListData)
    let filterList = list.filter((item) => {
      if (item && item.onlyPathKey && apiListData.includes(item.onlyPathKey)) {
        return { ...item }
      }
      if (item && item.children && item.children.length) {
        let filterChildern = item.children.filter((child) =>
          apiListData.includes(child.onlyPathKey)
        )
        if (filterChildern.length) {
          item.children = [...filterChildern]
          return true
        }
      }
    })
    treeDataList.value = [...filterList]
    console.log('左侧树型数据 === ', treeDataList.value)
    handleActiveTree()
  } catch (error) {
    console.error('api树型数据错误', error)
  }
}

const activeTreeKey = ref<any>(null)
const handleActiveTree = () => {
  try {
    let list: any = treeDataList.value
    let activeItem = ''
    if (list[0] && list[0].children.length) {
      activeItem = list[0].children[0].onlyPathKey
      apiInfoData.value = list[0].children[0]
    } else {
      activeItem = list[0].onlyPathKey
      apiInfoData.value = list[0]
    }
    activeTreeKey.value = [`${activeItem}`]
  } catch (error) {
  } finally {
  }
}

const filterNode = (value, data) => {
  if (!value) return true
  return data.label.includes(value)
}

const apiInfoData = ref<any>({})
const handleTreeClick = (treeItem) => {
  if (!treeItem?.children?.length) {
    // apiInfoData.value.mockData()
    console.log('treeItem === ', treeItem)
    apiInfoData.value = treeItem
  }
}
</script>

<style scoped>
.detail-info-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  .left-wrap,
  .right-wrap {
    height: 100%;
    overflow: auto;
  }
  .left-wrap {
    width: 250px;
    display: flex;
    flex-direction: column;
    .tree-block {
      margin-top: 15px;
      flex: 1;
      overflow: auto;
      .tree-item {
        width: 100%;
        display: flex;
        .tree-label {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .tree-icon {
          margin-left: 10px;
        }
      }
    }
  }
  .right-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .content-wrap {
      flex: 1;
      overflow: auto;
    }
  }
}
</style>
