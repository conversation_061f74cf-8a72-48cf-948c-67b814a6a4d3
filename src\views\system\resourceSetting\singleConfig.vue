<template>
  <Dialog
    v-model="dialogVisible"
    :title="'数据权限'"
    width="1200"
    destroy-on-close
    :close-on-click-modal="false"
    v-loading="loading"
    @full-screen-change="(_val) => fullScreenChange(_val)"
  >
    <el-card class="card mb-16px" shadow="never" v-loading="loading">
      <template #header>
        <div class="flex" style="justify-content: space-between; align-items: center">
          <el-input
            class="mt-16px"
            style="width: 40%"
            v-model="filterKey"
            clearable
            placeholder="请输入租户名进行搜索"
            @input="filterFn"
          >
            <template #prefix>
              <icon-ep-search class="ml-5px" />
            </template>
          </el-input>
          <div
            class="flex"
            style="justify-content: space-between; align-items: center"
            v-if="isEdit"
          >
            <div
              ><span>父子联动(选中父节点，自动选择子节点):</span>
              <el-switch v-model="checkStrictly" active-text="是" inactive-text="否" inline-prompt
            /></div>
            <el-button type="primary" @click="batchRevoke" class="ml-16px">批量撤销</el-button>
          </div>
        </div>
      </template>
      <!-- <el-input
        v-model="filterKey"
        clearable
        placeholder="请输入租户名进行搜索"
        @input="filterFn"
        class="mb-16px"
      /> -->
      <div :style="{ width: '100%', height: `${tableHeight}px` }">
        <el-auto-resizer>
          <template #default="{ height }">
            <el-table-v2
              :data="treeOptions"
              :height="height"
              ref="tableRef"
              :width="2400"
              row-key="id"
              :columns="columns"
              :expand-column-key="'name'"
              v-model:expanded-row-keys="expandedRowKeys"
              :default-expanded-row-keys="defaultExpandedRowKeys"
              size="small"
              :estimated-row-height="100"
              fixed
            />
          </template>
        </el-auto-resizer>
      </div>
    </el-card>
    <template #footer>
      <el-button @click="close">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
  <senceDialog ref="senceDialogRef" @submit="getSenceDialogSubmit" />
  <Dialog v-model="duplicateCheckVisible" :title="'确认'" width="1200">
    <div :style="{ width: '100%', height: `${tableHeight}px` }">
      <el-auto-resizer>
        <template #default="{ width, height }">
          <el-table-v2
            :data="authorizerRows"
            :height="height"
            :width="width"
            row-key="id"
            :columns="comfirmColumns"
            size="small"
            :estimated-row-height="100"
            fixed
          />
        </template>
      </el-auto-resizer>
    </div>
    <template #footer>
      <el-button @click="closeDuplicateCheck" :loading="loading">{{
        t('common.cancel')
      }}</el-button>
      <el-button @click="submitDuplicateCheck" type="primary" :loading="loading">确定</el-button>
    </template>
  </Dialog>
  <Dialog v-model="reasonVisible" :title="'撤销理由'">
    <el-input type="textarea" placeholder="请填写撤销理由" v-model="reason" />
    <template #footer>
      <el-button @click="closeReason" :loading="loading">{{ t('common.cancel') }}</el-button>
      <el-button @click="submitReason" type="primary" :loading="loading">确定</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'singleConfig'
})

const { t } = useI18n() // 国际化
const dialogVisible = ref(false)

const message = useMessage()

/************************************* 树形结构start *************************************************/
const expandedRowKeys = ref(['id'])
const defaultExpandedRowKeys = ref([])
const originExpandedRowKeys = ref([])

const columnWidth = ref(460)

const tableHeight = ref(360)

const fullScreenChange = (value) => {
  if (value) {
    const windowWidth = document.documentElement.offsetWidth
    columnWidth.value = windowWidth / 2
    nextTick(() => {
      const windowHeight = document.documentElement.offsetHeight
      tableHeight.value = windowHeight - 55 - 60 - 63
    })
  } else {
    columnWidth.value = 520
    nextTick(() => {
      tableHeight.value = 360
    })
  }
}

import { ElButton, ElCheckbox } from 'element-plus'

/********************************** 撤销相关start ***************************************************/

const authorizers: Ref<any[]> = ref([])
const authorizerRows: Ref<any[]> = ref([])

const reasonVisible = ref(false)

const signleRow = ref()

const isbatch = ref(false)

const selectRowData = (_rowData) => {
  if (authorizers.value.indexOf(_rowData.id) === -1) {
    authorizers.value.push(_rowData.id)
    authorizerRows.value.push(_rowData)
    _rowData.isSelect = true
    if (_rowData.children && _rowData.children) {
      //选中就把子级全都选中
      checkTree(_rowData.children, true)
    }
  } else {
    authorizerRows.value.splice(authorizers.value.indexOf(_rowData.id), 1)
    authorizers.value.splice(authorizers.value.indexOf(_rowData.id), 1)
    _rowData.isSelect = false
    if (_rowData.children && _rowData.children) {
      //选中就把子级全都选中
      checkTree(_rowData.children, false)
    }
  }
}

// 重复检查
const duplicateCheckVisible = ref(false)

const batchRevoke = async () => {
  isbatch.value = true
  if (authorizers.value.length === 0) {
    message.error('请选择租户')
    return
  }

  duplicateCheckVisible.value = true

  // reasonVisible.value = true
}

const closeDuplicateCheck = () => {
  duplicateCheckVisible.value = false
}

const submitDuplicateCheck = () => {
  if (authorizers.value.length === 0) {
    message.error('租户必须要有一个或以上！')
    return
  }
  reasonVisible.value = true
}

const revokePermi = async (_rowData) => {
  isbatch.value = false
  signleRow.value = _rowData
  await message.confirm('确定撤销？')
  reasonVisible.value = true
}

// 二次确认批量撤销时，删除某个租户
const delPermi = async (_rowData) => {
  selectRowData(_rowData)
}

const reason: Ref<string | undefined> = ref()

const closeReason = () => {
  reason.value = undefined
  reasonVisible.value = false
  isSelectAll.value = false
}

import { quickRevokeResourceAuthorizationAssist } from '@/api/system/resource'

const submitReason = async () => {
  if (!reason.value) {
    message.error('请输入撤销理由！')
    return
  }

  try {
    loading.value = true

    await quickRevokeResourceAuthorizationAssist({
      reason: reason.value,
      authorizers: isbatch.value ? authorizers.value : [signleRow.value.id],
      applicants: [rowValue.value.id]
    })
    message.success('撤销成功')
    authorizers.value = []
    closeReason()
    closeDuplicateCheck()
    originAuthIds.value = []
    managementCustomerDataScope.value = initTreeData(
      await getListTenantSceneTrees([rowValue.value.id])
    ) //指定客户数据权限

    treeOptions.value = managementCustomerDataScope.value.map((el) => {
      el.scene = []
      return el
    })
    originTreeOptions.value = deepClone(treeOptions.value)
    // 将树的第一层展开
    originTreeOptions.value.forEach((el) => {
      if (el.children && el.children.length > 0) {
        defaultExpandedRowKeys.value.push(rowValue.value.id)
      }
    })
  } finally {
    loading.value = false
  }
}

/********************************** 撤销相关end ***************************************************/

// true是都选上，false都不选，null没强制要求
const checkTree = (_list, _forceTrue: null | boolean = null, _isDefultExpand = false) => {
  _list.forEach((el) => {
    if (el.children && el.children.length > 0) {
      if (filterKey.value) {
        expandedRowKeys.value.push(el.id)
      }
      checkTree(el.children, _forceTrue, _isDefultExpand)
    }

    const disabled = !el.resources || (el.resources && el.resources.length === 0)

    if (!disabled) {
      if (_forceTrue === true && checkStrictly.value) {
        //如果不加父子联动就只选自己
        el.isSelect = true
        if (authorizers.value.indexOf(el.id) === -1) {
          // 带进选中id列表里
          authorizers.value.push(el.id)
          authorizerRows.value.push(el)
        }
      } else if (_forceTrue === false && checkStrictly.value) {
        el.isSelect = false
        if (authorizers.value.indexOf(el.id) !== -1) {
          // 踢出选中id列表
          authorizerRows.value.splice(authorizers.value.indexOf(el.id), 1)
          authorizers.value.splice(authorizers.value.indexOf(el.id), 1)
        }
      } else {
        if (authorizers.value.indexOf(el.id) === -1) {
          el.isSelect = false
        } else {
          el.isSelect = true
        }
      }
    }
  })
}

const isSelectAll = ref(false)

const changeSelectAll = () => {
  if (isSelectAll.value) {
    isSelectAll.value = false
    checkTree(treeOptions.value, false)
  } else {
    isSelectAll.value = true
    checkTree(treeOptions.value, true)
  }
}

const indeterminate = computed(() => {
  return authorizers.value.length > 0 && authorizers.value.length < originAuthIds.value.length
})

const columns = computed(() => {
  return [
    {
      key: 'selection',
      width: 50,
      cellRenderer: ({ rowData }) => (
        <ElCheckbox
          modelValue={rowData.isSelect}
          onChange={() => selectRowData(rowData)}
          disabled={!rowData.resources || (rowData.resources && rowData.resources.length === 0)}
          class="mt-4px mb-4px"
        />
      ),
      headerCellRenderer: () => (
        <ElCheckbox
          modelValue={isSelectAll.value}
          onChange={changeSelectAll}
          indeterminate={indeterminate.value}
        />
      )
    },
    {
      dataKey: 'name',
      key: 'name',
      width: 460,
      title: '租户',
      cellRenderer: ({ rowData }) => <span>{`${rowData.name}（${rowData.id}）`}</span>
    },
    {
      width: columnWidth.value,
      title: '权限信息',
      cellRenderer: ({ rowData }) => (
        <div style={{ minHeight: '20px', paddingTop: '10px', paddingBottom: '10px' }}>
          <div style={{ lineHeight: '20px' }} v-html={showResourceText(rowData.resourcesMap)}></div>
        </div>
      )
    },
    {
      width: 120,
      key: 'oprate',
      title: '操作',
      cellRenderer: ({ rowData }) => (
        <div>
          {!rowData.resources || (rowData.resources && rowData.resources.length === 0) ? (
            ''
          ) : (
            <ElButton link type="danger" onClick={() => revokePermi(rowData)}>
              撤销
            </ElButton>
          )}
        </div>
      )
    }
  ]
})

const comfirmColumns = computed(() => {
  return [
    {
      dataKey: 'name',
      key: 'name',
      width: 460,
      title: '租户',
      cellRenderer: ({ rowData }) => <span>{`${rowData.name}（${rowData.id}）`}</span>
    },
    {
      width: columnWidth.value,
      title: '权限信息',
      cellRenderer: ({ rowData }) => (
        <div style={{ minHeight: '20px', paddingTop: '10px', paddingBottom: '10px' }}>
          <div style={{ lineHeight: '20px' }} v-html={showResourceText(rowData.resourcesMap)}></div>
        </div>
      )
    },
    {
      width: 120,
      key: 'oprate',
      title: '操作',
      cellRenderer: ({ rowData }) => (
        <div>
          {!rowData.resources || (rowData.resources && rowData.resources.length === 0) ? (
            ''
          ) : (
            <ElButton link type="danger" onClick={() => delPermi(rowData)}>
              删除
            </ElButton>
          )}
        </div>
      )
    }
  ]
})

const senceMap = computed(() => {
  return sceneList.value.reduce((a, b) => {
    return { ...a, [b.value]: b }
  }, {})
})

const permiPointMap = computed(() => {
  return getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce((a, b) => {
    return { ...a, [b.value]: b.hasOwnProperty('label') ? b.label : b.value }
  }, {})
})

const showResourceText = (_resources) => {
  if (JSON.stringify(_resources) === '{}' || !_resources) {
    return '-'
  } else {
    const keys = Object.keys(_resources)
    let hasNotResources = false
    let strArr = []
    keys.forEach((el) => {
      if (_resources[el] && _resources[el].length > 0) {
        strArr.push(
          `<p><span style="font-weight:800">${
            senceMap.value[el] ? senceMap.value[el].label : el
          }</span><span class="blue-txt" style="color: var(--el-color-primary)">【${_resources[el]
            .map((el) => permiPointMap.value[el])
            .join('')}】</span></p>`
        )
      }
    })
    if (strArr.length === 0) {
      return '-'
    } else {
      return strArr.join('')
    }
  }
}

import senceDialog from './senceDialog.vue'

const senceDialogRef = ref()

const configSence = (_rowData) => {
  senceDialogRef.value.open(_rowData, sceneList.value)
}

const sceneList = computed(() => {
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE) || []
})

const originAuthIds: Ref<string[]> = ref([])

// 初始化处理树结构数据
const initTreeData = (tree) => {
  return tree.map((el) => {
    el.isSelect = false
    if (el.children) {
      el.children = initTreeData(el.children)
    }
    if (!el.resources) {
      el.resourcesMap = {}
    } else {
      originAuthIds.value.push(el.id)
      let tempResourcesMap = {}
      el.resources.forEach((e) => {
        if (!tempResourcesMap[e.scene]) {
          tempResourcesMap[e.scene] = [e.permissionType]
        } else {
          tempResourcesMap[e.scene].push(e.permissionType)
        }
      })
      el.resourcesMap = tempResourcesMap
    }
    sceneList.value.forEach((e) => {
      if (!el.resourcesMap[e.value]) {
        el.resourcesMap[e.value] = []
      }
    })

    if (!el.sence) {
      el.sence = []
    }

    return el
  })
}

const managementCustomerDataScope = ref()

const treeOptions = ref<any[]>([]) // 租户树形结构

/************************************* 树形结构end *************************************************/
/************************************************** 搜索过滤start ******************************************************************/

const originTreeOptions = ref<any[]>([]) // 原租户树形结构

// 进行关键字进一步搜索
import { debounce, cloneDeep } from 'lodash-es'

const filterKey = ref('') //进一步搜索的关键字

// const filterFn = debounce((_val) => {
//   if (filterKey.value) {
//     // 只在有关键字时进行深拷贝和过滤
//     treeOptions.value = filterListFn(cloneDeep(unref(originTreeOptions.value)), filterKey.value)
//   } else {
//     // 没有关键字时，使用缓存list
//     treeOptions.value = cloneDeep(originTreeOptions.value)
//   }
// }, 500)

// 递归过滤下拉菜单数据
const filterListFn = (_arr, _searchName) => {
  return _arr.filter((el) => {
    const isUnMatchName = el.name.indexOf(_searchName) === -1
    if (!isUnMatchName) {
      // 匹配上名字，且有子节点时，递归过滤子节点
      if (el.children && el.children.length > 0) {
        el.children = filterListFn(el.children, _searchName)
      }
      return true // 匹配上名字的元素不过滤
    } else {
      // 没匹配上名字，但有子节点时，递归过滤子节点
      if (el.children && el.children.length > 0) {
        el.children = filterListFn(el.children, _searchName)
        // 仅当过滤后的子节点为空时，才过滤当前节点
        return el.children.length > 0
      }
      return false // 没有匹配上名字且没有子节点或子节点过滤完的元素被过滤
    }
  })
}

/************************************************** 搜索过滤end ******************************************************************/

const authorizeMap = ref({})

const getSenceDialogSubmit = (_id, _rowData) => {
  authorizeMap.value[_id] = _rowData
  changeOriginList(originTreeOptions.value, _id, _rowData)
}

const changeOriginList = (_list, _id, _rowData) => {
  for (let i in _list) {
    if (_list[i].id === _id) {
      _list[i].resourcesMap = deepClone(_rowData.resourcesMap)
      return
    }
    if (_list[i].children && _list[i].children.length > 0) {
      changeOriginList(_list[i].children, _id, _rowData)
    }
  }
}

const submit = () => {}

import { getListTenantSceneTrees, getListTenantSceneTreesProxy } from '@/api/system/resource'
import { deepClone } from '@/utils/deep'

const loading = ref(false)

const rowValue = ref()

const route = useRoute()

const isEdit = computed(() => route.query.isEdit)

const open = async (row, _tenantList) => {
  rowValue.value = undefined
  reason.value = undefined
  filterKey.value = ''
  rowValue.value = row
  defaultExpandedRowKeys.value = []
  originExpandedRowKeys.value = []
  dialogVisible.value = true
  authorizers.value = []
  authorizerRows.value = []
  try {
    loading.value = true
    originAuthIds.value = []
    managementCustomerDataScope.value = initTreeData(
      await getListTenantSceneTreesProxy([row.id], isEdit.value ? _tenantList : [])
    ) //指定客户数据权限

    treeOptions.value = managementCustomerDataScope.value.map((el) => {
      el.scene = []
      return el
    })
    originTreeOptions.value = deepClone(treeOptions.value)
    // 将树的第一层展开
    originTreeOptions.value.forEach((el) => {
      if (el.children && el.children.length > 0) {
        defaultExpandedRowKeys.value.push(el.id)
        originExpandedRowKeys.value.push(el.id)
      }
    })
  } finally {
    loading.value = false
  }
}

/**-------------------------------------- 父子联动start --------------------------------------------------*/

const checkStrictly = ref(true)

const filterFn = debounce((_val) => {
  if (filterKey.value) {
    // 只在有关键字时进行深拷贝和过滤
    treeOptions.value = filterListFn(cloneDeep(unref(originTreeOptions.value)), filterKey.value)
  } else {
    // 没有关键字时，使用缓存list
    treeOptions.value = cloneDeep(originTreeOptions.value)
  }
  nextTick(() => {
    if (!filterKey.value) {
      expandedRowKeys.value = [...originExpandedRowKeys.value]
    } else {
      expandedRowKeys.value = []
    }

    checkTree(treeOptions.value, null, true)
  })
}, 500)

/**-------------------------------------- 父子联动end --------------------------------------------------*/

defineExpose({
  open
})

const close = () => {
  dialogVisible.value = false
}
</script>
<style lang="scss" scoped>
.blue-txt {
  color: 'var(--el-color-primary)';
}
</style>
