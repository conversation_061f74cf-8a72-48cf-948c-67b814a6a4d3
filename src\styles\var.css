:root {
  --gold-color: #e2a32c;

  --dark-bg-color: #293146;

  /* left menu start */
  --left-menu-border-color: '#eee';

  --left-menu-max-width: 200px;

  --left-menu-en-max-width: 360px;

  --left-menu-min-width: 64px;

  --left-menu-bg-color: var(--el-color-primary);

  --left-menu-bg-light-color: #fff;

  --left-menu-bg-active-color: var(--el-color-primary);

  --left-menu-text-color: #bfcbd9;

  --left-menu-text-active-color: #fff;

  --left-menu-collapse-bg-active-color: var(--el-color-primary);
  /* left menu end */

  /* logo start */
  --logo-height: 50px;

  --logo-title-text-color: #fff;

  --logo-border-color: 'inherit';
  /* logo end */

  /* header start */

  --top-header-bg-color: var(--el-color-primary-dark-2);

  --top-header-text-color: #fff;

  --top-header-hover-color: var(--el-color-primary-light-2);

  --top-tool-height: var(--logo-height);

  --top-tool-p-x: 0;

  --top-tool-border-color: var(--el-color-primary-dark-2);

  --tags-view-height: 35px;

  --tags-view-border-color: #eee;
  /* header start */

  /* tab menu start */
  --tab-menu-max-width: 80px;

  --tab-menu-min-width: 30px;

  --tab-menu-collapse-height: 36px;

  --tab-menu-border-color: #eee;
  /* tab menu end */

  --app-content-padding: 20px;

  --app-contnet-bg-color: #f5f7f9;

  --app-footer-height: 50px;

  --transition-time-02: 0.2s;

  --goldpace-color: #2482f4;
  --el-color-primary: #2482f4; /*rgba(65, 105, 225, 1)*/
  --el-color-primary-light-1: #3a8ff5;
  --el-color-primary-light-2: #509be6;
  --el-color-primary-light-3: #66a8f7;
  --el-color-primary-light-4: #7cb4f8;
  --el-color-primary-light-5: #92c1f9;
  --el-color-primary-light-6: #a8cdfa;
  --el-color-primary-light-7: #beddfb;
  --el-color-primary-light-8: #d4e6fc;
  --el-color-primary-light-9: #eaeffd;
  --el-color-primary-dark-2: #1d68c3; /*比主色深20%*/
  --el-color-primary-dark-4: #164eb6; /*比主色深40%*/

  --client-color-primary: #2482f4; /**客户端的主题色*/
  --client-color-primary-light-1: #3a8ff5;
  --client-color-primary-light-2: #509be6;
  --client-color-primary-light-3: #66a8f7;
  --client-color-primary-light-4: #7cb4f8;
  --client-color-primary-light-5: #92c1f9;
  --client-color-primary-light-6: #a8cdfa;
  --client-color-primary-light-7: #beddfb;
  --client-color-primary-light-8: #d4e6fc;
  --client-color-primary-light-9: #eaeffd;
}

html,
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.login-main {
  --el-color-primary: var(--client-color-primary);
  --el-button-hover-border-color: var(--client-color-primary-light-3) !important;
  --el-button-hover-bg-color: var(--client-color-primary-light-7) !important;
}
