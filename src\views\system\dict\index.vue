<template>
  <!-- 列表 -->
  <UmvContent>
    <!-- 搜索工作栏 -->
    <UmvQuery v-model="queryParams" :opts="queryOpts" @check="handleQuery" @reset="handleQuery" />

    <UmvTable v-loading="loading" :data="list" :columns="columns" ref="tableRef" @refresh="getList">
      <template #tools>
        <el-button
          v-hasPermi="['system:dict:create']"
          plain
          size="small"
          type="primary"
          @click="openForm('create')"
        >
          <icon-ep-plus class="mr-5px" style="font-size: 12px" />
          新增
        </el-button>
        <el-button
          v-hasPermi="['system:dict:export']"
          :loading="exportLoading"
          plain
          size="small"
          type="success"
          @click="handleExport"
        >
          <icon-ep-download class="mr-5px" style="font-size: 12px" />
          导出
        </el-button>
      </template>

      <template #pagination>
        <Pagination
          v-model:limit="queryParams.pageSize"
          v-model:page="queryParams.pageNo"
          :total="total"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单弹窗：添加/修改 -->
    <DictTypeForm ref="formRef" @success="getList" />
  </UmvContent>
</template>

<script lang="tsx" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as DictTypeApi from '@/api/system/dict/dict.type'
import DictTypeForm from './DictTypeForm.vue'
import download from '@/utils/download'
import { useDictStoreWithOut } from '@/store/modules/dict'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { checkPermi } from '@/utils/permission'
import UmvContent from '@/components/UmvContent'

defineOptions({ name: 'SystemDictType' })

const dictStore = useDictStoreWithOut()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 字典表格数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: '',
  type: '',
  status: undefined,
  createTime: []
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '字典名称',
    defaultVal: '',
    controlRender: (form) => (
      <el-input
        v-model={form.name}
        placeholder="请输入字典名称"
        clearable
        onKeyup={(e) => e.key === 'Enter' && handleQuery()}
      />
    )
  },
  type: {
    label: '字典类型',
    defaultVal: '',
    controlRender: (form) => (
      <el-input
        v-model={form.type}
        placeholder="请输入字典类型"
        clearable
        onKeyup={(e) => e.key === 'Enter' && handleQuery()}
      />
    )
  },
  status: {
    label: '状态',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.status} placeholder="请选择字典状态" clearable>
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  createTime: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form) => (
      <el-date-picker
        v-model={form.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        clearable
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
      />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: '字典编号', align: 'center' },
  { prop: 'name', label: '字典名称', align: 'center', showOverflowTooltip: true },
  { prop: 'type', label: '字典类型', align: 'center', width: '300' },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    renderTemplate: (scope) => <dict-tag type={DICT_TYPE.COMMON_STATUS} value={scope.row.status} />
  },
  { prop: 'remark', label: '备注', align: 'center' },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        {checkPermi(['system:dict:update']) && (
          <el-button link type="primary" onClick={() => openForm('update', scope.row.id)}>
            修改
          </el-button>
        )}
        <router-link to={'/system/dict/type/data/' + scope.row.type}>
          <el-button link type="primary">
            数据
          </el-button>
        </router-link>
        {checkPermi(['system:dict:delete']) && (
          <el-button link type="danger" onClick={() => handleDelete(scope.row.id)}>
            删除
          </el-button>
        )}
      </div>
    )
  }
])

const queryFormRef = ref() // 搜索的表单
const tableRef = ref() // 表格引用
const exportLoading = ref(false) // 导出的加载中

/** 查询字典类型列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DictTypeApi.getDictTypePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DictTypeApi.deleteDictType(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
    await dictStore.resetDict()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DictTypeApi.exportDictType(queryParams.value)
    download.excel(data.data, '字典类型.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
