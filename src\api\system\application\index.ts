import request from '@/config/axios'

// 获取套餐接口
export const getTenantPackage = async (params?) => {
  return await request.get({ url: '/system/tenant-package/page', params })
}

// 添加套餐接口
export const addTenantPackage = async (data) => {
  return await request.post({ url: '/system/tenant-package/create', data })
}

// 配置套餐接口
export const setTenantPackage = async (data) => {
  return await request.post({ url: '/system/tenant-package/addPackageMenu', data })
}

// 获取已订阅的套餐接口
export const getSubTenantPackage = async (params?) => {
  return await request.get({ url: '/system/tenant-package/getSubscription', params })
}

// 订阅套餐接口
export const subscribePackage = async (data) => {
  return await request.post({ url: '/system/tenant-package/subscribePackage', data })
}

// 套餐已选菜单接口
export const getPackageMenus = async (params) => {
  return await request.get({ url: '/system/tenant-package/getPackageMenus', params })
}
