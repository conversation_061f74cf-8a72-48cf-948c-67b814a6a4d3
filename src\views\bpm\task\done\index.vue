<template>
  <UmvContent>
    <!-- 搜索工作栏 -->
    <template #search>
      <UmvQuery
        ref="queryFormRef"
        v-model="queryParams"
        :opts="queryOpts"
        @check="handleQuery"
        @reset="getList"
      />
    </template>

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="getList">
      <!-- 分页区域 -->
      <template #pagination>
        <Pagination
          v-model:limit="queryParams.pageSize"
          v-model:page="queryParams.pageNo"
          :total="total"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单弹窗：详情 -->
    <TaskDetail ref="detailRef" @success="getList" />
  </UmvContent>
</template>

<script setup lang="tsx">
defineOptions({
  name: 'BpmTodoTask'
})

import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'
import TaskDetail from './TaskDetail.vue'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'

const { push } = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: '',
  createTime: []
})

// 搜索条件配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '任务名称',
    defaultVal: '',
    controlRender: (form) => <el-input v-model={form.name} placeholder="请输入任务名称" clearable />
  },
  createTime: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form) => (
      <el-date-picker
        v-model={form.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
      />
    )
  }
})

const queryFormRef = ref() // 搜索的表单

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: '任务编号', align: 'center', width: '300px' },
  { prop: 'name', label: '任务名称', align: 'center' },
  { prop: 'processInstance.name', label: '所属流程', align: 'center' },
  { prop: 'processInstance.startUserNickname', label: '流程发起人', align: 'center' },
  {
    prop: 'result',
    label: '状态',
    align: 'center',
    renderTemplate: (scope) => (
      <dict-tag type={DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT} value={scope.row.result} />
    )
  },
  { prop: 'reason', label: '原因', align: 'center' },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    renderTemplate: (scope) => (
      <>
        <el-button link type="primary" onClick={() => openDetail(scope.row)}>
          详情
        </el-button>
        <el-button link type="primary" onClick={() => handleAudit(scope.row)}>
          流程
        </el-button>
      </>
    )
  }
])

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getDoneTaskPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (row: TaskApi.TaskVO) => {
  detailRef.value.open(row)
}

/** 处理审批按钮 */
const handleAudit = (row) => {
  push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id,
      taskId: row.id
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
