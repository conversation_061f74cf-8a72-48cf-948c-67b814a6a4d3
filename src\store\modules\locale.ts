/*
 * @Author: HoJack <EMAIL>
 * @Date: 2025-01-23 09:35:42
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2025-02-18 13:57:30
 * @Description:
 */
import { defineStore } from 'pinia'
import { store } from '../index'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import zhTw from 'element-plus/es/locale/lang/zh-tw'

import en from 'element-plus/es/locale/lang/en'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { LocaleDropdownType } from '@/types/localeDropdown'

const { wsCache } = useCache()

const elLocaleMap = {
  'zh-CN': zhCn,
  'zh-TW': zhTw,
  en: en
}
interface LocaleState {
  currentLocale: LocaleDropdownType
  localeMap: LocaleDropdownType[]
}
/**
 * 获取默认语言类型 (国际版本则返回 'en')
 *
 * @returns 返回语言类型，如果开启了国际化则返回 'en'，否则返回缓存中的语言类型或默认 'zh-CN'
 */
const getDefaultLang = (): LocaleType => {
  if (import.meta.env.VITE_APP_INTERNATIONAL === 'en_US') {
    return wsCache.get('lang') || 'en'
  }
  return wsCache.get('lang') || 'zh-CN'
}

export const useLocaleStore = defineStore('locales', {
  state: (): LocaleState => {
    return {
      currentLocale: {
        lang: getDefaultLang(),
        elLocale: elLocaleMap[getDefaultLang()]
      },
      // 多语言
      localeMap: [
        {
          lang: 'zh-CN',
          name: '简体中文'
        },
        {
          lang: 'zh-TW',
          name: '繁體中文'
        },
        {
          lang: 'en',
          name: 'English'
        }
      ]
    }
  },
  getters: {
    getCurrentLocale(): LocaleDropdownType {
      return this.currentLocale
    },
    /**
     * 获取地区映射数组
     *
     * @returns 返回地区映射数组(国际版本则过滤 'zh-CN'，否则过滤 'en')
     */
    getLocaleMap(): LocaleDropdownType[] {
      return import.meta.env.VITE_APP_INTERNATIONAL === 'en_US'
        ? this.localeMap.filter((item) => item.lang !== 'zh-CN')
        : this.localeMap.filter((item) => item.lang !== 'en')
    }
  },
  actions: {
    setCurrentLocale(localeMap: LocaleDropdownType) {
      // this.locale = Object.assign(this.locale, localeMap)
      this.currentLocale.lang = localeMap?.lang
      this.currentLocale.elLocale = elLocaleMap[localeMap?.lang]
      wsCache.set(CACHE_KEY.LANG, localeMap?.lang)
    }
  }
})

export const useLocaleStoreWithOut = () => {
  return useLocaleStore(store)
}
