# UmvTable 增强型表格组件

基于Element Plus的增强型表格组件，支持全屏、列设置、斑马纹、边框等功能。是对基础表格组件的扩展，提供了更多的功能和样式选项。

## 功能特点

- 支持表格标题设置
- 支持全屏显示
  - 当在ContentWrap组件中使用时，全屏功能会自动对ContentWrap进行全屏
  - 当在普通容器中使用时，全屏功能仅对UmvTable进行全屏
- 支持列显示控制与拖拽排序
- 支持斑马纹切换
- 支持边框显示控制
- 支持表格尺寸快速切换
- 支持自定义插槽扩展
- 支持TSX/JSX渲染函数（推荐用于操作列）

## 组件布局

UmvTable 组件的头部区域布局如下：
- 左侧：自定义工具栏 (通过 `tools` 插槽定义)
- 中间：表格标题 (通过 `title` 属性设置)
- 右侧：表格操作按钮组 (内置的功能按钮，如刷新、全屏、列设置等)

## 布局注意事项

**重要提示**：当UmvTable没有自适应填充UmvContent高度时，请确保包裹UmvQuery和UmvTable的元素具有`h-full !flex flex-col`这样的属性。这样可以使UmvTable正确撑满父容器。

```vue
<template>
  <!-- 正确的包裹方式 -->
  <div class="h-full !flex flex-col">
    <umv-query />
    <umv-table :data="tableData" :columns="columns" />
  </div>
</template>
```

通过以上方式，即使在复杂布局中，UmvTable也能正确填充UmvContent的高度。

## 定义表格列的方式

UmvTable 组件支持三种方式定义表格列，但不要混用这些方式:

1. 通过 `columns` 属性定义列（支持列设置功能）
2. 在默认插槽中使用 `el-table-column` 组件（支持复杂表头）
3. **【推荐】** 通过 `columns` 属性结合 `renderTemplate` 定义列（TSX渲染，最灵活）

> **重要提示**: 当组件检测到默认插槽中有 `el-table-column` 组件时，将会忽略 `columns` 属性中定义的列。

### 方式1: 使用 columns 属性 + slot插槽（不推荐用于操作列）

```vue
<template>
  <umv-table
    title="用户列表"
    :data="tableData"
    :columns="columns"
  >
    <!-- 自定义工具栏 -->
    <template #tools>
      <el-button type="primary" size="small">导出Excel</el-button>
    </template>
    
    <!-- 自定义操作列内容 (不推荐，应使用renderTemplate) -->
    <template #operation="{ row }">
      <el-button type="primary" link>编辑</el-button>
    </template>
  </umv-table>
</template>

<script setup>
import { ref } from 'vue'

const columns = ref([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  { prop: 'operation', label: '操作', slot: 'operation', width: '150px' }
])
</script>
```

### 方式2: 使用 el-table-column 定义列

```vue
<template>
  <umv-table 
    title="用户列表"
    :data="tableData"
  >
    <!-- 自定义工具栏 -->
    <template #tools>
      <el-button type="primary" size="small">批量操作</el-button>
    </template>
    
    <!-- 表格列定义 -->
    <el-table-column prop="id" label="ID" width="80" />
    <el-table-column prop="name" label="姓名" min-width="120" />
    <el-table-column label="操作" width="150">
      <template #default="{ row }">
        <el-button type="primary" link>编辑</el-button>
      </template>
    </el-table-column>
  </umv-table>
</template>
```

### 【推荐】方式3: 使用 columns 属性 + renderTemplate（最佳实践）

```tsx
<template>
  <umv-table
    title="用户列表"
    :data="tableData"
    :columns="columns"
  >
    <!-- 只需要定义工具栏，操作列通过renderTemplate实现 -->
    <template #tools>
      <el-button type="primary" size="small">导出Excel</el-button>
    </template>
  </umv-table>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import UmvTable from '@/components/UmvTable'
import { ElButton } from 'element-plus'
import { Edit, Delete, View } from '@element-plus/icons-vue'
import type { TableColumn } from '@/components/UmvTable/src/types'

const tableData = ref([
  { id: 1, name: '张三', age: 25, address: '北京市朝阳区' },
  { id: 2, name: '李四', age: 30, address: '上海市浦东新区' },
  { id: 3, name: '王五', age: 28, address: '广州市天河区' }
])

// 表格列配置，使用renderTemplate定义操作列
const columns = ref<TableColumn[]>([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  { prop: 'age', label: '年龄', width: '80px' },
  { prop: 'address', label: '地址', minWidth: '200px', showOverflowTooltip: true },
  { 
    prop: 'operation', 
    label: '操作',  
    fixed: 'right', 
    width: '200px',
    // 使用renderTemplate定义操作列（推荐）
    renderTemplate: (scope) => (
      <div class="flex justify-center space-x-2">
        <ElButton type="primary" size="small" link onClick={() => handleView(scope.row)}>
          <el-icon class="mr-1"><View /></el-icon>
          查看
        </ElButton>
        <ElButton type="primary" size="small" link onClick={() => handleEdit(scope.row)}>
          <el-icon class="mr-1"><Edit /></el-icon>
          编辑
        </ElButton>
        <ElButton type="danger" size="small" link onClick={() => handleDelete(scope.row)}>
          <el-icon class="mr-1"><Delete /></el-icon>
          删除
        </ElButton>
      </div>
    )
  }
])

// 操作方法直接在同一文件中定义
const handleView = (row) => {
  console.log('查看详情:', row)
}

const handleEdit = (row) => {
  console.log('编辑行:', row)
}

const handleDelete = (row) => {
  console.log('删除行:', row)
}
</script>
```

## 操作列的最佳实践

**强烈推荐使用renderTemplate而非slot插槽来定义操作列**。这是因为:

1. **类型安全** - 直接在TSX中访问行数据，获得完整的类型提示
2. **内联定义** - 无需在模板和脚本之间切换，提高开发效率
3. **更灵活的逻辑** - 可以在渲染函数中直接使用复杂的JavaScript逻辑
4. **更好的可读性** - 操作列的渲染逻辑与列定义放在一起，代码更加内聚

### renderTemplate用法详解

renderTemplate是一个函数，接收包含当前行数据的scope对象，返回JSX/TSX元素:

```tsx
{
  prop: 'status',
  label: '状态',
  renderTemplate: (scope) => (
    <ElTag type={scope.row.status ? 'success' : 'danger'}>
      {scope.row.status ? '启用' : '禁用'}
    </ElTag>
  )
}
```

scope对象包含以下属性:
- `row`: 当前行数据对象
- `column`: 当前列的配置信息
- `$index`: 当前行的索引

### renderTemplate的复杂示例

```tsx
// 条件渲染示例
{
  prop: 'status',
  label: '状态',
  renderTemplate: (scope) => {
    const status = scope.row.status
    const type = status === 'active' ? 'success' : 
                status === 'pending' ? 'warning' : 'danger'
    
    return (
      <ElTag type={type}>
        <el-icon class="mr-1">
          {status === 'active' ? <Check /> : <Close />}
        </el-icon>
        {status.toUpperCase()}
      </ElTag>
    )
  }
}

// 列表渲染示例
{
  prop: 'tags',
  label: '标签',
  renderTemplate: (scope) => (
    <div class="flex flex-wrap">
      {scope.row.tags.map((tag, index) => (
        <ElTag 
          key={index} 
          class="m-1" 
          effect="plain"
          closable
          onClose={() => handleRemoveTag(scope.row, tag)}
        >
          {tag}
        </ElTag>
      ))}
    </div>
  )
}
```



## 基础用法

```vue
<template>
  <umv-table
    title="用户列表"
    :data="tableData"
    :columns="columns"
    :disabled-column-keys="['id']"
    @refresh="handleRefresh"
  >
    <!-- 自定义工具栏 (位于最左侧) -->
    <template #tools>
      <el-button type="success" size="small">导出</el-button>
      <el-button type="warning" size="small">批量操作</el-button>
    </template>

    <!-- 自定义插槽 -->
    <template #action="{ row }">
      <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
      <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
    </template>

    <!-- 自定义分页 -->
    <template #pagination>
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </template>
  </umv-table>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { UmvTable, type TableColumn } from '@/components/UmvTable'

const tableData = ref([
  { id: 1, name: '张三', age: 25, address: '北京市朝阳区' },
  { id: 2, name: '李四', age: 30, address: '上海市浦东新区' },
  { id: 3, name: '王五', age: 28, address: '广州市天河区' }
])

const columns = ref<TableColumn[]>([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  { prop: 'age', label: '年龄', width: '80px' },
  { prop: 'address', label: '地址', minWidth: '200px' },
  { prop: 'action', label: '操作', slot: 'action', fixed: 'right', width: '200px' }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 处理刷新
const handleRefresh = () => {
  // 在这里重新加载表格数据
  console.log('刷新表格数据')
}

// 处理编辑操作
const handleEdit = (row: any) => {
  console.log('编辑行:', row)
}

// 处理删除操作
const handleDelete = (row: any) => {
  console.log('删除行:', row)
}
</script>
```

## 定义表格列的两种方式

UmvTable 组件支持两种方式定义表格列，但**不支持混用**这两种方式:

1. 通过 `columns` 属性定义列
2. 在默认插槽中使用 `el-table-column` 组件

> **重要提示**: 当组件检测到默认插槽中有 `el-table-column` 组件时，将会忽略 `columns` 属性中定义的列，并在控制台输出警告信息。请选择其中一种方式使用，避免混用。

### 方式1: 使用 columns 属性定义列

```vue
<template>
  <umv-table
    title="用户列表"
    :data="tableData"
    :columns="columns"
  >
    <!-- 自定义工具栏 -->
    <template #tools>
      <el-button type="primary" size="small">导出Excel</el-button>
    </template>
    
    <!-- 自定义操作列内容 -->
    <template #operation="{ row }">
      <el-button type="primary" link>编辑</el-button>
    </template>
  </umv-table>
</template>

<script setup>
import { ref } from 'vue'

const columns = ref([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  { prop: 'operation', label: '操作', slot: 'operation', width: '150px' }
])
</script>
```

此方式适合:
- 需要使用列设置功能，控制列的显示/隐藏和排序
- 列配置相对简单，可以通过对象配置表示
- 需要更统一的列样式管理

### 方式2: 使用 el-table-column 定义列

```vue
<template>
  <umv-table 
    title="用户列表"
    :data="tableData"
  >
    <!-- 自定义工具栏 -->
    <template #tools>
      <el-button type="primary" size="small">批量操作</el-button>
    </template>
    
    <!-- 表格列定义 -->
    <el-table-column prop="id" label="ID" width="80" />
    <el-table-column prop="name" label="姓名" min-width="120" />
    <el-table-column label="操作" width="150">
      <template #default="{ row }">
        <el-button type="primary" link>编辑</el-button>
      </template>
    </el-table-column>
  </umv-table>
</template>
```

此方式适合:
- 需要更复杂的列渲染逻辑，如嵌套表头、自定义渲染等
- 需要使用 Element Plus 的 `el-table-column` 组件的高级特性
- 不需要列设置功能（显示/隐藏和排序）

### 选择建议

1. 如果您需要使用列设置功能，建议使用 `columns` 属性
2. 如果您需要更复杂的表格结构，如多级表头、复杂的单元格渲染等，建议使用 `el-table-column`
3. 不要在同一个表格中混用两种方式，会导致渲染问题

## Props

### 主要属性

| 属性名              | 说明                 | 类型                   | 默认值 |
| ------------------ | ------------------- | --------------------- | ------ |
| title              | 表格标题             | string                | ''     |
| data               | 表格数据             | array                 | []     |
| columns            | 表格列配置           | TableColumn[]         | -      |
| disabledColumnKeys | 禁止控制显示隐藏的列 | string[]              | []     |
| defaultBorder      | 表格边框显示的默认值 | boolean               | true   |

### 列配置 (TableColumn)

| 属性名               | 说明                 | 类型                            | 默认值  |
| ------------------- | ------------------- | ------------------------------ | ------- |
| prop                | 列字段名             | string                         | -       |
| label               | 列标题               | string                         | -       |
| width               | 列宽度               | string                         | -       |
| minWidth            | 列最小宽度           | string                         | -       |
| fixed               | 列是否固定           | boolean / 'left' / 'right'     | -       |
| sortable            | 是否可排序           | boolean                        | -       |
| showOverflowTooltip | 内容超出时显示tooltip | boolean                        | -       |
| align               | 对齐方式             | 'left' / 'center' / 'right'    | 'center' |
| headerAlign         | 表头对齐方式         | 'left' / 'center' / 'right'    | 'center' |
| slot                | 自定义单元格内容的插槽名 | string                      | -       |
| headerSlot          | 自定义表头的插槽名    | string                         | -       |

此外，UmvTable 组件继承了 Element Plus 中 ElTable 的所有属性，你可以直接使用这些属性。

## 事件

| 事件名   | 说明                | 回调参数 |
| ------- | ------------------ | ------- |
| refresh | 点击刷新按钮时触发   | -       |

此外，UmvTable 组件继承了 Element Plus 中 ElTable 的所有事件。

## 插槽

| 插槽名         | 说明                |
| ------------- | ------------------ |
| tools         | 自定义左侧工具栏区域 |
| pagination    | 分页区域           |
| [column.slot] | 对应列的自定义内容  |
| [column.headerSlot] | 对应列的自定义表头 |

## 暴露的方法

UmvTable 组件暴露了以下方法，可以通过 ref 引用调用：

| 方法名              | 说明                               | 参数                          |
| ------------------ | ---------------------------------- | ---------------------------- |
| clearSelection     | 清空表格选择                        | -                            |
| toggleRowSelection | 切换某一行的选中状态                 | row, selected                |
| toggleAllSelection | 切换所有行的选中状态                 | -                            |
| toggleRowExpansion | 切换某一行的展开状态                 | row, expanded                |
| setCurrentRow      | 设置某一行为当前行                   | row                          |
| clearSort          | 清空排序                            | -                            |
| clearFilter        | 清空过滤条件                         | columnKeys                   |
| doLayout           | 重新计算表格布局                     | -                            |
| sort               | 手动对表格进行排序                   | prop, order                  |
| toggleFullscreen   | 切换全屏状态                         | -                            |
| toggleBorder       | 切换边框显示                         | -                            |
| setBorder          | 设置边框显示状态                     | value: boolean               |
| setSize            | 设置表格尺寸                         | value: 'small'/'default'/'large' |
| resetSettingColumns | 重置列设置                          | -                            |

## 使用示例

### 设置表格默认不显示边框

```vue
<template>
  <umv-table
    title="无边框表格"
    :data="tableData"
    :columns="columns"
    :default-border="false"
  >
    <template #tools>
      <el-button type="primary" size="small">添加数据</el-button>
    </template>
  </umv-table>
</template>

<script setup>
import { ref } from 'vue'

const tableData = ref([
  { id: 1, name: '张三', age: 25 },
  { id: 2, name: '李四', age: 30 }
])

const columns = ref([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  { prop: 'age', label: '年龄', width: '100px' }
])
</script>
```

### 通过ref控制表格边框显示

```vue
<template>
  <div>
    <el-button @click="toggleTableBorder">切换边框显示</el-button>
    <el-button @click="setTableBorder(true)">显示边框</el-button>
    <el-button @click="setTableBorder(false)">隐藏边框</el-button>
    
    <umv-table
      ref="tableRef"
      title="动态控制边框"
      :data="tableData"
      :columns="columns"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const tableRef = ref()
const tableData = ref([
  { id: 1, name: '张三', age: 25 },
  { id: 2, name: '李四', age: 30 }
])

const columns = ref([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  { prop: 'age', label: '年龄', width: '100px' }
])

// 切换边框显示
const toggleTableBorder = () => {
  tableRef.value.toggleBorder()
}

// 设置边框显示状态
const setTableBorder = (value) => {
  tableRef.value.setBorder(value)
}
</script>
```

### 带有自定义操作列的表格

```vue
<template>
  <umv-table
    title="用户管理"
    :data="tableData"
    :columns="columns"
    @refresh="loadData"
  >
    <!-- 自定义工具栏 -->
    <template #tools>
      <el-button type="primary" size="small">新增用户</el-button>
      <el-button type="success" size="small">导出数据</el-button>
    </template>
    
    <!-- 自定义操作列 -->
    <template #operation="{ row }">
      <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
      <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
    </template>
    
    <!-- 自定义分页 -->
    <template #pagination>
      <el-pagination
        v-model:current-page="page.current"
        v-model:page-size="page.size"
        :total="page.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  </umv-table>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UmvTable } from '@/components/UmvTable'

const tableData = ref([])
const columns = ref([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  { prop: 'age', label: '年龄', width: '100px' },
  { prop: 'email', label: '邮箱', minWidth: '180px' },
  { prop: 'operation', label: '操作', slot: 'operation', fixed: 'right', width: '150px' }
])

const page = ref({
  current: 1,
  size: 10,
  total: 0
})

// 加载数据
const loadData = () => {
  // 这里添加实际的数据加载逻辑
  console.log('加载数据，页码:', page.value.current, '每页数量:', page.value.size)
  
  // 模拟数据
  tableData.value = Array.from({ length: 10 }).map((_, index) => ({
    id: index + 1 + (page.value.current - 1) * page.value.size,
    name: `用户${index + 1}`,
    age: Math.floor(Math.random() * 40) + 18,
    email: `user${index + 1}@example.com`
  }))
  
  page.value.total = 100 // 模拟总数据量
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  page.value.current = val
  loadData()
}

// 处理每页数量变化
const handleSizeChange = (val: number) => {
  page.value.size = val
  page.value.current = 1 // 重置为第一页
  loadData()
}

// 编辑行
const handleEdit = (row: any) => {
  console.log('编辑行:', row)
  // 这里添加编辑逻辑
}

// 删除行
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认删除用户 ${row.name} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    console.log('删除行:', row)
    // 这里添加删除逻辑
    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除
  })
}

onMounted(() => {
  loadData()
})
</script>
```

## 在ContentWrap中使用UmvTable

UmvTable组件可以与ContentWrap组件配合使用，此时UmvTable的全屏功能会自动适配ContentWrap组件。

### ContentWrap + UmvTable配合使用示例

```vue
<template>
  <content-wrap title="用户管理" if-table>
    <umv-table :data="tableData" :columns="columns">
      <template #tools>
        <el-button type="primary" size="small">新增用户</el-button>
      </template>
    </umv-table>
  </content-wrap>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import ContentWrap from '@/components/ContentWrap'
import UmvTable from '@/components/UmvTable'
import type { TableColumn } from '@/components/UmvTable/src/types'

const tableData = ref([
  { id: 1, name: '张三', age: 25, address: '北京市朝阳区' },
  { id: 2, name: '李四', age: 30, address: '上海市浦东新区' }
])

const columns = ref<TableColumn[]>([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  { prop: 'age', label: '年龄', width: '80px' },
  { prop: 'address', label: '地址', minWidth: '200px' }
])
</script>
```

### 全屏功能说明

1. 当UmvTable在ContentWrap组件内部使用时：
   - 点击UmvTable的全屏按钮会触发ContentWrap组件的全屏模式
   - 全屏样式由ContentWrap组件控制
   - 按ESC键可退出全屏

2. 当UmvTable单独使用时：
   - 点击全屏按钮只会使UmvTable组件全屏
   - 全屏样式由UmvTable自身控制
   - 同样支持ESC键退出全屏

无论哪种方式，全屏功能都会自动调整表格布局以适应新的容器大小。