<template>
  <el-dialog
    title="预览"
    v-model="show"
    width="800px"
    append-to-body
    destroy-on-close
    class="approval-edit-form"
    @close="close"
  >
    <el-form
      class="-mb-15px"
      :model="detailValue"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item
        label="业务端"
        prop="sizeId"
        :rules="{ required: true, trigger: ['blur', 'change'], message: '请输入业务端' }"
        :style="{ width: '100%' }"
      >
        <el-select v-model="detailValue.sizeId" @change="changeSizeId" :style="{ width: '100%' }">
          <el-option
            v-for="(item, index) in clientList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="菜单权限">
        <el-card class="cardHeight" v-loading="formLoading">
          <el-tree
            ref="treeRef"
            :data="menuOptions"
            :props="defaultProps"
            empty-text="暂无数据"
            node-key="id"
          />
        </el-card>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ApprovalEditForm'
})

import { defaultProps, handleTree } from '@/utils/tree'
import { getSubscriptionMenu } from '@/api/system/serviceAudit'
import { getTenantClientPage } from '@/api/system/businessSide'
import * as MenuApi from '@/api/system/menu'

const show = ref(false)

const formLoading = ref(false)

// 关闭
const close = () => {
  detailValue.value = {
    sizeId: undefined
  }
  menuOptions.value = undefined
  show.value = false
}

const detailValue = ref({
  sizeId: undefined
})

const menuOptions = ref()

// 切换业务端
const changeSizeId = async (val) => {
  try {
    formLoading.value = true
    // 所有选中的
    const checkMap = await getSubscriptionMenu({
      tenantId: rowData.value.id,
      clientId: val
    })
    let tree = []
    for (let key in checkMap) {
      tree.push(...checkMap[key])
    }
    console.log('tree', tree)
    menuOptions.value = handleTree(tree)
  } finally {
    formLoading.value = false
  }
}

const rowData = ref()

const clientList = ref([])

const getClientList = async () => {
  // 后端限制最大100
  const res = await getTenantClientPage({ pageNo: 1, pageSize: 100 })
  clientList.value = res.list
}

const open = (row) => {
  getClientList()
  rowData.value = row
  show.value = true
}

defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 690px;
  max-height: 400px;
  overflow-y: auto;
}
</style>
