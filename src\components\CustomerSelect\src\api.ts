/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-13 14:12:51
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2024-12-25 14:33:12
 * @Description:
 */
import request from '@/config/axios'

// 获取客户列表（分页和总分行）
export const getCustomerPageApi = (params: any) => {
  return request.get({
    url: '/app-api/customer/customer/pageNamesNode',
    params
  })
}

// 获取客户信息
export const getCustomerInfoApi = (params: any): any => {
  return request.get({
    url: '/app-api/customer/customer/queryOne',
    params
  })
}
/**
 * 根据客户编码获取用户列表
 *
 * @param customerCode 客户编码
 * @returns 用户列表的Promise对象
 */
export function getUsersByCusCode(customerCode) {
  return request.post({
    url: `/app-api/customer/customer/getCustomerIdsByCondition`,
    data: {
      customerCode
    }
  })
}
