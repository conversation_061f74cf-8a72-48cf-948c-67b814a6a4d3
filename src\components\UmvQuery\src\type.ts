/**
 * 查询表单项基础配置接口
 * @interface QueryOptionBase
 */
export interface QueryOptionBase {
  /**
   * 表单项标签
   * @type {string}
   */
  label: string

  /**
   * 是否隐藏标签，设置为 true 时不显示 label
   * @type {boolean}
   */
  hideLabel?: boolean

  /**
   * 数据索引，对应表单数据对象的属性名
   * 如果不提供，将使用配置对象的键名
   * @type {string}
   */
  dataIndex?: string

  /**
   * 默认值
   * @type {any}
   */
  defaultVal: any

  /**
   * 事件处理函数或事件名称
   * @type {string | Function}
   */
  event?: string | Function

  /**
   * 事件处理函数对象
   * @type {Record<string, Function>}
   */
  eventHandle?: Record<string, Function>

  /**
   * 标签渲染函数，仅用于渲染表单项的标签部分
   * @type {Function}
   * @param {any} form 表单数据对象
   * @returns {any} 渲染结果，可以是VNode或HTML字符串
   */
  labelRender?: Function

  /**
   * 组件宽度
   * @type {string}
   */
  width?: string

  /**
   * 组件的自定义类名
   * @type {string}
   */
  className?: string

  /**
   * 栅格跨度
   * @type {number}
   * @default 1
   */
  span?: number

  /**
   * 标签宽度
   * @type {string}
   */
  labelWidth?: string

  /**
   * 控制表单项是否显示
   * @type {boolean | ((form: any) => boolean)}
   * @default true
   */
  visible?: boolean | ((form: any) => boolean)

  /**
   * 其他自定义属性
   */
  [key: string]: any
}

/**
 * 基于渲染函数的查询表单项配置
 * @interface RenderBasedQueryOption
 * @extends {QueryOptionBase}
 */
export interface RenderBasedQueryOption extends QueryOptionBase {
  /**
   * 控件渲染函数，用于TSX方式渲染整个表单控件
   * @param {QueryForm} form 表单数据对象
   * @returns {any} 渲染结果，通常是JSX/TSX表达式
   */
  controlRender: (form: QueryForm) => any
}

/**
 * 查询表单项配置接口
 * @type {RenderBasedQueryOption}
 */
export type QueryOption = RenderBasedQueryOption

/**
 * UmvQuery组件属性接口
 * @interface UmvQueryProps
 */
export interface UmvQueryProps {
  /**
   * 查询表单项配置对象，key为dataIndex，值为QueryOption对象
   * @type {Record<string, QueryOption>}
   */
  opts: Record<string, QueryOption>

  /**
   * 标签宽度
   * @type {string}
   * @default '120px'
   */
  labelWidth?: string

  /**
   * 查询按钮配置属性
   * @type {Record<string, any>}
   */
  btnCheckConfig?: Record<string, any>

  /**
   * 重置按钮配置属性
   * @type {Record<string, any>}
   */
  btnResetConfig?: Record<string, any>

  /**
   * 查询按钮加载状态
   * @type {boolean}
   * @default false
   */
  loading?: boolean

  /**
   * 是否显示重置按钮
   * @type {boolean}
   * @default true
   */
  reset?: boolean

  /**
   * 自定义屏幕宽度与列数的映射关系
   * @type {Record<string, number>}
   * @description 用于自定义不同屏幕宽度对应的列数，键为屏幕宽度，值为对应的列数。例如：{1920: 6, 1600: 5, 1280: 4, 1000: 3, 768: 2}
   */
  colLengthMap?: Record<string, number>

  /**
   * 是否支持回车查询
   * @type {boolean}
   * @default true
   */
  boolEnter?: boolean

  /**
   * 是否显示展开按钮
   * @type {boolean}
   * @default true
   */
  isShowOpen?: boolean

  /**
   * 是否默认展开所有查询条件
   * @type {boolean}
   * @default false
   */
  isExpansion?: boolean

  /**
   * 默认显示的行数
   * @type {number}
   * @default 1
   */
  maxVisibleRows?: number

  /**
   * 默认展示的搜索条件数量
   * @type {number}
   * @default undefined
   * @description 当设置此属性时，只会展示指定数量的搜索条件，其余条件会被隐藏并可通过展开按钮显示
   */
  visibleSearchNum?: number

  /**
   * 收起按钮文本
   * @type {string}
   * @default '收起'
   */
  packUpTxt?: string

  /**
   * 展开按钮文本
   * @type {string}
   * @default '展开'
   */
  unfoldTxt?: string

  /**
   * 是否显示底部按钮区域
   * @type {boolean}
   * @default true
   */
  isFooter?: boolean

  /**
   * 配置变更时是否重置表单
   * @type {boolean}
   * @default false
   */
  configChangedReset?: boolean

  /**
   * 是否显示自定义宽度
   * @type {boolean}
   * @default false
   */
  isShowWidthSize?: boolean

  /**
   * 自定义宽度大小
   * @type {number}
   * @default 4
   */
  widthSize?: number

  /**
   * 是否使用下拉方式选择更多条件
   * @type {boolean}
   * @default false
   */
  isDropDownSelectMore?: boolean

  /**
   * 更多条件选择列表
   * @type {any[]}
   * @default []
   */
  moreCheckList?: any[]

  /**
   * 更多条件弹出框属性
   * @type {Record<string, any>}
   * @default {}
   */
  popoverAttrs?: Record<string, any>

  /**
   * 是否显示列调整按钮
   * @type {boolean}
   * @default false
   */
  isShowColumn?: boolean

  /**
   * 使用 footerBtn 插槽时是否显示默认按钮
   * @type {boolean}
   * @default true
   */
  showDefaultButtons?: boolean

  /**
   * 是否显示查询按钮
   * @type {boolean}
   * @default true
   */
  check?: boolean

  /**
   * 表单验证规则
   * @type {Record<string, any>}
   * @default {}
   */
  rules?: Record<string, any>
}

/**
 * 查询表单数据接口
 * @interface QueryForm
 */
export interface QueryForm {
  [key: string]: any
}

/**
 * 事件处理函数接口
 */
export interface EventHandle {
  /**
   * 事件处理函数对象
   */
  eventHandle: any
}
