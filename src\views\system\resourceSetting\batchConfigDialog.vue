<template>
  <Dialog
    v-model="dialogVisible"
    :title="'代客申请授权'"
    width="1000"
    destroy-on-close
    :close-on-click-modal="false"
    :loading="loading"
    @full-screen-change="fullScreenChange"
  >
    <el-card class="mb-16px" shadow="never">
      <template #header>申请方</template>
      <div class="tenant-warp" style="width: 100%">
        <el-form :model="tenantList" ref="tenantFormRef">
          <el-table :data="tenantList" max-height="300" scrollbar-always-on>
            <el-table-column label="申请租户" prop="name" />
            <el-table-column label="默认被授权人员">
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="$index + '.employId'"
                  :rules="[
                    { required: true, message: '请选择默认被授权人员', trigger: ['blur', 'change'] }
                  ]"
                >
                  <el-select v-model="row.employId" placeholder="请选择默认被授权人员">
                    <el-option
                      v-for="(item, index) in tenantMap[row.id]"
                      :value="item.id"
                      :label="item.nickname"
                      :key="index"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作" prop="oprate" width="100">
              <template #default="{ row }">
                <el-button link @click="delRow(row)" type="danger">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>

        <!-- <div class="flex mb-16px" v-for="(item, index) in tenantList" :key="index">
          <div class="tenant-name" style="width: 300px">{{ item.name }}</div>
          <div class="tenant-employ" style="width: 300px">
            <el-select v-model="item.employId" placeholder="请选择授权人员">
              <el-option
                v-for="(item, index) in tenantMap[item.id]"
                :value="item.id"
                :label="item.nickname"
                :key="index"
              />
            </el-select>
          </div>
        </div> -->
      </div>
    </el-card>
    <el-card class="mb-16px" shadow="never">
      <template #header> 业务场景权限 </template>
      <div class="flex" style="justify-content: center; align-items: center">
        <div
          type="primary"
          @click="selectAuth"
          style="width: 500px; cursor: pointer; text-align: center"
          v-html="senceAuthText"
        >
        </div>
      </div>
    </el-card>
    <el-card class="card mb-16px" shadow="never" ref="authCardRef">
      <template #header>
        <div class="flex" style="justify-content: space-between; align-items: center"
          ><span>授权方</span>
          <el-button type="primary" @click="addLicensor">添加授权方</el-button>
          <!-- <div
            ><span>父子联动(选中父节点，自动选择子节点):</span>
            <el-switch
              v-model="checkStrictly"
              active-text="是"
              inactive-text="否"
              inline-prompt /></div
        > -->
        </div>
      </template>
      <!-- <el-input
        v-model="filterKey"
        clearable
        placeholder="请输入租户名进行搜索"
        @input="filterFn"
        class="mb-16px"
      /> -->
      <div style="width: 100%; height: 500px">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              :data="authorizersRow"
              :height="height"
              ref="tableRef"
              :width="width"
              row-key="id"
              :columns="columns"
              v-model:expanded-row-keys="expandedRowKeys"
              :default-expanded-row-keys="defaultExpandedRowKeys"
              size="small"
              fixed
            />
          </template>
        </el-auto-resizer>
      </div>
      <!-- <el-table :data="authorizersRow">
        <el-table-column label="" />
      </el-table> -->
    </el-card>
    <el-input
      v-model="applicationReason"
      clearable
      placeholder="请输入代客申请理由"
      type="textarea"
      class="mb-16px"
    />
    <el-input
      v-model="approvalOpinion"
      clearable
      placeholder="请输入代客审批通过意见"
      type="textarea"
      class="mb-16px"
    />
    <template #footer>
      <el-button type="primary" @click="submitUpgrate" :loading="loading">{{
        t('common.ok')
      }}</el-button>
      <el-button @click="close" :loading="loading">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
  <senceDialog ref="senceDialogRef" @submit="getSenceDialogSubmit" @confirm="confirmDialogSubmit" />
  <selectTenantDialog ref="selectTenantRef" @success="getSelectTenant" />
</template>
<script setup lang="tsx">
defineOptions({
  name: 'BatchConfigDialog'
})

import selectTenantDialog from './selectTenantDialog.vue'

const { t } = useI18n() // 国际化
const dialogVisible = ref(false)

// 被授权的租户列表
const tenantList = ref([])

// 代客申请理由
const applicationReason = ref('')

// 代客审批通过意见
const approvalOpinion = ref('同意（代客）')

// 选中的授权方租户
const authorizers = ref([])

const message = useMessage()

const checkStrictly = ref(true)

// 删除行
const delRow = async (row) => {
  if (tenantList.value.length === 1) {
    message.error('最少需要一个申请租户！')
    return
  }
  await message.confirm('确定删除该申请租户？')
  tenantList.value = tenantList.value.filter((el) => {
    return el.id !== row.id
  })
}

/************************************* 树形结构start *************************************************/
const expandedRowKeys = ref(['id'])
// 默认展开的租户，取第一层
const defaultExpandedRowKeys = ref([])

// true是都选上，false都不选，null没强制要求
const checkTree = (_list, _forceTrue: null | boolean = null) => {
  _list.forEach((el) => {
    if (el.children && el.children.length > 0) {
      checkTree(el.children, _forceTrue)
    }
    if (_forceTrue === true && checkStrictly.value) {
      //如果不加父子联动就只选自己
      el.isSelect = true
      if (authorizers.value.indexOf(el.id) === -1) {
        // 带进选中id列表里
        authorizers.value.push(el.id)
      }
    } else if (_forceTrue === false && checkStrictly.value) {
      el.isSelect = false
      if (authorizers.value.indexOf(el.id) !== -1) {
        // 踢出选中id列表
        authorizers.value.splice(authorizers.value.indexOf(el.id), 1)
      }
    } else {
      if (authorizers.value.indexOf(el.id) === -1) {
        el.isSelect = false
      } else {
        el.isSelect = true
      }
    }
  })
}

// const selectRowData = (_rowData) => {
//   if (authorizers.value.indexOf(_rowData.id) === -1) {
//     authorizers.value.push(_rowData.id)
//     _rowData.isSelect = true
//     if (_rowData.children && _rowData.children) {
//       //选中就把子级全都选中
//       checkTree(_rowData.children, true)
//     }
//   } else {
//     authorizers.value.splice(authorizers.value.indexOf(_rowData.id), 1)
//     _rowData.isSelect = false
//     if (_rowData.children && _rowData.children) {
//       //选中就把子级全都选中
//       checkTree(_rowData.children, false)
//     }
//   }
// }

const authCardRef = ref()

const authCardWidth = ref(800)

const tableRef = ref()

const authCardChangeWidth = computed(() => {
  if (authCardRef.value) {
    if (authCardRef.value.$el.offsetWidth) {
      return authCardRef.value.$el.offsetWidth
    } else {
      return 800
    }
  } else {
    return 800
  }
})

// watch(
//   () => authCardChangeWidth.value,
//   (_val) => {
//     console.log('_val', _val)
//     if (_val) {
//       authCardWidth.value = authCardChangeWidth.value
//     }
//   },
//   {
//     immediate: true,
//     deep: true
//   }
// )

const isFullscreen = ref(false)

const fullScreenChange = (_val) => {
  isFullscreen.value = _val
}

const isHiddent = ref(false)

const tableWidth = ref(0)

const delTenant = (_row) => {
  if (authorizers.value.indexOf(_row.id) === -1) {
  } else {
    authorizersRow.value.splice(authorizers.value.indexOf(_row.id), 1)
    authorizers.value.splice(authorizers.value.indexOf(_row.id), 1)
  }
}

const columns = computed(() => {
  return [
    // {
    //   key: 'selection',
    //   width: 50,

    //   cellRenderer: ({ rowData }) => (
    //     <ElCheckbox
    //       modelValue={rowData.isSelect}
    //       onChange={() => selectRowData(rowData)}
    //       class="mt-4px mb-4px"
    //     />
    //   )
    // },
    {
      dataKey: 'name',
      key: 'name',
      // width: authCardWidth.value,
      width: 640,
      title: '租户',
      cellRenderer: ({ rowData }) => <span>{`${rowData.name}（${rowData.id}）`}</span>
    },
    {
      dataKey: 'id',
      key: 'id',
      width: 200,
      title: '租户id'
    },
    {
      width: 60,
      title: '操作',
      cellRenderer: ({ rowData }) => (
        <ElButton link type="danger" onClick={() => delTenant(rowData)}>
          删除
        </ElButton>
      )
    }
    // {
    //   width: 460,
    //   title: '权限',
    //   cellRenderer: ({ rowData }) => (
    //     <div style={{ minHeight: '20px', paddingTop: '10px', paddingBottom: '10px' }}>
    //       <span
    //         style={{ color: 'var(--el-color-primary)', cursor: 'pointer', lineHeight: '20px' }}
    //         onClick={() => configSence(rowData)}
    //       >
    //         {showResourceText(rowData.resourcesMap)}
    //       </span>
    //     </div>
    //   )
    // }
  ]
})

const senceMap = computed(() => {
  return sceneList.value.reduce((a, b) => {
    return { ...a, [b.value]: b }
  }, {})
})

const permiPointMap = computed(() => {
  return getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

const showResourceText = (_resources) => {
  if (JSON.stringify(_resources) === '{}' || !_resources) {
    return '点击去勾选权限'
  } else {
    const keys = Object.keys(_resources)
    let hasNotResources = false
    let strArr = []
    keys.forEach((el) => {
      if (_resources[el] && _resources[el].length > 0) {
        strArr.push(
          `${senceMap.value[el].label}【${_resources[el]
            .map((el) => permiPointMap.value[el])
            .join('，')}】`
        )
      }
    })
    if (strArr.length === 0) {
      return '点击去勾选权限'
    } else {
      return strArr.join('、')
    }
  }
}

import senceDialog from './senceDialog.vue'

// 初始化处理树结构数据
const initTreeData = (tree) => {
  return tree.map((el) => {
    el.isSelect = false
    if (el.children) {
      el.children = initTreeData(el.children)
    }
    if (!el.resources) {
      el.resourcesMap = {}
    } else {
      let tempResourcesMap = {}
      el.resources.forEach((e) => {
        if (!tempResourcesMap[e.scene]) {
          tempResourcesMap[e.scene] = [e.permissionType]
        } else {
          tempResourcesMap[e.scene].push(e.permissionType)
        }
      })
      el.resourcesMap = tempResourcesMap
    }
    sceneList.value.forEach((e) => {
      if (!el.resourcesMap[e.value]) {
        el.resourcesMap[e.value] = []
      }
    })

    if (!el.sence) {
      el.sence = []
    }

    return el
  })
}

const managementCustomerDataScope = ref()

const treeOptions = ref<any[]>([]) // 租户树形结构

/************************************* 树形结构end *************************************************/
/************************************************** 搜索过滤start ******************************************************************/

const originTreeOptions = ref<any[]>([]) // 原租户树形结构

// 进行关键字进一步搜索
// import { debounce, cloneDeep } from 'lodash-es'

// const filterKey = ref('') //进一步搜索的关键字

// const filterFn = debounce((_val) => {
//   if (filterKey.value) {
//     // 只在有关键字时进行深拷贝和过滤
//     treeOptions.value = filterListFn(cloneDeep(unref(originTreeOptions.value)), filterKey.value)
//   } else {
//     // 没有关键字时，使用缓存list
//     treeOptions.value = cloneDeep(originTreeOptions.value)
//   }
//   nextTick(() => {
//     checkTree(treeOptions.value)
//   })
// }, 500)

// // 递归过滤下拉菜单数据
// const filterListFn = (_arr, _searchName) => {
//   return _arr.filter((el) => {
//     const isUnMatchName = el.name.indexOf(_searchName) === -1
//     if (!isUnMatchName) {
//       // 匹配上名字，且有子节点时，递归过滤子节点
//       if (el.children && el.children.length > 0) {
//         el.children = filterListFn(el.children, _searchName)
//       }
//       return true // 匹配上名字的元素不过滤
//     } else {
//       // 没匹配上名字，但有子节点时，递归过滤子节点
//       if (el.children && el.children.length > 0) {
//         el.children = filterListFn(el.children, _searchName)
//         // 仅当过滤后的子节点为空时，才过滤当前节点
//         return el.children.length > 0
//       }
//       return false // 没有匹配上名字且没有子节点或子节点过滤完的元素被过滤
//     }
//   })
// }

const authorizersRow: Ref<any[]> = ref([])

const getSelectTenant = ({ _authorizers, _authorizersRow }) => {
  authorizers.value = _authorizers
  authorizersRow.value = _authorizersRow
}

/************************************************** 搜索过滤end ******************************************************************/

const authorizeMap = ref({})

const getSenceDialogSubmit = (_id, _rowData) => {
  authorizeMap.value[_id] = _rowData
  changeOriginList(originTreeOptions.value, _id, _rowData)
}

/********************************************************* 选择场景start **********************************************************************/

// 选中的场景
const senceRes = ref([])

const confirmDialogSubmit = (_senceList) => {
  senceRes.value = _senceList
}

const authMap = computed(() => {
  return getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce((a, b) => {
    return {
      ...a,
      [b.value]: b
    }
  }, {})
})

// 反射进场景配置
const senceAuthText = computed(() => {
  console.log('senceRes.value', senceRes.value)
  if (senceRes.value.length === 0) {
    return `<span style="color: var(--el-color-primary);">点击去勾选权限</span>`
  } else {
    return senceRes.value
      .filter((el) => el.resource.length > 0)
      .map((el) => {
        return `<p><span style="font-weight:800">${
          el.label
        }</span><span style="color: var(--el-color-primary);">【${el.resource.map((item) => {
          return authMap.value[item].label
        })}】</span></p>`
      })
      .join('')
  }
})

const senceDialogRef = ref()

const configSence = (_rowData) => {
  senceDialogRef.value.open(_rowData, sceneList.value)
}

const sceneList = computed(() => {
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE) || []
})

// 选择场景
const selectAuth = () => {
  senceDialogRef.value.open(senceRes.value, sceneList.value)
}

/********************************************************* 选择场景end **********************************************************************/

const changeOriginList = (_list, _id, _rowData) => {
  for (let i in _list) {
    if (_list[i].id === _id) {
      _list[i].resourcesMap = deepClone(_rowData.resourcesMap)
      return
    }
    if (_list[i].children && _list[i].children.length > 0) {
      changeOriginList(_list[i].children, _id, _rowData)
    }
  }
}

// 校验是否所有的租户都选上了授权用户
const validTenant = () => {
  for (let i in tenantList.value) {
    if (!tenantList.value[i].employId) {
      message.error('所有的申请租户都需要选择被授权人员！')
      return false
    }
  }
  return true
}

import * as RoleApi from '@/api/system/role'
import { listTenantUsers, quickResourceExposeAndApproval } from '@/api/system/resource'
import { deepClone } from '@/utils/deep'
import { ElButton, ElCheckbox } from 'element-plus'

const tenantIdList: Ref<any[]> = ref([])

const tenantMap: Ref<any> = ref({})

const proxyTenantIds: Ref<any[]> = ref([])

const open = async (_tenantList, _proxyTenantIds) => {
  tenantIdList.value = []
  tenantList.value = []
  applicationReason.value = ''
  approvalOpinion.value = '同意（代客）'
  senceRes.value = []
  defaultExpandedRowKeys.value = []
  authorizers.value = []
  authorizersRow.value = []

  proxyTenantIds.value = _proxyTenantIds

  tenantList.value = _tenantList.map((el) => {
    tenantIdList.value.push(el.id)
    return {
      name: el.name,
      id: el.id,
      employId: undefined
    }
  })

  // 根据租户获取其底下的用户，选择一个用户作为被授权的用户
  const res = await listTenantUsers(tenantIdList.value)

  // 转化成字典，用于获取名字
  tenantMap.value = res.reduce((a, b) => {
    return {
      ...a,
      [b.tenantId]: b.users
    }
  }, {})

  dialogVisible.value = true

  managementCustomerDataScope.value = initTreeData(
    await RoleApi.getListResourceListTenantSceneTreesProxy('-1', _proxyTenantIds)
  ) //指定客户数据权限

  treeOptions.value = managementCustomerDataScope.value.map((el) => {
    el.scene = []
    return el
  })
  // 保存原数组，用于筛选（暂时用不着）
  originTreeOptions.value = deepClone(treeOptions.value)

  // 将树的第一层展开
  originTreeOptions.value.forEach((el) => {
    if (el.children && el.children.length > 0) {
      defaultExpandedRowKeys.value.push(el.id)
    }
  })
}

defineExpose({
  open
})

const close = () => {
  dialogVisible.value = false
}

const loading = ref(false)

const emit = defineEmits('success')

/**------------------- 申请方校验start ------------------------*/

const tenantFormRef = ref()

/**------------------- 申请方校验end ------------------------*/

const submit = async () => {
  await tenantFormRef.value.validate((_res, _map) => {
    if (_map) {
      const key = Object.keys(_map)[0]
      tenantFormRef.value.scrollToField(key)
    }
  })
  if (!validTenant()) return
  if (senceRes.value.length === 0) {
    message.error('请勾选业务场景权限！')
    return
  }
  if (authorizers.value.length === 0) {
    message.error('请选择授权方租户！')
    return
  }
  if (!applicationReason.value) {
    message.error('请输入代客申请理由！')
    return
  }
  if (!approvalOpinion.value) {
    message.error('请输入代客审批通过意见！')
    return
  }

  /**------------------- 校验结束 ------------------------*/

  let resources: any[] = []
  senceRes.value.map((el: any) => {
    el.resource.forEach((item) => {
      resources.push({
        scene: el.value,
        permissionType: item,
        resourceType: 2
      })
    })
  })

  // 被授权人信息
  const applicants = tenantList.value.map((el) => {
    let obj: any = {}
    obj.tenantId = el.id
    obj.defaultResourceHolder = el.employId
    for (let key in tenantMap.value[el.name]) {
      if (tenantMap.value[el.name][key].id === el.employId) {
        obj.defaultResourceHolderName = el.nickname
        break
      }
    }
    return obj
  })

  /**------------------- 转换为后端需要的格式结束 ------------------------*/

  try {
    loading.value = true
    await quickResourceExposeAndApproval({
      resources,
      applicationReason: applicationReason.value,
      approvalOpinion: approvalOpinion.value,
      applicants,
      authorizers: authorizers.value
    })
    message.success('提交成功')
    dialogVisible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

const selectTenantRef = ref()

// 添加授权方
const addLicensor = () => {
  selectTenantRef.value.open(authorizers.value, authorizersRow.value, proxyTenantIds.value)
}

/**------------------------------------------- 获取客户token代替客户操作 start -------------------------------------------------*/
// 获取token做准备

import { useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache('sessionStorage')

const proxyMap = computed(() => {
  return wsCache.get('batchAuthProxyTenants')
})

import { requestWithTokenAndTenantId } from '@/config/axios'

const applyProcess = async (_exposerId, { resources }) => {
  // 授权方生成角色并暴露资源
  try {
    console.log(`proxyMap.value[${_exposerId}]`, proxyMap.value[_exposerId])
    const exposeRes = await requestWithTokenAndTenantId({
      method: 'POST',
      token: proxyMap.value[_exposerId].accessToken,
      tenantId: _exposerId,
      url: '/system/resource/expose',
      data: { resources }
    })

    // 所有申请方同时申请
    const noProcessedApplyResList = await Promise.allSettled(
      tenantList.value.map((el) => {
        return requestWithTokenAndTenantId({
          method: 'POST',
          token: proxyMap.value[el.id].accessToken,
          tenantId: el.id,
          url: '/system/resource/apply',
          data: {
            exposeNo: exposeRes.data.exposeNo,
            authorizer: exposeRes.data.resourceOwnerId,
            defaultResourceHolder: el.employId,
            defaultResourceHolderName: el.name,
            applicationReason: applicationReason.value
          }
        })
      })
    )

    const applyResList = noProcessedApplyResList
      .filter((el) => {
        return el.status === 'fulfilled'
      })
      .map((el) => {
        return el.value.data
      })

    // 授权方将所有申请同时进行同意并审批
    await Promise.allSettled(
      applyResList.map((el) => {
        return requestWithTokenAndTenantId({
          method: 'POST',
          token: proxyMap.value[_exposerId].accessToken,
          tenantId: _exposerId,
          url: '/system/resource/approval',
          data: {
            applicationNo: el.applicationNo,
            result: 2,
            approvalOpinion: approvalOpinion.value
          }
        })
      })
    )
  } finally {
  }
}

// 升级版提交
const submitUpgrate = async () => {
  await tenantFormRef.value.validate((_res, _map) => {
    if (_map) {
      const key = Object.keys(_map)[0]
      tenantFormRef.value.scrollToField(key)
    }
  })
  if (!validTenant()) return
  if (senceRes.value.length === 0) {
    message.error('请勾选业务场景权限！')
    return
  }
  if (authorizers.value.length === 0) {
    message.error('请选择授权方租户！')
    return
  }
  if (!applicationReason.value) {
    message.error('请输入代客申请理由！')
    return
  }
  if (!approvalOpinion.value) {
    message.error('请输入代客审批通过意见！')
    return
  }

  /**------------------- 校验结束 ------------------------*/

  let resources: any[] = []
  senceRes.value.map((el: any) => {
    el.resource.forEach((item) => {
      resources.push({
        scene: el.value,
        permissionType: item,
        resourceType: 2
      })
    })
  })

  // 被授权人信息
  const applicants = tenantList.value.map((el) => {
    let obj: any = {}
    obj.tenantId = el.id
    obj.defaultResourceHolder = el.employId
    for (let key in tenantMap.value[el.name]) {
      if (tenantMap.value[el.name][key].id === el.employId) {
        obj.defaultResourceHolderName = el.nickname
        break
      }
    }
    return obj
  })

  /**------------------- 转换为后端需要的格式结束 ------------------------*/

  try {
    loading.value = true
    for (let i = 0; i < authorizers.value.length; i++) {
      // 以单个授权方为起点，授权方生成角色并暴露资源->所有申请方同时申请->授权方将所有申请同时进行同意并审批，这整个过程作为一个单位，进行遍历处理
      await applyProcess(authorizers.value[i], { resources })
    }
    message.success('操作成功')
    dialogVisible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

/**------------------------------------------- 获取客户token代替客户操作 end -------------------------------------------------*/
</script>
<style lang="scss" scoped>
.tenant-warp {
  :deep(.el-form-item--default) {
    margin-bottom: 0px;
  }
}
</style>
