import request from '@/config/axios'

// 获取代客操作申请列表
export const getProxyTenantOperationPage = async (params: PageParam) => {
  return await request.get({ url: '/system/proxy-tenant-operation/page', params })
}

// 新增代客操作申请（草稿）
export const createProxyTenantOperation = async (data) => {
  return await request.post({ url: '/system/proxy-tenant-operation/create', data })
}

// 提交代客操作申请
export const proxyTenantOperationSubmitApply = async (id) => {
  return await request.put({ url: `/system/proxy-tenant-operation/submit-apply/${id}` })
}

// 开始代客操作
export const proxyTenantOperationStart = async (id) => {
  return await request.put({ url: `/system/proxy-tenant-operation/start/${id}` })
}

// 结束代客操作
export const proxyTenantOperationFinish = async (id) => {
  return await request.put({ url: `/system/proxy-tenant-operation/finish/${id}` })
}

// 获取代客操作申请（单个）
export const getProxyTenantOperation = async (params) => {
  return await request.get({ url: '/system/proxy-tenant-operation/get', params })
}

// 修改代客操作申请列表
export const updateProxyTenantOperation = async (data) => {
  return await request.put({ url: '/system/proxy-tenant-operation/update', data })
}

// 删除代客操作申请
export const deleteProxyTenantOperation = async (params) => {
  return await request.delete({ url: '/system/proxy-tenant-operation/delete', params })
}

// 上传接口
export const minioUpload = async (data) => {
  return request.postOriginal({
    url: '/app-api/infra/minio/upload',
    data,
    headersType: 'application/x-www-from-urlencoded;charset=UTF-8',
    timeout: 240000
  })
}

// 上传多个接口
export const minioMultiUpload = async (data) => {
  return request.postOriginal({
    url: '/app-api/infra/minio/multi-file-upload',
    data,
    headersType: 'application/x-www-from-urlencoded;charset=UTF-8',
    timeout: 240000
  })
}

// 根据工单获取租户id和Token
export const proxyTenantOperationToken = async (id, params?) => {
  return await request.get({
    url: `/system/proxy-tenant-operation-token/list-by-operation-id/${id}`,
    params
  })
}

// export const uploadFileApi = (data: FormData, onUploadProgress): Promise<IResponse> => {
//   return request.postOriginal({
//     url: '/makecard/makeCardFile/uploadfileList',
//     data,
//     headersType: 'application/x-www-from-urlencoded;charset=UTF-8',
//     onUploadProgress,
//     timeout: 240000
//   })
// }
