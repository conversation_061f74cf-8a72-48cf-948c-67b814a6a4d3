<!-- 由于element-plus中selecttree组件对多数据tree支持不好,重新封装 -->
<template>
  <div class="customer-select flex w-full">
    <el-select
      ref="treeSelectRef"
      v-model="customerIdCp"
      clearable
      check-strictly
      filterable
      :placeholder="isSearchByName ? '请选择或输入客户名称' : placeholder"
      popper-class="customer-select-option"
      remote
      :remote-method="filterMethod"
      @clear="clearHandler"
      @change="changeHandler"
      @blur="blurHandler"
      @focus="focusHandler"
      :multiple="multiple"
      v-bind="$attrs"
    >
      <!-- <template #tag>
       <el-input v-model="" />
    </template> -->
      <template #header>
        <div class="flex items-center justify-between">
          <el-tooltip :content="searchParams.branchTypeFlag ? '展示全部银行' : '展示总行树形结构'">
            <el-switch
              v-model="searchParams.branchTypeFlag"
              class="pr-10px"
              inline-prompt
              style="--el-switch-on-color: #13ce66; --el-switch-off-color: #e6a23c"
              active-text="总行"
              inactive-text="全部"
              @change="changebranchTypeFlag"
            />
          </el-tooltip>

          <el-input
            v-if="searchText || noMore"
            v-model="filterKey"
            placeholder="请输入关键字进一步进行搜索"
            @input="filterFn"
            clearable
          />
          <!-- <div class="float-right">{{ searchText || noMore ? '👌' : '👇向下滚动加载更多👇' }}</div> -->
        </div>
      </template>
      <template #default>
        <el-option :value="currentNodeKey" :label="currentNodeLabel">
          <el-tree-v2
            class="customer-select-tree"
            id="tree_v2"
            ref="treeV2Ref"
            :data="list"
            :height="260"
            :props="{ label: 'customerName', children: 'children', value: 'customerId' }"
            :node-key="nodeKey"
            :value-key="nodeKey"
            :current-node-key="currentNodeKey"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="nodeClick"
            :expand-on-click-node="false"
            :filter-method="treeFilter"
            highlight-current
            v-infinite-scroll="loadMore"
            :infinite-scroll-delay="1000"
            :infinite-scroll-disabled="disabledScroll"
            :infinite-scroll-distance="50"
            :infinite-scroll-immediate="true"
          >
            <template #default="{ node }">
              <el-tag type="primary" v-if="node.data.branchType === '0'">总</el-tag>
              <el-tag v-else type="success">分</el-tag>
              <span
                class="font-medium"
                v-html="handleHeightLight(node.label, searchText, filterKey)"
              ></span>
            </template>
          </el-tree-v2>
        </el-option>
      </template>
    </el-select>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'CustomerSelect'
})

import { TreeNode } from 'element-plus/es/components/tree-v2/src/types'
import { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type'

/** 接收父级传入的参数 start */
const props = defineProps({
  multiple: {
    //是否多选
    type: Boolean,
    default: false //默认值
  },

  preOption: {
    // 预选中的选项，用于回显
    type: Array,
    default: () => []
  },
  isBranch: {
    type: Boolean,
    default: true //默认值
  },
  // isInputRemote: {
  //   //是否在输入时直接调用搜索
  //   type: Boolean,
  //   default: true //默认值
  // },
  customerId: {
    type: String,
    default: '' //默认值
  },
  customerCode: {
    type: String,
    default: '' //默认值
  },
  placeholder: {
    type: String,
    default: '请输入客户名称' //默认值
  },
  isSearchByName: {
    type: Boolean,
    default: false //默认值
  }
})

//tree逻辑
const nodeKey = ref<string>('customerId') //默认 customerId为标志
const currentNodeKey = ref('') //当前选中节点
const currentNodeLabel = ref('') //当前选中节点
const defaultExpandedKeys = ref<string[]>([]) //默认展开节点
const treeSelectRef = ref<HTMLElement | null>(null)
const nodeClick = (data: TreeNodeData, node: TreeNode) => {
  currentNodeKey.value = data.customerId
  currentNodeLabel.value = data.customerName
  customerIdCp.value = data.customerId
  customerId.value = data.customerId
  ifSelected.value = true
  ;(treeSelectRef.value as any).blur()
  emit('change', data.customerId)
}
// select 筛选方法 treeV2 refs
const treeV2Ref: any = ref<HTMLElement | null>(null)
const selectFilter = (query: string) => {
  treeV2Ref.value.filter(query)
}
// ztree-v2 筛选方法
const treeFilter = (query: string, node: TreeNode) => {
  return node.label?.indexOf(query) !== -1
}
// watch(
//   () => props.isSearchByName,
//   (val) => {
//     if (val) {
//       nodeKey.value = 'customerName'
//     } else {
//       nodeKey.value = 'customerId'
//     }
//   }
// )

const customerId = defineModel<any>()
const customerName = defineModel<any>('customerName')
const customerCode = defineModel<any>('customerCode')

const customerInfo = defineModel<any>('customerInfo')

const emit = defineEmits(['selectCustomer', 'change', 'clear'])

const list: Ref<any[]> = ref([]) //下拉菜单数据

import { CustomerTreeVO, useCustomerTree } from './useCustomerTree'
const customData = useCustomerTree()

const searchParams = ref({
  branchTypeFlag: false, //是否总行
  customerName: '',
  pageSize: 500,
  pageNum: 1
})

onBeforeMount(async () => {
  //初始化,是否总行
  searchParams.value.branchTypeFlag = props.isBranch
})

/**
 * 获取客户列表
 *
 * @param _searchName 搜索名称，默认为空字符串
 * @returns 无返回值
 */
async function getCustomerList(_searchName = '') {
  try {
    const searchParamsUpdate = {
      ...searchParams.value,
      customerName: _searchName
    }

    // 使用展开运算符来保留原始searchParams中未列出的其他属性
    searchParams.value = { ...searchParamsUpdate }

    await customData.searchCustomerList(
      {
        branchTypeFlag: searchParams.value.branchTypeFlag,
        customerName: searchParams.value.customerName,
        pageSize: searchParams.value.pageSize,
        pageNum: searchParams.value.pageNum
      },
      props.preOption as never[]
    )

    list.value = [
      ...(props.preOption.length ? props.preOption : []),
      ...customData.customerList.value
    ]

    if (!searchParams.value.branchTypeFlag && searchParams.value.customerName) {
      list.value.forEach((item: CustomerTreeVO) => {
        setDefaultExpand(item)
      })
      treeV2Ref.value.setExpandedKeys(defaultExpandedKeys.value)
    } else {
      defaultExpandedKeys.value = []
      treeV2Ref.value.setExpandedKeys(defaultExpandedKeys.value)
    }
    //当关键字搜索有内容时,需要再进行过滤
    if (filterKey.value) {
      filterFn('')
    }
  } catch (error) {
    console.error('Error fetching customer list:', error)
  } finally {
    // 可以添加一些清理逻辑，如果需要的话
  }
}

onBeforeMount(async () => {
  await getCustomerList()
  //回显数据
  if (props.customerId) {
    customerIdCp.value = props.customerId
  }
  if (props.customerCode) {
    //通过code获取id
    const thisCustomerIds = await customData.getCustomerIdByCode(props.customerCode)
    if (thisCustomerIds?.length > 0) {
      customerIdCp.value = thisCustomerIds[0]
    } else {
      console.log('未查询到该客户编码信息')
    }
  }
})
//监听customerId数据回显
watch(
  () => props.customerId,
  (val) => {
    if (val) {
      nextTick(() => {
        // customerId.value = val
        customerIdCp.value = val
      })
    }
  }
)
//监听customerCode数据回显
watch(
  () => props.customerCode,
  (val) => {
    if (val) {
      nextTick(async () => {
        //通过code获取id
        const thisCustomerIds = await customData.getCustomerIdByCode(props.customerCode)
        if (thisCustomerIds?.length > 0) {
          customerIdCp.value = thisCustomerIds[0]
        } else {
          console.log('未查询到该客户编码信息')
        }
      })
    }
  }
)

//监听customerCode customerName数据,清空数据，支持表单事件清空数据后重置数据
watch(
  () => [customerCode.value, customerName.value],
  ([newCustomerCode, newCustomerName], [oldCustomerCode, oldCustomerName]) => {
    if (!newCustomerCode && oldCustomerCode) {
      console.log('customerCode  is null')

      clearHandler('')
      return
    }

    if (!newCustomerName) {
      console.log('customerName is null')
      if (props.isSearchByName && searchText.value && !oldCustomerName) {
        return
      }
      clearHandler('')
    }
  }
)

//无限滚动
const loadMore = () => {
  console.log('loadMore')
  if (!customData.customerSelectLoading.value) {
    searchParams.value.pageNum++
    getCustomerList(searchParams.value.customerName)
  }
}
//是否禁用滚动
const disabledScroll = computed(() => customData.customerSelectLoading.value || noMore.value)
//是否加载完成
const noMore = computed(
  () =>
    searchParams.value.pageNum >=
    Math.ceil(customData.currentList.value.total / searchParams.value.pageSize)
)

// 当值改变时进行数据的处理
const customerIdCp = computed({
  get: () => customerId.value,
  set: async (value: any) => {
    //单选
    if (!props.multiple) {
      console.log('computed')

      // 如果是空值，直接往外抛出undefined
      if (value == null || value == '') {
        customerId.value = ''
        customerName.value = ''
        customerCode.value = ''
        currentNodeKey.value = ''
        currentNodeLabel.value = ''
        clearHandler('')

        return
      }

      const thisCustomerId = value as string

      // 单选
      const customerObj =
        (customData.dicCustomer?.value.get(thisCustomerId) as CustomerTreeVO) ??
        (await customData.getCustomerInfoById(thisCustomerId)) //防止回显失败，当map中未能找到customerInfo，则调用接口获取
      console.log(customerObj)
      //双向绑定-customerId
      customerId.value = thisCustomerId
      //双向绑定-customerName
      customerName.value = customerObj?.customerName
      //双向绑定-customerCode
      customerCode.value = customerObj?.customerCode
      //双向绑定-customerInfo
      const serVO = customData.getCustomerServerVOByObj(customerObj)
      customerInfo.value = serVO
      emit('selectCustomer', serVO)

      //设置tree
      currentNodeKey.value = thisCustomerId
      currentNodeLabel.value = customerObj?.customerName
    } else {
      // 多选

      //双向绑定-customerId
      customerId.value = value
      //双向绑定-customerName
      customerName.value = value.map((id) => {
        const customerObj = customData.dicCustomer?.value.get(id) as CustomerTreeVO
        console.log(customerObj)

        return customerObj.customerName
      })
      //双向绑定-customerInfo
      customerInfo.value = value.map((id) => {
        const customerObj = customData.dicCustomer?.value.get(id) as CustomerTreeVO
        return customData.getCustomerServerVOByObj(customerObj)
      })
      emit('selectCustomer', customerInfo.value)
    }
  }
})

// watch(
//   () => customerId.value,
//   (val) => {
//     console.log('val', val)
//     console.log('customerIdCp', customerIdCp.value)
//     console.log('props.customerId', props.customerId)
//     if (val !== props.customerId) {
//       emit('change', val)
//     }
//   },
//   {
//     immediate: false,
//     deep: true
//   }
// )

/**
 * 清空处理函数
 *
 * @param val 清空参数
 */
const clearHandler = async (val) => {
  console.log('clearHandler')
  searchParams.value.pageNum = 1

  searchText.value = ''
  filterKey.value = ''
  customerId.value = ''
  customerName.value = ''
  customerCode.value = ''
  customerInfo.value = undefined
  currentNodeKey.value = ''
  currentNodeLabel.value = ''

  emit('clear', val)
  //清空后,重新获取列表
  list.value = []
  //changebranchTypeFlagLoading防止切换总分行过程中重复刷新getCustomerList
  if (changebranchTypeFlagLoading.value) return
  await getCustomerList()
}
// 选中数据
const ifSelected = ref<boolean>(false)
const changeHandler = (val) => {
  emit('change', val)
}

//失去焦点,清空搜索条件
const blurHandler = (e) => {
  //通过input输入时,且未选中数据时,将input的内容赋值给customerName和customerId
  if (props.isSearchByName && !ifSelected.value) {
    customerName.value = searchText.value
    customerId.value = searchText.value

    customerIdCp.value == searchText.value
    console.log(customerName.value, customerId.value)
  }
  //当不是名称搜索模式时,且有搜索内容时,清空搜索内容
  if (!props.isSearchByName && searchText.value) {
    searchText.value = ''
    //清空后,重新获取列表
    list.value = []
    getCustomerList()
  }
  filterCount.value = 0
  // if (!searchText.value) return
  // searchText.value = ''
}

const focusHandler = (e) => {
  console.log('focusHandler')
}

// 获取输入内容
const searchText = ref('')
const filterCount = ref(0)
const filterMethod = async (_value) => {
  console.log(
    'filterMethod',
    '_value',
    _value,
    'searchText.value',
    searchText.value,
    'filterCount',
    filterCount.value
  )

  //搜索条件为空则不重复搜索
  if (!_value && !searchText.value) return
  //当名称搜索模式,且输入空,缓存搜索有内容,不重复搜索
  if (props.isSearchByName && !_value && searchText.value && !filterCount.value) return
  if (_value && searchText.value == _value) {
    return
  }
  if (_value) {
    ifSelected.value = false
  }
  //只要有搜索内容不重新获取列表
  if (searchText.value && searchText.value == _value) {
    const thisList = unref(customData.customerList.value)
    list.value = thisList
    return
  }

  filterKey.value = ''

  // 远程搜索逻辑
  searchParams.value.pageNum = 1
  list.value = []
  // 更新 searchText.value 为当前搜索值
  searchText.value = _value ? _value : searchText.value

  //当打开下拉框时,搜索条件使用输入框内容
  if (filterCount.value) {
    searchText.value = _value
    await getCustomerList(_value)
  } else {
    await getCustomerList(_value || searchText.value)
  }

  filterCount.value++
}

/**
 * 设置默认展开项(递归list)
 *
 * @param customerItem 客户树形视图对象
 */
const setDefaultExpand = (customerItem: CustomerTreeVO) => {
  if (customerItem.children && customerItem.children?.length) {
    defaultExpandedKeys.value.push(customerItem.customerId)
    customerItem.children.forEach((item: CustomerTreeVO) => {
      setDefaultExpand(item)
    })
  }
}

// 进行关键字进一步搜索
import { debounce, cloneDeep, set } from 'lodash-es'

const filterKey = ref('') //进一步搜索的关键字

const filterFn = debounce((val) => {
  if (filterKey.value) {
    // 只在有关键字时进行深拷贝和过滤
    list.value = filterListFn(cloneDeep(unref(customData.customerList.value)), filterKey.value)
    list.value.forEach((item: CustomerTreeVO) => {
      setDefaultExpand(item)
    })
    treeV2Ref.value.setExpandedKeys(defaultExpandedKeys.value)
  } else {
    // 没有关键字时，使用缓存list
    list.value = customData.customerList.value
    defaultExpandedKeys.value = []
    treeV2Ref.value.setExpandedKeys(defaultExpandedKeys.value)
  }
}, 500)

// 递归过滤下拉菜单数据
const filterListFn = (_arr, _searchName) => {
  return _arr.filter((el) => {
    const isUnMatchName = el.customerName.indexOf(_searchName) === -1
    if (!isUnMatchName) {
      // 匹配上名字，且有子节点时，递归过滤子节点
      if (el.children && el.children.length > 0) {
        el.children = filterListFn(el.children, _searchName)
      }
      return true // 匹配上名字的元素不过滤
    } else {
      // 没匹配上名字，但有子节点时，递归过滤子节点
      if (el.children && el.children.length > 0) {
        el.children = filterListFn(el.children, _searchName)
        // 仅当过滤后的子节点为空时，才过滤当前节点
        return el.children.length > 0
      }
      return false // 没有匹配上名字且没有子节点或子节点过滤完的元素被过滤
    }
  })
}

// 处理高亮
const handleHeightLight = (str, searchText, filterKey) => {
  // 确保searchText和filterKey是字符串，并且去除两端空白
  searchText = searchText.trim()
  filterKey = filterKey.trim()

  const setHightLightStr = (targetStr) => {
    // 创建正则表达式，全局匹配，考虑大小写
    // const reg = new RegExp(/bbb(?![^<]*<\/span>)/, 'g')
    const reg = new RegExp(`${targetStr}(?![^<]*<\/span>)`, 'g')

    // 替换字符串中的搜索词为高亮格式
    const replaceStr = `<span style="color: var(--el-color-primary)">${targetStr}</span>`
    str = str.replace(reg, replaceStr)
  }

  // 如果没有搜索词或者过滤词与搜索词相同，则直接返回原字符串
  if (!searchText && !filterKey) {
    return str
  }

  if (searchText && !filterKey) {
    setHightLightStr(searchText)
  }

  if (filterKey && !searchText) {
    setHightLightStr(filterKey)
  }

  if (searchText && filterKey) {
    if (searchText.indexOf(filterKey) > -1) {
      setHightLightStr(searchText)
    } else if (filterKey.indexOf(searchText) > -1) {
      setHightLightStr(filterKey)
    }

    if (searchText.indexOf(filterKey) === -1 && filterKey.indexOf(searchText) === -1) {
      setHightLightStr(searchText)
      setHightLightStr(filterKey)
      return str
    }
  }

  return str
}

// 切换是否只看总行
const changebranchTypeFlagLoading = ref(false)
const changebranchTypeFlag = async (_val) => {
  //防止clearHandler函数清除customerCode重复触发getCustomerList
  changebranchTypeFlagLoading.value = true
  //缓存searchText
  const thisSearchText = searchText.value

  await clearHandler('')

  nextTick(async () => {
    console.log('changebranchTypeFlag')
    changebranchTypeFlagLoading.value = false

    await getCustomerList(thisSearchText)
    searchText.value = thisSearchText
  })
}
</script>
<style lang="scss">
.customer-select-option {
  min-width: 500px !important;
}
</style>

<style lang="scss" scoped>
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  // height: auto;
  min-height: 260px;
  padding: 0px 0px;
  padding-bottom: 30px;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li :deep(.el-tree .el-tree-node__content) {
  height: auto;
  padding: 0 20px;
}

.el-tree-node__label {
  font-weight: normal;
}

.el-tree :deep(.is-current .el-tree-node__label) {
  color: #409eff;
  font-weight: 700;
}

.el-tree :deep(.is-current .el-tree-node__children .el-tree-node__label) {
  color: #606266;
  font-weight: normal;
}

.selectInput {
  padding: 0 5px;
  box-sizing: border-box;
}

.el-select {
  width: 100% !important;
}
</style>
