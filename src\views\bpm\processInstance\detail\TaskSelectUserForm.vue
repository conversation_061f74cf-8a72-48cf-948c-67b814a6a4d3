<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-09-14 16:45:39
 * @Description:   转办/委派/抄送 选人
-->
<template>
  <Dialog v-model="dialogVisible" :title="handleTypeEnum[props.handleType]?.title" width="500">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="110px"
    >
      <el-form-item label="新审批人" prop="assigneeUserId">
        <el-select
          v-model="formData.assigneeUserId"
          :multiple="handleTypeEnum[props.handleType]?.ifSelectMore"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'TaskSelectUserForm'
})

import * as TaskApi from '@/api/bpm/task'
import * as UserApi from '@/api/system/user'

const emit = defineEmits(['handleForward', 'handleDelegate', 'success', 'setCcUserList']) // 定义 success 事件，用于操作成功后的回调

const handleTypeEnum = {
  Forward: { title: '转办', ifSelectMore: false },
  Delegate: { title: '转办', ifSelectMore: false },
  CC: { title: '抄送', ifSelectMore: true }
}

const props = defineProps({
  handleType: {
    type: String,
    default: 'Forward'
  } //操作类型:  转办1 委派2 抄送3
})

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const formData = ref({
  id: '',
  assigneeUserId: '' as string | []
})
const formRules = ref({
  assigneeUserId: [{ required: true, message: '新审批人不能为空', trigger: 'change' }]
})

const formRef = ref() // 表单 Ref
const userList = ref<any[]>([]) // 用户列表

/**
 * 打开弹窗
 * @param id  任务id
 * @param index   并行任务索引
 */

let auditFormsIndex = ref<number>(0)
const open = async (id: string, index: number) => {
  auditFormsIndex.value = index
  dialogVisible.value = true
  resetForm()
  formData.value.id = id
  // 获得用户列表
  userList.value = await UserApi.getSimpleUserList()
}

/** 提交表单 */
let CCUserList = ref<any>([]) //抄送用户列表

const submitForm = async () => {
  // 校验表单

  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  try {
    //抄送
    if (props.handleType === TaskApi.handleTypeEnum.CC) {
      CCUserList.value = userList.value.filter((item) =>
        (formData.value.assigneeUserId as [])?.find((item2) => item.id === item2)
      )
      emit('setCcUserList', {
        CCUserList: CCUserList.value,
        auditFormsIndex: auditFormsIndex.value
      })
    }
    //转办
    if (props.handleType === TaskApi.handleTypeEnum.Forward) {
      emit('handleForward', { auditFormsIndex })
      // 发送操作成功的事件
      emit('success')
    }
    //委派
    if (props.handleType === TaskApi.handleTypeEnum.Delegate) {
      emit('handleDelegate', {
        id: formData.value.id,
        userId: formData.value.assigneeUserId,
        auditFormsIndex: auditFormsIndex.value
      })
      // 发送操作成功的事件
      emit('success')
    }
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: '',
    assigneeUserId: ''
  }
  formRef.value?.resetFields()
}

defineExpose({ open, formData, CCUserList }) // 提供 openModal 方法，用于打开弹窗
</script>
