<template>
  <el-dialog :title="'租户'" v-model="visible" width="600px" append-to-body destroy-on-close>
    <div
      class="tenant-names-warp flex"
      style="min-height: 400px; text-align: center; justify-content: center; align-items: center"
    >
      <span>{{ tenantNames }}</span>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'TenantShowDialog'
})

const visible = ref(false)

const data: Ref<any> = ref({})

const tenantMap: Ref<any> = ref({})

const tenantNames: Ref<string> = ref('')

const open = (_row, _tenantMap) => {
  tenantMap.value = _tenantMap
  data.value = _row
  tenantNames.value = _row.cond
    .split(',')
    .map((el) => _tenantMap[el])
    .join(',')
  visible.value = true
}

defineExpose({
  open
})
</script>
