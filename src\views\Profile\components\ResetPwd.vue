<template>
  <el-form ref="formRef" :model="password" :rules="rules" label-width="80px">
    <el-form-item :label="t('profile.password.oldPassword')" prop="oldPassword" label-width="150">
      <InputPassword
        v-model="password.oldPassword"
        style="width: 100%"
        :placeholder="t('profile.password.oldPwdMsg')"
      />
    </el-form-item>
    <el-form-item :label="t('profile.password.newPassword')" prop="newPassword" label-width="150">
      <!-- 暂对复杂度没有需求，先注释掉 -->
      <InputPassword
        v-model="password.newPassword"
        :placeholder="t('profile.password.pwdRules')"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item
      :label="t('profile.password.confirmPassword')"
      prop="confirmPassword"
      label-width="150"
    >
      <InputPassword
        v-model="password.confirmPassword"
        :placeholder="t('profile.password.cfPwdMsg')"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item>
      <XButton :title="t('common.save')" type="primary" @click="submit(formRef)" />
      <XButton :title="t('common.reset')" type="danger" @click="reset(formRef)" />
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ResetPwd'
})

import type { FormInstance, FormRules } from 'element-plus'

import { InputPassword } from '@/components/InputPassword'
import { updateUserPassword } from '@/api/system/user/profile'

const { t } = useI18n()
const message = useMessage()
const formRef = ref<FormInstance>()
const password = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单校验
const equalToPassword = (_rule, value, callback) => {
  if (password.newPassword !== value) {
    callback(new Error(t('profile.password.diffPwd')))
  } else {
    callback()
  }
}

const validPassword = (_rule, value, callback) => {
  // let reg =
  //   !/(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+-=_\[\]\{\}\|,:;'"<>?\/.~!@#$%^&*()]{6,18}$/.test(
  //     value
  //   )
  let reg = !/^[a-zA-Z0-9-*/+-=_\[\]\{\}\|,:;'"<>?\/.~!@#$%^&*()]{6,18}$/.test(value)
  console.log('reg', reg, value)
  if (!value) {
    callback(t('profile.password.newPwdMsg'))
  } else if (reg) {
    callback(new Error(t('profile.password.pwdRules')))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  oldPassword: [
    { required: true, message: t('profile.password.oldPwdMsg'), trigger: 'blur' },
    { min: 6, max: 20, message: t('profile.password.pwdRules'), trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: t('profile.password.newPwdMsg'), trigger: 'blur' },
    // { min: 6, max: 20, message: t('profile.password.pwdRules'), trigger: 'blur' }
    { required: true, validator: validPassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: t('profile.password.cfPwdMsg'), trigger: 'blur' },
    { required: true, validator: equalToPassword, trigger: 'blur' }
  ]
})
const reset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  password.oldPassword = ''
  password.newPassword = ''
  password.confirmPassword = ''
  formEl.resetFields()
}
const submit = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid) => {
    if (valid) {
      await updateUserPassword(password.oldPassword, password.newPassword)
      message.success(t('common.updateSuccess'))
      reset(formEl)
    }
  })
}
</script>
