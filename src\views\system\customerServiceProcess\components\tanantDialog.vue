<template>
  <Dialog
    v-model="dialogVisible"
    :title="'被代理租户'"
    destroy-on-close
    :close-on-click-modal="false"
    width="800"
  >
    <el-table :data="rowData.proxyTenants" height="400px">
      <el-table-column label="id" prop="id" />
      <el-table-column label="名称" prop="name" />
    </el-table>
    <template #footer>
      <el-button @click="close">{{ t('common.close') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
const { t } = useI18n() // 国际化
const dialogVisible = ref(false)

const rowData: Ref<any> = ref({})

const close = () => {
  dialogVisible.value = false
}

const open = (_row) => {
  dialogVisible.value = true
  rowData.value = _row
}

defineExpose({
  open
})
</script>
