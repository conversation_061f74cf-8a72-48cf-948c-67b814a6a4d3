<template>
  <!-- 列表 -->
  <UmvContent>
    <UmvQuery
      v-model="queryParams"
      label-width="130px"
      :opts="queryOpts"
      @reset="getList"
      @check="handleQuery"
    />

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="getList">
      <template #tools>
        <el-button type="primary" @click="apply" size="small">申请代客操作</el-button>
      </template>
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>
  </UmvContent>
  <applyDialog ref="applyDialogRef" @success="getList" />
  <tanantDialog ref="tanantDialogRef" />
  <authorizeOperateDialog ref="authorizeOperateDialogRef" />
</template>
<script setup lang="tsx">
defineOptions({
  name: 'CustomerServiceProcess'
})

import * as TenantApi from '@/api/system/tenant'
import applyDialog from './components/applyDialog.vue'
import tanantDialog from './components/tanantDialog.vue'
import authorizeOperateDialog from './components/authorizeOperateDialog.vue'
import { DictDataType, getDictOptions } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import UmvContent from '@/components/UmvContent'
import { DictTag } from '@/components/DictTag'
import { ElButton, ElTag } from 'element-plus'
import { checkPermi } from '@/utils/permission'

import {
  getProxyTenantOperationPage,
  proxyTenantOperationStart,
  proxyTenantOperationFinish
} from '@/api/system/customerServiceProcess/index'

const getDetail = (_row) => {
  applyDialogRef.value.open('detail', _row)
}

const queryParams: Ref<any> = ref({
  pageNo: 1,
  pageSize: 10
})

/**----------------------------------------- 搜索条件start ----------------------------------------------*/

// 代客操作状态
const operationStatusDict = ref([
  {
    value: 0,
    label: '草稿'
  },
  {
    value: 1,
    label: '已提交申请'
  },
  {
    value: 2,
    label: '审批通过'
  },
  {
    value: 3,
    label: '审批不通过'
  },
  {
    value: 4,
    label: '取消申请'
  },
  {
    value: 5,
    label: '审批通过，处理代客操作中'
  },
  {
    value: 6,
    label: '代客操作已完成'
  },
  {
    value: 7,
    label: '已过期'
  }
])

const operationStatusMap = ref({
  0: {
    name: '草稿',
    type: 'info'
  },
  1: {
    name: '已提交申请',
    type: 'primary'
  },
  2: {
    name: '审批通过',
    type: 'primary'
  },
  3: {
    name: '审批不通过',
    type: 'danger'
  },
  4: {
    name: '取消申请',
    type: 'danger'
  },
  5: {
    name: '审批通过，处理代客操作中',
    type: 'warning'
  },
  6: {
    name: '代客操作已完成',
    type: 'success'
  },
  7: {
    name: '代客操作已完成',
    type: 'info'
  }
})

// 流程实例状态
const procStatusDict = ref([
  {
    value: 1,
    label: '进行中'
  },
  {
    value: 2,
    label: '已完成'
  }
])

const procStatusMap = ref({
  1: '进行中',
  2: '已完成'
})

// 流程实例处理结果
const procResultDict = ref([
  {
    value: 1,
    label: '处理中'
  },
  {
    value: 2,
    label: '审批通过'
  },
  {
    value: 3,
    label: '审批不通过'
  },
  {
    value: 4,
    label: '已取消'
  }
])

const procResultMap = ref({
  1: '处理中',
  2: '审批通过',
  3: '审批不通过',
  4: '已取消'
})

// 被代理操作的功能
const proxyFunctionsDict = ref([
  {
    value: 'batchProxyTenantAuth',
    label: '批量'
  },
  {
    value: 'singleProxyTenantOperation',
    label: '单个'
  }
])

const proxyFunctionsMap = ref({
  batchProxyTenantAuth: '批量',
  singleProxyTenantOperation: '单个'
})

// 查询条件配置
const queryOpts = ref<Record<string, QueryOption>>({
  applyReason: {
    label: '申请理由',
    defaultVal: '',
    controlRender: (form) => (
      <el-input v-model={form.applyReason} placeholder="请输入申请理由" clearable />
    )
  },
  operationStatus: {
    label: '代客操作状态',
    defaultVal: '',
    controlRender: (form) => (
      <el-select v-model={form.operationStatus} placeholder="请选择代客操作状态" clearable>
        {operationStatusDict.value.map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  procStatus: {
    label: '流程实例状态',
    defaultVal: '',
    controlRender: (form) => (
      <el-select v-model={form.procStatus} placeholder="请选择流程实例状态" clearable>
        {procStatusDict.value.map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  procResult: {
    label: '流程实例处理结果',
    defaultVal: '',
    controlRender: (form) => (
      <el-select v-model={form.procResult} placeholder="请选择流程实例处理结果" clearable>
        {procResultDict.value.map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  }
})

const message = useMessage()

// 开始代客授权
const startAuthorize = async (_row) => {
  try {
    await message.confirm('确认开始代客授权？')
    loading.value = true
    await proxyTenantOperationStart(_row.id)
    getList()
  } finally {
    loading.value = false
  }
}

// 结束代客授权
const finishAuthorize = async (_row) => {
  try {
    await message.confirm('确认结束代客授权？')
    loading.value = true
    await proxyTenantOperationFinish(_row.id)
    getList()
  } finally {
    loading.value = false
  }
}

/**----------------------------------------- 搜索条件end ----------------------------------------------*/

// 表格列定义
const columns = ref<TableColumn[]>([
  { prop: 'applyReason', label: '申请理由', minWidth: '200px' },
  {
    prop: 'createTime',
    label: '申请发起时间',
    align: 'center',
    width: '200px',
    renderTemplate: (scope) => (
      <span>{formatDate(scope.row.createTime, 'YYYY-MM-DD HH:mm:ss')}</span>
    )
  },
  {
    prop: 'proxyTenants',
    label: '被代理租户',
    align: 'center',
    width: '100px',
    renderTemplate: (scope) => (
      <ElButton onClick={() => showTenantList(scope.row)} type="primary" link>
        查看租户
      </ElButton>
    )
  },
  {
    prop: 'proxyFunctions',
    label: '代操作功能',
    align: 'center',
    width: '160px',
    renderTemplate: (scope) => (
      <span>
        {scope.row.proxyFunctions?.length > 0
          ? scope.row.proxyFunctions.map((el) => el.name).join('，')
          : ''}
      </span>
    )
  },
  {
    prop: 'tokenEffectiveDuration',
    label: '令牌有效时长',
    align: 'center',
    width: '120px',
    renderTemplate: (scope) => <span>{`${scope.row.tokenEffectiveDuration}秒`}</span>
  },
  {
    prop: 'expireAt',
    label: '过期时间',
    align: 'center',
    width: '200px',
    renderTemplate: (scope) => <span>{formatDate(scope.row.expireAt, 'YYYY-MM-DD HH:mm:ss')}</span>
  },
  {
    prop: 'operationStatus',
    label: '代客操作状态',
    align: 'center',
    width: '200px',
    renderTemplate: (scope) => (
      <ElTag type={operationStatusMap.value[scope.row.operationStatus].type}>
        {operationStatusMap.value[scope.row.operationStatus].name}
      </ElTag>
    )
  },
  {
    prop: 'procStatus',
    label: '审批状态',
    align: 'center',
    width: '100px',
    renderTemplate: (scope) => (
      <DictTag type="bpm_process_instance_status" value={scope.row.procStatus} />
    )
  },
  {
    prop: 'procResult',
    label: '审批结果',
    align: 'center',
    width: '100px',
    renderTemplate: (scope) => (
      <DictTag type="bpm_process_instance_result" value={scope.row.procResult} />
    )
  },
  {
    prop: 'oprate',
    label: '操作',
    align: 'center',
    fixed: 'right',
    width: '280px',
    renderTemplate: (scope) => (
      <div>
        <ElButton link type="primary" onClick={() => getDetail(scope.row)}>
          详情
        </ElButton>
        {scope.row.operationStatus == 5 && (
          <ElButton link type="primary" onClick={() => showAuthorizeOperate(scope.row)}>
            代客授权
          </ElButton>
        )}
        {scope.row.operationStatus == 2 && (
          <ElButton link type="primary" onClick={() => startAuthorize(scope.row)}>
            代客授权开始
          </ElButton>
        )}
        {scope.row.operationStatus == 5 && (
          <ElButton link type="primary" onClick={() => finishAuthorize(scope.row)}>
            代客授权完成
          </ElButton>
        )}
        {scope.row.operationStatus === 0 && (
          <ElButton link type="primary" onClick={() => edit(scope.row)}>
            编辑
          </ElButton>
        )}
      </div>
    )
  }
])

const applyDialogRef = ref()

const loading = ref(false)

const list = ref([]) // 列表的数据

const total = ref(0)

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getProxyTenantOperationPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const apply = () => {
  applyDialogRef.value.open('add')
}

const edit = (_row) => {
  applyDialogRef.value.open('edit', _row)
}

const queryFormRef = ref()

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const tanantDialogRef = ref()

const showTenantList = (_row) => {
  console.log('_row', _row)
  tanantDialogRef.value.open(_row)
}

const authorizeOperateDialogRef = ref()

const showAuthorizeOperate = (_row) => {
  authorizeOperateDialogRef.value.open(_row)
}

/** 初始化 **/
onMounted(async () => {
  await getList()
})
</script>
<style lang="less" scoped></style>
