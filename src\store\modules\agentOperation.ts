/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-03 14:54:00
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2024-12-24 14:00:45
 * @Description: 代客操作模式pinia模块
 */
import { defineStore } from 'pinia'
import router from '@/router/index'

import * as authUtil from '@/utils/auth'
import { getCustomerTokenList } from '@/api/system/agentOperation'
//用户信息store
import { useUserStoreWithOut } from '@/store/modules/user'
const userStore = useUserStoreWithOut()

type agentOperationStateType = {
  agentOperationMode: boolean
  ifShowDrawer: boolean
  currentCustomer: {
    proxyTenantId: string
    accessToken: string
    refreshToken: string
  }
  adminData: {
    tenantId: string
    accessToken: string
    refreshToken: string
  }
  customerList: any[]
  pageTotal: number
  isInternational: boolean
  internationalUrl: string
}
export const useAgentOperationStore = defineStore('agentOperation', {
  state: (): agentOperationStateType => ({
    agentOperationMode: false,
    ifShowDrawer: false,
    currentCustomer: {
      proxyTenantId: '',
      accessToken: '',
      refreshToken: ''
    },
    adminData: {
      tenantId: '',
      accessToken: '',
      refreshToken: ''
    },
    customerList: [],
    pageTotal: 0,
    isInternational: false,
    internationalUrl: ''
  }),

  getters: {
    getAgentOperationMode(): boolean {
      return this.agentOperationMode
    },
    getCustomerList(): any {
      return this.customerList
    },
    getCurrentCustomer(): any {
      return this.currentCustomer
    }
  },
  actions: {
    /**
     * 设置当前客户
     *
     * @param row 客户数据对象
     */
    setCurrentCustomer(row) {
      console.log(row)
      this.currentCustomer = row
      this.ifShowDrawer = false
      this.setLoginToken(false)
    },
    /**
     * 打开代客操作模式
     *
     * @param ifShowDrawer 是否显示抽屉，默认为 true
     */
    openAgentOperationMode(ifShowDrawer = true) {
      this.agentOperationMode = true
      this.ifShowDrawer = ifShowDrawer
      if (authUtil.getTenantId() == 'goldpac.com') {
        this.adminData.tenantId = authUtil.getTenantId()
        this.adminData.accessToken = authUtil.getAccessToken()
        this.adminData.refreshToken = authUtil.getRefreshToken()
      }
    },

    /**
     * 更改代客操作模式
     *
     * @param agentOperationMode 代客操作模式，如果为true则启用代客模式，如果为false则关闭代客模式
     */
    changeAgentOperationMode(agentOperationMode) {
      console.log(agentOperationMode)
      //关闭代客模式
      if (!agentOperationMode) {
        this.setLoginToken(true)
      }
    },

    /**
     * 设置登录令牌
     *
     * @param ifAdmin 是否为管理员身份
     */
    async setLoginToken(ifAdmin: boolean) {
      const tokenData = authUtil.getCompleteToken()

      if (ifAdmin) {
        authUtil.setTenantId(this.adminData.tenantId)
        authUtil.setToken(tokenData, false)

        this.currentCustomer = {
          proxyTenantId: '',
          accessToken: '',
          refreshToken: ''
        }
        // 重置国际版标识
        this.isInternational = false
        this.internationalUrl = ''
      } else {
        tokenData.access_token = this.currentCustomer.accessToken
        tokenData.refresh_token = this.currentCustomer.refreshToken
        authUtil.setTenantId(this.currentCustomer.proxyTenantId)
        authUtil.setToken(tokenData, false)
      }
      userStore.resetState()
      //代客操作前跳转首页避免404
      await router.push('/')

      await window.location.reload()
    },
    /**
     * 获取令牌列表
     *
     * @returns 无返回值
     */
    async getTokenList(data) {
      const res = await getCustomerTokenList(data)
      console.log(res)
      this.customerList = []
      this.pageTotal = 0
      if (res.list?.length) {
        this.customerList = res.list
        this.pageTotal = res.total
      }
    },
    /**
     * 更新国际版状态
     *
     * @param isInternational 是否国际版
     * @param internationalUrl 国际版API地址
     */
    updateInternationalStatus(isInternational: boolean, internationalUrl = '') {
      this.isInternational = isInternational
      this.internationalUrl = internationalUrl
    }
  },
  persist: true // 开启持化
})
