<template>
  <div class="approval-audit">
    <!-- 搜索 -->
    <ContentWrap>
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="租户名" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入租户名"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button @click="handleQuery"
            ><icon-ep-search style="font-size: 12px" class="mr-5px" /> 搜索</el-button
          >
          <el-button @click="resetQuery"
            ><icon-ep-refresh style="font-size: 12px" class="mr-5px" /> 重置</el-button
          >
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap ifTable>
      <el-table v-loading="loading" :data="list">
        <el-table-column label="租户id" align="center" prop="id" />
        <el-table-column label="租户名称" align="center" prop="name" />
        <el-table-column label="申请服务" align="center" prop="service" />
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-button link type="primary" @click="edit(row, 'check')"> 查看 </el-button>
            <el-button link type="primary" @click="preview(row)"> 预览 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNo"
        :total="total"
        @pagination="handleQuery"
      />
    </ContentWrap>
    <ApprovalEditForm ref="ApprovalEditFormRef" />
    <PreviewForm ref="PreviewFormRef" />
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'approvalAudit'
})

import ApprovalEditForm from './components/ApprovalEditForm.vue'
import PreviewForm from './components/PreviewForm.vue'
import { getTenantAuditList } from '@/api/system/serviceAudit'

const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  audit: 0
})

const loading = ref(false)

const ApprovalEditFormRef = ref()

// 编辑
const edit = (row, type) => {
  ApprovalEditFormRef.value.open({ ...row }, type)
}

const PreviewFormRef = ref()

// 预览
const preview = (row) => {
  PreviewFormRef.value.open({ ...row })
}

interface TenantRowVO {
  name: string
  id: string
  serivice: any[]
  time?: string
}

const list: Ref<TenantRowVO[] | never[]> = ref([])

const total = ref(0)

// 搜索
const handleQuery = async () => {
  try {
    loading.value = true
    const res = await getTenantAuditList(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

// 重置搜索条件
const resetQuery = () => {
  try {
    loading.value = true
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  handleQuery()
})
</script>
