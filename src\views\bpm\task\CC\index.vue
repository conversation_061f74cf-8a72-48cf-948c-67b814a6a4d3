<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="发起人" prop="startUserNickname">
        <el-input
          v-model="queryParams.startUserNickname"
          class="!w-240px"
          clearable
          placeholder="请输入发起人"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <icon-ep-search class="mr-5px" style="font-size: 12px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <icon-ep-refresh class="mr-5px" style="font-size: 12px" />
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="list">
      <el-table-column
        align="center"
        label="任务编号"
        prop="taskId"
        width="150px"
        show-overflow-tooltip
      />
      <el-table-column align="center" label="任务名称" prop="taskName" />
      <el-table-column align="center" label="流程名称" prop="processInstanceName" />
      <el-table-column align="center" label="流程发起人" prop="processInstanceStartUserNickname" />
      <el-table-column align="center" label="状态" prop="result">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.taskResult" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="原因" prop="taskReason" />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="流程开始时间"
        prop="processInstanceStartTime"
        width="180"
      />
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="handleAudit(scope.row)">流程详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script setup lang="ts">
defineOptions({
  name: 'BpmCcTask'
})

import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'

const { push } = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  startUserNickname: '',
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getCcTaskPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理审批按钮 */
const handleAudit = (row) => {
  push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
