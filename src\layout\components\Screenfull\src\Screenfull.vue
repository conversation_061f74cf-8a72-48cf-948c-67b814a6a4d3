<script setup lang="ts">
defineOptions({
  name: 'ScreenFull'
})

import { Icon } from '@/components/Icon'
import { useFullscreen } from '@vueuse/core'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('screenfull')

defineProps({
  color: propTypes.string.def('')
})

const { toggle, isFullscreen } = useFullscreen()

const toggleFullscreen = () => {
  toggle()
}
</script>

<template>
  <div :class="prefixCls" @click="toggleFullscreen">
    <icon-zmdi-fullscreen-exit v-if="isFullscreen" :style="{ color }" class="" />
    <icon-zmdi-fullscreen v-else :style="{ color }" class="" />
  </div>
</template>
