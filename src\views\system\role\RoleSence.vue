<!-- 高级数据权限 -->
<template>
  <el-form
    ref="formRef"
    v-loading="formLoading"
    :rules="rules"
    :model="formData"
    label-width="140px"
  >
    <el-form-item :label="t('system.role.name')">
      <el-tag>{{ formData.name }}</el-tag>
    </el-form-item>
    <el-form-item :label="t('system.role.type')">
      <el-tag>{{
        route.query.type === '1' ? t('system.role.systemRole') : t('system.role.customRole')
      }}</el-tag>
    </el-form-item>
    <el-form-item :label="t('system.role.code')">
      <el-tag>{{ formData.code }}</el-tag>
    </el-form-item>
    <el-form-item :label="t('system.role.dataScope')" class="flex">
      <div class="flex items-center mr-15px">
        <el-input v-model="filterData" @input="handlerFilter" placeholder="请输入过滤租户名称" />
        <div class="ml-10px flex items-center w-80">
          <el-switch
            v-model="filterEmptyPermission"
            @change="handlerFilter"
            active-text="过滤无权限信息租户"
          />
        </div>
      </div>
      <el-auto-resizer>
        <template #default="{ width, height }">
          <el-table-v2
            ref="tableRef"
            :height="500"
            :width="width"
            :estimated-row-height="40"
            size="small"
            row-key="id"
            :columns="columns"
            :data="treeOptions"
            expand-column-key="name"
            v-model:expanded-row-keys="defaultExpandedRowKeys"
          />
        </template>
      </el-auto-resizer>
    </el-form-item>
  </el-form>

  <!-- 业务场景-->
  <senceDialog ref="senceDialogRef" />
</template>
<script setup lang="tsx">
defineOptions({
  name: 'RoleSence'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { SystemDataScopeEnum } from '@/utils/constants'
import * as RoleApi from '@/api/system/role'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
let formData = reactive({
  id: 0,
  name: '',
  code: '',
  dataScope: undefined
  // 移除autoAuthRuleIds属性
})

const originFormData = {
  id: 0,
  name: '',
  code: '',
  dataScope: undefined
  // 移除autoAuthRuleIds属性
}

const rules = ref({
  dataScope: [{ required: true, message: t('system.role.rDesc1'), trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

const columns = computed(() => [
  {
    key: 'name',
    dataKey: 'name',
    width: 300,
    title: t('system.role.tenantname')
  },
  {
    key: 'id',
    dataKey: 'id',
    width: 150,
    title: 'id'
  },
  {
    width: 800,
    title: '权限信息',
    cellRenderer: ({ rowData }) => (
      <div style={{ minHeight: '20px', paddingTop: '10px', paddingBottom: '10px' }}>
        <div style={{ lineHeight: '20px' }} v-html={showResourceText(rowData)}></div>
      </div>
    )
  },
  {
    width: 120,
    key: 'oprate',
    title: '操作',
    cellRenderer: ({ rowData }) => (
      <ElButton
        disabled={route.query.type !== '2'}
        link
        type="primary"
        onClick={() => openSenceDialog(rowData)}
      >
        授权
      </ElButton>
    )
  }
])
// table列配置
const showResourceText = (rowData) => {
  console.log(rowData)

  let resources = rowData?.resources || []
  if (resources?.length < 1) return '-'
  let target = new Map()
  let textResult = ''
  console.log(resources)

  resources?.forEach((item) => {
    if (target.get(item.scene) && target.get(item.scene)?.length > 0) {
      let arr = target.get(item.scene)
      arr.push(item?.permissionType)
      target.set(item.scene, arr)
    } else {
      console.log('item?.permissionType', item?.permissionType)
      let arr = [] as any
      arr.push(item?.permissionType)
      target.set(item.scene, arr)
    }
  })

  sceneList.value.forEach((item) => {
    if (target.get(item.value) && target.get(item.value)?.length > 0) {
      textResult =
        textResult +
        `<div>${item.label}:
    ${target.get(item.value).map((permissionTypeItem) => permiPointMap.value[permissionTypeItem])}
      </div>`
    }
  })
  rowData.resourcesMap = target
  return textResult
  //   console.log(sceneList.value)
  // console.log(permiPointMap.value)
}

const treeOptions = ref<any[]>([]) // 部门树形结构

const deptExpand = ref(false) // 展开/折叠
const tableRef = ref() // 权限表格组件 Ref
const treeNodeAll = ref(false) // 全选/全不选
const checkStrictly = ref(true) // 是否严格模式，即父子不关联

const defaultExpandedRowKeys = ref([])
const initData = (treeData) => {
  // let treeData = await RoleApi.getListResourceListTenantSceneTrees(row.id)
  return treeData.map((item) => {
    if (item?.children && item?.children.length > 0) {
      defaultExpandedRowKeys.value.push(item?.id)
      item.children = initData(item.children)
    }
    return {
      ...item
    }
  })
}

const init = async (row: RoleApi.RoleVO) => {
  // console.log(row)
  // RoleApi.getRole(row.id).then((res) => {
  //   console.log(res)
  // })
  formData = reactive({ ...originFormData })
  resetForm()
  // 重置过滤条件
  filterEmptyPermission.value = false
  filterData.value = ''

  // 加载 Dept 列表。注意，必须放在前面，不然下面 setChecked 没数据节点

  // 设置数据
  formData.id = row.id
  formData.name = row.name
  formData.code = row.code
  // 移除autoAuthRuleIds相关代码

  // managementCustomerDataScope.value = initTreeData(
  //   await RoleApi.getListResourceListTenantSceneTrees(row.id)
  // )
  let resData = await RoleApi.getListResourceListTenantSceneTrees(row.id)
  let myData = await initData(resData)
  // console.log(myData)
  treeOptions.value = myData
  origintreeOptions.value = cloneDeep(myData) // 原始数据，用于重置
  console.log(sceneList.value)
  console.log(permiPointMap.value)

  formData.dataScope = row.dataScope

  // 所以在高级数据权限弹窗时需要判断dataScope是否为1，不是的话全都转为6，显示客户，提交也是按6提交
  if (formData.dataScope !== SystemDataScopeEnum.ALL) {
    //todo formData.dataScope这个逻辑目前只有1 和6
    formData.dataScope = 6
  }
}

/** 重置表单 */
const resetForm = () => {
  // 重置选项
  treeNodeAll.value = false
  deptExpand.value = false
  checkStrictly.value = true
  // 重置表单
  formData.value = {
    id: 0,
    name: '',
    code: '',
    dataScope: undefined
    // 移除autoAuthRuleIds
  }
  formRef.value?.resetFields()
}
//过滤
import { filter } from '@/utils/tree'

const filterData = ref()
const filterEmptyPermission = ref(false) // 是否过滤无权限节点
const origintreeOptions = ref<any[]>([]) // 原始数据，用于重置

// 检查节点是否有权限信息
const hasPermissions = (node) => {
  // 检查当前节点是否有权限
  if (node?.resources?.length > 0) {
    return true
  }

  // 检查子节点是否有权限
  if (node?.children && node.children.length > 0) {
    return node.children.some((child) => hasPermissions(child))
  }

  return false
}

const handlerFilter = (val) => {
  // 重置为原始数据
  let filteredData = cloneDeep(origintreeOptions.value)

  // 按名称过滤
  if (filterData.value) {
    filteredData = filter(filteredData, (item) => item.name.includes(filterData.value))
  }

  // 过滤无权限节点
  if (filterEmptyPermission.value) {
    filteredData = filterTreeByPermission(filteredData)
  }

  treeOptions.value = filteredData
}

// 过滤无权限的节点，保留有权限的父节点
const filterTreeByPermission = (treeData) => {
  return treeData.filter((node) => {
    // 检查节点是否有权限信息
    const hasPermission = hasPermissions(node)

    if (hasPermission) {
      // 如果节点有权限或子节点有权限，则递归过滤子节点
      if (node?.children && node.children.length > 0) {
        node.children = filterTreeByPermission(node.children)
      }
      return true
    }

    return false
  })
}

/**
 * 业务场景数据字典
 */
const sceneList = computed(() => {
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE) || []
})

const permiPointMap = computed(() => {
  return getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

//编辑业务场景

import senceDialog from './senceDialog.vue'
import { cloneDeep } from 'lodash-es'

const senceDialogRef = ref()
const openSenceDialog = (row) => {
  senceDialogRef.value.open(row, sceneList.value)
}

const route = useRoute()
const router = useRouter()
watch(
  () => route.query.id,
  (val) => {
    if (!val) return
    init({
      ...(route.query as unknown as RoleApi.RoleVO)
    })
  },
  {
    immediate: true
  }
)
//提供给senceDialog使用，用于重置数据
provide('init', init)
</script>
<style lang="scss" scoped></style>
