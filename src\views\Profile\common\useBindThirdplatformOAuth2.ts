import { socialAuthRedirect } from '@/api/system/user/socialUser'

const useBindThirdplatformOAuth2 = () => {
  /**
   * 绑定第三方账号
   *
   * @param type 第三方平台类型  谷歌34
   * @param ifRedirect 是否需要重定向
   * @param ifLogin 是否登录,重定向地址将变化
   * @returns 无返回值
   */
  const bindThirdplatformOAuth2 = (type: number, ifRedirect = false, ifLogin = false) => {
    // const redirectUri = location.origin + '/#/user/profile?type=' + row.type
    //绑定社交平台
    let redirectUri = location.origin + `/thirdplatform/oauth2/bind/${type}`
    //使用社交平台登录
    if (ifLogin) {
      redirectUri = location.origin + `/thirdplatform/oauth2/login/${type}`
    }
    // https://uat-service.umvcard.com/#/useradfasdf/?xxxxx
    // const redirectUri = h5url + '/#/user/profile?type=' + row.type
    // 进行跳转
    socialAuthRedirect(type, encodeURIComponent(redirectUri)).then((res) => {
      // window.location.href = res
      if (ifRedirect) {
        window.location.href = res
        return
      }
      window.open(res)
    })
  }

  return {
    bindThirdplatformOAuth2
  }
}

export default useBindThirdplatformOAuth2
