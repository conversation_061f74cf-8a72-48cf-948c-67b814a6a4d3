<template>
  <ContentWrap>
    <div class="new-add-wrap" v-loading="loading">
      <el-form :model="queryForm" :rules="formRules" ref="formRef" label-width="150">
        <el-form-item label="服务凭证名称" prop="name">
          <el-input
            v-model="queryForm.name"
            placeholder="请输入服务凭证名称"
            maxlength="40"
            clearable
          />
        </el-form-item>
        <el-form-item label="api 列表" required prop="apiListData">
          <section class="api-tree-wrap">
            <div class="top-block">
              <el-checkbox
                v-model="selectAllTree"
                label="是否全选"
                size="large"
                @change="handleSelectAllTree"
              />
              <el-checkbox
                v-model="expandAllTree"
                label="全部展开"
                size="large"
                @change="handleexpandAllTree"
              />
            </div>
            <el-divider style="margin: 0" />
            <el-tree
              ref="treeRef"
              class="tree-block"
              :data="treeDataList"
              :props="treeProps"
              node-key="onlyPathKey"
              show-checkbox
              :default-expanded-keys="expandedTreeKeys"
              :default-expand-all="expandAllTree"
              @check-change="handleTreeCheck"
            />
          </section>
        </el-form-item>
        <el-form-item label="密钥" prop="caKeyPhrase">
          <el-input
            v-model="queryForm.caKeyPhrase"
            placeholder="请输入密钥"
            clearable
            :disabled="type === 'edit'"
          />
        </el-form-item>
        <el-form-item label="数据加密令牌" prop="token">
          <el-input
            v-model="queryForm.token"
            placeholder="请输入数据加密令牌"
            clearable
            :disabled="type === 'edit'"
          />
        </el-form-item>
        <el-form-item label="凭证到期时间" prop="expireTime" v-if="type != 'edit'">
          <el-date-picker
            v-model="queryForm.expireTime"
            :value-format="'YYYY-MM-DD'"
            :disabled-date="disabledDate"
            placeholder="请输入凭证到期时间"
          />
        </el-form-item>
        <el-form-item label="接入网络白名单" prop="netWhiteList">
          <el-input v-model="queryForm.netWhiteList" placeholder="请输入ip" clearable />
          <!-- <IPInput /> -->
        </el-form-item>
        <el-form-item label="是否加密调用" prop="isEncrypted">
          <el-radio-group v-model="queryForm.isEncrypted">
            <el-radio :value="true" size="large">是</el-radio>
            <el-radio :value="false" size="large">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="防重放时间（毫秒）" prop="antiReplayTime">
          <el-input
            type="number"
            v-model="queryForm.antiReplayTime"
            placeholder="请输入ip"
            clearable
          />
        </el-form-item>
      </el-form>

      <section style="padding-top: 20px; text-align: center">
        <el-button type="primary" @click="handleSubmit(formRef)">确定</el-button>
        <el-button style="margin-left: 60px" @click="handleClose">取消</el-button>
      </section>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { newAddCredentialApi } from '@/api/system/vocherManagement'
import ContentWrap from '@/components/ContentWrap/src/ContentWrap.vue'
const props = defineProps({
  type: {
    type: String,
    default: ''
  },
  editForm: {
    type: Object,
    default: () => {}
  },
  apiManagementList: {
    type: Array,
    default: () => []
  }
})
const { type, editForm, apiManagementList } = toRefs(props)

const message = useMessage()
const loading = ref(false)
const queryForm = reactive<any>({
  name: '',
  apiListData: [],
  token: '',
  netWhiteList: '',
  caKeyPhrase: '',
  isEncrypted: false,
  antiReplayTime: 5000,
  expireTime: undefined
})
// ip地址和子网掩码正则检验
let ipPortRexg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)\/\d{1,5}$/
const formRules = ref({
  name: [{ required: true, message: '请输入', trrigger: 'blur' }],
  apiListData: [
    { type: 'array', required: true, message: '请勾选api列表', trrigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!queryForm.apiListData.length) {
          return callback(new Error('请勾选api列表'))
        }
        callback()
      },
      trrigger: 'blur'
    }
  ],
  caKeyPhrase: [
    { required: type.value === 'edit' ? false : true, message: '请输入密钥', trrigger: 'blur' }
  ],
  token: [{ required: false, message: '请输入', trrigger: 'blur' }],
  expireTime: [{ required: true, message: '请输入凭证到期时间', trrigger: 'blur' }],
  netWhiteList: [
    { required: false, message: '请输入', trrigger: 'blur' },
    { pattern: ipPortRexg, message: '输入的ip/子网掩码格式不对，参考例子：0.0.0.0/0' }
  ]
})
const formRef = ref()

const selectAllTree = ref(false)
const expandAllTree = ref(true)
const treeRef = ref()
const treeDataList = ref<any>([])
const treeProps = ref({
  children: 'children',
  label: 'name'
})

const treeListIds = ref<any>([])

/** 全选 */
const handleSelectAllTree = (flag) => {
  try {
    if (flag) {
      nextTick(() => {
        treeRef.value!.setCheckedKeys(treeListIds.value, false)
      })
    } else {
      nextTick(() => {
        treeRef.value!.setCheckedKeys([], false)
      })
    }
  } catch (error) {}
}

/** 展开全部 */
const expandedTreeKeys = ref<any>([])
const handleexpandAllTree = (flag) => {
  try {
    const nodes = treeRef.value?.store.nodesMap
    for (let node in nodes) {
      nodes[node].expanded = flag
    }
  } catch (error) {
    message.error('展开收起功能失效：' + error)
  }
}

/** 勾选checkbox触发 */
const handleTreeCheck = (data, checked, indeterminate) => {
  let checkedIds = treeRef.value!.getCheckedKeys(true) // false-父节点的id也存到集合，true-单纯只保存勾选的子节点
  queryForm.apiListData = [...checkedIds]
  console.log('选中的ids === ', checkedIds)
  formRef.value.validateField('apiListData', () => null)
  // 判断全勾选了，则全选按钮勾上
  isSelectAllTree()
}

// 判断全勾选了，则全选checkbox勾上
const isSelectAllTree = () => {
  if (!queryForm.apiListData.length) {
    selectAllTree.value = false
    return
  }
  let selectAllFlag = queryForm.apiListData.length === treeListIds.value.length
  selectAllTree.value = selectAllFlag
}

onMounted(() => {
  currentDate.value = new Date()
  treeDataList.value = apiManagementList.value
  console.log('新增，打印勾选列表 === ', treeDataList.value)
  treeListIds.value = dealIds(apiManagementList.value)
  if (type.value === 'edit') {
    initEdit()
  }
})

/** 将树结构的数据，children的onlyPathKey全部抽出来，保存在数组中，用于判断/勾选全部 */
const dealIds = (list) => {
  if (!list || !list.length) return []
  let result: any = []
  list.forEach((item) => {
    if (item && item.children && item.children.length) {
      let childrenIds = dealIds(item.children)
      result = [...result, ...childrenIds]
    } else {
      result.push(item.onlyPathKey)
    }
  })
  return result
}

const initEdit = () => {
  try {
    queryForm.name = editForm.value.name
    queryForm.token = editForm.value.token
    queryForm.netWhiteList = editForm.value.netWhiteList
    queryForm.isEncrypted = editForm.value.isEncrypted
    queryForm.antiReplayTime = editForm.value.antiReplayTime
    //编辑,保存id
    if (type.value == 'edit') {
      updateId.value = editForm.value.id
    }
    let apiListData = editForm.value.apiListData || ''
    if (apiListData.length) {
      queryForm.apiListData = [...apiListData]
      nextTick(() => {
        treeRef.value!.setCheckedKeys([...apiListData], false) // 这个false，是否仅选择子节点，即使子节点全选了，父节点也不选中
      })
    } else {
      queryForm.apiListData = []
    }
  } catch (error) {
    console.error('编辑初始化error：', error)
  }
}

const emits = defineEmits(['close-dlg', 'success'])
const handleClose = () => {
  emits('close-dlg')
}

const handleSubmit = (formEl) => {
  console.log('formEl', formEl)
  console.log('queryForm', queryForm)
  if (!formEl) return
  formEl.validate((valid, fields) => {
    if (!valid) return
    if (type.value == 'edit') {
      submitEdit()
    } else {
      submitNewAdd()
    }
  })
}
import { updateCredentialApi } from '@/api/system/vocherManagement'

//修改api凭证
const updateId = ref(0)
const submitEdit = async () => {
  try {
    let res = await updateCredentialApi({
      id: updateId.value,
      name: queryForm.name,
      apiListData: queryForm.apiListData,
      netWhiteList: queryForm.netWhiteList,
      isEncrypted: queryForm.isEncrypted,
      antiReplayTime: queryForm.antiReplayTime
    })
    console.log(res)

    message.success('编辑成功')

    emits('success')
    emits('close-dlg', true)
  } catch (error) {
  } finally {
    loading.value = false
  }
  emits('close-dlg')
}

const submitNewAdd = async () => {
  try {
    loading.value = true

    console.log('新增 params === ', queryForm)
    const res = await newAddCredentialApi(queryForm)
    console.log('新增res === ', res)
    if (res.code == 0) {
      message.success('新增成功')
      emits('success')
      emits('close-dlg', true)
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}

const selectedDateTime = ref(null)
const currentDate = ref(new Date())

// 禁用今天及之前的日期
const disabledDate = (time) => {
  // 今天0点的时间戳
  const today = new Date(currentDate.value.toDateString()).getTime()
  // 目标日期的时间戳（忽略时分秒）
  const targetDate = new Date(time.toDateString()).getTime()
  // 禁用：目标日期 < 明天（即今天及之前）
  return targetDate < today + 24 * 60 * 60 * 1000
}

// 判断是否选择的是明天
const isTomorrow = computed(() => {
  if (!selectedDateTime.value) return false
  const today = new Date(currentDate.value.toDateString()).getTime()
  const selectedDate = new Date(selectedDateTime.value.toDateString()).getTime()
  return selectedDate === today + 24 * 60 * 60 * 1000
})

// 禁用小时（仅当选择的是明天时生效）
const disabledHours = () => {
  if (isTomorrow.value) {
    // 明天时，禁用当前小时之前的所有小时
    const currentHour = currentDate.value.getHours()
    return Array.from({ length: currentHour }, (_, i) => i)
  }
  return []
}

// 禁用分钟（仅当选择的是明天且小时等于当前小时时生效）
const disabledMinutes = (selectedHour) => {
  if (isTomorrow.value && selectedHour === currentDate.value.getHours()) {
    // 明天且小时相同，禁用当前分钟之前的所有分钟
    const currentMinute = currentDate.value.getMinutes()
    return Array.from({ length: currentMinute }, (_, i) => i)
  }
  return []
}

// 禁用秒数（仅当选择的是明天、小时和分钟都等于当前时生效）
const disabledSeconds = (selectedHour, selectedMinute) => {
  if (
    isTomorrow.value &&
    selectedHour === currentDate.value.getHours() &&
    selectedMinute === currentDate.value.getMinutes()
  ) {
    // 明天、小时和分钟都相同，禁用当前秒数之前的所有秒
    const currentSecond = currentDate.value.getSeconds()
    return Array.from({ length: currentSecond }, (_, i) => i)
  }
  return []
}

// 选中时间变化时更新当前时间
const handleChange = () => {
  currentDate.value = new Date()
}
</script>

<style scoped>
.new-add-wrap {
  .api-tree-wrap {
    width: 100%;
    height: 220px;
    border: 1px solid #e5e7eb;
    padding: 1px 11px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .top-block {
      text-align: left;
    }
    .tree-block {
      flex: 1;
      overflow: auto;
    }
  }
}
</style>
