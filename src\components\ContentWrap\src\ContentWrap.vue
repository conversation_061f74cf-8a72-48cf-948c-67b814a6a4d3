<template>
  <ElCard
    ref="contentWrapRef"
    :body-style="{ height: '100%', paddingBottom: ifTable ? '0px' : 'var(--el-card-padding)' }"
    :class="[
      prefixCls,
      props.ifTable ? 'tableClass' : '',
      isFullscreen ? 'content-wrap--fullscreen' : ''
    ]"
    shadow="never"
  >
    <template v-if="title" #header>
      <div class="flex items-center">
        <span class="text-16px font-700">{{ title }}</span>
        <ElTooltip v-if="message" effect="dark" placement="right">
          <template #content>
            <div class="max-w-200px">{{ message }}</div>
          </template>
          <Icon class="ml-5px" icon="bi:question-circle-fill" :size="14" />
        </ElTooltip>
      </div>
    </template>
    <div class="!h-full flex flex-col">
      <div v-if="$slots.search" class="!mb-0">
        <div v-show="showSearchForm">
          <slot name="search"> </slot>
        </div>
        <el-divider class="!m-0 !mb-3">
          <el-link
            v-if="showSearchForm"
            :underline="false"
            @click="() => (showSearchForm = false)"
            :icon="CaretTop"
          >
            {{ t('common.shrink') }}
          </el-link>
          <el-link
            v-if="!showSearchForm"
            :underline="false"
            @click="() => (showSearchForm = true)"
            :icon="CaretBottom"
          >
            {{ t('common.expand') }}
          </el-link>
        </el-divider>
      </div>
      <slot></slot>
      <div v-if="$slots.pagination" class="!mt-0">
        <slot name="pagination"> </slot>
      </div>
    </div>
  </ElCard>
</template>

<script setup lang="ts">
import { ElCard, ElTooltip } from 'element-plus'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { nextTick, onMounted, onUnmounted, ref, provide, watch, onActivated, onUpdated } from 'vue'
import { debounce } from 'lodash-es'

const { t } = useI18n()
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('content-wrap')

const props = defineProps({
  title: propTypes.string.def(''),
  message: propTypes.string.def(''),
  ifTable: {
    type: Boolean,
    default: false
  }
})

// 定义暴露给外部的方法
const emit = defineEmits(['fullscreen-change'])

// 全屏状态
const isFullscreen = ref(false)

// ContentWrap的DOM引用
const contentWrapRef = ref<any>(null)

// 切换全屏状态
const toggleFullscreen = () => {
  // 直接切换，不使用nextTick，避免延迟响应
  isFullscreen.value = !isFullscreen.value

  // 处理进入全屏时的文档滚动行为
  if (isFullscreen.value) {
    // 阻止body滚动
    document.body.style.overflow = 'hidden'
  } else {
    // 恢复body滚动
    document.body.style.overflow = ''
  }

  // 触发全屏状态变更事件
  emit('fullscreen-change', isFullscreen.value)
}

// 使用依赖注入提供ContentWrap的全屏功能和状态
provide('contentWrapFullscreen', {
  toggleFullscreen,
  isFullscreen,
  // 添加一个方法让子组件可以监听全屏状态变化
  onFullscreenChange: (callback: (isFullscreen: boolean) => void) => {
    watch(
      isFullscreen,
      (newVal) => {
        callback(newVal)
      },
      { immediate: true }
    )
  }
})

// 处理ESC键退出全屏
const handleEscKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && isFullscreen.value) {
    e.preventDefault() // 阻止默认行为
    e.stopPropagation() // 阻止事件冒泡
    toggleFullscreen()
  }
}

const setHeight = () => {
  if (!props.ifTable || !contentWrapRef.value || !contentWrapRef.value.$el) return

  nextTick(() => {
    try {
      let tableRefEl = contentWrapRef.value.$el
      const tableTop = tableRefEl.getBoundingClientRect().top
      const clientHeight = document?.querySelector('#app')?.clientHeight as number
      // const footerHeight = document?.querySelector('.v-footer')?.clientHeight as number
      const footerHeight = 0

      if (clientHeight && tableTop) {
        const calculatedHeight = clientHeight - tableTop - footerHeight - 20
        if (calculatedHeight > 100) {
          // 确保计算的高度是合理的
          tableRefEl.style.height = `${calculatedHeight}px`
        }
      }
    } catch (err) {
      console.error('ContentWrap设置高度失败:', err)
    }
  })
}

// 使用lodash的debounce函数
const debouncedSetHeight = debounce(setHeight, 100)

// 处理窗口大小变化，使用特定的事件名称以避免冲突
const handleContentWrapResize = () => {
  if (props.ifTable) {
    debouncedSetHeight()
  }
}

onMounted(() => {
  if (props.ifTable) {
    debouncedSetHeight()
  }
  // 添加ESC键监听 - 使用capture模式确保事件在捕获阶段被处理
  document.addEventListener('keydown', handleEscKeydown, { capture: true })
  // 添加窗口大小变化监听，使用特定的事件名称
  window.addEventListener('resize', handleContentWrapResize)
  // 监听自定义事件
  window.addEventListener('content-wrap-resize', debouncedSetHeight)
})

onActivated(() => {
  if (props.ifTable) {
    debouncedSetHeight()
  }
})

onUpdated(() => {
  if (props.ifTable) {
    debouncedSetHeight()
  }
})

onUnmounted(() => {
  // 移除ESC键监听 - 确保使用相同的配置
  document.removeEventListener('keydown', handleEscKeydown, { capture: true })
  // 移除窗口大小变化监听，使用特定的事件名称
  window.removeEventListener('resize', handleContentWrapResize)
  // 移除自定义事件监听
  window.removeEventListener('content-wrap-resize', debouncedSetHeight)
})

//是否展示搜索
let showSearchForm = ref(true)

// 暴露方法给父组件
defineExpose({
  toggleFullscreen,
  isFullscreen,
  setHeight
})
</script>

<style scoped>
/* 全屏样式 */
.content-wrap--fullscreen {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2000;
  margin: 0;
  padding: 0;
  height: 100vh !important;
  width: 100vw !important;
  overflow: auto;
  transition: all 0.3s ease-in-out;
  background-color: var(--el-bg-color);
  border-radius: 0 !important;
}

.content-wrap--fullscreen :deep(.el-card__body) {
  height: 100% !important;
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 20px;
}

.content-wrap--fullscreen :deep(.el-card__header) {
  padding: 12px 20px;
}
</style>
