import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import ErrorNotificationIcon from '@/components/Common/ErrorNotificationIcon.vue'
import { useI18n } from './useI18n'

type NotificationType = '' | 'success' | 'warning' | 'info' | 'error'
interface MessageConfigType {
  // 标题
  title?: string
  // 通知栏正文内容
  message?: string | VNode
  // 是否将 message 属性作为 HTML 片段处理
  dangerouslyUseHTMLString?: boolean
  // 通知的类型
  type?: NotificationType
  // 自定义图标。若设置了 type，则 icon 会被覆盖
  icon?: string | Component
  // 自定义类名
  customClass?: string
  // 显示时间，单位为毫秒。值为 0 则不会自动关闭
  duration?: number
  // 自定义弹出位置
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  // 是否显示关闭按钮
  showClose?: boolean
  // 关闭时的回调函数
  onClose?: () => void
  // 点击 Notification 时的回调函数
  onClick?: () => void
  // 相对屏幕顶部的偏移量，偏移的距离，在同一时刻，所有的 Notification 实例应当具有一个相同的偏移量
  offset?: number
  // 设置 notification 的根元素，默认为 document.body
  appendTo?: string | HTMLElement
  // 初始 zIndex
  zIndex?: number
}

//默认配置
const messageConfig: MessageConfigType = {
  duration: 5000
}

export const useMessage = () => {
  const { t } = useI18n()
  return {
    /**
     * 消息提示
     * @param {string} content - 提示内容
     * @param {MessageConfigType} config - 配置参数
     */
    info(content: string, config?: MessageConfigType) {
      ElMessage.info({ message: content, ...config, ...messageConfig })
    },

    /**
     * 错误消息
     * @param {string} content - 提示内容
     * @param {MessageConfigType} config - 配置参数
     */
    error(content: string, config?: MessageConfigType) {
      ElMessage.error({ message: content, ...config, ...messageConfig })
    },

    /**
     * 成功消息
     * @param {string} content - 提示内容
     * @param {MessageConfigType} config - 配置参数
     */
    success(content: string, config?: MessageConfigType) {
      ElMessage.success({ message: content, ...config, ...messageConfig })
    },

    /**
     * 警告消息
     * @param {string} content - 提示内容
     * @param {MessageConfigType} config - 配置参数
     */
    warning(content: string, config?: MessageConfigType) {
      ElMessage.warning({ message: content, ...config, ...messageConfig })
    },

    /**
     * 弹出提示
     * @param {string} content - 提示内容
     *
     */
    alert(content: string) {
      ElMessageBox.alert(content, t('common.confirmTitle'))
    },

    /**
     * 错误提示
     * @param {string} content - 提示内容
     *
     */
    alertError(content: string) {
      ElMessageBox.alert(content, t('common.confirmTitle'), { type: 'error' })
    },

    /**
     * 成功提示
     * @param {string} content - 提示内容
     *
     */
    alertSuccess(content: string) {
      ElMessageBox.alert(content, t('common.confirmTitle'), { type: 'success' })
    },

    /**
     * 警告提示
     * @param {string} content - 提示内容
     *
     */
    alertWarning(content: string) {
      ElMessageBox.alert(content, t('common.confirmTitle'), { type: 'warning' })
    },
    /**
     * 通知提示
     * @param {string} content - 提示内容
     * @param {MessageConfigType} config - 配置参数
     */
    notify(content: string, config?: MessageConfigType) {
      ElNotification.info({ message: content, ...config, ...messageConfig })
    },

    /**
     * 错误通知
     * @param {string} content - 提示内容
     * @param {MessageConfigType} config - 配置参数
     *
     */
    notifyError(content: string, config?: MessageConfigType) {
      ElNotification({
        icon: ErrorNotificationIcon,
        message: content,
        ...config,
        ...messageConfig
      })
    },

    /**
     * 成功通知
     * @param {string} content - 提示内容
     * @param {MessageConfigType} config - 配置参数
     */
    notifySuccess(content: string, config?: MessageConfigType) {
      ElNotification.success({ message: content, ...config, ...messageConfig })
    },
    /**
     * 警告通知
     * @param {string} content - 提示内容
     *
     */
    notifyWarning(content: string, config?: MessageConfigType) {
      ElNotification.warning({ message: content, ...config, ...messageConfig })
    },
    /**
     * 确认窗体
     * @param {string} content - 提示内容
     * @param {string} tip - 提示标题
     */
    confirm(content: string, tip?: string) {
      return ElMessageBox.confirm(content, tip ? tip : t('common.confirmTitle'), {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
    },
    /**
     * 删除窗体
     * @param {string} content - 提示内容
     * @param {string} tip - 提示标题
     */
    delConfirm(content?: string, tip?: string) {
      return ElMessageBox.confirm(
        content ? content : t('common.delMessage'),
        tip ? tip : t('common.confirmTitle'),
        {
          confirmButtonText: t('common.ok'),
          cancelButtonText: t('common.cancel'),
          type: 'warning'
        }
      )
    },
    /**
     * 导出窗体
     * @param {string} content - 提示内容
     * @param {string} tip - 提示标题
     */
    exportConfirm(content?: string, tip?: string) {
      return ElMessageBox.confirm(
        content ? content : t('common.exportMessage'),
        tip ? tip : t('common.confirmTitle'),
        {
          confirmButtonText: t('common.ok'),
          cancelButtonText: t('common.cancel'),
          type: 'warning'
        }
      )
    },
    /**
     * 提交内容
     * @param {string} content - 提示内容
     *@param {string} tip - 提示标题
     */
    prompt(content: string, tip: string) {
      return ElMessageBox.prompt(content, tip, {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
    }
  }
}
