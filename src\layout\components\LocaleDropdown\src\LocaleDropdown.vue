<script setup lang="ts">
defineOptions({
  name: 'LocaleDropdown'
})

import { useLocaleStore } from '@/store/modules/locale'
import { useLocale } from '@/hooks/web/useLocale'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'
import { usePermissionStore } from '@/store/modules/permission'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('locale-dropdown')

const props = defineProps({
  color: propTypes.string.def(''),
  isGetMenu: {
    type: [Boolean],
    default: true
  }
})

const localeStore = useLocaleStore()

const langMap = computed(() => localeStore.getLocaleMap)

const currentLang = computed(() => localeStore.getCurrentLocale)

const permissionStore = usePermissionStore()

import { useDictStoreWithOut } from '@/store/modules/dict'
const dictStore = useDictStoreWithOut()

const setLang = async (lang: LocaleType) => {
  if (lang === unref(currentLang).lang) return

  localeStore.setCurrentLocale({
    lang
  })
  const { changeLocale } = useLocale()
  changeLocale(lang)
  // 需要重新加载页面让整个语言多初始化
  const loadingInstance = ElLoading.service({
    lock: true,
    background: 'rgba(0, 0, 0, 0.7)'
  })
  props.isGetMenu && (await permissionStore.generateRoutes(true))
  props.isGetMenu && (await userStore.regetUserInfo())
  props.isGetMenu && (await dictStore.resetDict())
  window.location.reload()
  loadingInstance.close()
}
</script>
<template>
  <ElDropdown :class="prefixCls" trigger="click" @command="setLang">
    <icon-ion-language :style="{ color }" class="cursor-pointer" />
    <template #dropdown>
      <ElDropdownMenu>
        <ElDropdownItem v-for="item in langMap" :key="item.lang" :command="item.lang">
          {{ item.name }}
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>
</template>
