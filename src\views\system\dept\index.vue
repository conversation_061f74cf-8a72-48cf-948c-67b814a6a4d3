<template>
  <!-- 列表 -->
  <UmvContent>
    <UmvQuery v-model="queryParams" :opts="queryOpts" @check="handleQuery" @reset="resetQuery" />

    <UmvTable
      v-loading="loading"
      :data="list"
      row-key="id"
      :default-expand-all="isExpandAll"
      @refresh="getList"
      v-if="refreshTable"
    >
      <template #tools>
        <el-button
          type="primary"
          plain
          size="small"
          @click="openForm('create')"
          v-hasPermi="['system:dept:create']"
        >
          <icon-ep-plus style="font-size: 12px" class="mr-5px" />{{ t('common.newAdd') }}
        </el-button>
        <el-button type="danger" plain size="small" @click="toggleExpandAll">
          <Icon icon="ep:sort" class="mr-5px" />{{ t('common.collapse') }}
        </el-button>
      </template>
      <el-table-column prop="name" :label="t('system.dept.name')" width="260" />
      <el-table-column prop="code" :label="t('system.dept.code')" width="220" />
      <el-table-column prop="leader" :label="t('system.dept.leader')" width="120">
        <template #default="scope">
          {{ userList.find((user) => user.id === scope.row.leaderUserId)?.nickname }}
        </template>
      </el-table-column>
      <el-table-column prop="sort" :label="t('system.dept.sort')" width="100" />
      <el-table-column prop="status" :label="t('system.dept.status')" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('system.dept.createTime')"
        align="center"
        prop="createTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column :label="t('common.operate')" align="center" class-name="fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:dept:update']"
          >
            {{ t('common.modify') }}
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:dept:delete']"
          >
            {{ t('common.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </UmvTable>

    <!-- 表单弹窗：添加/修改 -->
    <DeptForm ref="formRef" @success="getList" />
  </UmvContent>
</template>
<script lang="tsx" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import DeptForm from './DeptForm.vue'
import * as UserApi from '@/api/system/user'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import UmvTable from '@/components/UmvTable'
import UmvContent from '@/components/UmvContent'

defineOptions({ name: 'SystemDept' })

const message = useMessage() // 消息弹窗
const { t, ifEn } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref() // 列表的数据
const queryParams = ref({
  title: '',
  name: undefined,
  status: undefined,
  pageNo: 1,
  pageSize: 100
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: t('system.dept.title'),
    defaultVal: undefined,
    controlRender: (form) => (
      <el-input
        v-model={form.name}
        placeholder={t('system.dept.namePlaceholder')}
        clearable
        class="!w-260px"
      />
    )
  },
  status: {
    label: t('system.dept.status'),
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select
        v-model={form.status}
        placeholder={t('system.dept.statusPlaceholder')}
        clearable
        class="!w-260px"
      >
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  }
})

const queryFormRef = ref() // 搜索的表单
const isExpandAll = ref(true) // 是否展开，默认全部展开
const refreshTable = ref(true) // 重新渲染表格状态
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeptApi.getDeptPage(queryParams.value)
    list.value = handleTree(data)
  } finally {
    loading.value = false
  }
}

/** 展开/折叠操作 */
const toggleExpandAll = () => {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value.pageNo = 1
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  //先判断该部门下是否有用户关联
  const params = {
    deptId: id,
    ...queryParams.value
  }
  const data = await UserApi.getUserPage(params)
  if (data.total > 0) {
    return message.notifyError(t('system.dept.rDesc8')) //'该部门下有关联用户，请将关联用户移至其它部门！')
  }

  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DeptApi.deleteDept(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  // 获取用户列表
  userList.value = await UserApi.getSimpleUserList()
})
</script>
