<template>
  <div class="authorization-rule">
    <ContentWrap>
      <el-form :model="queryParams" :inline="true" ref="queryForm" label-width="140px">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="规则名称" prop="name" style="width: 100%">
              <el-input v-model="queryParams.name" placeholder="请输入规则名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="规则匹配器类型" prop="matcherType" style="width: 100%">
              <el-select
                v-model="queryParams.matcherType"
                placeholder="请选择规则匹配器类型"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="(item, index) in toolMap.matcherTypesList"
                  :label="item.value"
                  :value="item.key"
                  :key="index"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item style="width: 100%">
              <el-button @click="getList">
                <icon-ep-search class="mr-5px" style="font-size: 12px" />
                {{ t('common.query') }}
              </el-button>
              <el-button @click="resetQuery">
                <icon-ep-refresh class="mr-5px" style="font-size: 12px" />
                {{ t('common.reset') }}
              </el-button>
              <el-button
                type="primary"
                plain
                @click="openForm('create')"
                v-hasPermi="['system:user:create']"
              >
                <icon-ep-plus style="font-size: 12px" class="mr-2px" /> {{ t('common.newAdd') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <!-- 列表 -->
      <el-table v-loading="loading" :data="list">
        <el-table-column label="编号" align="center" prop="id" width="60" />
        <el-table-column label="名称" align="center" prop="name" min-width="160" />
        <el-table-column label="匹配器类型" align="center" prop="matcherType" min-width="160">
          <template #default="{ row }">
            {{ toolMap.matcherTypes[row.matcherType] }}
          </template>
        </el-table-column>
        <el-table-column label="匹配器" align="center" min-width="200">
          <template #default="{ row }">
            {{
              `${
                toolMap.ruleListMatchersMap[row.matcherType][
                  row.matcherType === 20 ? row.matchScriptId : row.matcherBeanId
                ]
              }[${row.matcherType === 20 ? row.matchScriptId : row.matcherBeanId}]`
            }}
          </template>
        </el-table-column>
        <el-table-column label="业务场景" align="center" prop="scenes" min-width="200">
          <template #default="{ row }">
            {{ sceneTransform(row.scenes) }}
          </template>
        </el-table-column>
        <el-table-column label="权限类型" align="center" prop="permissionTypes" min-width="160">
          <template #default="{ row }">
            {{ permissionTypesTransform(row.permissionTypes) }}
          </template>
        </el-table-column>
        <el-table-column label="备注信息" align="center" prop="remark" min-width="200" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ utils.formatTime(scope.row.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="200"
          align="center"
          class-name="small-padding fixed-width"
          fixed="right"
        >
          <template #default="scope">
            <el-button type="primary" link @click="openForm('edit', scope.row)">修改</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <oprateForm ref="oprateFormRef" @success="getList" />
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'AuthorizationRule'
})

import oprateForm from './components/oprateForm.vue'
import * as utils from '@/utils/index'
import {
  getResourceAutomaticAuthorizationRulePage,
  deleteResourceAutomaticAuthorizationRule
} from '@/api/system/authorizationRule/index'
const { t } = useI18n() // 国际化
// 查询参数
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  matcherType: undefined,
  matcherBeanId: undefined,
  matchScriptId: undefined,
  matchScript: undefined,
  scenes: undefined,
  permissionTypes: undefined,
  remark: undefined,
  createTime: undefined
})

const total = ref(0)

const loading = ref(false)

/** 查询列表 */
const getList = async () => {
  try {
    loading.value = true
    const res = await getResourceAutomaticAuthorizationRulePage(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleQuery = async () => {
  queryParams.value.pageNo = 1
  getList()
}

const resetQuery = async () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    matcherType: undefined,
    matcherBeanId: undefined,
    matchScriptId: undefined,
    matchScript: undefined,
    scenes: undefined,
    permissionTypes: undefined,
    remark: undefined,
    createTime: []
  }
  handleQuery()
}

const list: Ref<any> = ref([])

const message = useMessage() // 消息弹窗

const handleDelete = async (row) => {
  await message.delConfirm('确定删除？')
  try {
    await deleteResourceAutomaticAuthorizationRule(row.id)
    message.success('删除成功')
    getList()
  } finally {
  }
}

const oprateFormRef = ref()

// 新增或编辑
const openForm = (_type, row?) => {
  oprateFormRef.value.open(row, toolMap.value)
}

const exportLoading = ref(false)

// 导出
const handleExport = async () => {}

const systemPermissionPointsMap = computed(() => {
  //权限类型Map
  return getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce((a, b) => {
    return {
      ...a,
      [b.value]: b.label
    }
  }, {})
})

const permissionTypesTransform = (arr) => {
  //权限类型显示
  return arr.map((el) => systemPermissionPointsMap.value[el]).join(',')
}

const sceneMap = computed(() => {
  //场景
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE).reduce((a, b) => {
    return {
      ...a,
      [b.value]: b.label
    }
  }, {})
})

const sceneTransform = (arr) => {
  //场景显示
  return arr.map((el) => sceneMap.value[el]).join(',')
}

import {
  getResourceAutomaticAuthorizationRuleListMatcherTypes,
  getResourceAutomaticAuthorizationRuleListMatchers
} from '@/api/system/authorizationRule/index'

const toolMap = ref({
  matcherTypes: {},
  matcherTypesList: [],
  ruleListMatchers: {},
  ruleListMatchersMap: {}
})

// 初始化所有工具map
const getInitData = async () => {
  toolMap.value.matcherTypesList = await getResourceAutomaticAuthorizationRuleListMatcherTypes()
  toolMap.value.matcherTypes = toolMap.value.matcherTypesList.reduce((a, b) => {
    return {
      ...a,
      [b.key]: b.value
    }
  }, {})
  toolMap.value.matcherTypesList.map(async (el) => {
    const res = await getResourceAutomaticAuthorizationRuleListMatchers(el.key)
    toolMap.value.ruleListMatchers[el.key] = res
    toolMap.value.ruleListMatchersMap[el.key] = res.reduce((a, b) => {
      return {
        ...a,
        [b.key]: b.value
      }
    }, {})
  })
}

onMounted(() => {
  getInitData()
  getList()
})
</script>
