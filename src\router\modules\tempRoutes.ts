import { Layout } from '@/utils/routerHelper'

const tempRoutes: AppRouteRecordRaw[] = [
  {
    path: '/systemSubscribeTest',
    component: Layout,
    name: 'systemSubscribeTest',
    meta: {
      alwaysShow: false,
      canTo: true
    },
    children: [
      {
        path: 'systemSubscribe',
        component: () => import('@/views/demo/subscribe/index.vue'),
        name: 'systemSubscribe',
        meta: {
          canTo: true,
          noCache: true,
          noTagsView: false,
          title: '订阅管理'
        }
      }
    ]
  }
  // {
  //   path: '/application',
  //   component: Layout,
  //   name: 'Application',
  //   meta: {
  //     alwaysShow: false,
  //     canTo: true
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/system/application/index.vue'),
  //       name: 'ApplicationIndex',
  //       meta: {
  //         canTo: true,
  //         noCache: true,
  //         noTagsView: false,
  //         title: '应用管理'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/resourceSetting',
  //   component: Layout,
  //   name: 'ResourceSetting',
  //   meta: {
  //     alwaysShow: false,
  //     canTo: true
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/system/resourceSetting/index.vue'),
  //       name: 'ResourceSettingIndex',
  //       meta: {
  //         canTo: true,
  //         noCache: true,
  //         noTagsView: false,
  //         title: '代客授权'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/customerServiceProcessSetting',
  //   component: Layout,
  //   name: 'customerServiceProcessSetting',
  //   meta: {
  //     alwaysShow: false,
  //     canTo: true
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/system/customerServiceProcess/index.vue'),
  //       name: 'customerServiceProcess',
  //       meta: {
  //         canTo: true,
  //         noCache: true,
  //         noTagsView: false,
  //         title: '代客操作流程'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/menuPreview',
  //   component: Layout,
  //   name: 'menuPreview',
  //   meta: {
  //     alwaysShow: false,
  //     canTo: true
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/system/menuPreview/index.vue'),
  //       name: 'menuPreviewIndex',
  //       meta: {
  //         canTo: true,
  //         noCache: true,
  //         noTagsView: false,
  //         title: '菜单预览'
  //       }
  //     }
  //   ]
  // }
]

export default tempRoutes
