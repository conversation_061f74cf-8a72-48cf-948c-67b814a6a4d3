export type NotOfUserReqParams = {
  /**
   * 应用编码
   */
  applicationCode?: string
  /**
   * 角色标识，模糊匹配
   */
  code?: string
  /**
   * 开始时间
   */
  createTime?: Date[]
  /**
   * 角色名称，模糊匹配
   */
  name?: string
  /**
   * 页码，从 1 开始
   */
  pageNo: number
  /**
   * 每页条数，最大值为 100
   */
  pageSize: number
  /**
   * 展示状态，参见 CommonStatusEnum 枚举类
   */
  status?: number
  /**
   * 角色类型，精确匹配。参考 RoleTypeEnum 枚举
   */
  types?: number[]
  /**
   * 用户角色编号列表
   */
  userRoleIds?: number[]
  [property: string]: any
}
/**
 * RoleRespVO，管理后台 - 角色信息 Response VO
 */
export type NotOfUserRes = {
  /**
   * 应用id
   */
  appId?: number
  /**
   * 应用编码
   */
  applicationCode?: string
  /**
   * 已选授权规则ID列表
   */
  autoAuthRuleIds?: number[]
  /**
   * 子角色集合
   */
  children?: NotOfUserRes[]
  /**
   * 客户端Id
   */
  clientId?: number
  /**
   * 客户端名
   */
  clientName?: string
  /**
   * 角色编码
   */
  code: string
  /**
   * 创建时间
   */
  createTime: Date
  /**
   * 数据范围，参见 DataScopeEnum 枚举类
   */
  dataScope: number
  /**
   * 数据范围(指定部门数组)
   */
  dataScopeDeptIds?: number[]
  /**
   * 角色是否可编辑
   */
  editable: boolean
  /**
   * 角色暴露方
   */
  exposer?: string
  /**
   * 暴露的角色编码
   */
  exposeRoleCode?: string
  /**
   * 角色编号
   */
  id: number
  /**
   * 是否为暴露角色
   */
  isExpose?: boolean
  /**
   * 是否过期
   */
  isStale?: boolean
  /**
   * 角色名称
   */
  name: string
  pappId?: number
  /**
   * 父角色编码
   */
  parentCode?: string
  /**
   * 父角色编号
   */
  parentId?: number
  /**
   * 备注
   */
  remark?: string
  /**
   * 显示顺序不能为空
   */
  sort: number
  /**
   * 状态，参见 CommonStatusEnum 枚举类
   */
  status: number
  /**
   * 角色标签
   */
  tags?: string[]
  /**
   * 角色类型，参见 RoleTypeEnum 枚举类
   */
  type: number
  [property: string]: any
}
//已分配角色列表查询参数
export type OfUserReqParams = {
  /**
   * 应用编码
   */
  applicationCode?: string
  /**
   * 角色标识，模糊匹配
   */
  code?: string
  /**
   * 开始时间
   */
  createTime?: Date[]
  /**
   * 角色名称，模糊匹配
   */
  name?: string
  /**
   * 页码，从 1 开始
   */
  pageNo: number
  /**
   * 每页条数，最大值为 100
   */
  pageSize: number
  /**
   * 展示状态，参见 CommonStatusEnum 枚举类
   */
  status?: number
  /**
   * 角色类型，精确匹配。参考 RoleTypeEnum 枚举
   */
  types?: number[]
  /**
   * 用户角色编号列表
   */
  userRoleIds?: number[]
  [property: string]: any
}
//已分配角色列表查询结果
export type OfUserRes = {
  /**
   * 应用id
   */
  appId?: number
  /**
   * 应用编码
   */
  applicationCode?: string
  /**
   * 已选授权规则ID列表
   */
  autoAuthRuleIds?: number[]
  /**
   * 子角色集合
   */
  children?: OfUserRes[]
  /**
   * 客户端Id
   */
  clientId?: number
  /**
   * 客户端名
   */
  clientName?: string
  /**
   * 角色编码
   */
  code: string
  /**
   * 创建时间
   */
  createTime: Date
  /**
   * 数据范围，参见 DataScopeEnum 枚举类
   */
  dataScope: number
  /**
   * 数据范围(指定部门数组)
   */
  dataScopeDeptIds?: number[]
  /**
   * 角色是否可编辑
   */
  editable: boolean
  /**
   * 角色暴露方
   */
  exposer?: string
  /**
   * 暴露的角色编码
   */
  exposeRoleCode?: string
  /**
   * 角色编号
   */
  id: number
  /**
   * 是否为暴露角色
   */
  isExpose?: boolean
  /**
   * 是否过期
   */
  isStale?: boolean
  /**
   * 角色名称
   */
  name: string
  pappId?: number
  /**
   * 父角色编码
   */
  parentCode?: string
  /**
   * 父角色编号
   */
  parentId?: number
  /**
   * 备注
   */
  remark?: string
  /**
   * 显示顺序不能为空
   */
  sort: number
  /**
   * 状态，参见 CommonStatusEnum 枚举类
   */
  status: number
  /**
   * 角色标签
   */
  tags?: string[]
  /**
   * 角色类型，参见 RoleTypeEnum 枚举类
   */
  type: number
  [property: string]: any
}
