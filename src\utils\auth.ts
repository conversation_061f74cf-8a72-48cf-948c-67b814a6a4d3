import { useCache } from '@/hooks/web/useCache'
import { decrypt, encrypt } from '@/utils/jsencrypt'

const { wsCache } = useCache()

const AccessTokenKey = 'ACCESS_TOKEN'
const RefreshTokenKey = 'REFRESH_TOKEN'
const CompleteTokenKey = 'COMPLETE_TOKEN'

// 获取token
export const getAccessToken = () => {
  // 此处与TokenKey相同，此写法解决初始化时Cookies中不存在TokenKey报错
  return wsCache.get(AccessTokenKey) ? wsCache.get(AccessTokenKey) : wsCache.get('ACCESS_TOKEN')
}

// 刷新token
export const getRefreshToken = () => {
  return wsCache.get(RefreshTokenKey)
}

// 设置token
import { OAuth2OpenAccessTokenRespType } from '@/api/login/types'

/**
 * 设置OAuth2令牌
 *
 * @param tokenData OAuth2令牌数据对象
 * @param ifSetCompleteToken 是否设置完整的令牌对象，默认为true
 */
export const setToken = (tokenData: OAuth2OpenAccessTokenRespType, ifSetCompleteToken = true) => {
  wsCache.set(
    RefreshTokenKey,
    tokenData?.refresh_token ? tokenData.refresh_token : tokenData.refreshToken
    // {
    //   exp: parseInt(tokenData.expires_in as unknown as string)
    // }
  )
  wsCache.set(
    AccessTokenKey,
    tokenData?.access_token ? tokenData.access_token : tokenData.accessToken
  )
  if (!ifSetCompleteToken) return
  wsCache.set(CompleteTokenKey, tokenData)
}

// 获取完整token，目的是存储过期时间
export const getCompleteToken = (): OAuth2OpenAccessTokenRespType => {
  return wsCache.get(CompleteTokenKey)
}

// 删除token
export const removeToken = () => {
  wsCache.delete(AccessTokenKey)
  wsCache.delete(RefreshTokenKey)
  wsCache.delete(CompleteTokenKey)
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return 'Bearer ' + token
}
// ========== 账号相关 ==========

const LoginFormKey = 'LOGINFORM'

export type LoginFormType = {
  tenantId: string
  account: string
  username: string
  password: string
  rememberMe: boolean
}

export const getLoginForm = () => {
  const loginForm: LoginFormType = wsCache.get(LoginFormKey)
  if (loginForm) {
    loginForm.password = decrypt(loginForm.password) as string
  }
  return loginForm
}

export const setLoginForm = (loginForm: LoginFormType) => {
  loginForm.password = encrypt(loginForm.password) as string
  wsCache.set(LoginFormKey, loginForm, { exp: 30 * 24 * 60 * 60 })
}

export const removeLoginForm = () => {
  wsCache.delete(LoginFormKey)
}

// ========== 租户相关 ==========

const TenantIdKey = 'TENANT_ID'
const TenantNameKey = 'TENANT_NAME'

export const getTenantName = () => {
  return wsCache.get(TenantNameKey)
}

export const setTenantName = (username: string) => {
  wsCache.set(TenantNameKey, username, { exp: 30 * 24 * 60 * 60 })
}

export const removeTenantName = () => {
  wsCache.delete(TenantNameKey)
}

export const getTenantId = () => {
  return wsCache.get(TenantIdKey)
}

export const setTenantId = (username: string) => {
  wsCache.set(TenantIdKey, username)
}

export const removeTenantId = () => {
  wsCache.delete(TenantIdKey)
}

export const extractTenantIdsFromUrl = (url: string) => {
  const regex = /(?:\?|&)tenantId=([^&]+)/g
  const tenantIds: string[] = []
  let match: RegExpExecArray | null
  while ((match = regex.exec(url))) {
    tenantIds.push(match[1])
  }
  return tenantIds
}
