/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 13:59:22
 * @LastEditors: <PERSON>J<PERSON>
 * @LastEditTime: 2023-06-16 14:11:59
 * @Description:
 */
import request from '@/config/axios'

// 通过授权码获取token
interface tokenRequset {
  code: string
  redirect_uri: string
  grant_type: string
}
// export const getToken = (params: tokenRequset) => {
//   // 发起请求
//   return request.getOriginal({
//     url: `/system/oauth2/token`,
//     params
//   })
// }

export const getToken = (params: tokenRequset) => {
  // 发起请求
  return request.postOriginal({
    url: `/auth/business/get-access-token`,
    params
  })
}

// 获得授权信息
export const getAuthorize = (clientId: string) => {
  return request.get({ url: '/system/oauth2/authorize?clientId=' + clientId })
}

// // 自动-发起授权获取重定向url
// export const autoAuthorize = (clientId: string, redirectUri: string, state?: string) => {
//   // 发起请求
//   return request.post({
//     url: '/system/oauth2/authorize',
//     headers: {
//       'Content-type': 'application/x-www-form-urlencoded'
//     },
//     params: {
//       auto_approve: false,
//       response_type: 'code',
//       scope: JSON.stringify({ 'user.read': true, 'user.write': true }),
//       client_id: clientId,
//       redirect_uri: redirectUri,
//       state: state
//     }
//   })
// }

// 自动-发起授权获取重定向url - 独立服务
export const autoAuthorize = (clientId: string, redirectUri: string, state?: string) => {
  // 发起请求
  return request.post({
    url: '/auth/oauth2/authorize',
    headers: {
      'Content-type': 'application/x-www-form-urlencoded'
    },
    params: {
      auto_approve: false,
      response_type: 'code',
      scope: JSON.stringify({ 'user.read': true, 'user.write': true }),
      client_id: clientId,
      redirect_uri: redirectUri,
      state: state
    }
  })
}

// 发起授权获取重定向url
export const authorize = (
  responseType: string,
  clientId: string,
  redirectUri: string,
  state: string,
  autoApprove: boolean,
  checkedScopes: string[],
  uncheckedScopes: string[]
) => {
  // 构建 scopes
  const scopes = {}
  for (const scope of checkedScopes) {
    scopes[scope] = true
  }
  for (const scope of uncheckedScopes) {
    scopes[scope] = false
  }
  // 发起请求
  return request.post({
    url: '/system/oauth2/authorize',
    headers: {
      'Content-type': 'application/x-www-form-urlencoded'
    },
    params: {
      response_type: responseType,
      client_id: clientId,
      redirect_uri: redirectUri,
      state: state,
      auto_approve: autoApprove,
      scope: JSON.stringify(scopes)
    }
  })
}
