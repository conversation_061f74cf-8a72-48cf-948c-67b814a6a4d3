<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-09-15 16:18:34
 * @Description: 
-->
<template>
  <el-card v-loading="loading as boolean" class="box-card">
    <template #header>
      <span class="el-icon-picture-outline">审批记录</span>
    </template>
    <el-col :offset="4" :span="16">
      <div class="block">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in tasks"
            :key="index"
            :icon="taskResultTpye[item.result].icon"
            :type="taskResultTpye[item.result].type"
          >
            <p style="font-weight: 700">任务：{{ item.name }}</p>
            <el-card :body-style="{ padding: '10px' }">
              <!-- 任务状态 -->
              <el-divider class="w-3/4" content-position="left">
                <el-tag :type="taskResultTpye[item.result]?.type">
                  {{ taskResultTpye[item.result]?.text }}
                </el-tag>
              </el-divider>

              <label v-if="item.assigneeUser" style="font-weight: normal; margin-right: 30px">
                审批人：{{ item.assigneeUser.nickname }}
                <el-tag size="small" type="info">{{
                  item.assigneeUser.deptName ?? '暂无数据'
                }}</el-tag>
              </label>
              <label v-if="item.createTime" style="font-weight: normal">创建时间：</label>
              <label style="color: #8a909c; font-weight: normal">
                {{ formatDate(item?.createTime) }}
              </label>
              <label v-if="item.endTime" style="margin-left: 30px; font-weight: normal">
                审批时间：
              </label>
              <label v-if="item.endTime" style="color: #8a909c; font-weight: normal">
                {{ formatDate(item?.endTime) }}
              </label>
              <label v-if="item.durationInMillis" style="margin-left: 30px; font-weight: normal">
                耗时：
              </label>
              <label v-if="item.durationInMillis" style="color: #8a909c; font-weight: normal">
                {{ formatPast2(item?.durationInMillis) }}
              </label>
              <p v-if="item.reason">
                <el-tag :type="taskResultTpye[item.result].type">{{ item.reason }}</el-tag>
              </p>
            </el-card>
            <!-- 委派任务 -->
            <el-card
              class="mt-3"
              :body-style="{ padding: '15px' }"
              v-for="(delegateListItem, delegateListIndex) in item.delegateList"
              :key="delegateListIndex"
              shadow="never"
            >
              <el-divider content-position="left">
                <!-- 任务状态 -->
                <el-tag :type="taskResultTpye[delegateListItem.result]?.type">
                  {{ taskResultTpye[delegateListItem.result]?.text }}
                </el-tag>
                <span
                  class="text-sm ml-3 p-1 px-2 border-1 border-[#d3d4d6] rounded text-[#909399]"
                >
                  {{ formatDate(delegateListItem?.createTime) }}
                </span>
                -
                <span
                  class="text-sm ml-3 p-1 px-2 border-1 border-[#d3d4d6] rounded text-[#909399]"
                >
                  {{
                    delegateListItem?.endTime ? formatDate(delegateListItem?.endTime) : '任务待处理'
                  }}
                </span>
                <span class="ml-4" v-if="delegateListItem?.endTime">
                  耗时:
                  {{ formatPast2(delegateListItem?.endTime - delegateListItem?.createTime) }}</span
                >
              </el-divider>
              <div>
                <span class="text-sm mr-4 p-1 px-2 border-1 border-[#d3d4d6] rounded">
                  {{ delegateListItem?.ownerUserNickname }}
                </span>
                <el-tag>委派给</el-tag>
                <span class="text-sm ml-4 p-1 px-2 border-1 border-[#d3d4d6] rounded">
                  {{ delegateListItem?.assigneeUserNickname }}
                </span>
                <span class="ml-3 text-[#909399] text-sm"
                  >: {{ delegateListItem?.reason ?? '审批意见为空' }}</span
                >
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-col>
  </el-card>
</template>
<script setup lang="ts">
defineOptions({
  name: 'BpmProcessInstanceTaskList'
})

import { formatDate, formatPast2 } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'

defineProps({
  loading: propTypes.bool, // 是否加载中
  tasks: Array<any> // 流程任务的数组
})

/** 获得任务对应的 icon */
const taskResultTpye = {
  1: {
    icon: 'el-icon-time',
    type: 'primary',
    text: '处理中'
  },
  2: {
    icon: 'el-icon-check',
    type: 'success',
    text: '通过'
  },
  3: {
    icon: 'el-icon-close',
    type: 'danger',
    text: '不通过'
  },
  4: {
    icon: 'el-icon-remove-outline',
    type: 'info',
    text: '取消'
  }
}
</script>
