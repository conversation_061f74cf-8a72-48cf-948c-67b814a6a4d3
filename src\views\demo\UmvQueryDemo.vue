<template>
  <div class="p-5 bg-el-bg-color rounded shadow-md">
    <h2 class="text-xl font-bold mb-2">UmvQuery 查询条件组件示例</h2>
    <p class="mb-4 text-gray-600">UmvQuery 是一个灵活的查询表单组件，支持多种配置方式和布局</p>

    <h3 class="text-lg font-semibold mb-3">基础示例</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <umv-query :opts="baseQueryOpts" @check="handleBaseCheck" @reset="handleBaseReset" />
      <div v-if="baseQueryResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ baseQueryResult }}</pre>
      </div>
    </div>

    <h3 class="text-lg font-semibold mb-3">展开/收起功能</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <umv-query
        :opts="expandQueryOpts"
        :max-visible-rows="1"
        :is-expansion="false"
        :is-show-open="true"
        unfold-txt="展开更多条件"
        pack-up-txt="收起条件"
        @check="handleExpandCheck"
        @reset="handleExpandReset"
      />
      <div v-if="expandQueryResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ expandQueryResult }}</pre>
      </div>
    </div>

    <h3 class="text-lg font-semibold mb-3">v-model双向绑定</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <umv-query
        :opts="vModelQueryOpts"
        v-model="queryParams"
        @check="handleVModelCheck"
        @reset="handleVModelReset"
      />
      <div class="flex space-x-4 mt-3">
        <el-button type="primary" @click="updateQueryParams">修改参数</el-button>
        <el-button @click="resetQueryParams">重置参数</el-button>
      </div>
      <div class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">当前双向绑定的数据：</p>
        <pre class="mt-2 text-xs">{{ queryParams }}</pre>
      </div>
      <div v-if="vModelQueryResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ vModelQueryResult }}</pre>
      </div>
    </div>

    <h3 class="text-lg font-semibold mb-3">自定义按钮</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <umv-query
        :opts="customBtnQueryOpts"
        :btn-check-bind="{ btnTxt: '搜索', type: 'success' }"
        :btn-reset-bind="{ btnTxt: '清空', type: 'warning' }"
        @check="handleCustomBtnCheck"
        @reset="handleCustomBtnReset"
      >
        <template #querybar>
          <el-button type="primary" size="default" @click="handleExport">
            <el-icon><Download /></el-icon>导出
          </el-button>
        </template>
      </umv-query>
      <div v-if="customBtnQueryResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ customBtnQueryResult }}</pre>
      </div>
    </div>

    <h3 class="text-lg font-semibold mb-3">更多条件下拉</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <umv-query
        :opts="moreQueryOpts"
        :is-drop-down-select-more="true"
        :more-check-list="moreCheckList"
        :popover-attrs="{
          showTxt: '更多筛选',
          title: '所有查询条件',
          allTxt: '全选',
          reverseTxt: '反选',
          clearTxt: '清空'
        }"
        @check="handleMoreCheck"
        @reset="handleMoreReset"
        @get-check-list="handleGetCheckList"
      />
      <div v-if="moreQueryResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ moreQueryResult }}</pre>
      </div>
    </div>

    <h3 class="text-lg font-semibold mb-3">自定义插槽</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <umv-query :opts="slotQueryOpts" @check="handleSlotCheck" @reset="handleSlotReset">
        <template #custom-range="{ param }">
          <div class="flex items-center">
            <el-date-picker
              v-model="param.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </div>
        </template>
        <template #custom-status="{ param }">
          <div class="flex items-center space-x-2">
            <el-radio-group v-model="param.status">
              <el-radio label="1">启用</el-radio>
              <el-radio label="0">禁用</el-radio>
              <el-radio label="">全部</el-radio>
            </el-radio-group>
          </div>
        </template>
        <template #footerBtn="slotProps">
          <el-button type="primary" @click="slotProps.check">查询</el-button>
          <el-button @click="slotProps.reset">重置</el-button>
          <el-button type="success" @click="handleBatchExport">批量导出</el-button>
        </template>
      </umv-query>
      <div v-if="slotQueryResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ slotQueryResult }}</pre>
      </div>
    </div>

    <h3 class="text-lg font-semibold mb-3">单选框和复选框示例</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <umv-query
        :opts="radioCheckboxOpts"
        @check="handleRadioCheckboxCheck"
        @reset="handleRadioCheckboxReset"
      />
      <div v-if="radioCheckboxResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ radioCheckboxResult }}</pre>
      </div>
    </div>

    <h3 class="text-lg font-semibold mb-3">自定义栅格列数</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <el-radio-group v-model="customColumnCount" class="mb-4">
        <el-radio :label="1">1列布局</el-radio>
        <el-radio :label="2">2列布局</el-radio>
        <el-radio :label="3">3列布局</el-radio>
        <el-radio :label="4">4列布局</el-radio>
      </el-radio-group>

      <umv-query
        :opts="gridQueryOpts"
        :is-show-width-size="true"
        :width-size="customColumnCount"
        isExpansion
        @check="handleGridCheck"
        @reset="handleGridReset"
      />
      <div v-if="gridQueryResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ gridQueryResult }}</pre>
      </div>
    </div>

    <h3 class="text-lg font-semibold mb-3">列调整示例</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <umv-query
        :opts="columnAdjustOpts"
        :is-show-column="true"
        @check="handleColumnAdjustCheck"
        @reset="handleColumnAdjustReset"
        @column-change="handleColumnChange"
      />
      <div v-if="columnAdjustResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ columnAdjustResult }}</pre>
      </div>
      <div class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">当前列数：{{ currentColumns }}列布局</p>
        <p class="text-gray-500 text-sm mt-1">点击右上角"列调整"按钮可以切换不同的列布局</p>
      </div>
    </div>

    <h3 class="text-lg font-semibold mb-3">控制显示的搜索条件数量</h3>
    <div class="mb-6 border border-gray-200 rounded p-4">
      <p class="mb-3 text-gray-600">通过 visibleSearchNum 属性控制默认显示的搜索条件数量</p>
      <el-radio-group v-model="visibleNum" class="mb-4">
        <el-radio :label="2">显示2个条件</el-radio>
        <el-radio :label="3">显示3个条件</el-radio>
        <el-radio :label="4">显示4个条件</el-radio>
        <el-radio :label="0">显示全部</el-radio>
      </el-radio-group>

      <umv-query
        :opts="visibleSearchOpts"
        :visible-search-num="visibleNum === 0 ? undefined : visibleNum"
        @check="handleVisibleSearchCheck"
        @reset="handleVisibleSearchReset"
      />
      <div v-if="visibleSearchResult" class="mt-4 p-3 bg-gray-50 rounded">
        <p class="font-semibold">查询结果：</p>
        <pre class="mt-2 text-xs">{{ visibleSearchResult }}</pre>
      </div>
    </div>

    <div class="mt-8 p-4 border border-dashed border-gray-300 rounded bg-gray-50">
      <h3 class="mt-0 mb-2.5 text-gray-600 font-semibold">组件说明</h3>
      <p class="mb-2">UmvQuery 组件提供以下主要功能：</p>
      <ul class="pl-5 list-disc mb-3">
        <li>支持多种表单控件（输入框、下拉框、单选框、复选框、日期选择器等）</li>
        <li>可配置的布局网格系统</li>
        <li>支持表单项的展开/收起功能</li>
        <li>自定义标签渲染</li>
        <li>支持插槽自定义内容</li>
        <li>支持"更多条件"下拉选择</li>
        <li>响应式设计，适应不同屏幕尺寸</li>
        <li>支持v-model双向绑定，便于外部操作和获取表单数据</li>
      </ul>
      <p class="mb-2">常见用法：</p>
      <ul class="pl-5 list-disc mb-3">
        <li>使用 opts 属性配置表单项</li>
        <li>使用 childComp 属性配置带子组件的控件（如下拉框、单选框组、复选框组）</li>
        <li>使用 btn-check-bind 和 btn-reset-bind 配置按钮样式</li>
        <li>使用 max-visible-rows 控制默认显示的行数</li>
        <li>使用具名插槽实现自定义控件</li>
        <li>使用 v-model 实现表单数据的双向绑定</li>
      </ul>
      <p class="mb-2">childComp 属性示例：</p>
      <pre class="mt-2 text-xs p-3 bg-gray-100 rounded">
{
  comp: 'el-select',           // 父组件
  dataIndex: 'department',
  childComp: {
    comp: 'el-option',         // 子组件
    option: [                  // 选项列表
      { label: '研发部', value: 'dev' },
      { label: '市场部', value: 'market' }
    ],
    bind: { ... }              // 子组件的其他属性
  }
}
      </pre>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed } from 'vue'
import { Download } from '@element-plus/icons-vue'
import {
  ElMessage,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElRadioGroup,
  ElRadio,
  ElCheckboxGroup,
  ElCheckbox
} from 'element-plus'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'

// 定义表单数据接口
interface QueryForm {
  [key: string]: any
}

defineOptions({
  name: 'UmvQueryDemo'
})

// 基础示例的配置
const baseQueryOpts = ref<Record<string, QueryOption>>({
  keyword: {
    label: '关键词',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.keyword}
        placeholder="请输入姓名/编号/手机号"
        style="width: 100%"
        clearable
      />
    )
  },
  department: {
    label: '部门',
    dataIndex: 'department',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.department} placeholder="请选择部门" style="width: 100%" clearable>
        <ElOption label="研发部" value="dev" />
        <ElOption label="市场部" value="market" />
        <ElOption label="财务部" value="finance" />
        <ElOption label="人事部" value="hr" />
      </ElSelect>
    )
  },
  status: {
    label: '状态',
    dataIndex: 'status',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.status} placeholder="请选择状态" style="width: 100%" clearable>
        <ElOption label="启用" value="1" />
        <ElOption label="禁用" value="0" />
      </ElSelect>
    )
  },
  createTime: {
    label: '创建时间',
    dataIndex: 'createTime',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElDatePicker
        v-model={form.createTime}
        type="date"
        placeholder="请选择创建时间"
        style="width: 100%"
        value-format="YYYY-MM-DD"
      />
    )
  }
})
const baseQueryResult = ref('')

// 展开/收起示例的配置
const expandQueryOpts = reactive<Record<string, QueryOption>>({
  name: {
    label: '姓名',
    dataIndex: 'name',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput v-model={form.name} placeholder="请输入姓名" style="width: 100%" clearable />
    )
  },
  department: {
    label: '部门',
    dataIndex: 'department',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.department} placeholder="请选择部门" style="width: 100%" clearable>
        <ElOption label="研发部" value="dev" />
        <ElOption label="市场部" value="market" />
        <ElOption label="财务部" value="finance" />
        <ElOption label="人事部" value="hr" />
      </ElSelect>
    )
  },
  position: {
    label: '职位',
    dataIndex: 'position',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.position} placeholder="请选择职位" style="width: 100%" clearable>
        <ElOption label="前端开发" value="fe" />
        <ElOption label="后端开发" value="be" />
        <ElOption label="产品经理" value="pm" />
        <ElOption label="设计师" value="ui" />
        <ElOption label="测试工程师" value="qa" />
      </ElSelect>
    )
  },
  status: {
    label: '状态',
    dataIndex: 'status',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.status} placeholder="请选择状态" style="width: 100%" clearable>
        <ElOption label="在职" value="1" />
        <ElOption label="离职" value="0" />
        <ElOption label="休假" value="2" />
      </ElSelect>
    )
  },
  entryTime: {
    label: '入职时间',
    dataIndex: 'entryTime',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElDatePicker
        v-model={form.entryTime}
        type="date"
        placeholder="请选择入职时间"
        style="width: 100%"
        value-format="YYYY-MM-DD"
      />
    )
  },
  address: {
    label: '工作地点',
    dataIndex: 'address',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.address} placeholder="请选择工作地点" style="width: 100%" clearable>
        <ElOption label="北京" value="beijing" />
        <ElOption label="上海" value="shanghai" />
        <ElOption label="广州" value="guangzhou" />
        <ElOption label="深圳" value="shenzhen" />
      </ElSelect>
    )
  }
})
const expandQueryResult = ref('')

// v-model双向绑定示例的配置
const vModelQueryOpts = reactive<Record<string, QueryOption>>({
  name: {
    label: '姓名',
    dataIndex: 'name',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput v-model={form.name} placeholder="请输入姓名" style="width: 100%" clearable />
    )
  },
  department: {
    label: '部门',
    dataIndex: 'department',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.department} placeholder="请选择部门" style="width: 100%" clearable>
        <ElOption label="研发部" value="dev" />
        <ElOption label="市场部" value="market" />
        <ElOption label="财务部" value="finance" />
        <ElOption label="人事部" value="hr" />
      </ElSelect>
    )
  },
  status: {
    label: '状态',
    dataIndex: 'status',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.status} placeholder="请选择状态" style="width: 100%" clearable>
        <ElOption label="在职" value="1" />
        <ElOption label="离职" value="0" />
        <ElOption label="休假" value="2" />
      </ElSelect>
    )
  }
})
// 双向绑定的数据对象
const queryParams = ref<QueryForm>({
  name: '',
  department: '',
  status: ''
})
const vModelQueryResult = ref('')

// 自定义按钮示例的配置
const customBtnQueryOpts = reactive<Record<string, QueryOption>>({
  name: {
    label: '姓名',
    comp: 'el-input',
    dataIndex: 'name',
    defaultVal: '',
    placeholder: '请输入姓名'
  },
  status: {
    label: '状态',
    comp: 'el-select',
    dataIndex: 'status',
    defaultVal: '',
    placeholder: '请选择状态',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '在职', value: '1' },
        { label: '离职', value: '0' }
      ]
    }
  }
})
const customBtnQueryResult = ref('')

// 更多条件下拉示例的配置
const moreQueryOpts = reactive<Record<string, QueryOption>>({
  keyword: {
    label: '关键词',
    comp: 'el-input',
    dataIndex: 'keyword',
    defaultVal: '',
    placeholder: '请输入姓名/编号'
  },
  department: {
    label: '部门',
    comp: 'el-select',
    dataIndex: 'department',
    defaultVal: '',
    placeholder: '请选择部门',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '研发部', value: 'dev' },
        { label: '市场部', value: 'market' },
        { label: '财务部', value: 'finance' },
        { label: '人事部', value: 'hr' }
      ]
    }
  }
})
const moreCheckList = computed(() => [
  { label: '职位', prop: 'position', comp: 'el-input' },
  { label: '状态', prop: 'status', comp: 'el-select' },
  { label: '入职时间', prop: 'entryTime', comp: 'el-date-picker' },
  { label: '学历', prop: 'education', comp: 'el-select' },
  { label: '工作地点', prop: 'workPlace', comp: 'el-input' }
])
const moreQueryResult = ref('')

// 自定义插槽示例的配置
const slotQueryOpts = reactive<Record<string, QueryOption>>({
  name: {
    label: '姓名',
    comp: 'el-input',
    dataIndex: 'name',
    defaultVal: '',
    placeholder: '请输入姓名'
  },
  department: {
    label: '部门',
    comp: 'el-select',
    dataIndex: 'department',
    defaultVal: '',
    placeholder: '请选择部门',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '研发部', value: 'dev' },
        { label: '市场部', value: 'market' },
        { label: '财务部', value: 'finance' },
        { label: '人事部', value: 'hr' }
      ]
    }
  },
  dateRange: {
    label: '日期范围',
    slotName: 'custom-range',
    dataIndex: 'dateRange',
    defaultVal: []
  },
  status: {
    label: '状态',
    slotName: 'custom-status',
    dataIndex: 'status',
    defaultVal: ''
  }
})
const slotQueryResult = ref('')

// 单选框和复选框示例的配置
const radioCheckboxOpts = reactive<Record<string, QueryOption>>({
  name: {
    label: '姓名',
    comp: 'el-input',
    dataIndex: 'name',
    defaultVal: '',
    placeholder: '请输入姓名'
  },
  gender: {
    label: '性别',
    comp: 'el-radio-group',
    dataIndex: 'gender',
    defaultVal: '',
    childComp: {
      comp: 'el-radio',
      option: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' },
        { label: '其他', value: 'other' }
      ]
    }
  },
  hobbies: {
    label: '兴趣爱好',
    comp: 'el-checkbox-group',
    dataIndex: 'hobbies',
    defaultVal: [],
    childComp: {
      comp: 'el-checkbox',
      option: [
        { label: '阅读', value: 'reading' },
        { label: '运动', value: 'sports' },
        { label: '音乐', value: 'music' },
        { label: '旅行', value: 'travel' },
        { label: '电影', value: 'movie' }
      ]
    }
  }
})
const radioCheckboxResult = ref('')

// 自定义栅格列数示例的配置
const gridQueryOpts = reactive<Record<string, QueryOption>>({
  keyword: {
    label: '关键词',
    comp: 'el-input',
    dataIndex: 'keyword',
    defaultVal: '',
    placeholder: '请输入关键词'
  },
  department: {
    label: '部门',
    comp: 'el-select',
    dataIndex: 'department',
    defaultVal: '',
    placeholder: '请选择部门',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '研发部', value: 'dev' },
        { label: '市场部', value: 'market' },
        { label: '财务部', value: 'finance' },
        { label: '人事部', value: 'hr' }
      ]
    }
  },
  position: {
    label: '职位',
    comp: 'el-select',
    dataIndex: 'position',
    defaultVal: '',
    placeholder: '请选择职位',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '前端开发', value: 'fe' },
        { label: '后端开发', value: 'be' },
        { label: '产品经理', value: 'pm' },
        { label: '设计师', value: 'ui' },
        { label: '测试工程师', value: 'qa' }
      ]
    }
  },
  status: {
    label: '状态',
    comp: 'el-select',
    dataIndex: 'status',
    defaultVal: '',
    placeholder: '请选择状态',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '在职', value: '1' },
        { label: '离职', value: '0' },
        { label: '休假', value: '2' }
      ]
    }
  },
  entryTime: {
    label: '入职时间',
    comp: 'el-date-picker',
    dataIndex: 'entryTime',
    defaultVal: '',
    placeholder: '请选择入职时间',
    bind: {
      type: 'date',
      valueFormat: 'YYYY-MM-DD'
    }
  }
})
const gridQueryResult = ref('')

// 列调整示例的配置
const columnAdjustOpts = reactive<Record<string, QueryOption>>({
  keyword: {
    label: '关键词',
    comp: 'el-input',
    dataIndex: 'keyword',
    defaultVal: '',
    placeholder: '请输入姓名/编号/手机号'
  },
  department: {
    label: '部门',
    comp: 'el-select',
    dataIndex: 'department',
    defaultVal: '',
    placeholder: '请选择部门',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '研发部', value: 'dev' },
        { label: '市场部', value: 'market' },
        { label: '财务部', value: 'finance' },
        { label: '人事部', value: 'hr' }
      ]
    }
  },
  position: {
    label: '职位',
    comp: 'el-select',
    dataIndex: 'position',
    defaultVal: '',
    placeholder: '请选择职位',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '前端开发', value: 'fe' },
        { label: '后端开发', value: 'be' },
        { label: '产品经理', value: 'pm' },
        { label: '设计师', value: 'ui' }
      ]
    }
  },
  status: {
    label: '状态',
    comp: 'el-select',
    dataIndex: 'status',
    defaultVal: '',
    placeholder: '请选择状态',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '在职', value: '1' },
        { label: '离职', value: '0' },
        { label: '休假', value: '2' }
      ]
    }
  },
  entryDate: {
    label: '入职日期',
    comp: 'el-date-picker',
    dataIndex: 'entryDate',
    defaultVal: '',
    placeholder: '请选择入职日期',
    bind: {
      type: 'date',
      valueFormat: 'YYYY-MM-DD'
    }
  },
  level: {
    label: '级别',
    comp: 'el-select',
    dataIndex: 'level',
    defaultVal: '',
    placeholder: '请选择级别',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '初级', value: 'junior' },
        { label: '中级', value: 'mid' },
        { label: '高级', value: 'senior' },
        { label: '专家', value: 'expert' }
      ]
    }
  }
})
const columnAdjustResult = ref('')
const currentColumns = ref(4) // 默认4列布局

// 基础示例的方法
const handleBaseCheck = (form: QueryForm) => {
  baseQueryResult.value = JSON.stringify(form, null, 2)
  ElMessage.success('查询成功')
}

const handleBaseReset = () => {
  baseQueryResult.value = ''
  ElMessage.info('表单已重置')
}

// 展开/收起示例的方法
const handleExpandCheck = (form: QueryForm) => {
  expandQueryResult.value = JSON.stringify(form, null, 2)
  ElMessage.success('查询成功')
}

const handleExpandReset = () => {
  expandQueryResult.value = ''
  ElMessage.info('表单已重置')
}

// v-model双向绑定示例的方法
const handleVModelCheck = (form: QueryForm) => {
  vModelQueryResult.value = JSON.stringify(form, null, 2)
  ElMessage.success('查询成功')
}

const handleVModelReset = () => {
  vModelQueryResult.value = ''
  ElMessage.info('表单已重置')
}

const updateQueryParams = () => {
  queryParams.value = {
    name: '张三',
    department: 'dev',
    status: '1'
  }
  ElMessage.success('已通过外部修改查询参数')
}

const resetQueryParams = () => {
  queryParams.value = {
    name: '',
    department: '',
    status: ''
  }
  ElMessage.info('已重置查询参数')
}

// 自定义按钮示例的方法
const handleCustomBtnCheck = (form: QueryForm) => {
  customBtnQueryResult.value = JSON.stringify(form, null, 2)
  ElMessage.success('搜索成功')
}

const handleCustomBtnReset = () => {
  customBtnQueryResult.value = ''
  ElMessage.info('表单已清空')
}

const handleExport = () => {
  ElMessage.success('开始导出数据')
}

// 更多条件下拉示例的方法
const handleMoreCheck = (form: QueryForm) => {
  moreQueryResult.value = JSON.stringify(form, null, 2)
  // 显示当前查询条件数量
  const conditionCount = Object.keys(moreQueryOpts).length
  ElMessage.success(`查询成功，当前有${conditionCount}个查询条件`)
}

const handleMoreReset = () => {
  moreQueryResult.value = ''
  // 重置为初始状态，只保留基本条件
  const initialOpts = {
    keyword: moreQueryOpts.keyword,
    department: moreQueryOpts.department
  }
  Object.keys(moreQueryOpts).forEach((key) => {
    if (key !== 'keyword' && key !== 'department') {
      delete moreQueryOpts[key]
    }
  })
  ElMessage.info('表单已重置，只保留基本条件')
}

const handleGetCheckList = (checkedList: Record<string, any>) => {
  console.log('选中的条件列表：', checkedList)
  // 获取选中的条件后，可以动态添加到查询表单中
  Object.keys(checkedList).forEach((key) => {
    if (!moreQueryOpts[key]) {
      const option: QueryOption = {
        label: checkedList[key].label,
        comp: checkedList[key].comp,
        dataIndex: key,
        defaultVal: '',
        placeholder: `请输入${checkedList[key].label}`
      }

      // 对于下拉框等组件，添加childComp属性
      if (checkedList[key].comp === 'el-select') {
        option.childComp = {
          comp: 'el-option',
          option: []
        }
      } else if (checkedList[key].comp === 'el-radio-group') {
        option.childComp = {
          comp: 'el-radio',
          option: []
        }
      } else if (checkedList[key].comp === 'el-checkbox-group') {
        option.childComp = {
          comp: 'el-checkbox',
          option: []
        }
      }

      moreQueryOpts[key] = option
    }
  })
}

// 自定义插槽示例的方法
const handleSlotCheck = (form) => {
  console.log(form)

  slotQueryResult.value = JSON.stringify(form, null, 2)
  ElMessage.success('查询成功')
}

const handleSlotReset = () => {
  slotQueryOpts.name.defaultVal = ''
  slotQueryOpts.department.defaultVal = ''
  slotQueryOpts.dateRange.defaultVal = []
  slotQueryOpts.status.defaultVal = ''
  slotQueryResult.value = ''
  ElMessage.info('表单已重置')
}

const handleBatchExport = () => {
  ElMessage.success('开始批量导出数据')
}

// 单选框和复选框示例的方法
const handleRadioCheckboxCheck = (form: QueryForm) => {
  radioCheckboxResult.value = JSON.stringify(form, null, 2)
  ElMessage.success('查询成功')
}

const handleRadioCheckboxReset = () => {
  radioCheckboxResult.value = ''
  ElMessage.info('表单已重置')
}

// 自定义栅格列数示例的方法
const customColumnCount = ref(1)

const handleGridCheck = (form: QueryForm) => {
  gridQueryResult.value = JSON.stringify(form, null, 2)
  ElMessage.success('查询成功')
}

const handleGridReset = () => {
  gridQueryOpts.keyword.defaultVal = ''
  gridQueryOpts.department.defaultVal = ''
  gridQueryResult.value = ''
  ElMessage.info('表单已重置')
}

// 列调整示例的方法
const handleColumnAdjustCheck = (form: QueryForm) => {
  columnAdjustResult.value = JSON.stringify(form, null, 2)
  ElMessage.success('查询成功')
}

const handleColumnAdjustReset = () => {
  columnAdjustResult.value = ''
  ElMessage.info('表单已重置')
}

const handleColumnChange = (columns: number) => {
  currentColumns.value = columns
  ElMessage.success(`列布局已调整为${columns}列`)
}

// 控制显示的搜索条件数量示例的方法
const visibleNum = ref(0)
const visibleSearchOpts = reactive<Record<string, QueryOption>>({
  keyword: {
    label: '关键词',
    comp: 'el-input',
    dataIndex: 'keyword',
    defaultVal: '',
    placeholder: '请输入关键词'
  },
  department: {
    label: '部门',
    comp: 'el-select',
    dataIndex: 'department',
    defaultVal: '',
    placeholder: '请选择部门',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '研发部', value: 'dev' },
        { label: '市场部', value: 'market' },
        { label: '财务部', value: 'finance' },
        { label: '人事部', value: 'hr' }
      ]
    }
  },
  position: {
    label: '职位',
    comp: 'el-select',
    dataIndex: 'position',
    defaultVal: '',
    placeholder: '请选择职位',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '前端开发', value: 'fe' },
        { label: '后端开发', value: 'be' },
        { label: '产品经理', value: 'pm' },
        { label: '设计师', value: 'ui' },
        { label: '测试工程师', value: 'qa' }
      ]
    }
  },
  status: {
    label: '状态',
    comp: 'el-select',
    dataIndex: 'status',
    defaultVal: '',
    placeholder: '请选择状态',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '在职', value: '1' },
        { label: '离职', value: '0' },
        { label: '休假', value: '2' }
      ]
    }
  },
  entryTime: {
    label: '入职时间',
    comp: 'el-date-picker',
    dataIndex: 'entryTime',
    defaultVal: '',
    placeholder: '请选择入职时间',
    bind: {
      type: 'date',
      valueFormat: 'YYYY-MM-DD'
    }
  }
})
const visibleSearchResult = ref('')

const handleVisibleSearchCheck = (form: QueryForm) => {
  visibleSearchResult.value = JSON.stringify(form, null, 2)
  ElMessage.success('查询成功')
}

const handleVisibleSearchReset = () => {
  visibleSearchOpts.keyword.defaultVal = ''
  visibleSearchOpts.department.defaultVal = ''
  visibleSearchOpts.position.defaultVal = ''
  visibleSearchOpts.status.defaultVal = ''
  visibleSearchOpts.entryTime.defaultVal = ''
  visibleSearchResult.value = ''
  ElMessage.info('表单已重置')
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 12px;
}
</style>
