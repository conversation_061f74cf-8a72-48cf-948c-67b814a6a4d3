<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-01 14:48:20
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-24 12:19:13
 * @Description: 
-->
<script setup lang="ts">
defineOptions({
  name: 'GoldButton'
})

import { PropType } from 'vue'
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  modelValue: propTypes.bool.def(false),
  loading: propTypes.bool.def(false),
  preIcon: propTypes.string.def(''),
  postIcon: propTypes.string.def(''),
  title: propTypes.string.def(''),
  type: propTypes.oneOf(['', 'primary', 'success', 'warning', 'danger', 'info']).def(''),
  link: propTypes.bool.def(false),
  circle: propTypes.bool.def(false),
  round: propTypes.bool.def(false),
  plain: propTypes.bool.def(false),
  onClick: { type: Function as PropType<(...args) => any>, default: null }
})
const getBindValue = computed(() => {
  const delArr: string[] = ['title', 'preIcon', 'postIcon', 'onClick']
  const attrs = useAttrs()
  const obj = { ...attrs, ...props }
  for (const key in obj) {
    if (delArr.indexOf(key) !== -1) {
      delete obj[key]
    }
  }
  return obj
})
</script>

<template>
  <ElButton v-bind="getBindValue" :loading="props.loading" @click="onClick">
    <Icon v-if="preIcon" :icon="preIcon" class="mr-1px" />
    {{ title ? title : '' }}
    <Icon v-if="postIcon" :icon="postIcon" class="mr-1px" />
  </ElButton>
</template>
<style lang="scss" scoped>
:deep(.el-button.is-text) {
  margin-left: 0;
  padding: 8px 4px;
}

:deep(.el-button.is-link) {
  margin-left: 0;
  padding: 8px 4px;
}

.el-button {
  // background: linear-gradient(114deg, #eecd91, #d5a147);
  background: var(--client-color-primary);
  color: #fff;
  border-color: #fff;
}
</style>
