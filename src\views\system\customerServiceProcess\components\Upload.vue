<template>
  <div class="flex box-tip">
    <el-upload
      :multiple="props.multiple"
      :disabled="props.disabled"
      :limit="props.limit"
      :accept="props.accept"
      :show-file-list="props.isShowFileList"
      :auto-upload="props.autoUpload"
      v-model:file-list="fileList"
      ref="uploadModuleRef"
      :before-upload="beforeUpload"
      :on-remove="onRemove"
      :on-change="fileChange"
      :on-exceed="onExceedHandle"
      class="upload"
    >
      <slot name="btn">
        <el-button type="primary" class="btn-upload" :disabled="props.disabled">
          {{ props.btnText }}
        </el-button>
      </slot>
      <template #tip>
        <div class="el-upload__tip">{{ props.uploadTip }}</div>
      </template>
      <template #file="uploadFileSlot">
        <div class="file-list">
          <div class="icon">
            <el-icon size="12" v-if="uploadFileSlot.file.isImage">
              <Picture />
            </el-icon>
            <el-icon size="12" v-else>
              <Document />
            </el-icon>
          </div>
          <div class="content">
            <div class="flex">
              <div class="name" @click="onPreview(uploadFileSlot)" style="cursor: pointer">{{
                uploadFileSlot.file.name
              }}</div>
              <el-button
                type="danger"
                link
                @click="onSlotRemove(uploadFileSlot.file.uid)"
                :disabled="props.disabled"
              >
                {{ t('common.delete') }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import { Document, Picture } from '@element-plus/icons-vue'
import { minioUpload } from '@/api/system/customerServiceProcess/index'
// import * as makeCardApi from '@/api/makeCardService/index'

const uploadModuleRef = ref()

const { t } = useI18n()

const fileList = ref<any>([])

const props = defineProps({
  showFileList: {
    type: Array,
    default: () => []
  },
  uploadTip: {
    type: String,
    default: ''
  },
  btnText: {
    type: String,
    default: '上传文件'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 8
  },
  multiple: {
    type: Boolean,
    default: false
  },
  uploadData: {
    type: Object,
    default: () => {}
  },
  //是否自动上传
  autoUpload: {
    type: Boolean,
    default: false
  },
  //上传最大字节限制
  maxSize: {
    type: Number,
    default: 1024 * 1024
  },
  mergeUpload: {
    type: Boolean,
    default: false
  },
  isShowFileList: {
    type: Boolean,
    default: true
  },
  accept: {
    type: String,
    default: ''
  },
  limitFormat: {
    type: Array,
    default: () => []
  }
})

const { maxSize } = toRefs(props)

const message = useMessage()

// 监听页面展示
watch(
  () => props.showFileList,
  () => {
    // 如果存在已上传的图片列表，进行回显
    if (props.showFileList && props.showFileList.length) {
      let showFileList: any[] = []
      console.log('props.showFileList777777666666666', props.showFileList)
      props.showFileList.forEach((file: any) => {
        const fileType = file.realFileName.substring(file.realFileName.lastIndexOf('.') + 1)
        let isImage = ['png', 'gif', 'jpg'].includes(fileType)
        showFileList.push({
          name: file?.fileName,
          url: file?.previewSite,
          status: 'success',
          isImage,
          ...file
        })
      })
      fileList.value = showFileList
    } else {
      fileList.value = []
    }
  },
  { immediate: true, deep: true }
)

const emit = defineEmits(['success', 'file-change', 'delete-file'])

function fileChange(_file, fileArr) {
  fileList.value = fileArr
  if (!props.autoUpload) {
    let canUpload = verifyFile(_file)
    if (!canUpload) {
      fileArr.forEach((v: any, i) => {
        if (v.uid === _file.uid) {
          fileList.value.splice(i, 1)
        }
      })
    }
  }
  emit('file-change', fileList.value)
}

function onSlotRemove(uid) {
  let index: any = 0
  fileList.value.forEach((v: any, i) => {
    if (v.uid == uid) {
      index = i
    }
  })
  fileList.value.splice(index, 1)
  emit('delete-file', fileList.value)
}

import { uploadEncrytion } from '@/utils/uploadEncrytion'

const submitFile = async () => {
  return new Promise(async (resolve, reject) => {
    const loadingUpFileList: any = []

    fileList.value.forEach((file) => {
      if (file.status !== 'success' && !file.url) {
        loadingUpFileList.push(file)
      }
    })
    if (loadingUpFileList?.length < 1) {
      return resolve(fileList.value)
    }
    let resList: any = []
    for (let i = 0; i < loadingUpFileList.length; i++) {
      const formData: FormData = new FormData()
      formData.append('file', loadingUpFileList[i].raw)
      await uploadEncrytion(formData, loadingUpFileList[i].raw)
      const res = await minioUpload(formData)
      res.data.uid = loadingUpFileList[i].uid
      resList.push(res.data)
    }
    // loadingUpFileList.forEach((file) => {
    //   const formData: FormData = new FormData()
    //   formData.append('file', file.raw)
    //   const res = await minioUpload(formData)
    //   debugger
    //   res.uid = file.uid
    //   resList.push(res)
    // })
    try {
      if (resList.length > 0) {
        console.log('resList', resList)
        resList.forEach((el) => {
          fileList.value.forEach((_item, _fileIndex) => {
            if (_item.uid && _item.uid === el.uid) {
              fileList.value[_fileIndex] = el
            }
          })
        })
      }
      return resolve(fileList.value)
    } catch (e) {
      message.error('文件上传失败')
      return reject(false)
    }
  })
}

const submitFileBase64 = () => {
  return new Promise(async (resolve, reject) => {
    try {
      if (fileList?.value?.length < 1) {
        return resolve([])
      }
      await fileList.value.forEach(async (item, index) => {
        fileList.value[index].imgBase64 = await fileToBase64(item.raw)
        if (index === fileList?.value?.length - 1) {
          return resolve(fileList.value)
        }
      })
    } catch (e) {
      message.error('文件上传失败')
      return reject(false)
    }
  })
}

const fileToBase64 = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      const base64 = reader.result
      resolve(base64)
    }
    reader.readAsDataURL(file)
  })
}

const onRemove = (file, fileArr) => {
  message.warning(`您移除了文件【${fileName}】`, { fileName: file.name })
  emit('delete-file', fileArr)
}

// 上传之前钩子
const beforeUpload = (file) => {
  return verifyFile(file)
}
function verifyFile(file) {
  if (file.size > maxSize.value) {
    message.error('文件大小超出限制，请重新上传')
    return false
  }
  if (props.limitFormat.includes('*')) {
    return true
  }
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
  let fileSuffix = props.limitFormat.includes(fileType)
  if (!fileSuffix) {
    message.error('请检查附件格式重新上传')
  }
  return fileSuffix
}
// 超出上传数量
const onExceedHandle = () => {
  message.error('超出上传文件数量限制！')
}

// 检验是否上传完成
const checkComplete = () => {
  return new Promise<boolean>((resolve, reject) => {
    if (fileList.value.length > 0) {
      let arr = fileList.value.map((item) => {
        return item.url
      })
      if (arr.includes(undefined)) {
        message.warning('请等待文件上传完成')
        reject(false)
      } else {
        resolve(true)
      }
    } else {
      resolve(true)
    }
  })
}

// 清空待上传文件
const clearFile = () => {
  fileList.value.splice(0, fileList?.value?.length)
}

// 点击文件列表中已上传文件的钩子
const onPreview = (_file) => {
  console.log('_file', _file)
  previewFile(_file.file)
}

import envController from '@/controller/envController'

const previewFile = (file) => {
  console.log('window.location.origin', window.location.origin)
  // 校验只有图片可以预览
  if (file.raw) {
    const _file = file.raw
    let blob = new Blob([_file], { type: _file.type })
    let resultUrl = window.URL.createObjectURL(blob)
    window.open(`${resultUrl}`, '_blank')
  } else {
    window.open(
      // `${window.location.origin}${envController.getOssUrl()}${file.previewSite}`,
      `${window.location.origin}${file.previewSite}`,
      '_blank'
    )
  }
}

defineExpose({
  fileList,
  clearFile,
  checkComplete,
  submitFile,
  submitFileBase64
})
</script>

<style lang="scss" scoped>
.el-upload__tip {
  line-height: 16px;
  color: darkgray;
  width: 300px;
  position: absolute;
  top: 0px;
  left: 160px;
}
.btn-upload {
  width: 140px;
  height: 40px;
}
.upload {
  width: 300px;
  margin-right: 20px;
  position: relative;
}
.file-list {
  display: flex;
  align-items: center;
  .icon {
    width: 12px;
    height: 12px;
    display: flex;
    margin-left: 10px;
  }
  .content {
    flex: 1;
    margin-left: 8px;
    .name {
      width: 220px;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      &:hover {
        color: #409eff;
      }
    }
    .text {
      font-size: 14px;
      color: #409eff;
      margin-left: 10px;
    }
    .slot-button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
