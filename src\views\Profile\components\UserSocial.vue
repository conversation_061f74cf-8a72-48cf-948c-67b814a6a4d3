<template>
  <el-table :data="socialUsers" :show-header="false">
    <el-table-column fixed="left" :title="t('profile.userSocial.index')" type="seq" width="60" />
    <el-table-column align="left" :label="t('profile.userSocial.socialPlatform')">
      <template #default="{ row }">
        <div class="flex flex-row justify-center items-center">
          <Icon :icon="`svg-icon:${row.icon}`" class="cursor-pointer hover:text-gold !text-xl" />
          <p class="ml-5">{{ row.title }}</p>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="center" :label="t('profile.userSocial.option')">
      <template #default="{ row }">
        <template v-if="row.openid">
          {{ t('profile.userSocial.binded') }}
          <XTextButton
            class="mr-5"
            :title="t('profile.userSocial.unbind')"
            type="primary"
            @click="unbind(row)"
          />
        </template>
        <template v-else>
          {{ t('profile.userSocial.unbinded') }}

          <XTextButton
            class="mr-5"
            :title="t('profile.userSocial.bind')"
            type="primary"
            @click="bindThirdplatformOAuth2(row.type)"
          />
        </template>
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup lang="ts">
defineOptions({
  name: 'UserSocial'
})

import { SystemUserSocialTypeEnum } from '@/utils/constants'
import { getUserProfile, ProfileVO } from '@/api/system/user/profile'
import { socialBind, socialUnbind } from '@/api/system/user/socialUser'
const { t } = useI18n()
const message = useMessage()
const socialUsers = ref<any[]>([])
const userInfo = ref<ProfileVO>()

const initSocial = async () => {
  const res = await getUserProfile()
  userInfo.value = res
  for (const i in SystemUserSocialTypeEnum) {
    const socialUser = { ...SystemUserSocialTypeEnum[i] }
    socialUsers.value.push(socialUser)
    if (userInfo.value?.socialUsers) {
      for (const j in userInfo.value.socialUsers) {
        if (socialUser.type === userInfo.value.socialUsers[j].type) {
          socialUser.openid = userInfo.value.socialUsers[j].openid
          break
        }
      }
    }
  }
}
const route = useRoute()
const bindSocial = () => {
  // 社交绑定
  const type = route.query.type
  const code = route.query.code
  const state = route.query.state
  const redirectUri = location.origin + `/thirdplatform/oauth2/bind/${type}`
  if (!code) {
    return
  }
  socialBind(type, code, state, redirectUri).then(() => {
    message.success(t('profile.userSocial.bindSuccess'))
    initSocial()
  })
}
//绑定社交平台
import useBindThirdplatformOAuth2 from '../common/useBindThirdplatformOAuth2'
const { bindThirdplatformOAuth2 } = useBindThirdplatformOAuth2()

//解除社交平台绑定
const unbind = async (row) => {
  const res = await socialUnbind(row.type, row.openid)
  if (res) {
    row.openid = undefined
  }
  message.success(t('profile.userSocial.unbindSuccess'))
}

onMounted(async () => {
  await initSocial()
})

watch(
  () => route,
  (newRoute) => {
    bindSocial()
    console.log(newRoute)
  },
  {
    immediate: true
  }
)
</script>
