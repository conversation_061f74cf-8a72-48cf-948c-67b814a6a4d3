/*
 * @Author: HoJack
 * @Date: 2023-12-13 14:12:14
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-13 14:19:53
 * @Description:
 */
export default {
  api: {
    operationFailed: '操作失败',
    errorTip: '错误提示',
    errorMessage: '操作失败,系统异常!',
    timeoutMessage: '登录超时,请重新登录!',
    apiTimeoutMessage: '接口请求超时,请刷新页面重试!',
    apiRequestFailed: '请求出错，请稍候重试',
    networkException: '网络异常',
    networkExceptionMsg: '网络异常，请检查您的网络连接是否正常!',
    errMsg401: '用户没有权限（令牌、用户名、密码错误）!',
    errMsg403: '用户得到授权，但是访问是被禁止的。!',
    errMsg404: '网络请求错误,未找到该资源!',
    errMsg405: '网络请求错误,请求方法未允许!',
    errMsg408: '网络请求超时!',
    errMsg500: '服务器错误,请联系管理员!',
    errMsg501: '网络未实现!',
    errMsg502: '网络错误!',
    errMsg503: '服务不可用，服务器暂时过载或维护!',
    errMsg504: '网络超时!',
    errMsg505: 'http版本不支持该请求!',
    errMsg901: '演示模式，无法进行写操作!'
  },
  app: {
    title: 'UMV卡云',
    logoutTip: '温馨提醒',
    logoutMessage: '是否确认退出系统?',
    menuLoading: '菜单加载中...'
  },
  exception: {
    backLogin: '返回登录',
    backHome: '返回首页',
    subTitle403: '抱歉，您无权访问此页面。',
    subTitle404: '抱歉，您访问的页面不存在。',
    subTitle500: '抱歉，服务器报告错误。',
    noDataTitle: '当前页无数据',
    networkErrorTitle: '网络错误',
    networkErrorSubTitle: '抱歉，您的网络连接已断开，请检查您的网络！'
  },
  lock: {
    unlock: '点击解锁',
    alert: '锁屏密码错误',
    backToLogin: '返回登录',
    entry: '进入系统',
    placeholder: '请输入锁屏密码或者用户密码'
  },
  login: {
    backSignIn: '返回',
    signInFormTitle: '登录',
    ssoFormTitle: '三方授权',
    mobileSignInFormTitle: '手机登录',
    qrSignInFormTitle: '二维码登录',
    signUpFormTitle: '注册',
    forgetFormTitle: '重置密码',
    signInTitle: '开箱即用的中后台管理系统',
    signInDesc: '输入您的个人详细信息开始使用！',
    policy: '我同意xxx隐私政策',
    scanSign: `扫码后点击"确认"，即可完成登录`,
    loginButton: '登录',
    registerButton: '注册',
    rememberMe: '记住我',
    forgetPassword: '忘记密码?',
    otherSignIn: '其他登录方式',
    // notify
    loginSuccessTitle: '登录成功',
    loginSuccessDesc: '欢迎回来',
    // placeholder
    accountPlaceholder: '请输入账号',
    passwordPlaceholder: '请输入密码',
    smsPlaceholder: '请输入验证码',
    mobilePlaceholder: '请输入手机号码',
    policyPlaceholder: '勾选后才能注册',
    diffPwd: '两次输入密码不一致',
    userName: '账号',
    password: '密码',
    confirmPassword: '确认密码',
    email: '邮箱',
    smsCode: '短信验证码',
    mobile: '手机号码',
    ssoAuthTip: 'SSO 授权后的回调页',
    authCode: '授权码: ',
    usingCode: '正在使用 code 授权码，进行 accessToken 访问令牌的获取',
    getToken: '获取token',
    loginFail: '登录失败',
    ssoLoading: '正在获取访问权限,请稍等...',
    mailLogin: '邮箱登录',
    inputMail: '请输入邮箱',
    secSend: 's后重新发送',
    mobileLogin: '手机登录',
    inputMobile: '请输入手机号',
    inputMainAccount: '请输入主账号',
    pwdLogin: '密码登录',
    ramLogin: 'RAM账号登录',
    verLogin: '验证码登录',
    sendVer: '发送验证码',
    sendSuccess: '发送成功',
    sendFail: '发送失败',
    inputRightMail: '请输入正确格式的邮箱',
    inputNickname: '请输入昵称',
    phone: '手机',
    authMethod: '认证方式',
    inputPhoneVer: '请输入手机验证码',
    inputEmailVer: '请输入邮箱验证码',
    ver: '认证',
    selectAuthMethod: '请选择认证方式',
    phoneVer: '手机认证',
    mailVer: '邮箱认证',
    inputVer: '请输入验证码',
    phoneVered: '手机已认证',
    mailVered: '邮箱已认证',
    linkOverdute: '链接已过期',
    verSuccess: '认证成功',
    inputMobileOrMail: '请输入正确的手机号码或邮箱',
    inputMobileOrMailRight: '请输入正确格式的邮箱或手机号码'
  },
  permission: {
    hasPermission: `请设置操作权限标签值`,
    hasRole: `请设置角色权限标签值`
  },
  user: {
    gerUserInfoFail: '获取用户信息失败',
    noRole: '该用户未分配角色!请联系管理员分配用户角色!',
    loginVer: '登录认证',
    phoneVered: '手机号码已认证',
    mailVered: '邮箱已认证',
    certified: '已认证',
    email: '邮箱',
    linkValidTerm: '链接有效期为：',
    phoneLink: '手机认证链接：',
    emailLink: '邮箱认证链接：',
    copyAll: '复制全部',
    copy: '复制',
    min: '分钟',
    rDesc1: '新增成功，认证链接请在 操作>更多>登录认证 中获取',
    tips: '提示',
    confirms: '确 认',
    unlock: '解锁',
    isUnlock: '是否锁定',
    selectUnlockUser: '请选择需要解锁的用户',
    sureUnlockUser: '确定解锁用户？'
  },
  dictFail: '获取数据字典失败',
  welcomeUse: '欢迎使用',
  errorCode: {
    code401: '认证失败，无法访问系统资源',
    code403: '当前操作没有权限',
    code404: '访问资源不存在',
    codeDefault: '系统未知错误，请反馈给管理员'
  },
  service: {
    invalidToken: '无效的刷新令牌',
    expiredToken: '刷新令牌已过期',
    code901: 'code为901,请联系管理员',
    unFindRole: '未能找到用户角色,登录已失效,请重新登录!',
    loginInvalid: '登录已失效',
    pleaseRelogin: ',请重新登录!'
  },
  hooks: {
    web: {
      validfail: '校验失败'
    }
  }
}
