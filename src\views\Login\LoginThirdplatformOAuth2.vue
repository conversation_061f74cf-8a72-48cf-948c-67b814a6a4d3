<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-21 11:25:53
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-06-28 10:35:32
 * @Description:   第三方Oauth2登录回调页面
-->
<template>
  <div class="bg-light-50 sso-callback text-dark-800">
    <!-- <div class="flex justify-center h-20%">第三方Oauth2登录回调页面</div>
    <div class="flex justify-center h-20%">正在努力登录ing,请稍后</div> -->
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SsoCallback'
})

import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// 登录

import { encrypt } from '@/utils/jsencrypt'
import { aesEncrypt, getAes<PERSON>ey } from '@/utils/ase'
import * as authUtil from '@/utils/auth'
import * as LoginApi from '@/api/login'

const clientId = import.meta.env.VITE_APP_CLIENT_ID

const loading = ElLoading.service({
  lock: true,
  text: `Third-party Oauth2 login callback page. Logging in, please wait.
`,
  background: 'rgba(0, 0, 0, 0.7)'
})

const handleLogin = async () => {
  try {
    const type = route.query.type as string
    const code = route.query.code as string
    const state = route.query.state as string
    const redirectUri = location.origin + `/thirdplatform/oauth2/login/${type}`

    //手机验证码
    let obj: LoginApi.AuthV2LoginReqVO<any> = {
      loginMethod: 60,
      payload: {},
      sk: ''
      // imageVerificationCodeId: '',
      // imageVerificationCode: ''
    }

    let valueObj: LoginApi.SocialLoginPayload = {
      clientId,
      type,
      code,
      state,
      redirectUri
    }

    let publicKey = ''

    const aesKey = getAesKey()

    publicKey = await LoginApi.getEncodeKey()

    obj.payload = aesEncrypt(JSON.stringify(valueObj), aesKey)
    obj.sk = encrypt(aesKey, publicKey) as string

    const res = await LoginApi.v2login(obj)
    console.log(res)

    if (!res) {
      return
    }

    authUtil.setTenantId(res.loginInfo.tenantId)

    // 查看当前账户，是否从来没有重置过密码，产品要求必须要重置过密码才能使用其他功能
    if (res.loginInfo.loginFirstTime) {
      sessionStorage.setItem('firstLoginInfo', JSON.stringify({ ...res }))
      // emit('changeFormType', 3)
      return
    }

    authUtil.setToken(res)
    router.push({ path: '/HomePage' })
    loading.close()
  } catch (error) {
    console.log('登录失败' + error)
    //登录失败后，跳转到宣传首页
    router.push({
      name: 'Home',
      query: {
        redirect: '/HomePage'
      }
    })
    loading.close()
  } finally {
  }
}

watch(
  () => route.query.code,
  (val: string) => {
    if (val) {
      handleLogin()
    }
  },
  {
    immediate: true
  }
)
</script>
<style lang="scss" scope>
.sso-callback {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/svgs/sso.svg');
  background-size: auto;
  background-repeat: no-repeat;
  background-position: center;
}
</style>
