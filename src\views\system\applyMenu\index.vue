<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="菜单名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入菜单名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          class="!w-240px"
          clearable
          placeholder="请选择菜单状态"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <icon-ep-search class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <icon-ep-refresh class="mr-5px" style="font-size: 12px" />
          重置
        </el-button>
        <el-button
          plain
          type="primary"
          @click="openForm('create')"
          v-if="queryObj?.isCheck !== 'true'"
        >
          <icon-ep-plus class="mr-5px" style="font-size: 12px" />
          新增
        </el-button>
        <el-button plain type="danger" @click="toggleExpandAll">
          <icon-ep-sort class="mr-5px" style="font-size: 12px" />
          展开/折叠
        </el-button>
        <el-button plain @click="refreshMenu" v-if="queryObj?.isCheck !== 'true'">
          <icon-ep-refresh class="mr-5px" style="font-size: 12px" />
          刷新菜单缓存
        </el-button>
        <el-button plain type="warning" @click="openBatchCreateMenu" v-if="ifAdmin">
          <icon-ep-plus class="mr-5px" style="font-size: 12px" />
          批量新增
        </el-button>
        <span
          class="ml-5 text-gold"
          v-if="envController.getEnvironment() == 'uat' || envController.getEnvironment() == 'sit'"
        >
          修改菜单需要联系管理员,请不要随意修改,以免造成系统异常
        </span>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 详情 -->
  <ContentWrap v-if="appId">
    <el-row :gutter="20">
      <el-col :span="6">名称：{{ queryObj.name }}</el-col>
      <el-col :span="6">版本号：{{ queryObj.ver }}</el-col>
      <el-col :span="6">应用ID：{{ queryObj.id }}</el-col>
      <el-col :span="6">
        复制菜单JSON
        <el-button @click="copy" type="primary">copy</el-button>
      </el-col>
      <el-col :span="6">
        输出菜单tree
        <el-button @click="copyTree" type="primary">copy tree</el-button>
      </el-col>
    </el-row>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      border
      v-if="refreshTable"
      v-loading="loading"
      :data="list"
      :default-expand-all="isExpandAll"
      row-key="id"
    >
      <el-table-column
        resizable
        :show-overflow-tooltip="true"
        label="菜单名称"
        prop="name"
        width="180"
      >
        <template #default="scope">
          {{ scope.row.name }}
          <el-tag v-if="!scope.row.visible" type="error">隐</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="图标" prop="icon" width="80">
        <template #default="scope">
          <Icon v-if="scope.row.icon" :icon="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort" width="60" />
      <el-table-column
        :show-overflow-tooltip="true"
        label="权限标识"
        width="100"
        prop="permission"
      />
      <el-table-column :show-overflow-tooltip="true" label="路由路径" prop="path" width="220" />
      <el-table-column :show-overflow-tooltip="true" label="编码" prop="code" width="220" />
      <el-table-column :show-overflow-tooltip="true" label="组件路径" prop="component" />
      <el-table-column
        :show-overflow-tooltip="true"
        label="组件名称"
        prop="componentName"
        width="200"
      />
      <el-table-column label="业务场景" prop="status" width="100">
        <template #default="scope">
          <dict-tag type="system_business_scene" :value="scope.row.scene" />
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150">
        <template #default="scope">
          <div v-if="queryObj?.isCheck !== 'true'">
            <el-button link type="primary" @click="openForm('update', scope.row.id)">
              修改
            </el-button>
            <el-button link type="primary" @click="openForm('create', undefined, scope.row.id)">
              新增
            </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </div>
          <el-button v-else link type="primary" @click="openForm('detail', scope.row.id)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <MenuForm ref="formRef" @success="getList" :app-id="appId" />
  <!-- 批量新增菜单 -->
  <BatchCreateMenu
    ref="batchCreateMenuRef"
    v-model:applicationId="appId"
    v-model:oldMenueTree="oldMenueTree"
    :appCode="query.code"
    @success="getList"
  />
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemMenu'
})

import envController from '@/controller/envController'

/**超管模式👇👇👇👇👇👇 */
const ifAdmin = computed(() => {
  const route = useRoute()
  return route?.query?.ifAdmin as string | undefined
})
import BatchCreateMenu from './BatchCreateMenu.vue'
let batchCreateMenuRef = ref()
const openBatchCreateMenu = () => {
  batchCreateMenuRef.value.open()
}
/**超管模式 👆👆👆👆👆👆*/

//复制菜单数组
import { useClipboard } from '@vueuse/core'

const copy = async (text: string) => {
  const { copy, copied, isSupported } = useClipboard({ source: JSON.stringify(oldMenueTree.value) })
  if (!isSupported) {
    message.error(t('common.copyError'))
  } else {
    await copy()
    if (unref(copied)) {
      message.success(t('common.copySuccess'))
    }
  }
}
const getNodeNameList = () => {
  let treeData = handleTree(cloneDeep(oldMenueTree.value))
  console.log(treeData)

  let nameList = []
  function traverse(nodes, parentName = '') {
    nodes.forEach((node) => {
      // 拼接当前节点的名字
      const fullName = parentName ? `${parentName}-${node.name}` : node.name
      nameList.push(fullName)

      // 递归遍历子节点
      if (node.children && node.children.length > 0) {
        traverse(node.children, fullName)
      }
    })
  }

  // 开始递归遍历
  traverse(treeData)

  return nameList
}
const copyTree = async (parentName = '') => {
  console.log(getNodeNameList())
  const excelData = getNodeNameList().join('\n')

  console.log(excelData)
}

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { handleTree } from '@/utils/tree'
import * as MenuApi from '@/api/system/menu'
import MenuForm from './MenuForm.vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { cloneDeep } from 'lodash-es'
const { query } = useRoute() // 路由
const { wsCache } = useCache()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const queryParams = reactive({
  name: undefined,
  status: undefined
})
const queryFormRef = ref() // 搜索的表单
const isExpandAll = ref(false) // 是否展开，默认全部折叠
const refreshTable = ref(true) // 重新渲染表格状态

// 应用id
const appId = computed(() => {
  return (query?.id as unknown as number) - 0
})
const queryObj = computed(() => {
  return query
})

/** 查询列表 */
let oldMenueTree = ref([])
const getList = async () => {
  loading.value = true
  try {
    const data = await MenuApi.getAppMenuList({ ...queryParams, applicationId: appId.value })
    oldMenueTree.value = cloneDeep(data)
    list.value = handleTree(data)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number, parentId?: number) => {
  formRef.value.open(type, id, parentId)
}

/** 展开/折叠操作 */
const toggleExpandAll = () => {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

/** 刷新菜单缓存按钮操作 */
const refreshMenu = async () => {
  try {
    await message.confirm('即将更新缓存刷新浏览器！', '刷新菜单缓存')
    // 清空，从而触发刷新
    wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
    // 刷新浏览器
    location.reload()
  } catch {}
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MenuApi.deleteMenu(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  console.log('appId', appId.value)
  getList()
})
</script>
