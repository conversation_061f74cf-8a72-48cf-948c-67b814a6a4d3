<template>
  <UmvContent>
    <!-- 搜索工作栏 -->
    <template #search>
      <UmvQuery
        ref="queryFormRef"
        v-model="queryParams"
        :opts="queryOpts"
        @check="handleQuery"
        @reset="getList"
      />
    </template>

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="getList">
      <!-- 自定义工具栏 -->
      <template #tools>
        <el-button
          type="primary"
          plain
          size="small"
          v-hasPermi="['bpm:process-instance:query']"
          @click="handleCreate"
        >
          <icon-ep-plus style="font-size: 12px" class="mr-5px" /> 发起流程
        </el-button>
      </template>

      <!-- 分页区域 -->
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>
  </UmvContent>
</template>

<script setup lang="tsx">
defineOptions({
  name: 'BpmProcessInstance'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { checkPermi } from '@/utils/permission'

const router = useRouter() // 路由
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: '',
  processDefinitionId: undefined,
  category: undefined,
  status: undefined,
  result: undefined,
  createTime: []
})

// 搜索条件配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '流程名称',
    defaultVal: '',
    controlRender: (form) => <el-input v-model={form.name} placeholder="请输入流程名称" clearable />
  },
  processDefinitionId: {
    label: '所属流程',
    defaultVal: '',
    controlRender: (form) => (
      <el-input v-model={form.processDefinitionId} placeholder="请输入流程定义的编号" clearable />
    )
  },
  category: {
    label: '流程分类',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.category} placeholder="请选择流程分类" clearable>
        {getIntDictOptions(DICT_TYPE.BPM_MODEL_CATEGORY).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  status: {
    label: '状态',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.status} placeholder="请选择状态" clearable>
        {getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  result: {
    label: '结果',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.result} placeholder="请选择结果" clearable>
        {getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  createTime: {
    label: '提交时间',
    defaultVal: [],
    controlRender: (form) => (
      <el-date-picker
        v-model={form.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        clearable
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
      />
    )
  }
})

const queryFormRef = ref() // 搜索的表单

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: '流程编号', align: 'center', width: '150px', showOverflowTooltip: true },
  { prop: 'name', label: '流程名称', align: 'center', width: '200px' },
  {
    prop: 'category',
    label: '流程分类',
    align: 'center',
    renderTemplate: (scope) => (
      <dict-tag type={DICT_TYPE.BPM_MODEL_CATEGORY} value={scope.row.category} />
    )
  },
  {
    prop: 'tasks',
    label: '当前审批任务',
    width: '120',
    renderTemplate: (scope) => (
      <div class="flex flex-col items-baseline">
        {scope.row.tasks?.map((task) => (
          <el-button key={task.id} type="primary" link>
            <span>{task.name}</span>
          </el-button>
        ))}
      </div>
    )
  },
  {
    prop: 'status',
    label: '状态',
    renderTemplate: (scope) => (
      <dict-tag type={DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS} value={scope.row.status} />
    )
  },
  {
    prop: 'result',
    label: '结果',
    renderTemplate: (scope) => (
      <dict-tag type={DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT} value={scope.row.result} />
    )
  },
  {
    prop: 'createTime',
    label: '提交时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'endTime',
    label: '结束时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.endTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'left',
    width: '120',
    renderTemplate: (scope) => (
      <>
        {checkPermi(['bpm:process-instance:cancel']) && (
          <el-button link type="primary" onClick={() => handleDetail(scope.row)}>
            详情
          </el-button>
        )}
        {scope.row.result === 1 && checkPermi(['bpm:process-instance:query']) && (
          <el-button link type="primary" onClick={() => handleCancel(scope.row)}>
            取消
          </el-button>
        )}
      </>
    )
  }
])

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProcessInstanceApi.getMyProcessInstancePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 发起流程操作 **/
const handleCreate = () => {
  router.push({
    name: 'BpmProcessInstanceCreate'
  })
}

/** 查看详情 */
const handleDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消按钮操作 */
const handleCancel = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.id, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
