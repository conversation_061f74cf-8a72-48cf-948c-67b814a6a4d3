<template>
  <div class="innovate-product">
    <section class="w-full">
      <el-image
        v-if="ifEn"
        class="w-full"
        :src="`${basePath}/Home/InnovateProduct/en_US/innovate_product_banner.webp`"
        alt=""
      />

      <el-image
        v-else
        class="w-full"
        :src="`${basePath}/Home/InnovateProduct/innovate_product_banner.webp`"
        alt=""
      />
    </section>

    <section
      class="flex flex-col justify-center items-center pt-117px pb-161px <2xl:(pt-83px pb-115px)"
    >
      <el-image
        lazy
        class="scale-img w-986px h-375px <2xl:(w-701px h-267px)"
        :src="`${basePath}/Home/InnovateProduct/innovate_product_card.png`"
        alt=""
        @click="
          () => {
            showImagePreview = true
            imageIndex = 0
          }
        "
      />
      <div class="mt-62px text-40px <2xl:(mt-44px text-28px)">
        {{ t('home.InnovateProduct.title1') }}
      </div>
      <el-image
        lazy
        class="w-860px h-5px mt-23px <2xl:(w-612px h-4px mt-16px)"
        :src="`${basePath}/Home/InnovateProduct/innovate_product_line.png`"
        alt=""
      />
      <div style="color: #333333" class="text-16px my-40px <2xl:(text-14px my-28px)">
        {{ t('home.InnovateProduct.title1_item1') }}
      </div>

      <ArrowBtn
        @click="
          () => {
            showImagePreview = true
            imageIndex = 0
          }
        "
        :btnTitle="t('home.InnovateProduct.btnTitle')"
      />
    </section>

    <section class="protect-env flex flex-col items-center h-542px <2xl:(h-386px)">
      <el-image
        lazy
        class="scale-img w-986px h-310px relative -top-72px <2xl:(w-701px h-221px -top-51px)"
        :src="`${basePath}/Home/InnovateProduct/innovate_product_card2.png`"
        alt=""
        @click="
          () => {
            showImagePreview = true
            imageIndex = 1
          }
        "
      />
      <div class="mt-22px text-40px <2xl:(mt-16px text-28px)">
        {{ t('home.InnovateProduct.title2') }}
      </div>
      <el-image
        lazy
        class="w-860px h-5px mt-23px <2xl:(w-612px h-4px mt-16px)"
        :src="`${basePath}/Home/InnovateProduct/innovate_product_line.png`"
        alt=""
      />
      <div style="color: #333333" class="text-16px mt-40px <2xl:(text-14px mt-28px)">
        {{ t('home.InnovateProduct.title2_item1') }}
      </div>
    </section>

    <section class="flex flex-col items-center pb-293px <2xl:(pb-208px)">
      <ArrowBtn
        class="relative top-[-25px]"
        @click="
          () => {
            showImagePreview = true
            imageIndex = 1
          }
        "
        :btnTitle="t('home.InnovateProduct.btnTitle')"
      />

      <el-image
        lazy
        class="scale-img w-986px h-378px mt-91px <2xl:(w-701px h-269px mt-65px)"
        :src="`${basePath}/Home/InnovateProduct/innovate_product_card3.png`"
        alt=""
        @click="
          () => {
            showImagePreview = true
            imageIndex = 2
          }
        "
      />
      <div class="text-40px mt-94px <2xl:(text-28px mt-67px)">
        {{ t('home.InnovateProduct.title3') }}
      </div>
      <el-image
        lazy
        class="w-860px h-5px mt-23px <2xl:(w-612px h-4px mt-16px)"
        :src="`${basePath}/Home/InnovateProduct/innovate_product_line.png`"
        alt=""
      />
      <div style="color: #333333" class="text-16px my-40px <2xl:(text-14px my-28px)">
        {{ t('home.InnovateProduct.title3_item1') }}
      </div>

      <ArrowBtn
        @click="
          () => {
            showImagePreview = true
            imageIndex = 2
          }
        "
        :btnTitle="t('home.InnovateProduct.btnTitle')"
      />
    </section>

    <section
      class="protect-env flex flex-col justify-top items-center relative h-542px <2xl:(h-386px)"
    >
      <el-image
        lazy
        class="scale-img relative w-986px h-422px -top-184px <2xl:(w-701px h-300px -top-131px)"
        :src="`${basePath}/Home/InnovateProduct/innovate_product_card4.png`"
        alt=""
        @click="
          () => {
            showImagePreview = true
            imageIndex = 3
          }
        "
      />
      <div class="text-40px -mt-90px <2xl:(text-28px -mt-64px)">
        {{ t('home.InnovateProduct.title4') }}
      </div>
      <el-image
        lazy
        class="w-860px h-5px mt-23px <2xl:(w-612px h-4px mt-16px)"
        :src="`${basePath}/Home/InnovateProduct/innovate_product_line.png`"
        alt=""
      />
      <div style="color: #333333" class="text-16px mt-40px <2xl:(text-14px mt-28px)">
        {{ t('home.InnovateProduct.title4_item1') }}
      </div>
    </section>

    <section class="text-center pb-44px <2xl:(pb-44px)">
      <ArrowBtn
        class="relative top-[-25px]"
        @click="
          () => {
            showImagePreview = true
            imageIndex = 3
          }
        "
        :btnTitle="t('home.InnovateProduct.btnTitle')"
      />
    </section>
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="ifEn ? urlListEn_US : urlList"
      :initial-index="imageIndex"
      hide-on-click-modal
      teleported
      close-on-press-escape
      @close="showImagePreview = false"
    />
  </div>
</template>

<script setup lang="ts">
const { t, ifEn } = useI18n()
const basePath = import.meta.env.BASE_URL === '/' ? '' : import.meta.env.BASE_URL
import ArrowBtn from './ArrowBtn.vue'

const urlList = [
  new URL('/Home/InnovateProduct/innovate_product_preview.png', import.meta.url).href,
  new URL('/Home/InnovateProduct/innovate_product_preview2.png', import.meta.url).href,
  new URL('/Home/InnovateProduct/innovate_product_preview3.png', import.meta.url).href,
  new URL('/Home/InnovateProduct/innovate_product_preview4.png', import.meta.url).href
]
const urlListEn_US = [
  new URL('/Home/InnovateProduct/en_US/innovate_product_preview.jpg', import.meta.url).href,
  new URL('/Home/InnovateProduct/en_US/innovate_product_preview2.jpg', import.meta.url).href,
  new URL('/Home/InnovateProduct/en_US/innovate_product_preview3.jpg', import.meta.url).href,
  new URL('/Home/InnovateProduct/en_US/innovate_product_preview4.jpg', import.meta.url).href
]
const imageIndex = ref(0)
let showImagePreview = ref(false)
</script>

<style lang="scss" scoped>
.innovate-product {
  .scale-img {
    transition: all 0.3s;
    &:hover {
      transform: scale(1.1);
    }
  }

  .protect-env {
    background: url('/Home/InnovateProduct/innovate_product_bg.png') center/100% 100% no-repeat;
  }
}
</style>
