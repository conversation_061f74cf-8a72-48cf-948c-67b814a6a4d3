/*
 * @Author: 陈浩杰 <EMAIL>
 * @Date: 2024-11-26 14:15:00
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2024-12-05 14:45:07
 * @Description:  pinyin 拼音排序
 */
import { pinyin } from 'pinyin-pro'

/**
 * 递归处理以拼音进行的排序
 * @param _arr 排序的列表
 * @param sortName 拼音排序的依据名称字段
 */
export const recursionSortListFn = (_arr, sortName) => {
  // 先对顶层数组进行排序
  _arr.sort((a, b) => {
    const posA = pinyin(a[sortName].trim(), { toneType: 'none' }).replaceAll(' ', '')
    const posB = pinyin(b[sortName].trim(), { toneType: 'none' }).replaceAll(' ', '')
    // return comparePinyin(posA, posB)
    return posA.localeCompare(posB)
  })

  // 再递归处理每个子列表
  _arr.forEach((item) => {
    if (item.children && item.children.length > 1) {
      item.children = recursionSortListFn(item.children, sortName)
    }
  })
  return _arr
}
