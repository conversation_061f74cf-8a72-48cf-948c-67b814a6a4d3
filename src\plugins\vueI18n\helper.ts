export const setHtmlPageLang = (locale: LocaleType) => {
  document.querySelector('html')?.setAttribute('lang', locale)
}

import { set } from 'lodash-es'
import i18nModules from './i18nModules'

/**
 * 导入翻译文件
 * @param lang 语言
 * @returns
 */
export const importLangModule = (lang: LocaleType) => {
  const langModule = {} // 翻译文件

  // 处理所有模块的翻译文件
  Object.keys(i18nModules).forEach((moduleName) => {
    const moduleConfig = i18nModules[moduleName]
    const files = moduleConfig.langFiles[lang]
    const modulePath = moduleConfig.path

    for (const key in files) {
      const fileModuleName = key.replace(
        new RegExp(`(\\.\\.\\/\\.\\.\\/|${modulePath}\\/${lang}\\/|\\.ts)`, 'g'),
        ''
      )

      const moduleNameArray = fileModuleName.split('/')

      if (moduleNameArray.length > 1) {
        // 处理二级目录
        set(langModule, moduleNameArray[0], langModule[moduleNameArray[0]] || {})
        set(langModule[moduleNameArray[0]], moduleNameArray[1], files[key].default)
      } else {
        // 根据合并模式处理
        if (moduleConfig.mergeMode === 'flat') {
          // 扁平模式：直接合并翻译对象，避免重复嵌套
          const translationObj = files[key].default || {}
          Object.keys(translationObj).forEach((translationKey) => {
            set(langModule, translationKey, translationObj[translationKey])
          })
        } else {
          // 嵌套模式：保持原有结构
          set(langModule, fileModuleName, files[key].default || {})
        }
      }
    }
  })

  langModule['OAuth 2.0'] = 'OAuth 2.0' // 避免菜单名是 OAuth 2.0 时，一直 warn 报错
  return langModule
}
