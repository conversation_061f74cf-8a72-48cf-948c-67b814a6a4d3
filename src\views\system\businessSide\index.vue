<template>
  <UmvContent>
    <UmvQuery
      v-model="queryParams"
      ref="queryFormRef"
      :opts="queryOpts"
      @check="handleQuery"
      @reset="handleQuery"
    />
    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="handleQuery">
      <template #tools>
        <el-button type="primary" size="small" @click="openForm('add')">
          <icon-ep-plus style="font-size: 12px" class="mr-5px" />
          新增
        </el-button>
      </template>
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </template>
    </UmvTable>
    <el-dialog
      :title="editFormValue?.id ? '编辑' : '新增'"
      v-model="show"
      width="600px"
      append-to-body
      destroy-on-close
      @close="close"
    >
      <el-form :model="editFormValue" label-width="80px" ref="formRef">
        <el-form-item
          label="名称"
          prop="name"
          :rules="{ required: true, trigger: ['blur', 'change'], message: '请输入名称' }"
        >
          <el-input v-model="editFormValue.name" clearable placeholder="请输入名称" />
        </el-form-item>
        <el-form-item
          label="编号"
          prop="code"
          :rules="{ required: true, trigger: ['blur', 'change'], message: '请输入编号' }"
        >
          <el-input v-model="editFormValue.code" clearable placeholder="请输入编号" />
        </el-form-item>
        <el-form-item
          label="地址"
          prop="url"
          :rules="{ required: true, trigger: ['blur', 'change'], message: '请输入地址' }"
        >
          <el-input v-model="editFormValue.url" clearable placeholder="请输入地址" />
        </el-form-item>
        <el-form-item
          label="OAuth"
          prop="oauthClient"
          :rules="{ required: true, trigger: ['blur', 'change'], message: '请输入OAuth' }"
        >
          <el-input v-model="editFormValue.oauthClient" clearable placeholder="请输入OAuth" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </template>
    </el-dialog>
  </UmvContent>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'SystemRoleForm'
})

import {
  getTenantClientPage,
  addTenantClient,
  updateTenantClient,
  deleteTenantClient,
  tenantClientRowVO,
  tenantClientVO,
  tableResVO
} from '@/api/system/businessSide'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import UmvContent from '@/components/UmvContent'

const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  code: undefined
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '业务端名',
    defaultVal: '',
    controlRender: (form) => <el-input v-model={form.name} placeholder="请输入业务端名" clearable />
  },
  code: {
    label: '业务端编号',
    defaultVal: '',
    controlRender: (form) => (
      <el-input v-model={form.code} placeholder="请输入业务端编号" clearable />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'name', label: '名称', align: 'center' },
  { prop: 'code', label: '编号', align: 'center' },
  { prop: 'url', label: '地址', align: 'center' },
  { prop: 'oauthClient', label: 'OAuth', align: 'center' },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        <ElButton link type="primary" onClick={() => openForm('update', scope.row)}>
          编辑
        </ElButton>
        <ElButton link type="primary" onClick={() => del(scope.row)}>
          删除
        </ElButton>
      </div>
    )
  }
])

const loading = ref(false)

const list: Ref<tenantClientRowVO[] | never[]> = ref([])

// Partial：全部变成可选
const editFormValue: Ref<Partial<tenantClientVO>> = ref({
  name: undefined,
  url: undefined,
  code: undefined,
  oauthClient: undefined
})

const show = ref(false)

const editRow = ref()

// 操作
const openForm = (type, row?) => {
  if (type === 'add') {
    editFormValue.value = {
      name: undefined,
      url: undefined,
      code: undefined,
      oauthClient: undefined
    }
    show.value = true
  } else {
    editFormValue.value = { ...row }
    editRow.value = row
    show.value = true
  }
}

// 数据总量，分页用
const total = ref(0)

// 搜索
const handleQuery = async () => {
  try {
    loading.value = true
    const res: tableResVO<tenantClientRowVO> = await getTenantClientPage({ ...queryParams.value })
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const queryFormRef = ref()

// 重置搜索条件
const resetQuery = () => {
  try {
    loading.value = true
    queryParams.value = {
      pageNo: 1,
      pageSize: 10,
      name: undefined,
      code: undefined
    }
    handleQuery()
  } finally {
    loading.value = false
  }
}

const handleId = ref()

// 删除数据
const del = async (row) => {
  console.log('row', row)
  handleId.value = row.id
  ElMessageBox.confirm('确认删除？', '提示', {
    confirmButtonText: '确 认',
    cancelButtonText: '取 消'
  })
    .then(async () => {
      try {
        console.log('row.id', row.id)
        await deleteTenantClient({ id: row.id })
        ElMessage.success('删除成功')
        handleQuery()
      } finally {
      }
    })
    .catch(() => console.info('操作取消'))
}

// 关闭
const close = () => {
  editFormValue.value = {
    name: undefined,
    url: undefined,
    oauthClient: undefined
  }
  show.value = false
}

const submitLoading = ref(false)

const formRef = ref()

// 提交
const submit = async () => {
  await formRef.value.validate()
  try {
    if (editFormValue.value.id) {
      // 编辑
      await updateTenantClient(editFormValue.value as tenantClientVO)
    } else {
      //新增
      await addTenantClient(editFormValue.value as tenantClientVO)
    }
    editFormValue.value = {
      name: undefined,
      url: undefined,
      code: undefined,
      oauthClient: undefined
    }
    show.value = false
    ElMessage.success('提交成功')
    handleQuery()
  } finally {
    submitLoading.value = false
  }
}

onMounted(() => {
  handleQuery()
})
</script>
