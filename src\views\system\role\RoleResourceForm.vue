<!--角色-资源配置-->
<template>
  <Dialog
    v-model="dialogVisible"
    :title="t('system.role.resCfg')"
    width="1000"
    destroy-on-close
    @close="closeVisible"
  >
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :rules="rules"
      :model="formData"
      :label-width="ifEn ? '150px' : '80px'"
    >
      <el-form-item :label="t('system.role.name')">
        <el-tag>{{ formData.name }}</el-tag>
      </el-form-item>
      <el-form-item :label="t('system.role.code')">
        <el-tag>{{ formData.code }}</el-tag>
      </el-form-item>
    </el-form>
    <el-form-item :label="t('system.role.dataScope')" style="display: flex">
      <el-card class="card" shadow="never">
        <template #header>
          <el-button
            plain
            type="primary"
            class="ml-16px"
            @click="showAddPermi"
            v-if="sceneList.length !== getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE).length"
          >
            <icon-ep-plus class="mr-5px" style="font-size: 12px" />
            {{ t('system.role.newScene') }}
          </el-button>
        </template>
        <el-table
          ref="tableRef"
          row-key="id"
          size="small"
          :data="treeOptions"
          default-expand-all
          :style="{ width: '100%' }"
        >
          <el-table-column
            prop="resourceOwnerName"
            width="360"
            :label="t('system.role.resourceOwnerName')"
            fixed="left"
          />
          <el-table-column
            v-for="(item, index) in sceneList"
            :label="item.sceneName"
            :key="index"
            :width="200"
          >
            <template #header
              ><span :style="{ color: 'var(--el-color-primary)', cursor: 'pointer' }">{{
                item.sceneName
              }}</span>
            </template>
            <template #default="{ row }">
              <div :style="{ width: '100%', display: 'flex', alignItems: 'center' }">
                <el-tooltip :content="authorityPointsStr(row.resourceMap[item.scene])">
                  <span
                    :style="{
                      maxWidth: '100px',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis'
                    }"
                  >
                    {{ authorityPointsStr(row.resourceMap[item.scene]) }}</span
                  >
                </el-tooltip>
                <el-tooltip :content="t('system.role.editPermi')" v-if="row.isSelf">
                  <Edit
                    class="ml-16px mt-2px"
                    @click="
                      openAuthorityPoints(
                        row.resourceMap[item.scene],
                        getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS),
                        row,
                        item.scene,
                        'edit'
                      )
                    "
                    :style="{ width: '16px', cursor: 'pointer', color: 'var(--el-color-primary)' }"
                  />
                </el-tooltip>
                <el-tooltip :content="t('system.role.viewPermi')" v-else>
                  <View
                    class="ml-16px mt-2px"
                    @click="
                      openAuthorityPoints(
                        row.resourceMap[item.scene],
                        getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS),
                        row,
                        item.scene,
                        'view'
                      )
                    "
                    :style="{ width: '16px', cursor: 'pointer' }"
                  />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-form-item>
    <template #footer>
      <el-button
        :disabled="formLoading"
        type="primary"
        @click="submitForm"
        v-if="roleTypeStr !== '系统角色'"
        >{{ t('common.ok') }}</el-button
      >
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
    <Dialog
      v-model="authorityPointsVisiable"
      @close="closeAuthorityPointsChange"
      :title="t('system.role.desc6')"
    >
      <el-checkbox-group v-model="selectedAuth">
        <el-checkbox
          v-for="(it, i) in getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)"
          :key="it.label + i"
          :label="it.value"
          :disabled="viewType !== 'edit'"
          >{{ it.label }}</el-checkbox
        >
      </el-checkbox-group>

      <template #footer>
        <el-button type="primary" @click="submitAuthorityPointsChange" v-if="viewType === 'edit'">{{
          t('common.ok')
        }}</el-button>
        <el-button @click="closeAuthorityPointsChange" v-if="viewType === 'edit'">{{
          t('common.cancel')
        }}</el-button>
        <el-button @click="closeAuthorityPointsChange" v-else>{{ t('common.cancel') }}</el-button>
      </template>
    </Dialog>
    <Dialog v-model="addPermiShow" :title="t('system.role.newScene')" @close="closePermiAdd">
      <el-form label-width="80px" :model="permiForm" ref="permiAddFormRef">
        <el-form-item
          :label="t('system.role.scene')"
          prop="scene"
          :rules="{
            required: true,
            trigger: ['blur', 'change'],
            message: t('system.role.scenePlaceholder')
          }"
        >
          <el-select :placeholder="t('system.role.scenePlaceholder')" v-model="permiForm.scene">
            <el-option
              v-for="(item, index) in filterSenceList"
              :key="index"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitPermiAdd" v-if="roleTypeStr !== '系统角色'">{{
          t('common.ok')
        }}</el-button>
        <el-button @click="closePermiAdd">{{ t('common.cancel') }}</el-button>
      </template>
    </Dialog>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'RoleResourceForm'
})

import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import * as RoleApi from '@/api/system/role'
import { Plus, Minus, Edit, View } from '@element-plus/icons-vue'

const { t, ifEn } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const roleTypeStr = ref('')
const roleTypeEnum = ['系统角色', '自定义角色']

const isAdvanced = ref(false)

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
let formData: any = reactive({
  id: 0,
  name: '',
  code: '',
  dataScope: undefined,
  dataScopeDeptIds: [],
  resources: []
})

const originFormData = {
  id: 0,
  name: '',
  code: '',
  dataScope: undefined,
  dataScopeDeptIds: []
}

const rules = ref({
  dataScope: [{ required: true, message: t('system.role.rDesc1'), trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref
const treeOptions = ref<any[]>([]) // 部门树形结构
const deptExpand = ref(false) // 展开/折叠
const treeRef = ref() // 菜单树组件 Ref
const tableRef = ref() // 权限表格组件 Ref
const treeNodeAll = ref(false) // 全选/全不选
const checkStrictly = ref(true) // 是否严格模式，即父子不关联

const permissionPointMap = ref()

const sceneList: any = ref([])

// 转业务场景为map，查名字用
const systemBusinessSceneMap = computed(() => {
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

/** 打开弹窗 */
const open = async (row: RoleApi.RoleVO, isAd = false) => {
  formData = reactive({ ...originFormData })
  isAdvanced.value = isAd
  dialogVisible.value = true
  sceneList.value = []
  resetForm()
  // 加载 Dept 列表。注意，必须放在前面，不然下面 setChecked 没数据节点

  // 设置数据
  formData.id = row.id
  formData.name = row.name
  formData.code = row.code

  // 获取暴露的资源
  const res = await RoleApi.getListResourceExposes({ roleCode: row.code })
  treeOptions.value = res
  treeOptions.value.forEach((el) => {
    const resourceMap = {}
    el.resources.forEach((e) => {
      if (!resourceMap[e.scene]) {
        resourceMap[e.scene] = [e.permissionType]
        // 如果是isSelf就用这条数据的resources取出场景
        if (el.isSelf) {
          sceneList.value.push({
            scene: e.scene,
            sceneName: systemBusinessSceneMap.value[e.scene]
          })
        }
      } else {
        resourceMap[e.scene].push(e.permissionType)
      }
    })
    el.resourceMap = resourceMap
  })

  permissionPointMap.value = getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce(
    (a, b) => {
      return { ...a, [b.value]: b.label }
    },
    {}
  )

  roleTypeStr.value = roleTypeEnum[row.type - 1] //判断是否内置角色，如果是就不给编辑
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  if (roleTypeStr.value === '系统角色') {
    ElMessage.error(t('system.role.msg1'))
    return
  }
  formLoading.value = true
  try {
    const editingRow = treeOptions.value.find((el) => el.isSelf)
    let resources = []
    for (let key in editingRow.resourceMap) {
      editingRow.resourceMap[key].forEach((el) => {
        resources.push({
          scene: key,
          permissionType: el,
          resourceType: 2
        })
      })
    }

    console.log('resources', resources)

    await RoleApi.resourceExpose({
      roleCode: formData.code,
      resources: resources
    })
    ElMessage.success(t('common.handleSuccess'))
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  // 重置选项
  treeNodeAll.value = false
  deptExpand.value = false
  checkStrictly.value = true
  // 重置表单
  formData.value = {
    id: 0,
    name: '',
    code: '',
    dataScope: undefined,
    dataScopeDeptIds: []
  }
  treeRef.value?.setCheckedNodes([])
  tableRef.value?.clearSelection()
  formRef.value?.resetFields()
}

// 获取数组名称组合
const authorityPointsStr = (filterArr) => {
  return filterArr ? filterArr.map((el) => permissionPointMap.value[el]).join(',') : ''
}

// 是否显示弹窗
const authorityPointsVisiable = ref(false)

// 当前编辑的权限点
const editAuthorityPoints = ref([])

// 关闭权限点修改弹窗
const closeAuthorityPointsChange = () => {
  editAuthorityPoints.value = []
  authorityPointsVisiable.value = false
}

// 关闭弹窗
const closeVisible = () => {
  sceneList.value = []
}

// 选中的权限
const selectedAuth = ref([])

// 选中的行
const editRow = ref()
// 弹窗对应的场景id
const editSence = ref()

// 提交权限逻辑修改
const submitAuthorityPointsChange = async () => {
  try {
    editRow.value.resourceMap[editSence.value] = [...selectedAuth.value]
    ElMessage.success(t('common.submitSuccess'))
  } finally {
    closeAuthorityPointsChange()
  }
}

const viewType = ref()

// 打开权限选择弹窗
const openAuthorityPoints = (arr, authorityPoints, row, sence, type) => {
  // 洗干净数据
  editSence.value = undefined
  editRow.value = undefined
  selectedAuth.value = []
  viewType.value = undefined

  authorityPointsVisiable.value = true
  editAuthorityPoints.value = authorityPoints
  selectedAuth.value = arr ? [...arr] : []
  editRow.value = row
  editSence.value = sence
  viewType.value = type
}

/******************************** 新增场景start **********************************/
const permiForm = ref({
  scene: undefined,
  sceneName: undefined
})

const addPermiShow = ref(false)
const showAddPermi = () => {
  console.log('filterSenceList', filterSenceList.value)
  console.log('sceneMap', sceneMap.value)
  permiForm.value = {
    scene: undefined,
    sceneName: undefined
  }
  addPermiShow.value = true
}

const closePermiAdd = () => {
  addPermiShow.value = false
}

const permiAddFormRef = ref()

const submitPermiAdd = async () => {
  await permiAddFormRef.value.validate()
  console.log('permiForm', permiForm.value)

  sceneList.value.push({
    scene: permiForm.value.scene,
    sceneName: systemBusinessSceneMap.value[permiForm.value.scene]
  })
  addPermiShow.value = false
}

const sceneMap: any = computed(() => {
  return sceneList.value.reduce((a, b) => {
    return { ...a, [b.scene]: b }
  }, {})
})

// 将已有的场景过滤掉
const filterSenceList = computed(() => {
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE).filter((el) => {
    return !sceneMap.value[el.value]
  })
})

/******************************** 新增场景end **********************************/
</script>
<style lang="scss" scoped></style>
