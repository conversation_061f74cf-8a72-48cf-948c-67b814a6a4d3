/*
 * @Author: Ho<PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: hao-jie.chen <EMAIL>
 * @LastEditTime: 2025-01-15 09:29:01
 * @Description: axios封装   登录已失效===token失效
 */

import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestHeaders,
  AxiosResponse,
  InternalAxiosRequestConfig
} from 'axios'

import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import qs from 'qs'
import { config } from '@/config/axios/config'
import { getAccessToken, getRefreshToken, getTenantId, removeToken, setToken } from '@/utils/auth'
import errorCode from './errorCode'

import { resetRouter } from '@/router'

import { useCache } from '@/hooks/web/useCache'
import router from '@/router'

import { useAgentOperationStore } from '@/store/modules/agentOperation'

import * as authUtil from '@/utils/auth'

import { usePermissionStoreWithOut } from '@/store/modules/permission'

// 引入utils
import { getRequestId } from './utils'

const tenantEnable = import.meta.env.VITE_APP_TENANT_ENABLE
const { result_code, base_url, request_timeout } = config

// 需要忽略的提示。忽略后，自动 Promise.reject('error')

// 是否显示重新登录
export const isRelogin = { show: false }
// Axios 无感知刷新令牌，参考 https://www.dashingdog.cn/article/11 与 https://segmentfault.com/a/1190000020210980 实现
// 请求队列
let requestList: any[] = []

// 请求白名单，无须token的接口
const whiteList: string[] = ['/login', '/refresh-token', '/user/get-authentication-info']
// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: base_url, // api 的 base_url
  timeout: request_timeout, // 请求超时时间
  withCredentials: false // 禁用 Cookie 等信息
})

// request拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    //代客操作,国际版用户设置国际化域名地址
    const agentOperationStore = useAgentOperationStore()
    if (agentOperationStore.isInternational && agentOperationStore.internationalUrl) {
      config.baseURL = agentOperationStore.internationalUrl + import.meta.env.VITE_API_URL
    }

    // 是否需要设置 token
    let isToken = (config!.headers || {}).isToken === false
    whiteList.some((v) => {
      if (config.url) {
        config.url.indexOf(v) > -1
        return (isToken = false)
      }
    })
    if (config.headers.Authorization) {
      isToken = true
    }
    console.log('getAccessToken() && !isToken', getAccessToken() && !isToken)
    if (getAccessToken() && !isToken) {
      ;(config as Recordable).headers.Authorization = 'Bearer ' + getAccessToken() // 让每个请求携带自定义token
    }

    let isCustomTenantId = false

    if (config.headers) {
      if (config.headers['tenant-id']) {
        isCustomTenantId = true
      }
    }

    // 设置租户
    if (tenantEnable && tenantEnable === 'true' && !isCustomTenantId) {
      const tenantId = getTenantId()
      if (tenantId) (config as Recordable).headers['tenant-id'] = tenantId
    }
    //请求ID 请求头设置
    config.headers['request-id'] = getRequestId((config?.baseURL ?? '') + config.url)

    //国际版请求头设置
    config.headers['international'] = import.meta.env.VITE_APP_INTERNATIONAL

    //业务场景请求头设置
    const permissionStore = usePermissionStoreWithOut()
    config.headers['umv-scene'] = permissionStore.currentRouteScene

    const params = config.params || {}
    const data = config.data || false
    if (
      config.method?.toUpperCase() === 'POST' &&
      (config.headers as AxiosRequestHeaders)['Content-Type'] ===
        'application/x-www-form-urlencoded'
    ) {
      config.data = qs.stringify(data)
    }
    // get参数编码
    if (config.method?.toUpperCase() === 'GET' && params) {
      let url = config.url + '?'
      for (const propName of Object.keys(params)) {
        const value = params[propName]
        if (value !== void 0 && value !== null && typeof value !== 'undefined') {
          if (typeof value === 'object') {
            for (const val of Object.keys(value)) {
              const params = propName + '[' + val + ']'
              const subPart = encodeURIComponent(params) + '='
              url += subPart + encodeURIComponent(value[val]) + '&'
            }
          } else {
            url += `${propName}=${encodeURIComponent(value)}&`
          }
        }
      }
      // 给 get 请求加上时间戳参数，避免从缓存中拿数据
      // const now = new Date().getTime()
      // params = params.substring(0, url.length - 1) + `?_t=${now}`
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    return config
  },
  (error: AxiosError) => {
    // Do something with request error
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// response 拦截器
service.interceptors.response.use(
  async (response: AxiosResponse<any>) => {
    const { data, config } = response
    if (!data) {
      // 返回“[HTTP]请求没有返回值”;
      throw new Error()
    }
    const { t } = useI18n()
    // 未设置状态码则默认成功状态
    const code = data.code || result_code
    // 二进制数据则直接返回
    if (
      response.request.responseType === 'blob' ||
      response.request.responseType === 'arraybuffer'
    ) {
      return response
    }
    // 获取错误信息
    const msg = data.msg || errorCode[code] || errorCode['default']
    const ignoreMsgs = [
      t('sys.service.invalidToken'), // 刷新令牌被删除时，不用提示
      t('sys.service.expiredToken') // 使用刷新令牌，刷新获取新的访问令牌时，结果因为过期失败，此时需要忽略。否则，会导致继续 401，无法跳转到登出界面
    ]
    if (ignoreMsgs.indexOf(msg) !== -1) {
      // 如果是忽略的错误码，直接返回 msg 异常
      return Promise.reject(msg)
    } else if (code === 401) {
      const agentOperationStore = useAgentOperationStore()
      //如果是代理模式，则重新登录admin账号
      if (agentOperationStore.agentOperationMode) {
        ElMessageBox.alert('代客操作模式失败，将重新切回超管账号', '提示', {
          confirmButtonText: '确定',
          callback: () => {
            agentOperationStore.setLoginToken(true)
          }
        })
        return data
      }
      const _401whiteList = ['/system/auth/logout']
      if (!_401whiteList.some((el) => config.url?.indexOf(el) !== -1)) {
        return handle401(config)
      } else {
        return data
      }
    } else if (code === 403) {
      const _403whiteList = [
        '/system/resource/expose',
        '/system/resource/apply',
        '/system/resource/approval'
      ]
      if (!_403whiteList.some((el) => config.url?.indexOf(el) !== -1)) {
        return Promise.reject(data)
      } else {
        ElNotification.error({ title: msg })
        const agentOperationStore = useAgentOperationStore()
        //如果是代理模式，则重新登录admin账号
        if (agentOperationStore.agentOperationMode) {
          ElMessageBox.alert('代客操作模式失败，将重新切回超管账号', '提示', {
            confirmButtonText: '确定',
            callback: () => {
              agentOperationStore.setLoginToken(true)
            }
          })
        }
        return Promise.reject(data)
      }
    } else if (code === 500) {
      ElMessage.error(t('sys.api.errMsg500'))
      return Promise.reject(new Error(msg))
    } else if (code === 901) {
      ElMessage.error({
        offset: 300,
        dangerouslyUseHTMLString: true,
        message: `<div>${t('sys.service.code901')}</div>`
      })
      return Promise.reject(new Error(msg))
    } else if (code !== 200 && !(config as any)?.ignoreErrors) {
      if (msg === t('sys.service.invalidToken')) {
        // hard coding：忽略这个提示，直接登出
        console.log(msg)
      } else {
        ElNotification.error({ title: msg })
      }
      return Promise.reject(data)
    } else {
      return data
    }
  },
  (error: AxiosError) => {
    console.log('err' + error) // for debug
    const { config } = error
    const response = error.response as any
    let message = error.message
    //处理服务器code500时接口code401

    if (response.data.code === 401) {
      const _401whiteList = ['/system/auth/logout']
      if (!_401whiteList.some((el) => config?.url?.indexOf(el) !== -1)) {
        return handle401(config)
      }
    }

    const { t } = useI18n()

    if (message === 'Network Error') {
      message = t('sys.api.errorMessage')
    } else if (message.includes('timeout')) {
      message = t('sys.api.apiTimeoutMessage')
    } else if (message.includes('Request failed with status code')) {
      message = t('sys.api.apiRequestFailed') + message.substr(message.length - 3)
    }
    ElMessage.error(message)
    return Promise.reject(error)
  }
)
import { useUserStoreWithOut } from '@/store/modules/user'
const userStore = useUserStoreWithOut()

const refreshToken = async () => {
  const { t } = useI18n()

  axios.defaults.headers.common['tenant-id'] = getTenantId()
  axios.defaults.headers.common['client-id'] = userStore.getCurrentClientId

  const refreshTokenRes = (await axios.post(
    // base_url + '/system/auth/refresh-token?refreshToken=' + getRefreshToken()
    base_url +
      `/auth/refresh-token?clientId=${import.meta.env.VITE_APP_CLIENT_ID}&refreshToken=` +
      getRefreshToken() //独立服务
  )) as any

  if (refreshTokenRes.data.code == 401) {
    //  refreshtoken过期后返回的是401，不登出会导致死循环刷新token
    isRelogin.show = false
    return handleAuthorized(t('sys.service.loginInvalid'))
  }

  if (refreshTokenRes.data.code !== 0) {
    return handleAuthorized(t('sys.service.loginInvalid'))
  }
  return refreshTokenRes
}

const { wsCache } = useCache()

import { useLocaleStore } from '@/store/modules/locale'
import { useLocale } from '@/hooks/web/useLocale'

const localeStore = useLocaleStore()

const handleReloginInfo = () => {
  const tenantId = authUtil.getTenantId() //保存租户id

  resetRouter() // 重置静态路由表
  const loginForm = authUtil.getLoginForm()
  const lang = localeStore.getCurrentLocale.lang
  wsCache.clear()
  loginForm && authUtil.setLoginForm(loginForm)
  localeStore.setCurrentLocale({
    lang
  })
  const { changeLocale } = useLocale()
  changeLocale(lang)
  removeToken()
  userStore.resetState() //清除用户信息
  authUtil.setTenantId(tenantId)
}

const handleAuthorized = (confirmTitile?: string) => {
  const { t } = useI18n()
  if (!isRelogin.show) {
    isRelogin.show = true
    ElMessageBox.confirm(
      confirmTitile ? confirmTitile + t('sys.service.pleaseRelogin') : t('sys.api.timeoutMessage'),
      t('common.confirmTitle'),
      {
        showCancelButton: false,
        closeOnClickModal: false,
        showClose: false,
        confirmButtonText: t('login.relogin'),
        type: 'warning'
      }
    ).then(() => {
      handleReloginInfo()
      isRelogin.show = false
      // 将第三方应用参数isLoginOut改为false
      // 干掉token后再走一次路由让它过router.beforeEach的校验
      router.go(0)
    })
  }
  return Promise.reject(t('sys.api.timeoutMessage'))
}

/** 处理401 token失效/过期问题 */
// 是否正在刷新中
let isRefreshToken = false
let lastRefreshTokenTime = 0 // 上次请求时间

const handle401 = async (config) => {
  const currentTime = new Date().getTime()

  const { t } = useI18n()
  // 如果未认证，并且未进行刷新令牌，说明可能是访问令牌过期了
  if (!isRefreshToken) {
    // 1. 如果获取不到刷新令牌，则只能执行登出操作
    if (!getRefreshToken()) {
      //权鉴令牌为空
      return handleAuthorized(t('sys.service.loginInvalid'))
    }

    // 2. 进行刷新访问令牌
    try {
      isRefreshToken = true
      // 如果5秒内再次触发，则直接退出登录，避免重复刷新
      if (currentTime - lastRefreshTokenTime < 5000) {
        console.log('触发推出')
        return handleAuthorized('登录已失效')
      }
      lastRefreshTokenTime = currentTime //记录上次刷新时间
      const refreshTokenRes = await refreshToken()

      // 2.1 刷新成功，则回放队列的请求 + 当前请求
      setToken(refreshTokenRes.data.data)
      config.headers!.Authorization = 'Bearer ' + getAccessToken()
      requestList.forEach((cb: any) => {
        console.log('回调接口')
        cb()
      })
      requestList = []
      return service(config)
    } catch (e) {
      // 为什么需要 catch 异常呢？刷新失败时，请求因为 Promise.reject 触发异常。
      // 2.2 刷新失败，只回放队列的请求
      // requestList.forEach((cb: any) => {
      //   console.log('回调接口2')

      //   cb()
      // })
      // 提示是否要登出。即不回放当前请求！不然会形成递归
      console.log(e as string)

      // ElMessage.error(e as string)
      return handleAuthorized(t('sys.service.loginInvalid'))
    } finally {
      requestList = []
      isRefreshToken = false
    }
  } else {
    // 添加到队列，等待刷新获取到新的令牌
    return new Promise((resolve) => {
      requestList.push(() => {
        config.headers!.Authorization = 'Bearer ' + getAccessToken() // 让每个请求携带自定义token 请根据实际情况自行修改
        resolve(service(config))
      })
    })
  }
}

export { service, refreshToken, handleAuthorized }
