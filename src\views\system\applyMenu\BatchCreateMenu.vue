<template>
  <Dialog
    v-model="dialogVisible"
    title="批量新增菜单"
    :scroll="true"
    maxHeight="65vh"
    @close="emit('success')"
  >
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="菜单JSON" prop="menus">
        <el-input
          type="textarea"
          row="4"
          v-model="formData.menus"
          placeholder="请输入菜单JSON,非Tree"
        />
      </el-form-item>

      <el-form-item label="菜单权限" prop="menuIds">
        <el-card class="cardHeight">
          <template #header>
            <el-button @click="openOldAppDialog()"> 选择旧菜单 </el-button>
            <el-select
              v-if="oldAppListDialog"
              v-model="oldAppId"
              placeholder="选择旧应用"
              size="large"
              style="width: 240px"
              @change="getOldMenuTree"
            >
              <el-option
                v-for="item in oldAppList"
                :key="item.value"
                :label="item.name + item.ver"
                :value="item.id"
              />
            </el-select>
            新菜单长度:{{ formData?.menus && JSON.parse(formData.menus)?.length }}

            旧菜单长度:{{ oldMenueTree?.length }}

            <!-- 全选/全不选:
            <el-switch
              v-model="treeNodeAll"
              active-text="是"
              inactive-text="否"
              inline-prompt
              @change="handleCheckedTreeNodeAll"
            />
            全部展开/折叠:
            <el-switch
              v-model="menuExpand"
              active-text="展开"
              inactive-text="折叠"
              inline-prompt
              @change="handleCheckedTreeExpand"
            /> -->
          </template>
          <el-tree
            ref="treeRef"
            :data="handleTree(allMenuList)"
            :props="defaultProps"
            empty-text="暂无数据"
            node-key="id"
            show-checkbox
          >
            <template #default="{ node, data }">
              <span :class="[data.isDelete && 'del']">{{ node.label }}</span>
              <el-tag type="danger" effect="plain" round class="ml-3" v-if="data.ifNew">
                新增
              </el-tag>
            </template>
          </el-tree>
        </el-card>
      </el-form-item>
      <el-form-item label="新增菜单">
        <div class="flex flex-col">
          <span v-for="(item, index) in newMenuList" :key="index">
            {{ index + 1 }}-{{ findParentMenuName(item) }}- {{ item.name }}
          </span>
        </div>
      </el-form-item>
      <el-form-item label="删除菜单">
        <div class="flex flex-col">
          <span v-for="(item, index) in deleteMenuList" :key="index">
            {{ index + 1 }}- {{ findParentMenuName(item) }}-{{ item.name }}
          </span>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 选择旧应用 -->
</template>
<script setup lang="ts">
defineOptions({
  name: 'BatchCreateMenu'
})

import { defaultProps, handleTree } from '@/utils/tree'
import { ElTree } from 'element-plus'
import { isEmpty, cloneDeep } from 'lodash-es'

let message = useMessage()
const applicationId = defineModel('applicationId', { type: Number })
const oldMenueTree = defineModel('oldMenueTree', { type: Array })
const props = defineProps(['appCode'])

//通过旧应用-获取旧菜单树
let oldAppListDialog = ref<boolean>(false) //弹窗是否展示
let oldAppId = ref(null)

/** 查询列表 */
import { getTenantVersList } from '@/api/system/apply'

let oldAppList = ref() //旧应用列表

const getOldList = async () => {
  try {
    const data = await getTenantVersList({ code: props.appCode, pageNo: 1, pageSize: 20 })
    oldAppList.value = data.list
  } catch (error) {}
}

import { getAppMenuList } from '@/api/system/menu'
const getOldMenuTree = async () => {
  try {
    const data = await getAppMenuList({ applicationId: oldAppId.value })
    oldMenueTree.value = data
  } finally {
  }
}

//打开旧应用菜单弹窗
const openOldAppDialog = () => {
  oldAppListDialog.value = true
  getOldList()
}

interface formDataVO {
  menus: string
}
const { t } = useI18n() // 国际化

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData: Ref<formDataVO> = ref({
  menus: ''
})

const formRef = ref() // 表单 Ref
const menuOptions = ref<any[]>([]) // 树形结构数据

const menuExpand = ref(false) // 展开/折叠
const treeRef = ref<InstanceType<typeof ElTree>>() // 树组件 Ref
const treeNodeAll = ref(false) // 全选/全不选

const formRules = reactive({
  menus: [{ required: true, message: '菜单JSON不能为空', trigger: 'blur' }]
})

let allMenuList = ref() //新增菜单和旧菜单合并后的数据
let deleteMenuList = ref() //删除菜单
let newMenuList = ref() //新增菜单
//监听menus,处理树形结构数据
watch(
  [() => formData.value.menus, () => oldMenueTree.value],
  ([val, val2]) => {
    const loadingInstance = ElLoading.service({ fullscreen: true })

    try {
      console.log(isEmpty(val))
      if (isEmpty(val)) return
      let targetData = JSON.parse(val).map((item: MenuItemType) => {
        item.applicationId = applicationId.value
        //处理一下菜单名称的空格
        item.name = item.name.trim()
        item.componentName = item.componentName?.trim()
        return item
      })
      console.log(targetData)
      menuOptions.value = removeIdsAndParentIds(handleTree(cloneDeep(targetData)))

      allMenuList.value = handleOldNewMenu(cloneDeep(oldMenueTree.value), cloneDeep(targetData))
      console.log('新增菜单如下')
      newMenuList.value = allMenuList.value.filter((item) => item.ifNew)
      console.log(newMenuList.value)
      console.log('删除菜单如下')
      deleteMenuList.value = allMenuList.value.filter((item) => item.isDelete)
      console.log(deleteMenuList.value)
    } catch (error) {
      message.notifyError(error as any)
    } finally {
      loadingInstance.close()
    }
  },
  {
    deep: true
  }
)

//查找父菜单名称
const findParentMenuName = (menuItem: MenuItemType) => {
  let allMenu = allMenuList.value
  let parentMenu = ''
  let parentItem = allMenu.find((item) => item.id === menuItem.parentId)

  if (parentItem) {
    if (parentItem.parentId === 0) {
      // 如果是顶级菜单，直接返回其名称
      parentMenu = parentItem.name
    } else {
      // 否则，递归查找父菜单名称，并拼接
      parentMenu = findParentMenuName(parentItem)
      parentMenu += (parentMenu ? '-' : '') + parentItem.name
    }
  }

  return parentMenu
}

//删除id和parentId
const removeIdsAndParentIds = (tree) => {
  if (Array.isArray(tree)) {
    return tree.map((node) => removeIdsAndParentIds(node))
  }

  if (typeof tree === 'object' && tree !== null) {
    const { id, parentId, ...rest } = tree
    const newNode = {}

    for (const key in rest) {
      newNode[key] = removeIdsAndParentIds(rest[key])
    }

    return newNode
  }

  return tree
}
/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

import { batchCreateMenu } from '@/api/system/menu'

const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    await batchCreateMenu(menuOptions.value)
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  // 重置选项
  menuOptions.value = []
  treeNodeAll.value = false
  menuExpand.value = false
  oldAppId.value = null
  allMenuList.value = []
  deleteMenuList.value = []
  newMenuList.value = []
  // 重置表单
  formData.value = {
    menus: ''
  }
  treeRef.value?.setCheckedNodes([])
  formRef.value?.resetFields()
}

//关闭菜单

/** 全选/全不选 */
const handleCheckedTreeNodeAll = () => {
  treeRef?.value?.setCheckedNodes(treeNodeAll.value ? menuOptions.value : [])
}

/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}

// ###########################################################################
import { MenuItemType } from '@/api/system/menu'

const oldMenuArr: Ref<MenuItemType[]> = ref([])

const newMenuIdArr: Ref<MenuItemType[]> = ref([])

// 将旧菜单的id转为新菜单id并转为map
const oldToNewPrarentIdMap: Ref<any> = ref({})

/**
 * 处理旧菜单数据
 * 新菜单和旧菜单只有名字相同，新菜单里匹配不上旧菜单名字的，说明是新增的菜单（即使是修改的菜单名也表示为新增菜单）；
 * 旧菜单里匹配不上新菜单名字的，说明是删除的菜单（即使是修改的菜单也表示为删除菜单）
 */
const handleOldNewMenu = (oldMenuList: MenuItemType[], newMenuList: MenuItemType[]) => {
  let oldMenuParentIdObj = {}
  oldToNewPrarentIdMap.value = {}
  oldMenuArr.value = []

  oldMenuList.forEach((oldItem) => {
    let isDelete = true
    //旧菜单里匹配不上新菜单名字的，说明是删除的菜单
    newMenuList.forEach((newItem) => {
      //判断菜单名称  菜单的组件名称  ,如果之一不同,则是删除了菜单
      if (
        oldItem.name.trim() == newItem.name.trim() &&
        oldItem?.componentName?.trim() == newItem?.componentName?.trim()
      ) {
        isDelete = false
      }
    })
    if (isDelete) {
      oldMenuArr.value.push({
        ...oldItem,
        isDelete: true,
        disabled: true
      })

      if (oldMenuParentIdObj[oldItem.parentId]) {
        oldMenuParentIdObj[oldItem.parentId].push({
          ...oldItem,
          isDelete: true,
          disabled: true
        })
      }
    }
  })

  newMenuIdArr.value = []

  newMenuList = newMenuList.map((item) => {
    newMenuIdArr.value.push(item.id)
    item['ifNew'] = true
    item['menuId'] = item.id
    oldMenuList.some((oldItem) => {
      //判断菜单名称  菜单的组件名称  ,如果之一不同,则是新菜单
      if (
        item.name.trim() == oldItem.name.trim() &&
        item?.componentName?.trim() == oldItem?.componentName?.trim()
      ) {
        item['ifNew'] = false
        item['oldMenuId'] = oldItem.id
        // 构建新旧菜单id映射关系
        oldToNewPrarentIdMap.value[oldItem.id] = item.id
      }
    })
    return item
  })

  // 将旧菜单的parentId映射成新的parentId
  oldMenuArr.value.forEach((el) => {
    el.parentId = oldToNewPrarentIdMap.value[el.parentId]
      ? oldToNewPrarentIdMap.value[el.parentId]
      : el.parentId
  })

  return [...newMenuList, ...oldMenuArr.value]
}

// ###########################################################################
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
</style>
