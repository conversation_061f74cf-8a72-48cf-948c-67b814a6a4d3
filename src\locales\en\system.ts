/**系统管理 */
export default {
  /**用户管理 */
  user: {
    username: '<PERSON><PERSON><PERSON>',
    usernamePlaceholder: 'Please enter your username',
    nickname: 'Nickname',
    nicknamePlaceholder: 'Please enter your nickname',
    mobile: 'Mobile Number',
    mobilePlaceholder: 'Please enter your mobile number',
    status: 'Status',
    statusPlaceholder: 'User status',
    id: 'User ID',
    deptName: 'Department',

    deptNamePlaceholder: 'Please enter department name',
    desc1: 'Department not assigned',
    deptId: 'Affiliated Department',
    email: 'Email',
    password: 'User Password',
    emailPlaceholder: 'Please enter your email',
    passwordPlaceholder: 'Please enter your user password',
    remarkPlaceholder: 'Please enter content',
    sexPlaceholder: 'Please select',
    postIdsPlaceholder: 'Please select',
    deptIdPlaceholder: 'Please select affiliated department',
    roleIdsPlaceholder: 'Please select role',
    roleIds: 'Role',
    createTime: 'Creation Time',
    sex: 'User Gender',
    postIds: 'Position',
    remark: 'Remark',
    rDesc1: 'Username cannot be empty',
    rDesc2: 'Nickname cannot be empty',
    rDesc3: 'User password cannot be empty',
    rDesc4: 'Please enter a correct email address',
    rDesc5: 'Please enter a correct mobile number',
    rDesc7: 'Phone number cannot be empty',
    sSpRole: 'Assign Role',
    handleResetPwd: 'Reset Password',
    handleRole: 'Assign Role',
    roleDataXls: 'User Data',
    desc2: 'Modified successfully, the new password is:',
    userImport: 'User Import',
    desc3: 'Drag the file here, or click to upload',
    desc4: 'Whether to update existing user data',
    desc5: 'Only allow importing xls, xlsx format files.',
    desc6: 'Download Template',
    message1: 'Please upload a file',
    message2: 'Number of successful uploads:',
    message3: 'Number of successful updates:',
    message4: 'Number of failed updates:',
    message5: 'Upload failed, please upload again!',
    message6: 'You can only upload one file at most!',
    desc7: 'User import template',
    desc8: 'Confirm to {text}{username} user?',
    desc9: 'Please enter the new password for {username}'
  },

  role: {
    name: 'Role Name',
    code: 'Role Code',
    status: 'Status',
    applicationCode: 'Application',
    createTime: 'Creation Time',
    namePlaceholder: 'Please enter the role name',
    codePlaceholder: 'Please enter the role code',
    statusPlaceholder: 'Please select the status',
    applicationCodePlaceholder: 'Please select the application',
    typePlaceholder: 'Please select the role type',
    id: 'Role ID',
    type: 'Role Type',
    tags: 'Role Tags',
    sort: 'Display Order',
    remark: 'Remark',
    desc1:
      'Create a new role based on this role as a template to reuse the menu and permissions of this role',
    createRole: 'Create Role',
    menuPerm: 'Menu Permissions',
    dataPerm: 'Data Permissions',
    advanceDataPerm: 'Advanced Data Permissions',
    resCfg: 'Resource Configuration',
    tag: 'Tag',
    allSelectOrNot: 'Select All/None:',
    expandAllOrNot: 'Expand/Collapse All:',
    dataScope: 'Permission Scope',
    tenantname: 'Tenant Name',
    labelPlaceholder: 'Please enter the name',
    propNamePlaceholder: 'Please enter the field name',
    keyPlaceholder: 'Please enter the function point key value',
    desc2:
      'Parent-child linkage (selecting the parent node automatically selects the child nodes):',
    systemRole: 'System Role',
    customRole: 'Custom Role',
    ortherRole: 'Orther Role',
    newPerm: 'New Permission',
    loadText: 'Loading, please wait',
    desc3: 'Click to view permissions',
    desc4: 'Click to modify permissions',
    desc5: 'Select Permissions',
    desc6: 'Modify Permissions',
    desc7: 'Modify Business',
    desc8: 'Add Business',
    label: 'Name',
    propName: 'Field Name',
    authorityPoints: 'Function Points',
    rDesc1: 'Permission scope cannot be empty',
    msg1: 'Cannot operate on roles of type system built-in',
    desc9: 'Global',
    read: 'Read',
    write: 'Write',

    batchDelete: 'Batch Delete',
    save: 'Save',
    addRole: 'Assign Role',
    cancelAssign: 'Cancel Assignment',
    confirmCancelAssign: 'Are you sure to cancel the assignment of the selected roles?',

    newScene: 'Add New Scene',
    resourceOwnerName: 'Name',
    editPermi: 'Edit Permissions',
    viewPermi: 'View Permissions',
    scene: 'Scene',
    scenePlaceholder: 'Please select a scene',
    rDesc2: 'Please select role tags',
    roleList: 'Role List',
    desc10:
      '{tenantAppName} application has been upgraded, the {name} role has expired and needs to be recreated',
    autoAuthRule: 'Authorization rule',

    roleForm: {
      name: 'Role Name',
      code: 'Role Code',
      sort: 'Display Order',
      applicationCode: 'Service Subscription',
      status: 'Status',
      remark: 'Remarks',
      clientName: 'Client Name',
      namePlaceholder: 'Please enter the role name',
      codePlaceholder: 'Please enter the role code',
      clientNamePlaceholder: 'Please enter the client name',
      sortPlaceholder: 'Please enter the display order',
      remarkPlaceholder: 'Please enter remarks',
      applicationCodePlaceholder: 'Please select the service',
      statusPlaceholder: 'Please select the status',
      rDesc1: 'The role name cannot be empty',
      rDesc2: 'The service associated cannot be empty',
      rDesc3: 'The role code cannot be empty',
      rDesc4: 'The display order cannot be empty',
      rDesc5: 'The status cannot be empty',
      rDesc6: 'The remarks cannot be empty'
    }
  },

  dept: {
    title: 'Department Name',
    status: 'Status',
    namePlaceholder: 'Please enter the department name',
    statusPlaceholder: 'Please select the department status',
    name: 'Department Name',
    code: 'Code',
    leader: 'Leader',
    sort: 'Sorting',
    createTime: 'Creation Time',
    parentId: 'Parent Department',
    leaderUserId: 'Leader',
    phone: 'Phone Number',
    email: 'Email',
    codePlaceholder: 'Please enter the code',
    phonePlaceholder: 'Please enter the phone number',
    emailPlaceholder: 'Please enter the email',
    leaderUserIdPlaceholder: 'Please enter the leader',
    parentIdPlaceholder: 'Please select the parent department',
    rDesc1: 'The parent department cannot be empty',
    rDesc2: 'The department name cannot be empty',
    rDesc3: 'The display sorting cannot be empty',
    rDesc4: 'Please enter a correct email address',
    rDesc5: 'Please enter a correct phone number',
    rDesc6: 'The status cannot be empty',
    rDesc7: 'Phone number cannot be empty',
    rDesc8:
      'If there are associated users under this department, please move the associated users to another department!',
    topDivision: 'Top Department'
  },

  post: {
    name: 'Position Name',
    code: 'Position Code',
    status: 'Status',
    namePlaceholder: 'Please enter the position name',
    codePlaceholder: 'Please enter the position code',
    statusPlaceholder: 'Please select the status',
    id: 'Position ID',
    sort: 'Position Order',
    remark: 'Position Remark',
    createTime: 'Creation Time',
    postListXls: 'Position List.xls',
    sortPlaceholder: 'Please enter the position order',
    remarkPlaceholder: 'Please enter a remark',
    rDesc1: 'The position name cannot be empty',
    rDesc2: 'The position code cannot be empty',
    rDesc3: 'The position status cannot be empty',
    rDesc4: 'The position content cannot be empty'
  },

  subscribe: {
    name: 'Interaction End Name',
    ver: 'Version Number',
    subscribe: 'Subscribe',
    changeRelation: 'Switch Correspondence',
    checkoutValue0: 'Interaction End',
    checkoutValue1: 'Service',
    upgradable: 'Upgradable',
    bindVisible: 'Bind',
    bindData1: 'Client',
    bindData2: 'Management End',
    bindData3: 'Comprehensive Service End',
    bindData4: 'Sales End',
    menuPerm: 'Menu Permissions',
    subListContainer1: 'Unsubscribed Applications',
    subListContainer2: 'Subscribed Applications',
    upgradeApp: 'Application Upgrade',
    desc1: 'Inherit roles and menus',
    role: 'Role',
    addMenu: 'Add Menu',
    desc2:
      'After role migration, all current application information cannot be edited again, and all roles will be inherited by the new version',
    desc3: 'Confirm upgrade? After confirmation, you will automatically exit the system',
    desc4: 'Upgrade Failed',
    desc5: 'Cancel Upgrade',
    menuExpand: 'Expand All/Collapse',
    treeNodeAll2: 'Select All/Deselect All',
    serverCfg: 'Service configuration',
    desc6: 'Service Configuration menu and sort',
    id: 'the service id for the current tenant',
    applicationName: 'Application name',
    applicationCode: 'Application encoding',
    applicationVer: 'Application version',
    applicationType: 'Application type',
    type1: 'access app',
    type2: 'tenant app',
    type3: 'built-in app',
    applicationStatus: 'State',
    applicationDescription: 'Application description',
    applicationUrl: 'Application address',
    applicationOauthClient: 'OAuth client',
    sort: 'sort',
    isHomePage: 'Is there a home page',
    homeSetting: 'Home page setting',
    serviceSort: 'Service sort',
    Home: 'home page',
    setHome: 'Set as the home page',
    desc7:
      'The current side has set up the other home page, are you sure to set the current menu as the new home page?',
    desc8: 'Please enter a sort number',
    msgTitle: 'Promotion',
    sortCode: 'sort number',
    cancelSetting: 'Unset',
    menuDetail: 'menu details'
  }
}
