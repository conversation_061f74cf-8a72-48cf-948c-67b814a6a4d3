<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJack
 * @LastEditTime: 2023-10-19 20:10:13
 * @Description:  由于公司存在多个商户,将应用类比为商户(应用编码即等于商户号)
-->
<template>
  <!-- 列表 -->
  <UmvContent>
    <!-- 搜索 -->
    <template #search>
      <UmvQuery
        ref="umvQueryRef"
        v-model="queryParams"
        :opts="queryOpts"
        @check="handleQuery"
        @reset="resetQuery"
      />
    </template>

    <UmvTable v-loading="loading" :data="list" @refresh="getList" row-key="id">
      <template #tools>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['pay:app:create']">
          <icon-ep-plus style="font-size: 12px" class="mr-5px" /> {{ t('common.newAdd') }}
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['pay:app:export']"
        >
          <icon-ep-download style="font-size: 12px" class="mr-5px" /> {{ t('common.export') }}
        </el-button>
      </template>

      <!-- 使用默认插槽实现表格列 -->
      <el-table-column type="expand" fixed="left">
        <template #default="props">
          <div class="p-24px">
            <el-table :data="props.row.channels || []" border>
              <el-table-column label="渠道编码" align="center" prop="channelCode" />
              <el-table-column label="渠道名称" align="center" prop="channelName" />
              <el-table-column label="appId" align="center" prop="channelAppId" />
              <el-table-column label="商户号" align="center" prop="channelMchId" />
              <el-table-column label="渠道状态" align="center" prop="status">
                <template #default="scope">
                  <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" prop="oprate">
                <template #default="scope">
                  <el-button link type="primary" @click="editChannel(scope.row, props.row)">
                    {{ t('common.edit') }}
                  </el-button>
                  <el-button link type="danger" @click="delChannel(scope.row)">
                    {{ t('common.delete') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('pay.app.id')" align="center" prop="id" />
      <el-table-column :label="t('pay.app.type')" align="center" prop="type" width="120">
        <template #default="scope">
          {{ MchType[scope.row.type]?.name }}
        </template>
      </el-table-column>
      <el-table-column :label="t('pay.app.code')" align="center" prop="code" width="180" />
      <el-table-column :label="t('pay.app.name')" align="center" prop="name" width="180" />
      <el-table-column :label="t('pay.app.status')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('pay.app.payMerchantName')"
        align="center"
        prop="payMerchant.name"
        width="150"
      />
      <el-table-column
        :label="t('pay.app.createTime')"
        align="center"
        prop="createTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column :label="t('common.operate')" align="center" min-width="180" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="addChannel(scope.row)">
            {{ t('pay.app.addChannel') }}
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['pay:app:update']"
          >
            {{ t('common.edit') }}
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['pay:app:delete']"
          >
            {{ t('common.delete') }}
          </el-button>
        </template>
      </el-table-column>

      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单弹窗：添加/修改 -->
    <addChannelForm ref="addChannelFormRef" @success="getList" />
    <AppForm ref="formRef" @success="getList" />
    <AlipayChannelForm ref="alipayChannelFormRef" @success="getList" />
    <WeixinChannelForm ref="weixinChannelFormRef" @success="getList" />
    <hsChannelForm ref="hsChannelFormRef" @success="getList" />
  </UmvContent>
</template>
<script lang="tsx" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as AppApi from '@/api/pay/app'
import AppForm from '@/views/pay/app/AppForm.vue'
import { PayChannelEnum, PayType, MchType } from '@/utils/constants'
import addChannelForm from './components/addChannelForm.vue'
import * as ChannelApi from '@/api/pay/channel'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { UmvContent } from '@/components/UmvContent'
import { ElMessage, ElMessageBox } from 'element-plus'
import { checkPermi } from '@/utils/permission'

defineOptions({ name: 'PayApp' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const umvQueryRef = ref()
const queryParams = ref<any>({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  status: undefined,
  remark: undefined,
  payNotifyUrl: undefined,
  refundNotifyUrl: undefined,
  merchantName: undefined,
  createTime: []
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: t('pay.app.name'),
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.name}
        placeholder={t('pay.app.namePlaceholder')}
        clearable
      />
    )
  },
  contactName: {
    label: t('pay.app.contactName'),
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.contactName}
        placeholder={t('pay.app.contactNamePlaceholder')}
        clearable
      />
    )
  },
  status: {
    label: t('pay.app.status'),
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.status}
        placeholder={t('pay.app.statusPlaceholder')}
        clearable
      >
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((dict) => (
          <el-option key={dict.value} label={dict.label} value={dict.value} />
        ))}
      </el-select>
    )
  },
  createTime: {
    label: t('pay.app.createTime'),
    defaultVal: [],
    controlRender: () => (
      <el-date-picker
        v-model={queryParams.value.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        start-placeholder={t('common.startDateText')}
        end-placeholder={t('common.endDateText')}
      />
    )
  }
})

const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AppApi.getAppPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 初始化 **/
onMounted(async () => {
  await getList()
})
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  console.log('------')
}

/** 添加/修改操作 */
const formRef = ref()
/**
 * @description:
 * @param {*} type  操作类型  create/update
 * @param {*} id
 * @return {*}
 */
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AppApi.deleteApp(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AppApi.exportApp(queryParams.value)
    download.excel(data.data, t('pay.app.payAppXls') + '.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/**
 * 根据渠道编码判断渠道列表中是否存在
 *
 * @param channels 渠道列表
 * @param channelCode 渠道编码
 */
const isChannelExists = (channels, channelCode) => {
  if (!channels) {
    return false
  }
  return channels.indexOf(channelCode) !== -1
}

// 微信组件传参参数
/**
 * 修改支付渠道信息
 *
 * @param row 行记录
 * @param payCode 支付编码
 * @param type 支付类型
 */

/**
 * 新增支付渠道信息
 */
import AlipayChannelForm from './components/alipayChannelForm.vue'
let alipayChannelFormRef = ref()
import WeixinChannelForm from './components/weixinChannelForm.vue'
const hsChannelFormRef = ref()
import hsChannelForm from './components/hsChannelForm.vue'
let weixinChannelFormRef = ref()

const openChannelForm = async (row, payCode, type) => {
  console.log('type', type)
  if (type === PayType.WECHAT) {
    weixinChannelFormRef.value.open(row, payCode)
  }
  if (type === PayType.ALIPAY) {
    alipayChannelFormRef.value.open(row, payCode)
  }
  //打开徽商渠道弹出框
  if (type === PayType.HSBANK) {
    hsChannelFormRef.value.open(row, payCode)
  }
}

// 编辑渠道
const editChannel = (row, parentRow) => {
  openChannelForm(parentRow, row.channelCode, PayType[MchType[parentRow.type].payType])
}

// 新增渠道
const addChannel = (row) => {
  console.log('row.type', row.type)
  openChannelForm(row, undefined, PayType[MchType[row.type].payType])
}

// 删除渠道
const delChannel = (row) => {
  ElMessageBox.confirm('确认删除该渠道吗？', '提示', {
    confirmButtonText: '确 认',
    cancelButtonText: '取 消'
  })
    .then(async () => {
      await ChannelApi.deleteChannel(row.channelId)
      ElMessage.success('操作成功')
      getList()
    })
    .catch(() => console.info('操作取消'))
}

const getPayChannelBtnType = (row, payCode) => {
  return row.channels.find((item) => item.channelCode === payCode).status ? 'warning' : 'success'
}
</script>
