/**
 * 深拷贝
 * 传入一个对象
 *
 * @param   Object
 * @return  new Object
 */
export const deepClone = (obj, hash = new WeakMap()) => {
  if (obj === null) return obj
  if (obj instanceof Date) return new Date(obj)
  if (obj instanceof RegExp) return new RegExp(obj)
  if (typeof obj !== 'object') return obj
  if (hash.get(obj)) return hash.get(obj)
  const cloneObj: any = new obj.constructor()
  hash.set(obj, cloneObj)
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloneObj[key] = deepClone(obj[key], hash)
    }
  }
  return cloneObj
}
