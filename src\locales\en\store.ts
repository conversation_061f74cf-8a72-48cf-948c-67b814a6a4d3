export default {
  app: {
    unMobile: 'Mobile mode does NOT support other layout'
  },

  dict: {
    getDictFail: 'Retrieve dictionary failed'
  },

  store: {
    getCodeFail: 'Retrieve authorization code failed'
  },

  user: {
    soonLogout: 'Logout soon',
    logout: 'Logout',
    customerInfoRelogin: 'Retrieve customer information failed, please login again!',
    accountRelogin: 'Retrieve account information failed, please login again!',
    getUserInfoFail: 'Retrieve user information failed',
    noRole: 'No role setting for this user, please contact system administrator!',
    noClient:
      'The user does not have permission for the current interaction endpoint, please contact the administrator!',
    noClientLoginOut:
      'The user does not have permission for the current interaction endpoint, would you like to return to the login page?'
  }
}
