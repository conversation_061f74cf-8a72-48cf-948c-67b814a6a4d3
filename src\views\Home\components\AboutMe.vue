<template>
  <div class="about-us">
    <div class="banner-warp w-full overflow-hidden text-[#ffffff] px-[10%]">
      <div class="mt-120px text-5xl text-center">
        {{ t('home.AboutMe.title1') }}
      </div>
      <div class="my-25" :style="{ fontSize: '16px' }">
        <p class="mb-28px leading-7 indent-xl">
          {{ t('home.AboutMe.title1_item1') }}
        </p>
        <p class="leading-7 indent-xl">
          {{ t('home.AboutMe.title1_item2') }}
        </p>
      </div>
    </div>
    <el-image
      v-if="ifEn"
      class="hover-scale w-full"
      :src="`${basePath}/Home/AboutUs/en_US/umv_card_nums.webp`"
    />
    <el-image
      v-else
      class="hover-scale w-full"
      :src="`${basePath}/Home/AboutUs/umv_card_nums.webp`"
    />

    <div class="w-full bg-[#FAF9F7] pb-30 mt-[-10px]">
      <p class="pt-30 text-[40px] text-center">
        {{ t('home.AboutMe.title2') }}
      </p>
      <div class="flex max-w-375 mx-auto mt-32 justify-between flex-wrap rounded-2.5 px-7.5">
        <div
          class="w-[22.4%] bg-[#FFFFFF] text-center mb-7.5 mr-3 ml-3 shadow-sm hover-scale"
          v-for="(item, index) in proList"
          :key="index"
        >
          <div class="mt-38px mb-22px text-8 font-medium">{{ item.year }}</div>
          <div class="text-xs font-normal mb-11.75 text-[#4b5563] px-2">
            {{ item.text }}
          </div>
        </div>
      </div>
    </div>
    <el-image
      v-if="ifEn"
      lazy
      class="w-full"
      :src="`${basePath}/Home/AboutUs/en_US/umv_card_resource.webp`"
    />
    <el-image v-else lazy class="w-full" :src="`${basePath}/Home/AboutUs/umv_card_resource.webp`" />

    <div class="customer-warp w-full p-30 <xl:px-15 flex flex-col justify-center items-center">
      <p class="text-center text-5xl text-[#ffffff]">
        {{ t('home.AboutMe.title3') }}
      </p>
      <div class="flex mt-15 w-full max-w-[1460px]">
        <div
          class="w-full even:mx-10 flex justify-center items-center flex-col rounded-xl bg-[#ffffff] h-38 text-center hover-scale"
          v-for="(item, index) in cusList"
          :key="index"
        >
          <p class="text-3xl font-bold">{{ item.num }}</p>
          <p class="text-sm font-medium mt-2">
            {{ item.text }}
          </p>
        </div>
      </div>
      <div class="flex max-w-[1460px] justify-center mt-15">
        <el-tabs tab-position="right" class="cus-tabs flex pr-25" v-model="activeTab">
          <el-tab-pane :label="t('home.AboutMe.title3_item4')" name="finance" />
          <el-tab-pane :label="t('home.AboutMe.title3_item5')" name="gover" />
          <el-tab-pane :label="t('home.AboutMe.title3_item6')" name="people" />
        </el-tabs>
        <el-image lazy :src="activeImg" class="w-full" />
      </div>
    </div>
    <el-image
      lazy
      class="max-w-[1920px] w-full hover-scale"
      :src="`${basePath}/Home/AboutUs/umv_card_footer.webp`"
    />
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'AboutMe'
})

const { t, ifEn } = useI18n()
const basePath = import.meta.env.BASE_URL === '/' ? '' : import.meta.env.BASE_URL

import finance from './img/umv_card_finance.png'
import gover from './img/umv_card_gover.png'
import people from './img/umv_card_people.png'

const activeTab = ref('finance')

const imgMap = ref({
  finance,
  gover,
  people
})

const activeImg = computed(() => {
  return imgMap.value[activeTab.value]
})

const cusList = ref([
  {
    num: '1000+',
    text: t('home.AboutMe.title3_item1')
  },
  {
    num: '300+',
    text: t('home.AboutMe.title3_item2')
  },
  {
    num: '100+',
    text: t('home.AboutMe.title3_item3')
  }
])
const proList = ref([
  {
    year: 1995,
    text: t('home.AboutMe.title2_item1')
  },
  {
    year: 1996,
    text: t('home.AboutMe.title2_item2')
  },
  {
    year: 2001,
    text: t('home.AboutMe.title2_item3')
  },
  {
    year: 2005,
    text: t('home.AboutMe.title2_item4')
  },
  {
    year: 2012,
    text: t('home.AboutMe.title2_item5')
  },
  {
    year: 2018,
    text: t('home.AboutMe.title2_item6')
  },
  {
    year: 2021,
    text: t('home.AboutMe.title2_item7')
  },
  {
    year: 2023,
    text: t('home.AboutMe.title2_item8')
  }
])
</script>
<style lang="scss" scoped>
.cus-tabs {
  height: 240px;
  width: 176px;
  @media (max-width: 1205.9px) {
    width: 126px;
  }
  :deep(.el-tabs__active-bar) {
    background-color: #ffffff;
  }
  :deep(.el-tabs__header) {
    margin-left: 0px;
  }
  :deep(.el-tabs__item) {
    color: #ffffff;
    padding: 40px 110px 40px 30px;
    @media (max-width: 1205.9px) {
      padding: 40px 60px 40px 30px;
    }
    font-size: 18px;
  }
  :deep(.is-active) {
    background: rgba(255, 255, 255, 0.2);
  }
  :deep(.el-tabs__nav-wrap.is-right::after) {
    background: rgba(255, 255, 255, 0.1);
    width: 2px;
  }
}
.banner-warp {
  background: url('/Home/AboutUs/umv_card_banner3.webp') center/100% 100% no-repeat;
}
.customer-warp {
  background: url('/Home/AboutUs/umv_card_customer_bg.webp') center/100% 100% no-repeat;
}

//鼠标悬浮变大
.hover-scale {
  transition: all 0.3s;
  &:hover {
    transform: scale(1.1);
  }
}
</style>
