/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 13:59:22
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-06-30 14:35:31
 * @Description:
 */
import { service } from './service'

import { config } from './config'

const { default_headers } = config

import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()

const defaultLang = 'zh-CN'

const langMap = {
  'zh-CN': 'zh-cn',
  'zh-TW': 'zh-hk',
  en: 'en-us'
}
/**
 * @description: 请求参数类型
 */
type requestOptionType = {
  url: string
  method?: string
  params?: any
  data?: any
  headersType?: any
  responseType?: any
  onUploadProgress?: any
  headers?: any
  /**超时时间 */
  timeout?: number
  /**忽略错误,用于非标准code.由业务代码进行处理 */
  ignoreErrors?: boolean
}
const request = (option: requestOptionType) => {
  const { url, method, params, data, headersType, responseType } = option
  return service({
    ...option,
    url: url,
    method,
    params,
    data,
    responseType: responseType,
    headers: {
      'accept-language': langMap[wsCache.get(CACHE_KEY.LANG) || defaultLang],
      'Content-Type': headersType || default_headers
    }
  })
}

export const requestWithToken = (option: requestOptionType) => {
  const { url, method, params, data, headersType, responseType, token } = option
  return service({
    ...option,
    url: url,
    method,
    params,
    data,
    responseType: responseType,
    headers: {
      'Content-Type': headersType || default_headers,
      Authorization: 'Bearer ' + token,
      'accept-language': langMap[wsCache.get(CACHE_KEY.LANG) || defaultLang]
    }
  })
}

export const requestWithTokenAndTenantId = (option: requestOptionType) => {
  const { url, method, params, data, headersType, responseType, token, tenantId } = option
  return service({
    ...option,
    url: url,
    method,
    params,
    data,
    responseType: responseType,
    headers: {
      'Content-Type': headersType || default_headers,
      Authorization: 'Bearer ' + token,
      'tenant-id': tenantId,
      'accept-language': langMap[wsCache.get(CACHE_KEY.LANG) || defaultLang]
    }
  })
}

export default {
  get: async <T = any>(option: requestOptionType) => {
    const res = await request({ method: 'GET', ...option })
    return res.data as unknown as T
  },
  getOriginal: async <T = any>(option: requestOptionType) => {
    const res = await request({ method: 'GET', ...option })
    return res as unknown as T
  },
  post: async <T = any>(option: requestOptionType) => {
    const res = await request({ method: 'POST', ...option })
    return res.data as unknown as T
  },
  postOriginal: async <T = any>(option: requestOptionType) => {
    const res = await request({ method: 'POST', ...option })
    return res as unknown as T
  },
  putOriginal: async <T = any>(option: requestOptionType): Promise<T> => {
    const res = await request({ method: 'PUT', ...option })
    return res as unknown as T
  },
  putWithTokenOriginal: async <T = any>(option: requestOptionType): Promise<T> => {
    const res = await requestWithToken({ method: 'PUT', ...option })
    return res as unknown as T
  },
  delete: async <T = any>(option: requestOptionType) => {
    const res = await request({ method: 'DELETE', ...option })
    return res.data as unknown as T
  },
  put: async <T = any>(option: requestOptionType) => {
    const res = await request({ method: 'PUT', ...option })
    return res.data as unknown as T
  },
  download: async <T = any>(option: requestOptionType) => {
    const res = await request({ method: 'GET', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  postDownload: async <T = any>(option: requestOptionType) => {
    const res = await request({ method: 'POST', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  upload: async <T = any>(option: requestOptionType) => {
    option.headersType = 'multipart/form-data'
    const res = await request({ method: 'POST', ...option })
    return res as unknown as Promise<T>
  },
  putUpload: async <T = any>(option: requestOptionType) => {
    option.headersType = 'multipart/form-data'
    const res = await request({ method: 'PUT', ...option })
    return res as unknown as Promise<T>
  }
}
