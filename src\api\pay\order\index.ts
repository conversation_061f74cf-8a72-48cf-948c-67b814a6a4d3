import request from '@/config/axios'

export interface OrderVO {
  id: number
  merchantId: number
  appId: number
  channelId: number
  channelCode: string
  merchantOrderId: string
  subject: string
  body: string
  notifyUrl: string
  notifyStatus: number
  amount: number
  channelFeeRate: number
  channelFeeAmount: number
  status: number
  userIp: string
  expireTime: Date
  successTime: Date
  notifyTime: Date
  successExtensionId: number
  refundStatus: number
  refundTimes: number
  refundAmount: number
  channelUserId: string
  channelOrderNo: string
  createTime: Date
}

export interface OrderPageReqVO extends PageParam {
  merchantId?: number
  appId?: number
  channelId?: number
  channelCode?: string
  merchantOrderId?: string
  subject?: string
  body?: string
  notifyUrl?: string
  notifyStatus?: number
  amount?: number
  channelFeeRate?: number
  channelFeeAmount?: number
  status?: number
  expireTime?: Date[]
  successTime?: Date[]
  notifyTime?: Date[]
  successExtensionId?: number
  refundStatus?: number
  refundTimes?: number
  channelUserId?: string
  channelOrderNo?: string
  createTime?: Date[]
}

export interface OrderExportReqVO {
  merchantId?: number
  appId?: number
  channelId?: number
  channelCode?: string
  merchantOrderId?: string
  subject?: string
  body?: string
  notifyUrl?: string
  notifyStatus?: number
  amount?: number
  channelFeeRate?: number
  channelFeeAmount?: number
  status?: number
  expireTime?: Date[]
  successTime?: Date[]
  notifyTime?: Date[]
  successExtensionId?: number
  refundStatus?: number
  refundTimes?: number
  channelUserId?: string
  channelOrderNo?: string
  createTime?: Date[]
}

// 查询列表支付订单
export const getOrderPage = async (params: OrderPageReqVO) => {
  return await request.get({ url: '/pay/order/page', params })
}

// 查询详情支付订单
export const getOrder = async (id: number) => {
  return await request.get({ url: '/pay/order/get?id=' + id })
}

// 获得支付订单的明细
export const getOrderDetail = async (id: number) => {
  return await request.get({ url: '/pay/order/get-detail?id=' + id })
}

// 新增支付订单
export const createOrder = async (data: OrderVO) => {
  return await request.post({ url: '/pay/order/create', data })
}

// 修改支付订单
export const updateOrder = async (data: OrderVO) => {
  return await request.put({ url: '/pay/order/update', data })
}

// 删除支付订单
export const deleteOrder = async (id: number) => {
  return await request.delete({ url: '/pay/order/delete?id=' + id })
}

// 导出支付订单
export const exportOrder = async (params: OrderExportReqVO) => {
  return await request.download({ url: '/pay/order/export-excel', params })
}
// 提交支付订单
/**调用微信/支付宝接口参数 */
export interface submitOrderType {
  /**商户号(应用编码) */
  appCode: string | number
  /**支付单编号 */
  id: string | number
  /**支付渠道 */
  channelCode: string
  /**回跳地址 */
  returnUrl: string
  /**支付渠道的额外参数，例如说，微信公众号需要传递 openid 参数 */
  channelExtras?: object
  /**展示模式 */
  displayMode?: string
}

/**调用微信/支付宝接口 返回参数 */
export interface submitOrderResType {
  /**支付状态 */
  status: string
  /**展示内容:url /二维码/ 条形码 */
  displayContent: string

  /**展示模式 */
  displayMode: string
}
export const submitOrder = async (data: submitOrderType) => {
  return (await request.post({
    url: '/pay/order/submit',
    data: data
  })) as submitOrderResType
}
