<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-12-19 09:33:43
 * @Description: 
-->
<template>
  <div class="change-avatar">
    <CropperAvatar
      ref="cropperRef"
      :btnProps="{ preIcon: 'ant-design:cloud-upload-outlined' }"
      :showBtn="false"
      :value="avatar"
      width="120px"
      @change="handelUpload"
    />
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'UserAvatar'
})

import CropperAvatar from './Cropper/src/CropperAvatar.vue'
import { propTypes } from '@/utils/propTypes'
import { uploadAvatar } from '@/api/system/user/profile'

const props = defineProps({
  img: propTypes.string.def('')
})
const avatar = computed(() => {
  return props.img
})

const cropperRef = ref()
const handelUpload = async ({ data }) => {
  await uploadAvatar({ avatarFile: data })
  cropperRef.value.close()
}
</script>

<style lang="scss" scoped>
.change-avatar {
  img {
    display: block;
    margin-bottom: 15px;
    border-radius: 50%;
  }
}
</style>
