// 更新Element Plus导入路径，使用类型声明
import type { TableInstance } from 'element-plus'

export type typeDemo = {
  prop: string
  label?: string
}

// 直接定义TableColumn类型，避免从外部导入
export interface TableColumn {
  prop: string
  label?: string
  children?: TableColumn[]
  width?: string
  minWidth?: string
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  showOverflowTooltip?: boolean
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  slot?: string
  headerSlot?: string
  renderTemplate?: (scope: { row: any; column: any; $index: number }) => any
  [key: string]: any
}

// 定义Recordable类型，而不是从全局导入
export interface Recordable {
  [key: string]: any
}

export type TableProps = {
  columns?: TableColumn[]
  data?: Recordable[]
  border?: TableInstance['$props']['border']
  stripe?: TableInstance['$props']['stripe']
  size?: TableInstance['$props']['size']
  tableLayout?: TableInstance['$props']['tableLayout']
  loading?: TableInstance['$props']['loading']
  rowKey?: TableInstance['$props']['rowKey']
  showHeader?: TableInstance['$props']['showHeader']
  highlightCurrentRow?: TableInstance['$props']['highlightCurrentRow']
  rowClass?: TableInstance['$props']['rowClass']
  rowStyle?: TableInstance['$props']['rowStyle']
  cellClass?: TableInstance['$props']['cellClass']
  cellStyle?: TableInstance['$props']['cellStyle']
  headerRowClass?: TableInstance['$props']['headerRowClass']
  headerRowStyle?: TableInstance['$props']['headerRowStyle']
  headerCellClass?: TableInstance['$props']['headerCellClass']
  headerCellStyle?: TableInstance['$props']['headerCellStyle']
  emptyText?: TableInstance['$props']['emptyText']
  defaultExpandAll?: TableInstance['$props']['defaultExpandAll']
  expandRowKeys?: TableInstance['$props']['expandRowKeys']
  defaultSort?: TableInstance['$props']['defaultSort']
  tooltipEffect?: TableInstance['$props']['tooltipEffect']
  showSummary?: TableInstance['$props']['showSummary']
  sumText?: TableInstance['$props']['sumText']
  summaryMethod?: TableInstance['$props']['summaryMethod']
  spanMethod?: TableInstance['$props']['spanMethod']
  selectOnIndeterminate?: TableInstance['$props']['selectOnIndeterminate']
  indent?: TableInstance['$props']['indent']
  treeProps?: TableInstance['$props']['treeProps']
  lazy?: TableInstance['$props']['lazy']
  load?: TableInstance['$props']['load']
  scrollbarAlwaysOn?: TableInstance['$props']['scrollbarAlwaysOn']
  flexible?: TableInstance['$props']['flexible']
} & Recordable
