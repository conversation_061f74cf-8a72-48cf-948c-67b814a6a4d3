<template>
  <div class="app-container">
    <!-- 搜索 -->
    <ContentWrap>
      <!-- 操作工具栏 -->
      <el-row :gutter="10" justify="space-between">
        <el-col :span="1.5">
          <el-button type="primary" plain :icon="Plus" @click="handleAdd"> 发起订单 </el-button>
        </el-col>
        <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
        <el-col :span="3.5">
          <el-button @click="handleQuery"
            ><icon-ep-search style="font-size: 12px" class="mr-5px" />搜索</el-button
          >
          <el-button @click="resetQuery"
            ><icon-ep-refresh style="font-size: 12px" class="mr-5px" />重置</el-button
          >
        </el-col>
      </el-row>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list" width="100%">
        <el-table-column label="订单编号" align="center" prop="id" />
        <el-table-column label="用户编号" align="center" prop="userId" />
        <el-table-column label="商品名字" align="center" prop="spuName" />
        <el-table-column label="支付价格" align="center" prop="price">
          <template #default="scope">
            <span>￥{{ (scope.row.price / 100.0).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="退款金额" align="center" prop="refundPrice">
          <template #default="scope">
            <span>￥{{ (scope.row.refundPrice / 100.0).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ utils.formatTime(scope.row.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付单号" align="center" prop="payOrderId" />
        <el-table-column label="是否支付" align="center" prop="payed">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.payed" />
          </template>
        </el-table-column>
        <el-table-column
          label="支付时间"
          show-overflow-tooltip
          align="center"
          prop="payTime"
          :formatter="dateFormatter"
        />
        <el-table-column
          label="退款时间"
          show-overflow-tooltip
          align="center"
          prop="refundTime"
          :formatter="dateFormatter"
        />
        <el-table-column label="操作" align="center" class-name="small-padding " width="200" fixed>
          <template #default="scope">
            <el-button link type="primary" @click="handlePay(scope.row)" v-if="!scope.row.payed">
              <Icon icon="ep:edit" class="mr-5px" />
              前往支付
            </el-button>
            <el-button
              link
              type="primary"
              @click="handleRefund(scope.row)"
              v-if="scope.row.payed && !scope.row.payRefundId"
            >
              <Icon icon="ep:delete" class="mr-5px" />
              发起退款
            </el-button>
            <el-button
              link
              type="primary"
              @click="
                () => {
                  router.push({
                    name: 'PayCheckOut',
                    query: {
                      payOrderId: scope.row.payOrderId
                    }
                  })
                }
              "
              v-if="!scope.row.payed"
            >
              收银台支付
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="应用名称" prop="appId">
          <el-select
            v-model="form.appId"
            placeholder="请选择应用名称"
            clearable
            size="small"
            style="width: 380px"
          >
            <el-option v-for="item in appList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="商品" prop="spuId">
          <el-select
            v-model="form.spuId"
            placeholder="请输入下单商品"
            clearable
            size="small"
            style="width: 380px"
          >
            <el-option v-for="item in spus" :key="item.id" :label="item.name" :value="item.id">
              <span style="float: left">{{ item.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px"
                >￥{{ (item.price / 100.0).toFixed(2) }}</span
              >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="loadingBtn">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'PayDemoOrder'
})

import { dateFormatter } from '@/utils/formatTime'
import * as utils from '@/utils/index'
import { Plus } from '@element-plus/icons-vue'
import { createDemoOrder, getDemoOrderPage, refundDemoOrder } from '@/api/pay/demo'
import { DICT_TYPE } from '@/utils/dict'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
// 遮罩层
let loading = ref(false)
// 总条数

let total = ref(0)

// 示例订单列表
let list = ref([])

// 弹出层标题
let title = ref('')

// 是否显示弹出层
let open = ref(false)

// 查询参数
let queryParams = ref({
  pageNo: 1,
  pageSize: 10
})

// 表单参数
let form = ref<any>({})
// 表单校验
let rules = reactive({
  spuId: [{ required: true, message: '商品编号不能为空', trigger: 'blur' }]
})
// 商品数组
let spus = ref([
  {
    id: 1,
    name: '华为手机',
    price: 1
  },
  {
    id: 2,
    name: '小米电视',
    price: 10
  },
  {
    id: 3,
    name: '苹果手表',
    price: 100
  },
  {
    id: 4,
    name: '华硕笔记本',
    price: 1000
  },
  {
    id: 5,
    name: '蔚来汽车',
    price: 200000
  }
])
/** 查询列表 */
const getList = async () => {
  loading.value = true
  // 执行查询
  try {
    let res = await getDemoOrderPage(queryParams.value)
    list.value = res.list
    total.value = res.total
  } catch (error) {
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getList()
})

//获取应用信息
// import * as AppApi from '@/api/pay/app'
// let appList = ref<any>([])
// const getAppList = async () => {
//   let res = await AppApi.getAppPage({ pageNo: 1, pageSize: 100 })
//   appList.value = res.list
// }
// onMounted(() => {
//   getAppList()
// })

/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}
/** 表单重置 */
let formRef = ref()
const reset = () => {
  form.value = {
    spuId: undefined
  }
  formRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  // resetForm('queryForm')
  handleQuery()
}
/** 新增按钮操作 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = '发起订单'
}

/** 提交按钮 */
let loadingBtn = ref<boolean>(false)
const submitForm = async () => {
  loadingBtn.value = true
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value?.validate()
  if (!valid) return
  try {
    // 添加的提交
    let res = await createDemoOrder(form.value)
    open.value = false
    getList()
    message.success(t('common.createSuccess'))
  } catch (error) {
  } finally {
    loadingBtn.value = false
  }
}

/** 支付按钮操作 */
import { useRouter } from 'vue-router'
const router = useRouter()
const handlePay = (row) => {
  router.push({
    name: 'PayOrderSubmit',
    query: {
      payOrderId: row.payOrderId
    }
  })
}
/** 退款按钮操作 */
const handleRefund = (row) => {
  const id = row.id
  message
    .confirm('是否确认退款编号为"' + id + '"的示例订单?')
    .then(function () {
      return refundDemoOrder(id)
    })
    .then(() => {
      getList()
      message.success('发起退款成功！')
    })
    .catch(() => {})
}
</script>
