<template>
  <UmvContent>
    <UmvQuery
      v-model="queryParams"
      ref="queryFormRef"
      :opts="queryOpts"
      @check="handleQuery"
      @reset="resetQuery"
    />

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="getList">
      <template #tools> </template>
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>
    <!-- 表单弹窗：预览 -->
    <OrderExtensionDetail ref="OrderExtensionDetailRef" @success="getList" />

    <!-- 异步通知接口弹出框 -->
    <Dialog v-model="dialogVisible" :title="t('common.detail')" width="60%">
      <vue-json-pretty :data="jsonContent" />
    </Dialog>
  </UmvContent>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'OrderExtension'
})

import { PayChannelEnum } from '@/utils/constants'
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as OrderExtensionApi from '@/api/pay/orderExtension'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { checkPermi } from '@/utils/permission'
import { UmvContent } from '@/components/UmvContent'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const queryFormRef = ref() // 搜索的表单
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  no: '', // 商户订单号
  orderId: '' // 支付订单号
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  no: {
    label: t('pay.order.merchantOrderId'),
    defaultVal: '',
    controlRender: () => (
      <el-input
        v-model={queryParams.value.no}
        placeholder={t('pay.order.merchantOrderIdPlaceholder')}
        clearable
      />
    )
  },
  orderId: {
    label: '支付订单',
    defaultVal: '',
    controlRender: () => (
      <el-input v-model={queryParams.value.orderId} placeholder="请输入支付订单" clearable />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: t('pay.order.id'), align: 'center' },
  {
    prop: 'no',
    label: t('pay.order.merchantOrderId'),
    align: 'center',
    minWidth: '220',
    showOverflowTooltip: true
  },
  { prop: 'orderId', label: '订单编号', align: 'center', minWidth: '100' },
  { prop: 'channelCode', label: '渠道编码', align: 'center', minWidth: '140' },
  {
    prop: 'channelCodeName',
    label: t('pay.order.channelCodeName'),
    align: 'center',
    width: '120',
    renderTemplate: (scope) => (
      <span>{PayChannelEnum[scope.row.channelCode?.toLocaleUpperCase()]?.name}</span>
    )
  },
  { prop: 'userIp', label: '用户IP', align: 'center', minWidth: '140' },
  {
    prop: 'status',
    label: '支付状态',
    align: 'center',
    renderTemplate: (scope) => (
      <dict-tag type={DICT_TYPE.PAY_ORDER_STATUS} value={scope.row.status} />
    )
  },
  {
    prop: 'channelExtras',
    label: '渠道附加参数',
    align: 'center',
    showOverflowTooltip: true,
    renderTemplate: (scope) =>
      scope.row.channelExtras ? (
        <el-button
          type="primary"
          link
          onClick={() => {
            dialogVisible.value = !dialogVisible.value
            jsonContent.value = scope.row.channelExtras
          }}
        >
          查看
        </el-button>
      ) : null
  },
  {
    prop: 'channelNotifyData',
    label: '异步通知结果',
    align: 'center',
    showOverflowTooltip: true,
    renderTemplate: (scope) =>
      scope.row.channelNotifyData ? (
        <el-button
          type="primary"
          link
          onClick={() => {
            dialogVisible.value = !dialogVisible.value
            jsonContent.value = JSON.parse(scope.row.channelNotifyData)
          }}
        >
          查看
        </el-button>
      ) : null
  },
  {
    prop: 'createTime',
    label: t('pay.order.createTime'),
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.updateTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: t('common.operate'),
    align: 'center',
    fixed: 'right',
    minWidth: '150',
    renderTemplate: (scope) => (
      <div>
        {checkPermi(['pay:order:query']) && (
          <el-button type="primary" link onClick={() => openDetail(scope.row.id)}>
            真实订单
          </el-button>
        )}
      </div>
    )
  }
])

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 查询列表 */
const loading = ref(false) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<OrderExtensionApi.OrderExtensionVO[]>() // 列表的数据
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderExtensionApi.getOrderExtensionPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  getList()
})
/** 重置按钮操作 */
const resetQuery = () => {
  // queryFormRef.value.resetFields()
  // handleQuery()
}

// /** 导出按钮操作 */
// import download from '@/utils/download'
// const exportLoading = ref(false) // 导出等待

// const handleExport = async () => {
//   try {
//     // 导出的二次确认
//     await message.exportConfirm()
//     // 发起导出
//     exportLoading.value = true
//     const data = await OrderExtensionApi.exportOrder(queryParams)
//     download.excel(data.data, t('pay.order.payOrder') + '.xls')
//   } catch {
//   } finally {
//     exportLoading.value = false
//   }
// }

/** 预览详情 */
import OrderExtensionDetail from './OrderExtensionDetail.vue'

const OrderExtensionDetailRef = ref()
const openDetail = (id: number) => {
  OrderExtensionDetailRef.value.open(id)
}

//异步通知结果弹出框
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
const dialogVisible = ref(false)
const jsonContent = ref<any>('')
</script>
