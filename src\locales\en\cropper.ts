/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-12-13 14:25:53
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-12-13 14:32:26
 * @Description:
 */
export default {
  selectImage: 'Select Image',
  uploadSuccess: 'Uploaded success!',
  modalTitle: 'Avatar upload',
  okText: 'Confirm and upload',
  btn_reset: 'Reset',
  btn_rotate_left: 'Counterclockwise rotation',
  btn_rotate_right: 'Clockwise rotation',
  btn_scale_x: 'Flip horizontal',
  btn_scale_y: 'Flip vertical',
  btn_zoom_in: 'Zoom in',
  btn_zoom_out: 'Zoom out',
  preview: 'Preivew'
}
