/*
 * @Author: HoJack
 * @Date: 2023-05-31 13:59:22
 * @LastEditors: HoJack
 * @LastEditTime: 2023-06-14 14:51:27
 * @Description:
 */
/**
 * 配置浏览器本地存储的方式，可直接存储对象数组。
 */

import WebStorageCache from 'web-storage-cache'
import envController from '@/controller/envController'

type CacheType = 'localStorage' | 'sessionStorage'

export const CACHE_KEY = {
  IS_DARK: 'isDark',
  USER: 'user',
  LANG: 'lang',
  THEME: 'theme',
  LAYOUT: 'layout',
  ROLE_ROUTERS: 'roleRouters',
  ROLE_PERMISSIONS: 'rolePermissons',
  DICT_CACHE: 'dictCache',
  CURRENT_CLIENT_ID: 'currentClientId'
}

/**
 * 获取缓存前缀，用于区分不同路径下的缓存
 *
 * @returns 返回当前应用的缓存前缀
 */
const getCachePrefix = (): string => {
  // 如果是专线IP模式，根据当前路径确定前缀
  if (envController.isDirectIPMode && envController.isDirectIPMode()) {
    return 'service_'
  }

  // 非专线模式或无法确定前缀时，使用默认前缀
  return ''
}

// 创建一个代理类来包装WebStorageCache，添加前缀功能
class PrefixedStorageCache {
  private wsCache: WebStorageCache
  private prefix: string

  constructor(type: CacheType, prefix: string) {
    this.wsCache = new WebStorageCache({
      storage: type
    })
    this.prefix = prefix
  }

  // 添加前缀的辅助方法
  private addPrefix(key: string): string {
    return this.prefix + key
  }

  // 重写所有需要的方法，添加前缀处理
  set(key: string, value: any, options?: Partial<any>): void {
    return this.wsCache.set(this.addPrefix(key), value, options)
  }

  get(key: string): any {
    return this.wsCache.get(this.addPrefix(key))
  }

  delete(key: string): void {
    return this.wsCache.delete(this.addPrefix(key))
  }

  deleteAllExpires(): void {
    this.wsCache.deleteAllExpires()
  }

  clear(): void {
    // 如果有前缀，只清除带有当前前缀的项
    if (this.prefix) {
      const storage = window[this.wsCache.storage as keyof Window] as Storage
      if (storage) {
        const keysToDelete: string[] = []
        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i)
          if (key && key.startsWith(this.prefix)) {
            keysToDelete.push(key)
          }
        }
        // 删除收集到的键
        keysToDelete.forEach((key) => {
          this.wsCache.delete(key)
        })
      }
    } else {
      this.wsCache.clear()
    }
  }

  touch(key: string, exp: number): void {
    return this.wsCache.touch(this.addPrefix(key), exp)
  }

  add(key: string, value: any, options?: Partial<any>): void {
    return this.wsCache.add(this.addPrefix(key), value, options)
  }

  replace(key: string, value: any, options?: Partial<any>): void {
    return this.wsCache.replace(this.addPrefix(key), value, options)
  }
}

export const useCache = (type: CacheType = 'localStorage') => {
  const prefix = getCachePrefix()

  const wsCache = new PrefixedStorageCache(type, prefix)

  return {
    wsCache
  }
}
