import request from '@/config/axios'

// 创建资源自动授权规则
export function createResourceAutomaticAuthorizationRule(data) {
  return request.post({
    url: '/system/resource-automatic-authorization-rule/create',
    data: data
  })
}

// 更新资源自动授权规则
export function updateResourceAutomaticAuthorizationRule(data) {
  return request.put({
    url: '/system/resource-automatic-authorization-rule/update',
    method: 'put',
    data: data
  })
}

// 删除资源自动授权规则
export function deleteResourceAutomaticAuthorizationRule(id) {
  return request.delete({
    url: '/system/resource-automatic-authorization-rule/delete?id=' + id,
    method: 'delete'
  })
}

// 获得资源自动授权规则
export function getResourceAutomaticAuthorizationRule(id) {
  return request.get({
    url: '/system/resource-automatic-authorization-rule/get?id=' + id
  })
}

// 获得资源自动授权规则分页
export function getResourceAutomaticAuthorizationRulePage(query) {
  return request.get({
    url: '/system/resource-automatic-authorization-rule/page',
    params: query
  })
}

// 导出资源自动授权规则 Excel
export function exportResourceAutomaticAuthorizationRuleExcel(query) {
  return request.get({
    url: '/system/resource-automatic-authorization-rule/export-excel',
    params: query,
    responseType: 'blob'
  })
}

// 匹配器类型的接口
export function getResourceAutomaticAuthorizationRuleListMatcherTypes(query?) {
  return request.get({
    url: '/system/resource-automatic-authorization-rule/listMatcherTypes',
    params: query
  })
}

// 具体匹配器列表接口
export function getResourceAutomaticAuthorizationRuleListMatchers(str) {
  return request.get({
    url: `/system/resource-automatic-authorization-rule/listMatchers/${str}`
  })
}
