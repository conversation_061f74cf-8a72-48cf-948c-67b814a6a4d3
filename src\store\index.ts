/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2024-12-04 14:35:13
 * @FilePath: \common-service-center-web\src\store\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { App } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const store = createPinia()
store.use(piniaPluginPersistedstate) //pinia持久化插件

export const setupStore = (app: App<Element>) => {
  app.use(store)
}

export { store }
