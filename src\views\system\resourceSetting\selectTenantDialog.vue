<template>
  <Dialog
    v-model="dialogVisible"
    :title="'授权方选择'"
    destroy-on-close
    :close-on-click-modal="false"
    width="1000"
  >
    <div class="flex" style="justify-content: space-between; align-items: center">
      <el-input
        class="mt-16px"
        style="width: 40%"
        v-model="filterKey"
        clearable
        placeholder="请输入租户名进行搜索"
        @input="filterFn"
      >
        <template #prefix>
          <icon-ep-search class="ml-5px" />
        </template>
      </el-input>
      <div
        ><span>父子联动(选中父节点，自动选择子节点):</span>
        <el-switch v-model="checkStrictly" active-text="是" inactive-text="否" inline-prompt
      /></div>
    </div>

    <div class="flex">
      <div style="width: 660px; height: 500px">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              :data="treeOptions"
              :height="height"
              ref="tableRef"
              :width="660"
              row-key="id"
              :columns="columns"
              :expand-column-key="'name'"
              v-model:expanded-row-keys="expandedRowKeys"
              :default-expanded-row-keys="defaultExpandedRowKeys"
              :estimated-row-height="100"
              size="small"
              fixed
            />
          </template>
        </el-auto-resizer>
      </div>
      <div class="selected-tenant">
        <el-tag
          v-for="(item, index) in authorizersRow"
          :key="index"
          class="mr-8px mb-8px"
          closable
          @close="delAuthorizersRow(item)"
          >{{ item.name }}（{{ item.id }}）</el-tag
        >
      </div>
    </div>

    <template #footer>
      <el-button type="primary" @click="submit" :loading="loading">{{ t('common.ok') }}</el-button>
      <el-button @click="close" :loading="loading">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'SelectTenantDialog'
})

import { listTenantUsers, quickResourceExposeAndApproval } from '@/api/system/resource'
import * as RoleApi from '@/api/system/role'
import { deepClone } from '@/utils/deep'
import { ElCheckbox } from 'element-plus'
const { t } = useI18n() // 国际化
const dialogVisible = ref(false)

const loading = ref(false)

const close = () => {
  dialogVisible.value = false
}

const message = useMessage()

const emits = defineEmits(['success'])

const submit = () => {
  if (authorizers.value.length === 0) {
    message.error('请选择授权方租户！')
    return
  }
  close()
  emits('success', {
    _authorizers: authorizers.value,
    _authorizersRow: authorizersRow.value
  })
}

/************************************* 树形结构start *************************************************/

const checkStrictly = ref(true)

// 选中的授权方租户
const authorizers = ref([])

const authorizersRow = ref([])

const treeOptions = ref<any[]>([]) // 租户树形结构

const expandedRowKeys = ref(['id'])
// 默认展开的租户，取第一层
const defaultExpandedRowKeys = ref([])
const originExpandedRowKeys = ref([])

// true是都选上，false都不选，null没强制要求
const checkTree = (_list, _forceTrue: null | boolean = null) => {
  _list.forEach((el) => {
    if (el.children && el.children.length > 0) {
      if (filterKey.value) {
        expandedRowKeys.value.push(el.id)
      }
      checkTree(el.children, _forceTrue)
    }
    if (_forceTrue === true && checkStrictly.value) {
      //如果不加父子联动就只选自己
      el.isSelect = true
      if (authorizers.value.indexOf(el.id) === -1) {
        // 带进选中id列表里
        authorizers.value.push(el.id)
        authorizersRow.value.push(el)
      }
    } else if (_forceTrue === false && checkStrictly.value) {
      el.isSelect = false
      if (authorizers.value.indexOf(el.id) !== -1) {
        // 踢出选中id列表
        authorizersRow.value.splice(authorizers.value.indexOf(el.id), 1)
        authorizers.value.splice(authorizers.value.indexOf(el.id), 1)
      }
    } else {
      if (authorizers.value.indexOf(el.id) === -1) {
        el.isSelect = false
      } else {
        el.isSelect = true
      }
    }
  })
}

// 选择数据
const selectRowData = (_rowData) => {
  if (authorizers.value.indexOf(_rowData.id) === -1) {
    authorizers.value.push(_rowData.id)
    authorizersRow.value.push(_rowData)
    _rowData.isSelect = true
    if (_rowData.children && _rowData.children) {
      //选中就把子级全都选中
      checkTree(_rowData.children, true)
    }
  } else {
    authorizersRow.value.splice(authorizers.value.indexOf(_rowData.id), 1)
    authorizers.value.splice(authorizers.value.indexOf(_rowData.id), 1)

    _rowData.isSelect = false
    // if (isStrictly) {
    if (_rowData.children && _rowData.children) {
      //选中就把子级全都选中
      checkTree(_rowData.children, false)
    }
    // }
  }
}

const authCardRef = ref()

const authCardWidth = ref(600)

const tableRef = ref()

const authCardChangeWidth = computed(() => {
  if (authCardRef.value) {
    if (authCardRef.value.$el.offsetWidth) {
      return authCardRef.value.$el.offsetWidth
    } else {
      return 600
    }
  } else {
    return 600
  }
})

const isFullscreen = ref(false)

const fullScreenChange = (_val) => {
  isFullscreen.value = _val
}

const isHiddent = ref(false)

const tableWidth = ref(0)

const columns = computed(() => {
  return [
    {
      key: 'selection',
      width: 50,

      cellRenderer: ({ rowData }) => (
        <ElCheckbox
          modelValue={rowData.isSelect}
          onChange={() => selectRowData(rowData)}
          class="mt-4px mb-4px"
        />
      )
    },
    {
      dataKey: 'name',
      key: 'name',
      width: authCardWidth.value,
      title: '租户',
      cellRenderer: ({ rowData }) => <span>{`${rowData.name}（${rowData.id}）`}</span>
    }
  ]
})

// 初始化处理树结构数据
const initTreeData = (tree) => {
  return tree.map((el) => {
    el.isSelect = authorizers.value.indexOf(el.id) === -1 ? false : true
    if (el.children) {
      el.children = initTreeData(el.children)
    }
    if (!el.resources) {
      el.resourcesMap = {}
    } else {
      let tempResourcesMap = {}
      el.resources.forEach((e) => {
        if (!tempResourcesMap[e.scene]) {
          tempResourcesMap[e.scene] = [e.permissionType]
        } else {
          tempResourcesMap[e.scene].push(e.permissionType)
        }
      })
      el.resourcesMap = tempResourcesMap
    }

    if (!el.sence) {
      el.sence = []
    }

    return el
  })
}

const managementCustomerDataScope = ref()

/************************************* 树形结构end *************************************************/
const originTreeOptions = ref<any[]>([]) // 原租户树形结构

const open = async (_authorizers, _authorizersRow, _proxyTenantIds) => {
  filterKey.value = ''
  dialogVisible.value = true
  authorizersRow.value = deepClone(_authorizersRow)
  authorizers.value = [..._authorizers]
  defaultExpandedRowKeys.value = []
  originExpandedRowKeys.value = []
  managementCustomerDataScope.value = initTreeData(
    await RoleApi.getListResourceListTenantSceneTreesProxy('-1', _proxyTenantIds)
  ) //指定客户数据权限

  treeOptions.value = managementCustomerDataScope.value.map((el) => {
    el.scene = []
    return el
  })

  console.log('treeOptions.value', treeOptions.value)

  // 保存原数组，用于筛选
  originTreeOptions.value = deepClone(treeOptions.value)

  // 将树的第一层展开
  originTreeOptions.value.forEach((el) => {
    if (el.children && el.children.length > 0) {
      defaultExpandedRowKeys.value.push(el.id)
      originExpandedRowKeys.value.push(el.id)
    }
  })
}

/************************************* 筛选过滤start *************************************************/

const filterKey = ref('') //进一步搜索的关键字

// 进行关键字进一步搜索
import { debounce, cloneDeep } from 'lodash-es'

const filterFn = debounce((_val) => {
  if (filterKey.value) {
    // 只在有关键字时进行深拷贝和过滤
    treeOptions.value = filterListFn(cloneDeep(unref(originTreeOptions.value)), filterKey.value)
  } else {
    // 没有关键字时，使用缓存list
    treeOptions.value = cloneDeep(originTreeOptions.value)
  }
  nextTick(() => {
    if (!filterKey.value) {
      expandedRowKeys.value = [...originExpandedRowKeys.value]
    } else {
      expandedRowKeys.value = []
    }
    checkTree(treeOptions.value)
  })
}, 500)

// 递归过滤下拉菜单数据
const filterListFn = (_arr, _searchName) => {
  return _arr.filter((el) => {
    const isUnMatchName = el.name.indexOf(_searchName) === -1
    if (!isUnMatchName) {
      // 匹配上名字，且有子节点时，递归过滤子节点
      if (el.children && el.children.length > 0) {
        el.children = filterListFn(el.children, _searchName)
      }
      return true // 匹配上名字的元素不过滤
    } else {
      // 没匹配上名字，但有子节点时，递归过滤子节点
      if (el.children && el.children.length > 0) {
        el.children = filterListFn(el.children, _searchName)
        // 仅当过滤后的子节点为空时，才过滤当前节点
        return el.children.length > 0
      }
      return false // 没有匹配上名字且没有子节点或子节点过滤完的元素被过滤
    }
  })
}

/************************************* 筛选过滤end *************************************************/

const delAuthorizersRow = (_item) => {
  selectRowData(_item, false)
}

defineExpose({
  open
})
</script>
<style lang="scss">
.selected-tenant {
  width: 100%;
  height: 500px;
  margin: 25px;
  border: 1px solid var(--el-text-color-disabled);
  border-radius: 5px;
  padding: 8px;
  overflow-y: auto;
}
</style>
