<template>
  <div>
    <Dialog :title="t('system.subscribe.subscribe')" width="900" v-model="dialogVisible">
      <el-transfer
        v-model="subListContainer"
        v-loading="loading"
        :data="appList"
        :props="{ key: 'code', label: 'name' }"
        :titles="[t('system.subscribe.subListContainer1'), t('system.subscribe.subListContainer2')]"
      />
      <template #footer>
        <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitFn" v-loading="btnLoading">{{
          t('common.save')
        }}</el-button>
      </template>
    </Dialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SubscribePopup'
})

import {
  getTenantAppListFilter,
  createSubscription,
  getSubscriptionList,
  ApplicationSubscriptionPageRes
} from '@/api/system/apply'
const emit = defineEmits(['success'])
const { t } = useI18n() // 国际化

interface Option {
  id: number | string
  name: string
  disabled?: boolean
  status?: number
}

const appList = ref<ApplicationSubscriptionPageRes[]>([])
const loading = ref(false)

const btnLoading = ref(false)

const dialogVisible = ref(false)
const subListContainer: Ref<Option[]> = ref([])

const message = useMessage() // 消息弹窗

const open = async () => {
  dialogVisible.value = true
  loading.value = true
  // 获取穿梭框左边，也就是所有的应用
  //2024-8-24z  左穿梭框改造为过滤租户下应用
  appList.value = await getTenantAppListFilter({ pageNo: 1, pageSize: 100 })
  // 获取已经订阅的应用，只获取id，会自动选至右边
  subListContainer.value = (await getSubscriptionList({ pageNo: 1, pageSize: 100 })).list.map(
    (el) => el.applicationCode
  )
  console.log('subListContainer.value', subListContainer.value)
  // 对已有的应用进行过滤，让已选至右边的应用变为不可选
  appList.value.map((el: Option) => {
    subListContainer.value.indexOf(el.code) !== -1 && (el.disabled = true)
  })
  appList.value = appList.value.filter((el: Option) => el.status === 0 || el.disabled === true)
  loading.value = false
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const submitFn = async () => {
  let arr: any[] = []
  try {
    btnLoading.value = true
    appList.value.forEach((el: any) => {
      if (unref(subListContainer).indexOf(el.code) !== -1 && !el.disabled) {
        arr.push({
          applicationId: el.id
        })
      }
    })
    await createSubscription(arr)
    dialogVisible.value = false
    message.success(t('common.handleSuccess'))
    emit('success')
  } finally {
    btnLoading.value = false
  }
}
</script>
