import { useCache } from '@/hooks/web/useCache'

interface ContentCache {
  heightInitialized: boolean
  cachedHeight: number
  isResizeTriggered: boolean
}

// 缓存键
const CACHE_KEY = 'UMV_CONTENT_CACHE'

/**
 * UmvContent 组件的缓存 hook
 * 用于在不同组件实例间共享高度和状态信息
 */
export function useContentCache() {
  // 使用 localStorage 缓存以在不同实例间共享
  const { wsCache } = useCache('localStorage')

  // 初始化缓存，如果不存在则创建
  if (!wsCache.get(CACHE_KEY)) {
    wsCache.set(CACHE_KEY, {
      heightInitialized: false,
      cachedHeight: 0,
      isResizeTriggered: false
    })
  }

  /**
   * 获取缓存对象
   */
  const getCache = (): ContentCache => {
    return (
      wsCache.get(CACHE_KEY) || {
        heightInitialized: false,
        cachedHeight: 0,
        isResizeTriggered: false
      }
    )
  }

  /**
   * 更新缓存对象
   */
  const setCache = (cache: ContentCache): void => {
    wsCache.set(CACHE_KEY, cache)
  }

  /**
   * 更新缓存中的某个属性
   */
  const updateCache = <K extends keyof ContentCache>(key: K, value: ContentCache[K]): void => {
    const cache = getCache()
    cache[key] = value
    setCache(cache)
  }

  /**
   * 设置尺寸调整触发标志
   */
  const setResizeTriggered = (triggered: boolean): void => {
    updateCache('isResizeTriggered', triggered)
  }

  /**
   * 设置高度初始化状态和缓存高度
   */
  const setCachedHeight = (height: number): void => {
    const cache = getCache()
    cache.heightInitialized = true
    cache.cachedHeight = height
    cache.isResizeTriggered = false
    setCache(cache)
  }

  /**
   * 重置缓存
   */
  const resetCache = (): void => {
    setCache({
      heightInitialized: false,
      cachedHeight: 0,
      isResizeTriggered: false
    })
  }

  return {
    getCache,
    setCache,
    updateCache,
    setResizeTriggered,
    setCachedHeight,
    resetCache
  }
}
