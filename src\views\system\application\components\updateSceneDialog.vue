<!--
 * @Author: HoJ<PERSON> <EMAIL>
 * @Date: 2025-02-17 14:43:16
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2025-02-17 17:16:03
 * @Description: 
-->
<!--业务场景设置  -->

<template>
  <el-dialog title="业务场景" width="500px" v-model="show" @before-close="close">
    <el-form :model="formData" :rules="formRules" :label-width="80" ref="formRef">
      <el-form-item label="菜单名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入应用名称" />
      </el-form-item>

      <el-form-item label="业务场景" prop="scene">
        <el-select v-model="formData.scene" placeholder="请选择业务场景" clearable>
          <el-option
            v-for="dict in getStrDictOptions('system_business_scene')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="close">取 消</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'updateSceneDialog'
})

import { getStrDictOptions } from '@/utils/dict'

const show = ref(false)

const formData = ref()

const formRules = ref({
  clientId: [{ required: true, trigger: ['blur', 'change'], message: '请选择业务端' }]
})

const formLoading = ref(false)

import { getMenu } from '@/api/system/menu'

const open = async (id, _row?) => {
  if (_row) {
    formData.value = _row
  }

  formData.value = await getMenu(id)
  show.value = true
}
defineExpose({
  open
})

const message = useMessage()

//提交表单
import { updateMenu } from '@/api/system/menu'

const emits = defineEmits(['success'])

const formRef = ref()

const submitForm = async () => {
  await formRef.value.validate()
  try {
    formLoading.value = true
    await updateMenu(formData.value)
    message.success('操作成功')
    emits('success')
    close()
  } finally {
    formLoading.value = false
  }
}

const close = () => {
  show.value = false
}
</script>
