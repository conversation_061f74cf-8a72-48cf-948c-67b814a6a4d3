<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="160px"
    >
      <el-form-item :label="t('pay.app.name')" prop="name">
        <el-input v-model="formData.name" :placeholder="t('pay.app.namePlaceholder')" />
      </el-form-item>
      <!-- code 应用编码。 （提供给业务模块使用，同一租户下唯一。） 同商户号-->
      <el-form-item :label="t('pay.app.code')" prop="code">
        <el-row class="w-[100%]">
          <el-col :span="22">
            <el-input
              v-model="formData.code"
              :placeholder="t('pay.app.codePlaceholder')"
              :disabled="formType === 'update'"
            />
          </el-col>
          <el-col :span="2" class="flex items-center">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="t('pay.app.desc1')"
              placement="top-end"
            >
              <Icon :size="22" icon="ep:question-filled" />
            </el-tooltip>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :label="t('pay.app.merchantId')" prop="merchantId">
        <el-select v-model="formData.merchantId" :placeholder="t('pay.app.merchantIdPlaceholder')">
          <el-option
            v-for="item in merchantList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('pay.app.type')" prop="type">
        <el-radio-group v-model="formData.type" :disabled="formType === 'update'">
          <el-radio :label="1"> {{ t('pay.app.type_1') }} </el-radio>
          <el-radio :label="2"> {{ t('pay.app.type_2') }} </el-radio>
          <el-radio :label="3"> {{ t('pay.app.type_3') }} </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="t('pay.app.status')" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item :label="t('pay.app.payNotifyUrl')" prop="payNotifyUrl">
        <el-input
          v-model="formData.payNotifyUrl"
          :placeholder="t('pay.app.payNotifyUrlPlaceholder')"
        />
      </el-form-item>
      <el-form-item :label="t('pay.app.refundNotifyUrl')" prop="refundNotifyUrl">
        <el-input
          v-model="formData.refundNotifyUrl"
          :placeholder="t('pay.app.refundNotifyUrlPlaceholder')"
        />
      </el-form-item> -->
      <el-form-item :label="t('pay.app.remark')" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :placeholder="t('pay.app.remarkPlaceholder')"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">{{
        t('common.ok')
      }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as AppApi from '@/api/pay/app'
import * as MerchantApi from '@/api/pay/merchant'
import { CommonStatusEnum } from '@/utils/constants'

defineOptions({ name: 'PayAppForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
let formData = ref({
  id: undefined,
  name: undefined,
  code: '',
  type: 1,
  status: CommonStatusEnum.ENABLE,
  remark: undefined,
  payNotifyUrl: undefined,
  refundNotifyUrl: undefined,
  merchantId: undefined
})
const formRules = reactive({
  name: [
    { required: true, message: t('pay.app.rDesc1'), trigger: 'blur' },
    { min: 1, max: 30, message: t('pay.app.rDesc2'), trigger: 'blur' }
  ],
  code: [
    { required: true, message: t('pay.app.rDesc3'), trigger: 'blur' },
    { min: 1, max: 50, message: t('pay.app.rDesc4'), trigger: 'blur' }
  ],
  type: [{ required: true, message: t('pay.app.rDesc5'), trigger: 'blur' }],
  status: [{ required: true, message: t('pay.app.rDesc6'), trigger: 'blur' }],
  payNotifyUrl: [{ required: true, message: t('pay.app.rDesc7'), trigger: 'blur' }],
  refundNotifyUrl: [{ required: true, message: t('pay.app.rDesc8'), trigger: 'blur' }],
  merchantId: [{ required: true, message: t('pay.app.rDesc9'), trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const merchantList = ref([]) // 商户列表

/** 打开弹窗 */
/**
 *
 * @param type   create/update
 * @param id
 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? t('pay.app.newApp') : t('pay.app.editApp')
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AppApi.getApp(id)
    } finally {
      formLoading.value = false
    }
  }
  // 加载商户列表
  merchantList.value = await MerchantApi.getMerchantListByName()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AppApi.AppVO
    if (formType.value === 'create') {
      await AppApi.createApp(data)
      message.success(t('common.createSuccess'))
    } else {
      await AppApi.updateApp(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    code: '',
    type: 1,
    status: CommonStatusEnum.ENABLE,
    remark: undefined,
    payNotifyUrl: undefined,
    refundNotifyUrl: undefined,
    merchantId: undefined
  }
  formRef.value?.resetFields()
}
</script>
