<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="版本号" prop="ver">
        <el-input v-model="formData.ver" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="菜单权限" prop="menuIds">
        <el-card class="cardHeight">
          <template #header>
            全选/全不选:
            <el-switch
              v-model="treeNodeAll"
              active-text="是"
              inactive-text="否"
              inline-prompt
              @change="handleCheckedTreeNodeAll"
            />
            全部展开/折叠:
            <el-switch
              v-model="menuExpand"
              active-text="展开"
              inactive-text="折叠"
              inline-prompt
              @change="handleCheckedTreeExpand"
            />
          </template>
          <el-tree
            ref="treeRef"
            :data="menuOptions"
            :props="defaultProps"
            empty-text="暂无数据"
            node-key="id"
            show-checkbox
          />
        </el-card>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemTenantPackageForm'
})

import { defaultProps, handleTree } from '@/utils/tree'
import * as TenantPackageApi from '@/api/system/tenantPackage'
import * as MenuApi from '@/api/system/menu'
import { ElTree } from 'element-plus'
import { createVerApplication } from '@/api/system/apply'

interface formDataVO {
  ver: string | null
  menuIds: any
  appNo?: string
}

const { t } = useI18n() // 国际化

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType: Ref<formDataVO | string> = ref('') // 表单的类型：create - 新增；update - 修改
const formData: Ref<formDataVO> = ref({
  ver: null,
  menuIds: []
})

// const prop = defineProps({
//   applicationCode: {
//     type: String,
//     default: ''
//   }
// })

const applicationCode = ref('')

interface verVO {
  name: string
  id?: string
  code: string
}

const formRef = ref() // 表单 Ref
const menuOptions = ref<any[]>([]) // 树形结构数据
const originMenuOptions = ref<any[]>([]) //数组结构菜单
const menuExpand = ref(false) // 展开/折叠
const treeRef = ref<InstanceType<typeof ElTree>>() // 树组件 Ref
const treeNodeAll = ref(false) // 全选/全不选
const verName = ref('')

const appId = ref() //应用编号

// 验证应用编号
const validatorCheckAppNo = (_rule: any, _value: any, callback: any) => {
  console.log('unref(formData).appNo', unref(formData))
  if (unref(formData).appNo === null || unref(formData).appNo === '') {
    callback(new Error('应用编号不能为空'))
    return
  } else {
    callback()
  }
}

const formRules = reactive({
  ver: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
  appNo: [
    { required: true, message: '应用编号不能为空', trigger: 'blur' },
    { validator: validatorCheckAppNo, trigger: 'blur' }
  ]
  // menuIds: [{ validator: validatorMenu, trigger: 'blur' }]
})

/** 打开弹窗 */
const open = async (type: string, obj: verVO) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  verName.value = obj.name
  applicationCode.value = obj.code
  formType.value = type
  resetForm()
  // 加载 Menu 列表。注意，必须放在前面，不然下面 setChecked 没数据节点
  appId.value = obj.id
  originMenuOptions.value = await MenuApi.getAppMenuList({ applicationId: appId.value })
  menuOptions.value = handleTree(originMenuOptions.value)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TenantPackageApi.TenantPackageVO
    const menuIds = [
      ...(treeRef?.value?.getCheckedKeys(false) as unknown as Array<number>), // 获得当前选中节点
      ...(treeRef?.value?.getHalfCheckedKeys() as unknown as Array<number>) // 获得半选中的父节点
    ]

    data.menus = getMenuPerfect(menuIds, originMenuOptions.value)
    data.name = verName.value
    console.log('applicationCode.value', applicationCode.value)
    data.code = applicationCode.value
    delete data.menuIds
    await createVerApplication(data)
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const getMenuPerfect = (menuIds, menuOptions) => {
  let arr: any = []
  // return menuOptions.filter((el) => {
  //   return menuIds.indexOf(el.id) !== -1
  // })
  menuOptions.forEach((el) => {
    if (menuIds.indexOf(el.id) !== -1) {
      arr.push({ ...el, sort: 0, status: 0 })
    }
  })

  return arr
}

/** 重置表单 */
const resetForm = () => {
  // 重置选项
  treeNodeAll.value = false
  menuExpand.value = false
  // 重置表单
  formData.value = {
    ver: null,
    menuIds: []
  }
  treeRef.value?.setCheckedNodes([])
  formRef.value?.resetFields()
}

/** 全选/全不选 */
const handleCheckedTreeNodeAll = () => {
  treeRef?.value?.setCheckedNodes(treeNodeAll.value ? menuOptions.value : [])
}

/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
</style>
