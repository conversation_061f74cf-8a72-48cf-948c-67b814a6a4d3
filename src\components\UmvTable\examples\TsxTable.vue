<template>
  <div class="p-5 bg-el-bg-color rounded shadow-md">
    <h2 class="text-xl font-bold mb-4">使用renderTemplate的UmvTable示例</h2>

    <UmvTable title="TSX渲染示例" :data="tableData" :columns="columns" height="400px">
      <!-- 可以混合使用 slot 和 renderTemplate -->
      <template #tools>
        <el-button type="primary" size="small"> <icon-ep-plus class="mr-1" />新增 </el-button>
      </template>

      <!-- 分页 -->
      <template #pagination>
        <div class="flex justify-end mt-4">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </template>
    </UmvTable>
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import UmvTable from '@/components/UmvTable'
import { ElButton, ElTag } from 'element-plus'
import { Edit, Delete, View } from '@element-plus/icons-vue'
import type { TableColumn } from '@/components/UmvTable/src/types'

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '张三',
    age: 28,
    status: 1,
    tags: ['开发', '前端'],
    address: '北京市朝阳区',
    createTime: '2023-10-15 12:30:45'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    status: 0,
    tags: ['设计', 'UI'],
    address: '上海市浦东新区',
    createTime: '2023-09-20 09:15:30'
  },
  {
    id: 3,
    name: '王五',
    age: 24,
    status: 1,
    tags: ['测试', '运维'],
    address: '广州市天河区',
    createTime: '2023-11-05 15:45:22'
  }
])

// 表格列配置，使用renderTemplate定义TSX渲染函数
const columns = ref<TableColumn[]>([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名' },
  { prop: 'age', label: '年龄' },
  {
    prop: 'status',
    label: '状态',
    // 使用renderTemplate简单渲染
    renderTemplate: (scope) => (
      <ElTag type={scope.row.status ? 'success' : 'danger'}>
        {scope.row.status ? '启用' : '禁用'}
      </ElTag>
    )
  },
  {
    prop: 'tags',
    label: '标签',
    // 复杂的渲染逻辑
    renderTemplate: (scope) => (
      <div>
        {scope.row.tags.map((tag, index) => (
          <ElTag key={index} class="mr-1 mb-1" effect="plain">
            {tag}
          </ElTag>
        ))}
      </div>
    )
  },
  { prop: 'address', label: '地址', showOverflowTooltip: true },
  { prop: 'createTime', label: '创建时间' },
  {
    prop: 'operation',
    label: '操作',
    width: '200px',
    // 操作按钮组渲染
    renderTemplate: (scope) => (
      <div>
        <ElButton type="primary" link onClick={() => handleView(scope.row)}>
          <el-icon>
            <View />
          </el-icon>
          查看
        </ElButton>
        <ElButton type="primary" link onClick={() => handleEdit(scope.row)}>
          <el-icon>
            <Edit />
          </el-icon>
          编辑
        </ElButton>
        <ElButton type="danger" link onClick={() => handleDelete(scope.row)}>
          <el-icon>
            <Delete />
          </el-icon>
          删除
        </ElButton>
      </div>
    )
  }
])

// 操作方法
const handleView = (row) => {
  console.log('查看', row)
}

const handleEdit = (row) => {
  console.log('编辑', row)
}

const handleDelete = (row) => {
  console.log('删除', row)
}
</script>
