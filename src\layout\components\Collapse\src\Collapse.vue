<script setup lang="ts">
defineOptions({
  name: 'Collapse'
})

import { useAppStore } from '@/store/modules/app'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('collapse')

defineProps({
  color: propTypes.string.def('')
})

const appStore = useAppStore()

const collapse = computed(() => appStore.getCollapse)

const toggleCollapse = () => {
  const collapsed = unref(collapse)
  appStore.setCollapse(!collapsed)
}
</script>

<template>
  <div :class="prefixCls">
    <icon-ep-expand
      v-if="collapse"
      :style="{ color }"
      class="cursor-pointer text-[18px]"
      @click="toggleCollapse"
    />
    <icon-ep-fold
      v-else
      :style="{ color }"
      class="cursor-pointer text-[18px]"
      @click="toggleCollapse"
    />
  </div>
</template>
