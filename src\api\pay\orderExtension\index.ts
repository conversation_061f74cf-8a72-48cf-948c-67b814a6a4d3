import request from '@/config/axios'
// 定义一个泛型接口，其中 T 是 list 属性的类型
interface PromiseWithList<T> {
  list: T[]
  total: number
}
// 定义一个类型别名，表示返回 PromiseWithList 的 Promise 对象
type PromiseListWithTotal<T> = Promise<PromiseWithList<T>>

export interface OrderExtensionVO {
  id: number // 编号
  no: string // 商户订单编号
  orderId: number // 订单编号
  channelId: number // 渠道编号
  channelCode: string // 渠道编码
  userIp: string // 用户IP
  status: number // 支付状态
  statusName: string // 支付状态名称
  channelExtras: Map<string, string> // 渠道附加参数
  channelErrorCode: string // 调用渠道错误码
  channelErrorMsg: string // 调用渠道错误信息
  channelNotifyData: string // 异步通知结果
  createTime: Date // 创建时间
  updateTime: Date // 最后更新时间
}

// 查询列表订单
export const getOrderExtensionPage = async (params): PromiseListWithTotal<OrderExtensionVO> => {
  return await request.get({ url: '/pay/order/extension/page', params })
}
/**
 * 获取订单扩展详情
 *
 * @param orderExtensionId 订单扩展ID
 * @returns 返回 PromiseListWithTotal<OrderExtensionVO> 类型的订单扩展详情列表
 */
export const getOrderExtensionDeatail = async (orderExtensionId): Promise<OrderExtensionVO> => {
  return await request.get({ url: '/pay/order/extension/queryActualOrder/' + orderExtensionId })
}
