/**支付管理 */
export default {
  merchant: {
    no: '商户号',
    name: '商户全称',
    shortName: '商户简称',
    status: '开启状态',
    remark: '备注',
    createTime: '创建时间',
    noPlaceholder: '请输入商户号',
    namePlaceholder: '请输入商户全称',
    shortNamePlaceholder: '请输入商户简称',
    remarkPlaceholder: '请输入备注',
    statusPlaceholder: '字典状态',
    id: '商户编号',
    merchantXls: '商户信息',
    desc1: '确认要{text}{name}商户吗？',
    rMsg1: '商户名称不能为空',
    rMsg2: '商户简称不能为空',
    rMsg3: '状态不能为空',
    desc2: '新增商户信息',
    desc3: '修改商户信息'
  },
  order: {
    merchantId: '所属商户',
    appId: '应用',
    channelCode: '渠道',
    merchantOrderId: '商户订单编号',
    channelOrderNo: '渠道订单号',
    status: '支付状态',
    refundStatus: '退款状态',
    notifyStatus: '回调状态',
    createTime: '创建时间',
    merchantOrderIdPlaceholder: '请输入商户订单编号',
    channelOrderNoPlaceholder: '请输入渠道订单号',
    merchantIdPlaceholder: '请选择所属商户',
    appIdPlaceholder: '请选择应用',
    channelCodePlaceholder: '请输入渠道',
    statusPlaceholder: '请选择支付状态',
    refundStatusPlaceholder: '请选择退款状态',
    notifyStatusPlaceholder: '请选择订单回调商户状态',
    id: '编号',
    payMerchantName: '商户名称',
    appName: '应用名称',
    channelCodeName: '渠道名称',
    subject: '商品标题',
    body: '商品描述',
    notifyUrl: '异步通知地址',
    amount: '支付金额',
    channelFeeAmount: '手续金额',
    refundAmount: '退款金额',
    bizNotifyStatusName: '回调状态',
    successTime: '支付时间',
    payOrder: '支付订单',
    merchant: '商户',
    pay: '支付',
    no: '支付订单号',
    price: '金额',
    channelFeePrice: '手续费',
    channelFeeRate: '手续费比例',
    notifyUrl2: '回调地址',
    expireTime: '失效时间',
    notifyTime: '通知时间',
    payChannel: '支付渠道',
    userIp: '支付IP',
    refundTimes: '退款次数',
    desc1: '支付通道异步回调内容',
    name: '条形码',
    valuePlaceholder: '请输入条形码',
    payInfo: '支付信息',
    orderId: '支付单号',
    body2: '商品内容',
    expireTime2: '过期时间',
    returnUrl: '重定向地址',
    desc2: '选择支付宝支付',
    desc3: '提交支付中...',
    desc4: '选择微信支付',
    desc5: '或使用',
    desc6: '(扫码枪/扫码盒)',
    desc7: '扫码',
    paySure: '确认支付',
    desc8: '未传递支付单号，无法查看对应的支付信息',
    desc9: '支付订单不存在，请检查！',
    desc10: '支付订单不处于待支付状态，请检查！',
    desc11: '"支付宝"条码支付',
    desc12: '未实现该支付方式:',
    payWin: '支付窗口',
    desc13: '请使用手机浏览器“扫一扫”',
    desc14: '请使用“支付宝”扫一扫”扫码支付',
    desc15: '请使用微信“扫一扫”扫码支付',
    desc16: '支付成功！',
    desc17: '支付已关闭！'
  },
  refund: {
    merchantId: '所属商户',
    appId: '应用',
    channelCode: '渠道',
    type: '退款类型',
    merchantRefundNo: '支付订单号',
    status: '退款状态',
    notifyStatus: '退款回调状态',
    createTime: '创建时间',
    merchantRefundNoPlaceholder: '请输入商户退款订单号',
    merchantIdPlaceholder: '请选择所属商户',
    appIdPlaceholder: '请选择应用信息',
    channelCodePlaceholder: '请输入渠道',
    typePlaceholder: '请选择退款类型',
    statusPlaceholder: '请选择退款状态',
    notifyStatusPlaceholder: '请选择通知商户退款结果的回调状态',
    id: '编号',
    payMerchantName: '商户名称',
    appName: '应用名称',
    channelCodeName: '渠道名称',
    orderId: '交易订单号',
    merchantOrderId: '商户订单编号',
    payAmount: '支付金额(元)',
    refundAmount: '退款金额(元)',
    bizNotifyStatusName: '回调状态',
    reason: '退款原因',
    successTime: '退款成功时间',

    status_0: '未退款',
    status_10: '退款成功',
    status_20: '退款失败',
    merchantOrderId2: '商户订单号',
    merchantRefundId: '商户退款订单号',
    merchantRefundNoTag1: '交易',
    merchantRefundNoTag2: '渠道',
    payPrice: '支付金额',
    refundPrice: '退款金额',
    expireTime: '退款失效时间',
    channelCode2: '支付渠道',
    userIp: '支付IP',
    notifyUrl: '回调地址',
    notifyStatus2: '回调状态',
    notifyTime: '回调时间',
    channelOrderNo: '渠道订单号',
    channelRefundNo: '渠道退款单号',
    channelErrorCode: '渠道错误码',
    channelErrorMsg: '渠道错误码描述',
    channelNotifyData: '渠道额外参数'
  },

  app: {
    name: '应用名',
    contactName: '商户名称',
    status: '开启状态',
    createTime: '创建时间',
    namePlaceholder: '请输入应用名',
    contactNamePlaceholder: '请输入商户名称',
    statusPlaceholder: '请选择开启状态',
    id: '商户号',
    code: '应用编码',
    payMerchantName: '商户名称',
    alipayCfg: '支付宝配置',
    wxH5Cfg: '微信配置',
    payAppXls: '支付应用信息',
    merchantId: '所属商户',
    type: '商户类型',
    payNotifyUrl: '支付结果的回调地址',
    refundNotifyUrl: '退款结果的回调地址',
    remark: '备注',
    codePlaceholder: '请输入应用编号',
    payNotifyUrlPlaceholder: '请输入支付结果的回调地址',
    refundNotifyUrlPlaceholder: '请输入退款结果的回调地址',
    remarkPlaceholder: '请输入备注',
    merchantIdPlaceholder: '请选择所属商户',
    desc1: '应用编号即支付宝和微信上的商户号',
    type_1: '支付宝',
    type_2: '微信',
    type_3: '徽商银行',
    rDesc1: '应用名不能为空',
    rDesc2: '长度在 1 到 30 个字符',
    rDesc3: '应用编码不能为空',
    rDesc4: '长度在 1 到 50 个字符',
    rDesc5: '商户类型不能为空',
    rDesc6: '开启状态不能为空',
    rDesc7: '支付结果的回调地址不能为空',
    rDesc8: '退款结果的回调地址不能为空',
    rDesc9: '商户编号不能为空',
    newApp: '新增应用',
    editApp: '编辑应用',
    feeRate: '渠道费率',

    title1: '创建微信支付渠道',
    title2: '编辑微信支付渠道',

    config: {
      appId: '公众号 APPID',
      mchId: '商户号',
      status: '渠道状态',
      apiVersion: 'API 版本',
      mchKey: '商户密钥',
      keyContent: 'apiclient_cert.p12 证书',
      apiV3Key: 'API V3 密钥',
      privateKeyContent: 'apiclient_key.pem 证书',
      privateCertContent: 'apiclient_cert.perm证书'
    },

    feeRatePlaceholder: '请输入渠道费率',
    appIdPlaceholder: '请输入公众号 APPID',
    mchKeyPlaceholder: '请输入商户密钥',
    keyContentPlaceholder: '请上传 apiclient_cert.p12 证书',
    apiV3KeyPlaceholder: '请输入 API V3 密钥',
    privateKeyContentPlaceholder: '请上传 apiclient_key.pem 证书',
    privateCertContentPlaceholder: '请上传apiclient_cert.perm证书',
    clickUpload: '点击上传',

    rWxDesc1: '请输入渠道费率',
    rWxDesc2: '渠道状态不能为空',
    rWxDesc3: '请传入商户号',
    rWxDesc4: '请输入公众号APPID',
    rWxDesc5: 'API版本不能为空',
    rWxDesc6: '请输入商户密钥',
    rWxDesc7: '请上传 apiclient_cert.p12 证书',
    rWxDesc8: '请上传 apiclient_key.pem 证书',
    rWxDesc9: '请上传 apiclient_cert.perm证 书',
    rWxDesc10: '请上传 api V3 密钥值',

    desc2: '请上传指定格式{fileAccept}文件',
    desc3: '文件大小超过 2MB',

    zfbConfig: {
      appId: '开放平台 APPID',
      serverUrl: '网关地址',
      signType: '算法类型',
      mode: '公钥类型',
      privateKey: '应用私钥',
      alipayPublicKey: '支付宝公钥',
      appCertContent: '商户公钥应用证书',
      alipayPublicCertContent: '支付宝公钥证书',
      rootCertContent: '根证书',
      appIdPlaceholder: '请输入开放平台 APPID'
    },

    privateKeyPlaceholder: '请输入应用私钥',
    alipayPublicKeyPlaceholder: '请输入支付宝公钥',
    appCertContentPlaceholder: '请上传商户公钥应用证书',
    alipayPublicCertContentPlaceholder: '请上传支付宝公钥证书',
    rootCertContentPlaceholder: '请上传根证书',

    zfbTitle1: '创建支付宝支付渠道',
    zfbTitle2: '编辑支付宝支付渠道',
    serverUrl_1: '线上环境',
    serverUrl_2: '沙箱环境',
    mode_1: '公钥模式',
    mode_2: '证书模式',
    rzfb1: '请输入渠道费率',
    rzfb2: '渠道状态不能为空',
    rzfb3: '请输入开放平台上创建的应用的 ID',
    rzfb4: '请传入商户号',
    rzfb5: '请传入网关地址',
    rzfb6: '请传入签名算法类型',
    rzfb7: '公钥类型不能为空',
    rzfb8: '请输入商户私钥',
    rzfb9: '请输入支付宝公钥字符串',
    rzfb10: '请上传商户公钥应用证书',
    rzfb11: '请上传支付宝公钥证书',
    rzfb12: '请上传指定根证书',
    addChannel: '新增渠道'
  }
}
