<template>
  <div class="umv-card">
    <swiper v-bind="SwiperOptions" class="umv-card_banner-swiper">
      <swiper-slide>
        <el-image
          v-if="ifEn"
          class="w-full h-full"
          :src="`${basePath}/Home/UMVcard/en_US/umv_card_banner.webp`"
          alt=""
        />
        <el-image
          v-else
          class="w-full h-full"
          :src="`${basePath}/Home/UMVcard/umv_card_banner.webp`"
          alt=""
        />
      </swiper-slide>
      <swiper-slide>
        <el-image
          v-if="ifEn"
          class="w-full h-full"
          :src="`${basePath}/Home/UMVcard/en_US/umv_card_banner2.webp`"
          alt=""
        />
        <el-image
          v-else
          class="w-full h-full"
          :src="`${basePath}/Home/UMVcard/umv_card_banner2.webp`"
          alt=""
        />
      </swiper-slide>
    </swiper>

    <swiper v-bind="SwiperOptions" class="umv-card_banner-swiper other-banner">
      <swiper-slide>
        <div class="h-804px <2xl:(h-569px)">
          <div class="text-center pt-143px pb-109px text-40px <2xl:(pt-102px pb-78px text-28px)">
            {{ t('home.UMVCard.h1') }}
          </div>
          <div class="flex flex-row justify-center items-center">
            <div class="w-1/2 flex justify-end pr-80px <2xl:(pr-58px)">
              <el-image
                draggable="false"
                class="inline-block scale-animation w-371px h-402px <2xl:(w-264px h-286px)"
                :src="`${basePath}/Home/UMVcard/umv_card_oneCard.webp`"
                alt=""
              />
            </div>
            <div class="w-1/2 pl-80px <2xl:(pl-58px)">
              <div class="text-32px font-600 <2xl:(text-23px)">
                {{ t('home.UMVCard.title1') }}
              </div>
              <div class="text-item mt-31px <2xl:(mt-22px)">
                {{ t('home.UMVCard.title1_item1') }}
              </div>
              <div class="text-item mt-31px <2xl:(mt-22px)">
                {{ t('home.UMVCard.title1_item2') }}
              </div>
              <div class="text-item mt-31px <2xl:(mt-22px)">
                {{ t('home.UMVCard.title1_item3') }}
              </div>
              <div class="text-item mt-31px <2xl:(mt-22px)">
                {{ t('home.UMVCard.title1_item4') }}
              </div>
              <div class="text-item mt-31px <2xl:(mt-22px)">
                {{ t('home.UMVCard.title1_item5') }}
              </div>
              <div class="text-item mt-31px <2xl:(mt-22px)">
                {{ t('home.UMVCard.title1_item6') }}
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>
      <swiper-slide>
        <div class="h-804px <2xl:(h-569px)">
          <div class="text-center pt-143px pb-109px text-40px <2xl:(pt-102px pb-78px text-28px)">
            {{ t('home.UMVCard.title2') }}
          </div>
          <div class="flex flex-row justify-center items-center">
            <div class="w-1/2 flex justify-end pr-80px <2xl:(pr-58px)">
              <el-image
                class="inline-block scale-animation w-371px h-402px <2xl:(w-264px h-286px)"
                :src="`${basePath}/Home/UMVcard/umv_card_oneCard2.webp`"
                alt=""
              />
            </div>
            <div class="w-1/2 pl-80px <2xl:(pl-58px)">
              <div class="text-32px font-600 <2xl:(text-23px)">
                {{ t('home.UMVCard.title2_item1') }}
              </div>
              <div class="mt-31px text-16px leading-20px <2xl:(mt-22px text-14px leading-17px)">
                <div>
                  {{ t('home.UMVCard.title2_item2_1') }}
                </div>
                <div>
                  {{ t('home.UMVCard.title2_item2_2') }}
                </div>
              </div>
              <div class="mt-31px <2xl:(mt-22px)">
                <span class="text-item">
                  {{ t('home.UMVCard.title2_item3') }}
                </span>
                <span class="text-item ml-33px <2xl:(ml-22px)">
                  {{ t('home.UMVCard.title2_item4') }}
                </span>
              </div>
              <div class="mt-31px <2xl:(mt-22px)">
                <span class="text-item">
                  {{ t('home.UMVCard.title2_item5') }}
                </span>
                <span class="text-item ml-33px <2xl:(ml-22px)">
                  {{ t('home.UMVCard.title2_item6') }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper>

    <section class="h-120px flex flex-row justify-center items-center experience-bg <2xl:(h-85px)">
      <div class="mr-56px <2xl:(mr-40px)">
        <span>
          {{ t('home.UMVCard.title3') }}
        </span>
        <span class="text-gold">400-823-8798</span>
      </div>
      <ArrowBtn @click="goDemo()" :btnTitle="t('home.UMVCard.title3_item1')" />
    </section>

    <section
      style="background: #faf9f7"
      class="flex flex-col justify-center items-center pt-72px pb-40px <2xl:(pt-50px pb-55px)"
    >
      <div class="flex flex-row justify-center items-stretch w-[80%] <2xl:(w-[90%])">
        <div
          class="hover-scale w-full bg-white rounded-10px pt-36px pl-40px pr-4px pb-5 <2xl:( rounded-7px pt-26px pl-28px) shadow-xl"
        >
          <el-image
            lazy
            draggable="false"
            class="w-22px h-25px <2xl:(w-16px h-18px)"
            :src="`${basePath}/Home/UMVcard/umv_card_diy.png`"
            alt=""
          />
          <div
            class="mt-20px text-16px leading-15px font-bold <2xl:(mt-14px text-14px leading-12px)"
          >
            {{ t('home.UMVCard.title4') }}
          </div>
          <div class="mt-39px text-16px leading-16px <2xl:(mt-28px text-12px leading-12px)">
            {{ t('home.UMVCard.title4_item1') }}
          </div>
          <div class="mt-19px text-16px leading-16px <2xl:(mt-14px text-12px leading-12px)">
            {{ t('home.UMVCard.title4_item2') }}
          </div>
          <div class="mt-19px text-16px leading-16px <2xl:(mt-14px text-12px leading-12px)">
            {{ t('home.UMVCard.title4_item3') }}
          </div>
          <div class="mt-31px flex flex-wrap text-14px <2xl:(mt-22px text-11px)">
            <div class="capsule-item">
              {{ t('home.UMVCard.title4_item4_1') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title4_item4_2') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title4_item4_3') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title4_item4_4') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title4_item4_5') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title4_item4_6') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title4_item4_7') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title4_item4_8') }}
            </div>
          </div>
        </div>
        <div
          class="hover-scale w-full bg-white rounded-10px pt-36px pl-40px pr-4px mx-50px pb-5 <2xl:( rounded-7px pt-26px pl-28px mx-36px) shadow-xl"
        >
          <el-image
            lazy
            draggable="false"
            class="w-24px h-25px <2xl:(w-17px h-18px)"
            :src="`${basePath}/Home/UMVcard/umv_card_ai.png`"
            alt=""
          />
          <div
            class="mt-20px text-16px leading-15px font-bold <2xl:(mt-14px text-14px leading-12px)"
          >
            {{ t('home.UMVCard.title5') }}
          </div>
          <div
            style="color: #999999"
            class="mt-11px text-16px leading-16px <2xl:(mt-8px text-12px leading-12px)"
          >
            {{ t('home.UMVCard.title5_item1') }}
          </div>
          <el-row class="mt-30px flex flex-row <2xl:(mt-20px)">
            <el-col :span="12" class="ai-desc">
              {{ t('home.UMVCard.title5_item2') }}
            </el-col>
            <el-col :span="12" class="ai-desc">
              {{ t('home.UMVCard.title5_item3') }}
            </el-col>
          </el-row>
          <el-row class="mt-20px <2xl:(mt-12px)">
            <el-col :span="12" class="ai-desc">
              {{ t('home.UMVCard.title5_item4') }}
            </el-col>
            <el-col :span="12" class="ai-desc">
              {{ t('home.UMVCard.title5_item5') }}
            </el-col>
          </el-row>
          <el-row class="mt-20px <2xl:(mt-12px)">
            <el-col :span="16" class="ai-desc">
              {{ t('home.UMVCard.title5_item6') }}
            </el-col>
          </el-row>
          <div class="mt-25px flex flex-wrap text-14px <2xl:(mt-20px text-11px)">
            <div class="capsule-item">
              {{ t('home.UMVCard.title5_item7_1') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title5_item7_2') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title5_item7_3') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title5_item7_4') }}
            </div>
            <div class="capsule-item">
              {{ t('home.UMVCard.title5_item7_5') }}
            </div>
          </div>
        </div>
        <div
          class="hover-scale w-full bg-white rounded-10px pt-36px pl-40px pr-4px pb-5 <2xl:( rounded-7px pt-26px pl-28px) shadow-xl"
        >
          <el-image
            lazy
            draggable="false"
            class="w-24px h-24px <2xl:(w-17px h-17px)"
            :src="`${basePath}/Home/UMVcard/umv_card_youmi.png`"
            alt=""
          />
          <div
            class="mt-20px text-16px leading-15px font-bold <2xl:(mt-14px text-14px leading-12px)"
          >
            {{ t('home.UMVCard.title6') }}
          </div>
          <div class="mt-39px text-16px leading-16px <2xl:(mt-28px text-12px leading-12px)">
            {{ t('home.UMVCard.title6_item1') }}
          </div>
          <div class="mt-19px text-16px leading-16px <2xl:(mt-14px text-12px leading-12px)">
            {{ t('home.UMVCard.title6_item2') }}
          </div>
          <el-row class="mt-39px flex text-14px items-center <2xl:(mt-28px text-11px)">
            <el-col :span="12" class="flex flex-col items-start text-center">
              <div class="capsule-item w-full">
                {{ t('home.UMVCard.title6_item3_1') }}
              </div>
              <div class="capsule-item w-full">
                {{ t('home.UMVCard.title6_item3_2') }}
              </div>
              <div class="capsule-item w-full !p-0">
                {{ t('home.UMVCard.title6_item3_3') }}
              </div>
            </el-col>
            <el-col :span="12" class="flex flex-col justify-center items-center">
              <el-image
                lazy
                class="w-104px h-104px <2xl:(w-74px h-74px)"
                draggable="false"
                :src="`${basePath}/Home/UMVcard/umv_card_qrcode.png`"
                alt=""
              />
              <div class="mt-12px text-16px leading-16px <2xl:(mt-9px text-12px leading-12px)">
                {{ t('home.UMVCard.title6_item4') }}
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import ArrowBtn from './ArrowBtn.vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'

const { ifEn, t } = useI18n()
const basePath = import.meta.env.BASE_URL === '/' ? '' : import.meta.env.BASE_URL

const SwiperModules = [Pagination, Autoplay]
const SwiperOptions = {
  loop: true,
  speed: 1000,
  modules: SwiperModules,
  pagination: {
    enabled: true,
    clickable: true,
    renderBullet: function (index, className) {
      return `<span class="${className}"></span>`
    }
  },
  autoplay: {
    delay: 4000,
    disableOnInteraction: false
    // pauseOnMouseEnter: true 鼠标悬浮的时候停止自动轮播
  }
}

const goDemo = () => {
  window.open('https://client-demo.umvcard.com/index.html')
}
</script>

<style lang="scss" scoped>
@keyframes animation-scale {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  to {
    transform: scale(1);
  }
}

.scale-animation {
  animation: animation-scale 3s infinite;
}
//鼠标悬浮变大
.hover-scale {
  transition: all 0.3s;
  &:hover {
    transform: scale(1.1);
  }
}

.umv-card {
  .text-item {
    color: #333333;
    font-size: 16px;
  }
  .text-item::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('/Home/UMVcard/umv_card_listIcon.png') no-repeat;
    background-size: 100% 100%;
    margin-right: 11px;
  }

  .bg-white {
    background: #ffffff;
  }
  .experience-bg {
    background: url('/Home/UMVcard/umv_card_experience.png') center/100% 100% no-repeat;
  }

  .capsule-item {
    color: #666666;
    background: #f6f6f6;
    height: 38px;
    line-height: 38px;
    padding: 0 20px;
    margin-top: 8px;
    margin-right: 8px;
    border-radius: 4px;
  }

  .ai-desc {
    flex-shrink: 0;
    font-size: 16px;
    color: #333333;
  }
  .ai-desc::before {
    content: '';
    display: inline-block;
    margin-right: 6px;
    width: 4px;
    height: 4px;
    background: #0b55d4;
    vertical-align: middle;
    margin-bottom: 4px;
    opacity: 0.4;
    border-radius: 50%;
  }
}

@media (max-width: 1535.9px) {
  .umv-card {
    .text-item {
      font-size: 14px;
    }
    .text-item::before {
      width: 9px;
      height: 9px;
      background: url('/Home/UMVcard/umv_card_listIcon.png') center/100% 100% no-repeat;
      margin-right: 8px;
    }

    .capsule-item {
      height: 27px;
      line-height: 27px;
      padding: 0 14px;
      margin-top: 6px;
      margin-right: 6px;
      border-radius: 3px;
    }

    .ai-desc {
      font-size: 12px;
    }

    .ai-desc::before {
      margin-right: 4px;
      width: 3px;
      height: 3px;
      margin-bottom: 4px;
    }
  }
}
</style>
<style lang="scss">
.umv-card_banner-swiper {
  .swiper-pagination {
    text-align: left;
    padding-left: 13.5%;
    bottom: 10%;
  }

  .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: rgba(254, 163, 11, 0.3);
    border-radius: 2px;
    opacity: 1;
  }

  .swiper-pagination-bullet-active {
    background: #fea30b;
  }
}

.other-banner {
  .swiper-pagination {
    text-align: center;
    padding-left: 0;
    bottom: 5%;
  }
}

@media (max-width: 1535.9px) {
  .umv-card_banner-swiper {
    .swiper-pagination-bullet {
      width: 7px;
      height: 7px;
      border-radius: 1px;
    }
  }
}
</style>
