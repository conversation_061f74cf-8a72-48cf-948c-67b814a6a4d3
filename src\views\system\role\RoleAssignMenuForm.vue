<template>
  <Dialog v-model="dialogVisible" :title="t('system.role.menuPerm')">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :label-width="ifEn ? '150px' : '80px'"
    >
      <el-form-item :label="t('system.role.name')">
        <el-tag>{{ formData.name }}</el-tag>
      </el-form-item>
      <el-form-item :label="t('system.role.code')">
        <el-tag>{{ formData.code }}</el-tag>
      </el-form-item>
      <el-form-item :label="t('system.role.menuPerm')">
        <el-card class="cardHeight">
          <template #header>
            {{ t('system.role.allSelectOrNot') }}
            <el-switch
              v-model="treeNodeAll"
              :active-text="t('common.yes')"
              :inactive-text="t('common.no')"
              inline-prompt
              @change="handleCheckedTreeNodeAll"
              :disabled="RoleType === 1"
            />
            {{ t('system.role.expandAllOrNot') }}
            <el-switch
              v-model="menuExpand"
              :active-text="t('common.expand')"
              :inactive-text="t('common.shrink')"
              inline-prompt
              @change="handleCheckedTreeExpand"
            />
          </template>
          <el-tree
            ref="treeRef"
            :data="menuOptions"
            :props="defaultProps"
            :check-strictly="isCheckStrictly"
            :empty-text="t('common.emptyText')"
            node-key="id"
            :show-checkbox="RoleType !== 1"
          >
            <template #default="{ node }">
              <!-- <span>{{ `${node.label}(唯一标识)` }}</span> -->
              <span>{{ `${node.label}` }}</span>
              <!-- <el-tooltip :content="`描述`" placement="top">
                <Icon :size="16" icon="ep:info-filled" class="ml-1px relative" />
              </el-tooltip> -->
            </template>
          </el-tree>
        </el-card>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm" v-if="RoleType !== 1">{{
        t('common.ok')
      }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemRoleAssignMenuForm'
})

import { defaultProps, handleTree } from '@/utils/tree'
import * as RoleApi from '@/api/system/role'
import * as MenuApi from '@/api/system/menu'
import * as PermissionApi from '@/api/system/permission'

const { t, ifEn } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const isCheckStrictly = ref(true)

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = reactive({
  id: 0,
  name: '',
  code: '',
  menuIds: []
})
const formRef = ref() // 表单 Ref
const menuOptions = ref<any[]>([]) // 菜单树形结构
const menuExpand = ref(false) // 展开/折叠
const treeRef = ref() // 菜单树组件 Ref
const treeNodeAll = ref(false) // 全选/全不选
const RoleType = ref(2) //1是系统角色，2是自定义角色

/** 打开弹窗 */
const open = async (row: RoleApi.RoleVO, roleType: number) => {
  formLoading.value = true

  console.log('roleType', roleType)
  RoleType.value = roleType
  dialogVisible.value = true
  resetForm()
  let obj = row.id
    ? {
        roleId: row.id,
        applicationId: row.pappId || row.appId
      }
    : undefined
  console.log('obj09090909', obj)
  // 加载 Menu 列表。注意，必须放在前面，不然下面 setChecked 没数据节点
  menuOptions.value = handleTree(
    await MenuApi.getAppSimpleMenusList(obj).then((res) =>
      res.filter((item) => !/^(demo\d+|demo)$/.test(item.name))
    ) // todo:后续菜单功能完善,要剔除 2024-09-30
  ).filter((item) => item.status === 0)

  // 设置数据
  formData.id = row.id
  formData.name = row.name
  formData.code = row.code
  try {
    formData.value.menuIds = await PermissionApi.getRoleMenuList(row.id)
    // 设置选中
    formData.value.menuIds.forEach((menuId: number) => {
      treeRef.value.setChecked(menuId, true, false)
    })
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      roleId: formData.id,
      menuIds: [
        ...(treeRef.value.getCheckedKeys(false) as unknown as Array<number>), // 获得当前选中节点
        ...(treeRef.value.getHalfCheckedKeys() as unknown as Array<number>) // 获得半选中的父节点
      ]
    }
    await PermissionApi.assignRoleMenu(data)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  // 重置选项
  treeNodeAll.value = false
  menuExpand.value = false
  // 重置表单
  formData.value = {
    id: 0,
    name: '',
    code: '',
    menuIds: []
  }
  treeRef.value?.setCheckedNodes([])
  formRef.value?.resetFields()
}

/** 全选/全不选 */
const handleCheckedTreeNodeAll = () => {
  isCheckStrictly.value = false
  nextTick(() => {
    treeRef.value.setCheckedNodes(treeNodeAll.value ? menuOptions.value : [])
    isCheckStrictly.value = true
  })
}

/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
</style>
