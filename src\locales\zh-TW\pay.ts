/**支付管理 */
export default {
  merchant: {
    no: '商戶號',
    name: '商戶全稱',
    shortName: '商戶簡稱',
    status: '開啟狀態',
    remark: '備註',
    createTime: '創建時間',
    noPlaceholder: '請輸入商戶號',
    namePlaceholder: '請輸入商戶全稱',
    shortNamePlaceholder: '請輸入商戶簡稱',
    remarkPlaceholder: '請輸入備註',
    statusPlaceholder: '字典狀態',
    id: '商戶編號',
    merchantXls: '商戶信息',
    desc1: '確認要{text}{name}商戶嗎？',
    rMsg1: '商戶名稱不能為空',
    rMsg2: '商戶簡稱不能為空',
    rMsg3: '狀態不能為空',
    desc2: '新增商戶信息',
    desc3: '修改商戶信息'
  },
  order: {
    merchantId: '所屬商戶',
    appId: '應用',
    channelCode: '渠道',
    merchantOrderId: '商戶訂單編號',
    channelOrderNo: '渠道訂單號',
    status: '支付狀態',
    refundStatus: '退款狀態',
    notifyStatus: '回調狀態',
    createTime: '創建時間',
    merchantOrderIdPlaceholder: '請輸入商戶訂單編號',
    channelOrderNoPlaceholder: '請輸入渠道訂單號',
    merchantIdPlaceholder: '請選擇所屬商戶',
    appIdPlaceholder: '請選擇應用',
    channelCodePlaceholder: '請輸入渠道',
    statusPlaceholder: '請選擇支付狀態',
    refundStatusPlaceholder: '請選擇退款狀態',
    notifyStatusPlaceholder: '請選擇訂單回調商戶狀態',
    id: '編號',
    payMerchantName: '商戶名稱',
    appName: '應用名稱',
    channelCodeName: '渠道名稱',
    subject: '商品標題',
    body: '商品描述',
    notifyUrl: '異步通知地址',
    amount: '支付金額',
    channelFeeAmount: '手續金額',
    refundAmount: '退款金額',
    bizNotifyStatusName: '回調狀態',
    successTime: '支付時間',
    payOrder: '支付訂單',
    merchant: '商戶',
    pay: '支付',
    no: '支付訂單號',
    price: '金額',
    channelFeePrice: '手續費',
    channelFeeRate: '手續費比例',
    notifyUrl2: '回調地址',
    expireTime: '失效時間',
    notifyTime: '通知時間',
    payChannel: '支付渠道',
    userIp: '支付IP',
    refundTimes: '退款次數',
    desc1: '支付通道異步回調內容',
    name: '條形碼',
    valuePlaceholder: '請輸入條形碼',
    payInfo: '支付信息',
    orderId: '支付單號',
    body2: '商品內容',
    expireTime2: '過期時間',
    returnUrl: '重定向地址',
    desc2: '選擇支付寶支付',
    desc3: '提交支付中...',
    desc4: '選擇微信支付',
    desc5: '或使用',
    desc6: '(掃碼槍/掃碼盒)',
    desc7: '掃碼',
    paySure: '確認支付',
    desc8: '未傳遞支付單號，無法查看對應的支付信息',
    desc9: '支付訂單不存在，請檢查！',
    desc10: '支付訂單不處於待支付狀態，請檢查！',
    desc11: '"支付寶"條碼支付',
    desc12: '未實現該支付方式:',
    payWin: '支付窗口',
    desc13: '請使用手機瀏覽器“掃一掃”',
    desc14: '請使用“支付寶”掃一掃”掃碼支付',
    desc15: '請使用微信“掃一掃”掃碼支付',
    desc16: '支付成功！',
    desc17: '支付已關閉！'
  },
  refund: {
    merchantId: '所屬商戶',
    appId: '應用',
    channelCode: '渠道',
    type: '退款類型',
    merchantRefundNo: '支付訂單號',
    status: '退款狀態',
    notifyStatus: '退款回調狀態',
    createTime: '創建時間',
    merchantRefundNoPlaceholder: '請輸入商戶退款訂單號',
    merchantIdPlaceholder: '請選擇所屬商戶',
    appIdPlaceholder: '請選擇應用信息',
    channelCodePlaceholder: '請輸入渠道',
    typePlaceholder: '請選擇退款類型',
    statusPlaceholder: '請選擇退款狀態',
    notifyStatusPlaceholder: '請選擇通知商戶退款結果的回調狀態',
    id: '編號',
    payMerchantName: '商戶名稱',
    appName: '應用名稱',
    channelCodeName: '渠道名稱',
    orderId: '交易訂單號',
    merchantOrderId: '商戶訂單編號',
    payAmount: '支付金額(元)',
    refundAmount: '退款金額(元)',
    bizNotifyStatusName: '回調狀態',
    reason: '退款原因',
    successTime: '退款成功時間',

    status_0: '未退款',
    status_10: '退款成功',
    status_20: '退款失敗',
    merchantOrderId2: '商戶訂單號',
    merchantRefundId: '商戶退款訂單號',
    merchantRefundNoTag1: '交易',
    merchantRefundNoTag2: '渠道',
    payPrice: '支付金額',
    refundPrice: '退款金額',
    expireTime: '退款失效時間',
    channelCode2: '支付渠道',
    userIp: '支付IP',
    notifyUrl: '回調地址',
    notifyStatus2: '回調狀態',
    notifyTime: '回調時間',
    channelOrderNo: '渠道訂單號',
    channelRefundNo: '渠道退款單號',
    channelErrorCode: '渠道錯誤碼',
    channelErrorMsg: '渠道錯誤碼描述',
    channelNotifyData: '渠道額外參數'
  },

  app: {
    name: '應用名',
    contactName: '商戶名稱',
    status: '開啟狀態',
    createTime: '創建時間',
    namePlaceholder: '請輸入應用名',
    contactNamePlaceholder: '請輸入商戶名稱',
    statusPlaceholder: '請選擇開啟狀態',
    id: '商戶號',
    code: '應用編碼',
    payMerchantName: '商戶名稱',
    alipayCfg: '支付寶配置',
    wxH5Cfg: '微信配置',
    payAppXls: '支付應用信息',
    merchantId: '所屬商戶',
    type: '商戶類型',
    payNotifyUrl: '支付結果的回調地址',
    refundNotifyUrl: '退款結果的回調地址',
    remark: '備註',
    codePlaceholder: '請輸入應用編號',
    payNotifyUrlPlaceholder: '請輸入支付結果的回調地址',
    refundNotifyUrlPlaceholder: '請輸入退款結果的回調地址',
    remarkPlaceholder: '請輸入備註',
    merchantIdPlaceholder: '請選擇所屬商戶',
    desc1: '應用編號即支付寶和微信上的商戶號',
    type_1: '支付寶',
    type_2: '微信',
    type_3: '徽商銀行',
    rDesc1: '應用名不能為空',
    rDesc2: '長度在 1 到 30 個字符',
    rDesc3: '應用編碼不能為空',
    rDesc4: '長度在 1 到 50 個字符',
    rDesc5: '商戶類型不能為空',
    rDesc6: '開啟狀態不能為空',
    rDesc7: '支付結果的回調地址不能為空',
    rDesc8: '退款結果的回調地址不能為空',
    rDesc9: '商戶編號不能為空',
    newApp: '新增應用',
    editApp: '編輯應用',
    feeRate: '渠道費率',

    title1: '創建微信支付渠道',
    title2: '編輯微信支付渠道',

    config: {
      appId: '公眾號 APPID',
      mchId: '商戶號',
      status: '渠道狀態',
      apiVersion: 'API 版本',
      mchKey: '商戶密鑰',
      keyContent: 'apiclient_cert.p12 證書',
      apiV3Key: 'API V3 密鑰',
      privateKeyContent: 'apiclient_key.pem 證書',
      privateCertContent: 'apiclient_cert.perm證書'
    },

    feeRatePlaceholder: '請輸入渠道費率',
    appIdPlaceholder: '請輸入公眾號 APPID',
    mchKeyPlaceholder: '請輸入商戶密鑰',
    keyContentPlaceholder: '請上傳 apiclient_cert.p12 證書',
    apiV3KeyPlaceholder: '請輸入 API V3 密鑰',
    privateKeyContentPlaceholder: '請上傳 apiclient_key.pem 證書',
    privateCertContentPlaceholder: '請上傳apiclient_cert.perm證書',
    clickUpload: '點擊上傳',

    rWxDesc1: '請輸入渠道費率',
    rWxDesc2: '渠道狀態不能為空',
    rWxDesc3: '請傳入商戶號',
    rWxDesc4: '請輸入公眾號APPID',
    rWxDesc5: 'API版本不能為空',
    rWxDesc6: '請輸入商戶密鑰',
    rWxDesc7: '請上傳 apiclient_cert.p12 證書',
    rWxDesc8: '請上傳 apiclient_key.pem 證書',
    rWxDesc9: '請上傳 apiclient_cert.perm證 書',
    rWxDesc10: '請上傳 api V3 密鑰值',

    desc2: '請上傳指定格式{fileAccept}文件',
    desc3: '文件大小超過 2MB',

    zfbConfig: {
      appId: '開放平臺 APPID',
      serverUrl: '網關地址',
      signType: '算法類型',
      mode: '公鑰類型',
      privateKey: '應用私鑰',
      alipayPublicKey: '支付寶公鑰',
      appCertContent: '商戶公鑰應用證書',
      alipayPublicCertContent: '支付寶公鑰證書',
      rootCertContent: '根證書',
      appIdPlaceholder: '請輸入開放平臺 APPID'
    },

    privateKeyPlaceholder: '請輸入應用私鑰',
    alipayPublicKeyPlaceholder: '請輸入支付寶公鑰',
    appCertContentPlaceholder: '請上傳商戶公鑰應用證書',
    alipayPublicCertContentPlaceholder: '請上傳支付寶公鑰證書',
    rootCertContentPlaceholder: '請上傳根證書',

    zfbTitle1: '創建支付寶支付渠道',
    zfbTitle2: '編輯支付寶支付渠道',
    serverUrl_1: '線上環境',
    serverUrl_2: '沙箱環境',
    mode_1: '公鑰模式',
    mode_2: '證書模式',
    rzfb1: '請輸入渠道費率',
    rzfb2: '渠道狀態不能為空',
    rzfb3: '請輸入開放平臺上創建的應用的 ID',
    rzfb4: '請傳入商戶號',
    rzfb5: '請傳入網關地址',
    rzfb6: '請傳入簽名算法類型',
    rzfb7: '公鑰類型不能為空',
    rzfb8: '請輸入商戶私鑰',
    rzfb9: '請輸入支付寶公鑰字符串',
    rzfb10: '請上傳商戶公鑰應用證書',
    rzfb11: '請上傳支付寶公鑰證書',
    rzfb12: '請上傳指定根證書',
    addChannel: '新增渠道'
  }
}
