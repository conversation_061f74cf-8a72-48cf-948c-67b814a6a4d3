<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      :label-width="ifEn ? '180px' : '80px'"
      class="mt-16px"
    >
      <el-form-item :label="t('system.role.roleForm.name')" prop="name">
        <el-input
          v-model="formData.name"
          :placeholder="t('system.role.roleForm.namePlaceholder')"
          :disabled="formType === 'detail'"
        />
      </el-form-item>
      <el-form-item :label="t('system.role.roleForm.code')" prop="code">
        <el-input
          v-model="formData.code"
          :placeholder="t('system.role.roleForm.codePlaceholder')"
          :disabled="formType === 'detail'"
        />
      </el-form-item>
      <el-form-item :label="t('system.role.roleForm.clientName')">
        <el-input
          v-model="formData.clientName"
          :placeholder="t('system.role.roleForm.clientNamePlaceholder')"
          disabled
        />
      </el-form-item>
      <el-form-item :label="t('system.role.roleForm.sort')" prop="sort">
        <el-input
          v-model="formData.sort"
          :placeholder="t('system.role.roleForm.sortPlaceholder')"
          :disabled="formType === 'detail'"
        />
      </el-form-item>
      <el-form-item :label="t('system.role.roleForm.applicationCode')" prop="applicationCode">
        <el-select
          v-model="formData.applicationCode"
          clearable
          :disabled="appDisabled"
          collapse-tags
          :placeholder="t('system.role.roleForm.applicationCodePlaceholder')"
        >
          <el-option
            v-for="item in props.tenantAppList"
            :key="item.applicationCode"
            :label="item.name"
            :value="item.applicationCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('system.role.roleForm.status')" prop="status">
        <el-select
          v-model="formData.status"
          clearable
          :placeholder="t('system.role.roleForm.statusPlaceholder')"
          :disabled="formType === 'detail'"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('system.role.roleForm.remark')" prop="remark">
        <el-input
          v-model="formData.remark"
          :placeholder="t('system.role.roleForm.remarkPlaceholder')"
          type="textarea"
          :disabled="formType === 'detail'"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
        :disabled="formLoading"
        type="primary"
        @click="submitForm"
        v-if="formType !== 'detail'"
        >{{ t('common.ok') }}</el-button
      >
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemRoleForm'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
//import { TenantApplicationVO } from '@/api/system/tenant'
import * as RoleApi from '@/api/system/role'

const { t, ifEn } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改；add - 新建
const appDisabled = ref(false)
const formData: any = ref({
  id: undefined,
  name: '',
  code: '',
  sort: undefined,
  status: CommonStatusEnum.ENABLE,
  remark: '',
  applicationCode: undefined,
  parentId: undefined
})
const formRules = reactive({
  name: [{ required: true, message: t('system.role.roleForm.rDesc1'), trigger: 'blur' }],
  applicationCode: [{ required: true, message: t('system.role.roleForm.rDesc2'), trigger: 'blur' }],
  code: [{ required: true, message: t('system.role.roleForm.rDesc3'), trigger: 'change' }],
  sort: [{ required: true, message: t('system.role.roleForm.rDesc4'), trigger: 'change' }],
  status: [{ required: true, message: t('system.role.roleForm.rDesc4'), trigger: 'change' }],
  remark: [{ required: false, message: t('system.role.roleForm.rDesc5'), trigger: 'blur' }]
})

const props = defineProps({
  tenantAppList: {
    type: Array,
    default: () => []
  }
})

const applicationCode = ref()

const pappId = ref('')

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  appDisabled.value = false
  // 修改时，设置数据
  if (id) {
    appDisabled.value = true
    formLoading.value = true
    try {
      const tempFormData = await RoleApi.getRole(id)
      if (formType.value === 'add') {
        formData.value.applicationCode = tempFormData.applicationCode
        formData.value.parentId = id
        // pappId.value = tempFormData.pappId
        formData.value.clientName = tempFormData.clientName
      } else {
        formData.value = tempFormData
      }
    } finally {
      formLoading.value = false
    }
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    code: '',
    sort: undefined,
    status: CommonStatusEnum.ENABLE,
    applicationCode: undefined,
    remark: ''
  }
  formRef.value?.resetFields()
}
defineExpose({ open, applicationCode }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as RoleApi.RoleVO
    if (formType.value === 'create') {
      await RoleApi.createRole(data)
      message.success(t('common.createSuccess'))
    } else if (formType.value === 'add') {
      data.pappId = pappId.value
      await RoleApi.createRole(data)
      message.success(t('common.createSuccess'))
    } else {
      await RoleApi.updateRole(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    applicationCode.value = formData.value.applicationCode
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>
