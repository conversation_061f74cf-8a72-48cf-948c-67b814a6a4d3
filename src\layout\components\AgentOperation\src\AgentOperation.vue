<!--
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-03 14:16:33
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2024-12-16 09:48:02
 * @Description:  代客操作功能
-->
<template>
  <div :class="prefixCls">
    <div
      class="flex items-center text-[white] cursor-pointer"
      v-if="agentOperationStore.agentOperationMode"
    >
      <span @click="agentOperationStore.ifShowDrawer = true">
        <span v-if="agentOperationStore.getCurrentCustomer.customerName">
          代客操作模式，当前客户： {{ agentOperationStore.getCurrentCustomer.customerName }}
          <el-tag
            v-if="agentOperationStore.isInternational"
            type="danger"
            size="small"
            effect="dark"
            class="ml-1"
          >
            国际版用户
          </el-tag>
        </span>
        <span v-else>请选择代操作客户</span>
      </span>

      <el-switch
        v-model="agentOperationStore.agentOperationMode"
        class="ml-2"
        inline-prompt
        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
        active-text="代客操作模式"
        inactive-text="关闭"
        @change="agentOperationStore.changeAgentOperationMode"
      />
    </div>
  </div>

  <!-- 弹出框 -->
  <AgentOperationDrawer />
</template>
<script setup lang="ts">
defineOptions({
  name: 'AgentOperation'
})

// const { t } = useI18n()

import { useAgentOperationStore } from '@/store/modules/agentOperation'
const agentOperationStore = useAgentOperationStore()

import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('agentOperation')

defineProps({
  color: propTypes.string.def('')
})

//代客操作弹出框
import AgentOperationDrawer from './components/AgentOperationDrawer.vue'
</script>
