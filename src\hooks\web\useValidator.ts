/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 16:01:28
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-11-20 11:44:13
 * @Description:
 *
 */

//配合elementPlus的form表单校验使用实例
// import { useValidator, allPattern } from '@/hooks/web/useValidator'
// const rules = reactive<FormRules<typeof ruleForm>>({
//   demoTest: [
//     {
//       validator: useValidator(allPattern.emailPattern).byPattern,
//       message: '邮箱格式不正确',
//       trigger: 'blur'
//     }
//   ]
// })

import { useI18n } from '@/hooks/web/useI18n'
import * as patternUtils from '@/utils/pattern'

type Callback = (error?: string | Error | undefined) => void

interface LengthRange {
  min: number
  max: number
  message: string
}

/**导出所有正则 */
export const allPattern = {
  ...patternUtils
}

export const useValidator = (pattern?: RegExp) => {
  const { t } = useI18n()
  const required = (message?: string) => {
    return {
      required: true,
      message: message || t('common.required')
    }
  }

  const byPattern = (rule: any, value: any, callback: any) => {
    // 密码不能是特殊字符
    if (!pattern?.test(value)) {
      callback(new Error(t('sys.hooks.web.validfail')))
    } else {
      callback()
    }
  }

  const lengthRange = (val: any, callback: Callback, options: LengthRange) => {
    const { min, max, message } = options
    if (val.length < min || val.length > max) {
      callback(new Error(message))
    } else {
      callback()
    }
  }

  const notSpace = (val: any, callback: Callback, message: string) => {
    // 用户名不能有空格
    if (val.indexOf(' ') !== -1) {
      callback(new Error(message))
    } else {
      callback()
    }
  }

  const notSpecialCharacters = (val: any, callback: Callback, message: string) => {
    // 密码不能是特殊字符
    if (/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/gi.test(val)) {
      callback(new Error(message))
    } else {
      callback()
    }
  }

  // 两个字符串是否想等
  const isEqual = (val1: string, val2: string, callback: Callback, message: string) => {
    if (val1 === val2) {
      callback()
    } else {
      callback(new Error(message))
    }
  }

  return {
    required,
    byPattern,
    lengthRange,
    notSpace,
    notSpecialCharacters,
    isEqual
  }
}
