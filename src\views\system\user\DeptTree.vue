<template>
  <div class="head-container">
    <el-input
      v-model="deptName"
      class="mb-20px"
      clearable
      :placeholder="t('system.user.deptNamePlaceholder')"
    >
      <template #prefix>
        <icon-ep-search />
      </template>
    </el-input>
  </div>
  <div class="head-container">
    <el-tree
      ref="treeRef"
      :data="deptList"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      default-expand-all
      highlight-current
      node-key="id"
      :current-node-key="currentNodeKey"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        <span @dblclick.stop="handleNodeDblClick(data)" class="el-tree-node__label">{{
          node.label
        }}</span>
      </template>
    </el-tree>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SystemUserDeptTree'
})

import { ElTree } from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleTree } from '@/utils/tree'
import { debounce } from 'lodash-es'
const { t } = useI18n() // 国际化

const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()
const currentNodeKey = ref<number | undefined>(undefined) // 当前选中节点的key
const isDoubleClicking = ref(false) // 标记是否正在双击

/** 获得部门树 */
const getTree = async () => {
  const res = await DeptApi.getSimpleDeptList()
  deptList.value = []
  deptList.value.push(...handleTree(res))
  console.log('deptList', deptList.value)
  ;(!res || deptList.value.length > 0) &&
    deptList.value.unshift({
      children: undefined,
      id: -1,
      name: t('system.user.desc1') //'未分配部门'
    })
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 - 使用debounce防抖 */
const handleNodeClick = debounce(async (row: { [key: string]: any }) => {
  // 如果正在双击中，不处理单击事件
  if (isDoubleClicking.value) {
    isDoubleClicking.value = false
    return
  }

  currentNodeKey.value = row.id
  emits('node-click', row)
}, 250)

/** 处理部门被双击 */
const handleNodeDblClick = async (row: { [key: string]: any }) => {
  // 标记正在双击中，阻止单击事件
  isDoubleClicking.value = true

  // 如果双击的是当前选中的节点，则取消选中
  if (currentNodeKey.value === row.id) {
    currentNodeKey.value = undefined
    treeRef.value?.setCurrentKey(undefined)
    emits('node-dblclick', undefined)
  }
}

/** 重置当前选中的节点 */
const resetCurrentKey = () => {
  currentNodeKey.value = undefined
  treeRef.value?.setCurrentKey(undefined)
}

// 向父组件暴露方法
defineExpose({
  resetCurrentKey
})

const emits = defineEmits(['node-click', 'node-dblclick'])

watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 */
onMounted(async () => {
  await getTree()
})
</script>
