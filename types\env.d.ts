/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 13:59:23
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-06-25 11:00:38
 * @Description:
 */
/// <reference types="vite/client" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 定义一个类型别名，表示是否国际化  zh_CN、zh_HK、en_US、en_CA
type International = 'zh_CN' | 'en_US'

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_CLIENT: string
  readonly VITE_PORT: number
  readonly VITE_OPEN: string
  readonly VITE_DEV: string
  readonly VITE_APP_CAPTCHA_ENABLE: string
  readonly VITE_APP_TENANT_ENABLE: string
  readonly VITE_BASE_URL: string
  readonly VITE_UPLOAD_URL: string
  readonly VITE_API_URL: string
  readonly VITE_BASE_PATH: string
  readonly VITE_DROP_DEBUGGER: string
  readonly VITE_DROP_CONSOLE: string
  readonly VITE_SOURCEMAP: string
  readonly VITE_OUT_DIR: string
  readonly VITE_APP_INTERNATIONAL: International //是否国际版
  readonly VITE_SERVICE_DEV: string
  readonly VITE_SERVICE_SIT: string
  readonly VITE_SERVICE_UAT: string
  readonly VITE_SERVICE_PRO: string
  readonly VITE_SERVICE_INTERNATIONAL_PRO: string
  readonly VITE_CLIENT_DEV: string
  readonly VITE_CLIENT_SIT: string
  readonly VITE_CLIENT_UAT: string
  readonly VITE_CLIENT_PRO: string
  readonly VITE_CLIENT_INTERNATIONAL_PRO: string
  readonly VITE_MANAGE_DEV: string
  readonly VITE_MANAGE_SIT: string
  readonly VITE_MANAGE_UAT: string
  readonly VITE_MANAGE_PRO: string
  readonly VITE_SALE_DEV: string
  readonly VITE_SALE_SIT: string
  readonly VITE_SALE_UAT: string
  readonly VITE_SALE_PRO: string
}

declare global {
  interface ImportMeta {
    readonly env: ImportMetaEnv
  }
}
