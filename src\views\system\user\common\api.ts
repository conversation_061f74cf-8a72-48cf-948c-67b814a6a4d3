import request from '@/config/axios'

// 加载未分配给用户的角色分页列表
import type { NotOfUserReqParams, NotOfUserRes } from './types'
export const getRoleListNotOfUser = (
  userId,
  params: NotOfUserReqParams
): PromiseListWithTotal<NotOfUserRes> => {
  return request.get({ url: `/system/role/page/not-of-user/${userId}`, params })
}

//加载已分配给用户的角色分页列表
import type { OfUserReqParams, OfUserRes } from './types'

export const getRoleListOfUser = (
  userId,
  params: OfUserReqParams
): PromiseListWithTotal<OfUserRes> => {
  return request.get({ url: `/system/role/page/of-user/${userId}`, params })
}
//批量分配用户角色
export const setRolesOfUser = (userId, data: string[]): Promise<boolean> => {
  return request.put({
    url: `/system/permission/add-user-role/${userId}`,
    data
  })
}

//批量删除用户角色
export const removeRolesOfUser = (userId, data: string[]): Promise<boolean> => {
  return request.delete({
    url: `/system/permission/remove-user-role/${userId}`,
    data
  })
}
