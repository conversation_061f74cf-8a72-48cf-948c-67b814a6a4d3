<template>
  <!-- 搜索 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="130px"
    >
      <el-form-item label="租户名" prop="name">
        <!-- <CustomerSelect
          v-model:customer-name="queryParams.name"
          isSearchByName
          @keyup.enter="handleQuery"
          class="!w-240px"
        /> -->
        <el-input
          v-model.trim="queryParams.name"
          placeholder="请输入租户名"
          clearable
          :disabled="loading"
        />
      </el-form-item>
      <el-form-item
        label="业务端"
        prop="code"
        :rules="{ message: '选择业务端', required: true, trigger: ['blur', 'change'] }"
      >
        <el-select v-model="queryParams.code" class="!w-240px" clearable :disabled="loading">
          <el-option
            v-for="(item, index) in clientList"
            :key="index"
            :label="`${item.name}（${item.code}）`"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary" :disabled="loading"> 搜索 </el-button>
        <el-button @click="resetQuery" :disabled="loading"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap ifTable v-loading="loading">
    <el-table :data="list">
      <el-table-column label="序号" type="index" width="100px" />
      <el-table-column label="租户名">
        <template #default="{ row }">
          <div v-html="handleTenantStyle(row.tenantArr)"></div>
        </template>
      </el-table-column>
      <el-table-column label="租户数">
        <template #default="{ row }">
          {{ row.tenantArr.length }}
        </template>
      </el-table-column>
      <el-table-column label="菜单数">
        <template #default="{ row }">
          {{ row.menu.length }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button :disabled="row.menu.length === 0" link type="primary" @click="preview(row)"
            >查看菜单</el-button
          >
          <el-button link type="primary" @click="previewTenant(row)">查看租户</el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
  <previewMenuDialog ref="previewMenuDialogRef" />
  <previewTenantDialog ref="previewTenantDialogRef" />
</template>
<script setup lang="ts">
defineOptions({
  name: 'MenuPreview'
})

import previewMenuDialog from './components/previewMenuDialog.vue'
import previewTenantDialog from './components/previewTenantDialog.vue'
//获取客户端列表
import { useHook } from './common/useHook'
const { clientList, getClientList } = useHook()

import { getAllTenantMenus, getGroupTenantMenus } from '@/api/system/menu/index'

// 目前过滤掉所有非客户端的业务端搜索
const handledClient = computed(() => {
  return clientList.value.filter((el) => {
    return el.code.indexOf('UMVCLI') !== -1 || el.code.indexOf('UMVCL') !== -1
  })
})

const queryParams: Ref<any> = ref({
  name: undefined,
  code: 'UMVCLI',
  pageNo: 1,
  pageSize: 10
})

// 是否第一次搜索
const firstSearch = ref(true)

const searchName = ref('')

const loading = ref(false)

const queryFormRef = ref()

const clientCode = ref('UMVCLI')

const previewMenuDialogRef = ref()

const previewTenantDialogRef = ref()

const preview = (_row) => {
  console.log('_row.menu', _row.menu)
  previewMenuDialogRef.value.open(_row.menu)
}

const previewTenant = (_row) => {
  console.log('_row.menu', _row.tenantArr)
  previewTenantDialogRef.value.open(_row.tenantArr)
}

const message = useMessage()

/** 搜索按钮操作 */
const handleQuery = async () => {
  try {
    // loading.value = true
    console.log('queryParams.code', queryParams.value.code)
    await queryFormRef.value.validate()
    searchName.value = queryParams.value.name
    if (firstSearch.value) {
      // 是不是第一次搜索
      await message.confirm('该搜索操作需要花费较长时间，请耐心等待')
      clientCode.value = queryParams.value.code
      firstSearch.value = false
      getData()
    } else {
      if (queryParams.value.code !== clientCode.value) {
        clientCode.value = queryParams.value.code
        await message.confirm('该搜索操作需要花费较长时间，请耐心等待')
        getData()
      } else {
        filterList()
      }
    }
  } finally {
    // loading.value = false
  }
}

/** 重置按钮操作 */
const resetQuery = async () => {
  const originCode = queryParams.value.code
  const originSearchName = queryParams.value.name
  queryFormRef.value.resetFields()
  if (queryParams.value.code !== clientCode.value && !firstSearch.value) {
    try {
      await message.confirm('该搜索操作需要花费较长时间，请耐心等待')
    } catch (e) {
      queryParams.value.code = originCode
      queryParams.value.name = originSearchName
    }
  }
  searchName.value = ''
  if (queryParams.value.code !== clientCode.value && !firstSearch.value) {
    clientCode.value = queryParams.value.code
    getData()
  } else {
    list.value = originList.value
  }
}

// 处理租户样式
const handleTenantStyle = (_tenantList) => {
  // 只要获取前面两个出来就够了，而且只需要随便展示里面有的租户就行，不需要考虑顺序，剩下的都做省略号处理
  if (searchName.value) {
    const arr: String[] = []
    for (let i = 0; i < _tenantList.length; i++) {
      if (arr.length === 2) {
        break
      } else {
        if (_tenantList.length === 1) {
          arr.push(_tenantList[0].name)
          break
        } else if (_tenantList.length === 2) {
          arr.push(_tenantList[0].name)
          arr.push(_tenantList[1].name)
          break
        } else if (i === _tenantList.length - 2 && arr.length === 0) {
          // 到最后两个都还没有选中的，那就直接拿最后两个进去算了
          arr.push(_tenantList[_tenantList.length - 2].name)
          arr.push(_tenantList[_tenantList.length - 1].name)
          break
        } else if (i === _tenantList.length - 1 && arr.length === 1) {
          // 到最后一个只选中一个，那最后一个直接放进去就行了
          arr.push(_tenantList[_tenantList.length - 1].name)
          break
        } else {
          if (_tenantList[i].name.indexOf(searchName.value) !== -1) {
            arr.push(_tenantList[i].name)
          }
        }
      }
    }
    const str = (_tenantList.length > 2 ? arr.join('，') + '...' : arr.join(',')).replace(
      searchName.value,
      `<span style="color: var(--el-color-primary)">${searchName.value}</span>`
    )
    return str
  } else {
    const arr: String[] = []
    for (let i = 0; i < _tenantList.length; i++) {
      if (i < 2) {
        arr.push(_tenantList[i].name)
      } else {
        break
      }
    }
    return _tenantList.length > 2 ? arr.join('，') + '...' : arr.join(',')
  }
}

// 过滤搜索出来的列表
const filterList = () => {
  if (!searchName.value) {
    list.value = deepClone(originList.value)
  } else {
    list.value = list.value.filter((el) => {
      let unFilter = false
      for (let i = 0; i < el.tenantArr.length; i++) {
        if (el.tenantArr[i].name.indexOf(searchName.value) !== -1) {
          unFilter = true
        }
      }
      return unFilter
    })
  }
}

/**
 * 拼接菜单的code作为key
 */
const tentantMenuMap: Ref<any> = ref({})

interface tenantVO {
  name: String
  tenantId: String | number
}

interface GroupVO {
  tenantArr: tenantVO[]
  menu: any[]
}

const list: Ref<GroupVO[]> = ref([])

const originList: Ref<GroupVO[]> = ref([])

// 转换分组map为分组数组
const transferGroupMapToArr = () => {
  let emptyItem: any = undefined
  for (const key in tentantMenuMap.value) {
    if (key !== 'empty') {
      const menu = tentantMenuMap.value[key][0].menu
      list.value.push({
        tenantArr: tentantMenuMap.value[key].map((el) => {
          return { name: el.name, tenantId: el.tenantId }
        }),
        menu
      })
    } else {
      // 如果是空组
      const menu = []
      emptyItem = {
        tenantArr: tentantMenuMap.value[key].map((el) => {
          return { name: el.name, tenantId: el.tenantId }
        }),
        menu
      }
    }
  }
  list.value.sort((a, b) => {
    return b.tenantArr.length - a.tenantArr.length
  })
  list.value.push(emptyItem)
}

// 转化租户map为分组map
const transferTenantToMap = (_tenantMap) => {
  for (const key in _tenantMap) {
    let tanantObj = {
      name: key.split('-')[1],
      tenantId: key.split('-')[0],
      menu: _tenantMap[key]
    }
    if (_tenantMap[key].length === 0 || Array.isArray(_tenantMap[key]) === false) {
      // 如果是空数组，也就是没有配置菜单，作为一个独立的分组
      if (!tentantMenuMap.value['empty']) {
        tentantMenuMap.value['empty'] = [tanantObj]
      } else {
        tentantMenuMap.value['empty'].push(tanantObj)
      }
    } else {
      // 如果是
      const keyStr = _tenantMap[key].map((el) => el.code).join(',')
      if (!tentantMenuMap.value[keyStr]) {
        tentantMenuMap.value[keyStr] = [tanantObj]
      } else {
        tentantMenuMap.value[keyStr].push(tanantObj)
      }
    }
  }
}

const tenantIdsMap: Ref<any> = ref({})

const transferGroupTenant = (_res) => {
  const tenantMap = _res.tenantMap
  const menuMap = _res.menuMap
  for (const key in tenantMap) {
    if (tenantMap[key] && tenantMap[key].length > 0) {
      if (key !== '') {
        tenantIdsMap.value[key] = {
          tenantArr: tenantMap[key].map((el) => {
            return {
              tenantId: el.split('-')[0],
              name: el.split('-')[1]
            }
          })
        }
      } else {
        tenantIdsMap.value['empty'] = {
          tenantArr: tenantMap[key].map((el) => {
            return {
              tenantId: el.split('-')[0],
              name: el.split('-')[1]
            }
          })
        }
      }
    }
  }
  let emptyItem = undefined
  for (const key in menuMap) {
    if (tenantMap[key] && tenantMap[key].length > 0) {
      if (key !== '') {
        tenantIdsMap.value[key].menu = menuMap[key]
        list.value.push(tenantIdsMap.value[key])
      } else {
        tenantIdsMap.value['empty'].menu = menuMap[key]
        emptyItem = tenantIdsMap.value['empty']
      }
    }
  }
  list.value.sort((a, b) => {
    return b.tenantArr.length - a.tenantArr.length
  })
  emptyItem && list.value.push(emptyItem)
}

import { deepClone } from '@/utils/deep'

const getData = async () => {
  try {
    loading.value = true
    originList.value = []
    list.value = []
    tentantMenuMap.value = {}
    const res = await getGroupTenantMenus({ code: clientCode.value })
    transferGroupTenant(res)
    // const res = await getAllTenantMenus({ code: clientCode.value })
    // transferTenantToMap(res)
    // transferGroupMapToArr()
    // console.log('list', list.value)
    originList.value = deepClone(list.value)
    if (searchName.value) {
      filterList()
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getClientList()
  // getData()
})
</script>
