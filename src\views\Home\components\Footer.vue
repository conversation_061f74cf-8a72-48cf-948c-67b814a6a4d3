<template>
  <div
    :class="prefixCls"
    class="flex flex-col justify-center items-center text-14px text-center text-[var(--el-text-color-placeholder)] bg-[var(--app-contnet-bg-color)] h-120px dark:bg-[var(--el-bg-color)]"
  >
    <div class="flex flex-row justify-center h-25px">
      <p class="mr-2">
        {{ t('home.footer.slogan') }}
      </p>
      <p>
        {{ t('home.footer.slogan2') }}
      </p>
    </div>
    <div class="flex flex-row justify-center h-25px">
      <p
        class="mr-2 hover:text-gold hover:border-b-[1px] hover:border-b-gold cursor-pointer"
        @click="emit('changeTabIndex', 11)"
      >
        {{ t('home.footer.Agreement') }}
      </p>
      <p
        class="mr-2 hover:text-gold hover:border-b-[1px] hover:border-b-gold cursor-pointer"
        @click="emit('changeTabIndex', 22)"
      >
        {{ t('home.footer.Agreement2') }}
      </p>
      <p class="mr-2">
        {{ t('home.footer.Copyright') }}
      </p>
      <a
        class="mr-2 hover:text-gold hover:border-b-[1px] hover:border-b-gold"
        href="https://beian.miit.gov.cn/"
        target="_blank"
        rel="noopener noreferrer"
      >
        {{ t('home.footer.icp') }}
      </a>
      <!-- <a
        class="hover:text-gold hover:border-b-[1px] hover:border-b-gold"
        href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44040202000557"
        target="_blank"
        rel="noopener noreferrer"
      >
        粤公网安备 44040202000557 号
      </a> -->
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Footer'
})

import { useDesign } from '@/hooks/web/useDesign'
const { t } = useI18n()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('footer')

const emit = defineEmits(['changeTabIndex'])
</script>
