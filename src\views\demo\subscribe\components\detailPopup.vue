<template>
  <Dialog :title="t('common.detail')" width="800" v-model="detailVisible" @close="close">
    <el-form-item :label="t('system.subscribe.menuPerm')">
      <el-card class="cardHeight">
        <el-tree
          ref="treeRef"
          :data="menuOptions"
          :props="defaultProps"
          :empty-text="t('common.emptyText')"
          node-key="id"
        />
      </el-card>
    </el-form-item>

    <template #footer>
      <el-button @click="close">{{ t('common.close') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'BindPopup'
})

import { defaultProps, handleTree } from '@/utils/tree'
import { getTenantMenu } from '@/api/system/apply'
const { t } = useI18n() // 国际化
const detailVisible = ref(false)
const detailData = ref()
const emit = defineEmits(['success'])
const menuOptions = ref<any[]>([]) // 树形结构数据

const loading = ref(false)

const open = async (row) => {
  if (loading.value) return
  try {
    loading.value = true
    detailData.value = row
    const res = await getTenantMenu({
      clientId: row.id
    })
    console.log('res', res)
    menuOptions.value = handleTree(res)
    detailVisible.value = true
  } finally {
    loading.value = false
  }
}

const close = () => {
  detailVisible.value = false
}

const submitFn = () => {
  detailVisible.value = false
  emit('success')
}

defineExpose({ open })
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
</style>
