/**
 * 路由排序（适用两个不同参数进行排序的情况）
 * @param heightSortKey 优先级高的排序字段
 * @param sortKey 优先级次要的排序字段
 */
const compare = (sortKey, heightSortKey) => {
  return (obj1, obj2) => {
    const sortNum1 = obj1[sortKey]
    const heightSortNum1 = obj1[heightSortKey]
    const sortNum2 = obj2[sortKey]
    const heightSortNum2 = obj2[heightSortKey]
    if (heightSortNum1 == 0 || heightSortNum1 == null) {
      // 如果是0或者是null就直接往后排
      return 1
    } else if (heightSortNum2 == 0 || heightSortNum1 == null) {
      return -1
    } else if (heightSortNum1 < heightSortNum2) {
      return -1
    } else if (heightSortNum1 > heightSortNum2) {
      return 1
    } else {
      if (sortNum1 == 0 || sortNum1 == null) {
        // 如果是0或者是null就直接往后排
        return 1
      } else if (sortNum2 == 0 || sortNum2 == null) {
        return -1
      } else if (sortNum1 < sortNum2) {
        return -1
      } else if (sortNum1 > sortNum2) {
        return 1
      } else {
        return 0
      }
    }
  }
}

/**
 * 根据参数大小排序树状结构
 * @param _treeArray 需要排序的树
 * @param _sortNum 根据什么值的大小进行排序
 * @param _isDeepTree 是否深度树遍历
 */
export const sortTreeArrayByParam = (_treeArray, _sortNum, _heightSortNum, _isDeepTree = true) => {
  _treeArray.sort(compare(_sortNum, _heightSortNum))
  if (_isDeepTree) {
    _treeArray.forEach((el) => {
      if (el.children && el.children.length > 0) {
        sortTreeArrayByParam(el.children, _sortNum, _heightSortNum)
      }
    })
  }
}

// 普通排序
const commonCompare = (sortKey) => {
  return (obj1, obj2) => {
    const sortNum1 = obj1[sortKey]
    const sortNum2 = obj2[sortKey]
    if (sortNum1 < sortNum2) {
      return -1
    } else if (sortNum1 > sortNum2) {
      return 1
    } else {
      return 0
    }
  }
}

/**
 * 根据参数大小排序树状结构
 * @param _treeArray 需要排序的树
 * @param _heightSortNum 根据什么值的大小进行排序(优先)
 * @param _sortNum 根据什么值的大小进行排序(次要)
 * @param _isDeepTree 是否深度树遍历
 */
export const commonSortTreeArrayByParam = (_treeArray, _sortNum, _isDeepTree = true) => {
  _treeArray.sort(commonCompare(_sortNum))
  if (_isDeepTree) {
    _treeArray.forEach((el) => {
      if (el.children && el.children.length > 0) {
        commonSortTreeArrayByParam(el.children, _sortNum)
      }
    })
  }
}
