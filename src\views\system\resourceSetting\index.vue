<template>
  <!-- 列表 -->
  <UmvContent>
    <UmvQuery :opts="queryOpts" v-model="queryParams" @check="handleQuery" @reset="getList" />
    <UmvTable
      v-loading="loading"
      :data="list"
      :columns="columns"
      @select="handleSelectChange"
      @select-all="handleSelectAllChange"
      @refresh="getList"
      ref="tableRef"
    >
      <template #tools>
        <el-button
          type="primary"
          @click="batchConfig"
          v-if="isEdit"
          v-hasPermi="['system:resource-setting:batch-set']"
          size="small"
        >
          批量代客申请授权
        </el-button>
      </template>

      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>
  </UmvContent>

  <!-- 表单弹窗：添加/修改 -->
  <TenantForm ref="formRef" @success="getList" />

  <batchConfigDialog ref="batchConfigRef" @success="batchConfigSuccess" />
  <singleConfig ref="singleConfigRef" />
</template>
<script setup lang="tsx">
defineOptions({
  name: 'ResourceSetting'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as TenantApi from '@/api/system/tenant'
import * as TenantPackageApi from '@/api/system/tenantPackage'
import TenantForm from './TenantForm.vue'
import batchConfigDialog from './batchConfigDialog.vue'
import singleConfig from './singleConfig.vue'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import UmvContent from '@/components/UmvContent/src/UmvContent.vue'
import { ElButton } from 'element-plus'
import { checkPermi } from '@/utils/permission'
import { CustomerSelect } from '@/components/CustomerSelect'

const message = useMessage() // 消息弹窗

import { useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache('sessionStorage')

const { t } = useI18n() // 国际化

interface PACKAGE_TYPE {
  name: string
  id: string | number
}

interface SET_MEAL_TYPE {
  label: string
  value: string | number
}

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<any[]>([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: '',
  contactName: '',
  contactMobile: '',
  status: '',
  createTime: [] as string[],
  setMeal: [] as any[]
})

// 查询选项配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '租户名',
    controlRender: (form) => (
      <CustomerSelect
        v-model:customer-name={form.name}
        isSearchByName
        onKeyup-enter={handleQuery}
      />
    ),
    defaultVal: ''
  },
  contactName: {
    label: '联系人',
    defaultVal: '',
    controlRender: (form) => (
      <el-input v-model={form.contactName} placeholder="请输入联系人" clearable />
    )
  },
  contactMobile: {
    label: '联系手机',
    defaultVal: '',
    controlRender: (form) => (
      <el-input v-model={form.contactMobile} placeholder="请输入联系手机" clearable />
    )
  },
  status: {
    label: '租户状态',
    defaultVal: '',
    controlRender: (form) => (
      <el-select v-model={form.status} placeholder="请选择租户状态" clearable>
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  createTime: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form) => (
      <el-date-picker
        v-model={form.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
      />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: '', type: 'selection', width: '40px', fixed: 'left' },
  { prop: 'id', label: '租户编号', align: 'center' },
  { prop: 'name', label: '租户名', align: 'center' },
  { prop: 'contactName', label: '联系人', align: 'center' },
  { prop: 'contactMobile', label: '联系手机', align: 'center' },
  {
    prop: 'expireTime',
    label: '过期时间',
    align: 'center',
    width: '180px',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.expireTime)}</span>
    )
  },
  {
    prop: 'status',
    label: '租户状态',
    align: 'center',
    renderTemplate: (scope) => <dict-tag type={DICT_TYPE.COMMON_STATUS} value={scope.row.status} />
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    width: '180px',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    minWidth: '110px',
    fixed: 'right',
    renderTemplate: (scope) => (
      <div>
        {checkPermi(['system:resource-setting:data-permi']) && (
          <ElButton link type="primary" onClick={() => check(scope.row)}>
            数据权限
          </ElButton>
        )}
      </div>
    )
  }
])

const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const route = useRoute()

const isEdit = computed(() => route.query.isEdit)

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    let obj = { ...queryParams.value, tenantIds: isEdit.value ? proxyTenantIds.value : [] }
    console.log('obj55555555556666666666666666', obj)
    const data = await TenantApi.getTenantPageProxyChange(obj)
    list.value = data.list
    total.value = data.total
    nextTick(() => {
      setCheckedRowsShow()
    })
  } catch (error) {
    console.error('获取列表失败', error)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/**------------------------------------- 选择所有客户start -----------------------------------------------*/

// 选中的id列表
const selectedList = ref<any[]>([])

const tableRef = ref()

// 表格选中回显
const checkRowsShow = (checks, rows) => {
  rows.forEach((el) => {
    let index = -1
    checks.some((_item, _index) => {
      if (_item.id === el.id) {
        index = _index
      }
    })
    if (index === -1) {
      tableRef.value?.tableRef?.toggleRowSelection(el, false)
    } else {
      tableRef.value?.tableRef?.toggleRowSelection(el, true)
    }
  })
}

const setCheckedRowsShow = () => {
  checkRowsShow(selectedList.value, list.value)
}

const handleSelectChange = (_valArr, _row) => {
  let index = -1
  selectedList.value.some((el, _index) => {
    if (el.id === _row.id) {
      index = _index
    }
  })
  if (index === -1) {
    selectedList.value.push(_row)
  } else {
    selectedList.value.splice(index, 1)
  }
}

// 点击全选
const handleSelectAllChange = (_valArr) => {
  if (_valArr.length > 0) {
    list.value.forEach((el) => {
      let index = -1
      selectedList.value.some((_item, _index) => {
        if (_item.id === el.id) {
          index = _index
        }
      })
      if (index === -1) {
        selectedList.value.push(el)
      }
    })
  } else {
    list.value.forEach((el) => {
      let index = -1
      selectedList.value.some((_item, _index) => {
        if (_item.id === el.id) {
          index = _index
        }
      })
      if (index !== -1) {
        selectedList.value.splice(index, 1)
      }
    })
  }
}

/**------------------------------------- 选择所有客户end -----------------------------------------------*/

const tempVal = {
  name: '订单签审-稿样确认_管理端_47499',
  code: '_BIZ_UMVMGR_CARD_CONFIRM_2_47499',
  sort: -2147483648,
  remark: null,
  applicationCode: 'UMVMGR',
  appId: 650,
  parentCode: null,
  isExpose: false,
  exposeRoleCode: null,
  exposer: null,
  clientId: 159,
  clientName: null,
  status: 0,
  id: '1864155207447666689',
  dataScope: 6,
  dataScopeDeptIds: null,
  type: 2,
  createTime: 1733284210000,
  isStale: false,
  tags: null,
  children: null,
  parentId: null,
  editable: true,
  autoAuthRuleIds: null,
  pappId: null
}

const batchConfigRef = ref()

const batchConfig = async () => {
  if (selectedList.value.length === 0) {
    message.error('请选择租户！')
    return
  }
  batchConfigRef.value.open([...selectedList.value], proxyTenantIds.value)
}

const singleConfigRef = ref()

const check = async (_row) => {
  singleConfigRef.value.open(_row, proxyTenantIds.value)
}

// 批量操作成功
const batchConfigSuccess = () => {
  selectedList.value = []
  nextTick(() => {
    setCheckedRowsShow()
  })
}

const proxyMap = ref<Record<string, any>>({})

const proxyTenantIds = ref<string[]>([])

const getProxyTenants = () => {
  proxyMap.value = wsCache.get('batchAuthProxyTenants') || {}
  proxyTenantIds.value = []
  for (const key in proxyMap.value) {
    proxyTenantIds.value.push(key)
  }
  console.log('proxyTenantIds', proxyTenantIds.value)
}

/** 初始化 **/
onMounted(async () => {
  getProxyTenants()
  await getList()
})
</script>

<style scoped>
.tableClass {
  height: 100%;
}
</style>
