<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      :label-width="ifEn ? '180px' : '80px'"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item :label="t('system.user.nickname')" prop="nickname">
            <el-input
              v-model="formData.nickname"
              :placeholder="t('system.user.nicknamePlaceholder')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('system.user.deptId')" prop="deptId">
            <el-tree-select
              v-model="formData.deptId"
              :data="deptList"
              :props="defaultProps"
              check-strictly
              node-key="id"
              :placeholder="t('system.user.deptIdPlaceholder')"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="auth-warp">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="t('system.user.mobile')" prop="mobile" style="margin-bottom: 0">
              <el-input
                v-model="formData.mobile"
                maxlength="11"
                :placeholder="t('system.user.mobilePlaceholder')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="" :label-width="'0px'" style="margin-bottom: 0">
              <el-checkbox class="ml-16px" v-model="formData.mobileAuthenticated">{{
                t('sys.user.certified')
              }}</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="auth-warp">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="t('system.user.email')" prop="email" style="margin-bottom: 0">
              <el-input
                v-model="formData.email"
                maxlength="50"
                :placeholder="t('system.user.emailPlaceholder')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-col :span="12">
              <el-form-item label="" :label-width="'0px'" style="margin-bottom: 0">
                <el-checkbox class="ml-16px" v-model="formData.emailAuthenticated">{{
                  t('sys.user.certified')
                }}</el-checkbox>
              </el-form-item>
            </el-col>
          </el-col>
        </el-row>
      </div>

      <el-row>
        <el-col :span="12">
          <el-form-item :label="t('system.user.username')" prop="username">
            <el-input
              :disabled="formData.id != undefined"
              v-model="formData.username"
              :placeholder="t('system.user.usernamePlaceholder')"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item
            v-if="formData.id === undefined"
            :label="t('system.user.password')"
            prop="password"
          >
            <el-input
              v-model="formData.password"
              :placeholder="t('system.user.passwordPlaceholder')"
              show-password
              type="password"
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item :label="t('system.user.sex')">
            <el-select v-model="formData.sex" :placeholder="t('system.user.sexPlaceholder')">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="t('system.user.postIds')">
            <el-select
              v-model="formData.postIds"
              multiple
              :placeholder="t('system.user.postIdsPlaceholder')"
            >
              <el-option
                v-for="item in postList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('system.user.remark')">
            <el-input
              v-model="formData.remark"
              :placeholder="t('system.user.remarkPlaceholder')"
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">{{
        t('common.ok')
      }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemUserForm'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { defaultProps, handleTree } from '@/utils/tree'
import * as PostApi from '@/api/system/post'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'

const { t, ifEn } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  nickname: '',
  deptId: '',
  mobile: '',
  email: '',
  id: undefined,
  username: '',
  password: '',
  sex: undefined,
  postIds: [],
  remark: '',
  status: CommonStatusEnum.ENABLE,
  roleIds: [],
  mobileAuthenticated: true,
  emailAuthenticated: true
})
const formRules = reactive({
  username: [{ required: true, message: t('system.user.rDesc1'), trigger: 'blur' }],
  nickname: [{ required: true, message: t('system.user.rDesc2'), trigger: 'blur' }],
  password: [{ required: true, message: t('system.user.rDesc3'), trigger: 'blur' }],
  email: [
    {
      required: true,
      type: 'email',
      message: t('system.user.rDesc4'),
      trigger: ['blur', 'change']
    }
  ],
  mobile: [{ required: true, message: t('system.user.rDesc7'), trigger: 'blur' }]
  // mobile: [
  //   {
  //     required: true,
  //     pattern: /^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/,
  //     message: t('system.user.rDesc5'),
  //     trigger: 'blur'
  //   }
  // ]
})
const formRef = ref() // 表单 Ref
const deptList = ref<Tree[]>([]) // 树形结构
const postList = ref([]) // 岗位列表

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await UserApi.getUser(id)
    } finally {
      formLoading.value = false
    }
  }
  // 加载部门树
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  // 加载岗位列表
  postList.value = await PostApi.getSimplePostList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as UserApi.UserVO
    if (formType.value === 'create') {
      await UserApi.createUser(data)
      ElMessageBox.alert(t('sys.user.rDesc1'), t('sys.user.tips'), {
        confirmButtonText: t('sys.user.confirms')
      })
    } else {
      await UserApi.updateUser(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    nickname: '',
    deptId: '',
    mobile: '',
    email: '',
    id: undefined,
    username: '',
    password: '',
    sex: undefined,
    postIds: [],
    remark: '',
    status: CommonStatusEnum.ENABLE,
    roleIds: [],
    mobileAuthenticated: true,
    emailAuthenticated: true
  }
  formRef.value?.resetFields()
}
</script>
<style lang="scss" scoped>
.auth-warp {
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  width: 100%;
  padding: 8px;
  margin-bottom: 14px;
}
</style>
