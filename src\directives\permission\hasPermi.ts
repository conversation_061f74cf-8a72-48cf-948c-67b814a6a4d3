/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 13:59:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-12-13 14:20:43
 * @Description:
 */
import type { App } from 'vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

export function hasPermi(app: App<Element>) {
  app.directive('hasPermi', (el, binding) => {
    const { t } = useI18n()
    const { wsCache } = useCache()
    const { value } = binding
    const all_permission = '*:*:*'
    const permissions = wsCache.get(CACHE_KEY.ROLE_PERMISSIONS)

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      const hasPermissions = permissions?.some((permission: string) => {
        return all_permission === permission || permissionFlag.includes(permission)
      })

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(t('sys.permission.hasPermission'))
    }
  })
}
