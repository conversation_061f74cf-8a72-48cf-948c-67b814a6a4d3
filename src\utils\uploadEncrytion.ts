import md5 from 'crypto-js/md5'
import SparkMD5 from 'spark-md5'

function fileToFileStream(file) {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()

    const spark = new SparkMD5.ArrayBuffer()

    fileReader.onload = function (event) {
      if (event.target.readyState === FileReader.DONE) {
        // 读取完成，event.target.result 包含文件的 ArrayBuffer
        const arrayBuffer = event.target.result
        spark.append(arrayBuffer)
        const sparkMd5 = spark.end()
        console.log('sparkMd5', sparkMd5)
        // // 创建一个 Blob 对象
        // const blob = new Blob([arrayBuffer], { type: file.type })

        // // 将 Blob 对象转换为文件流
        // const fileStream = blob.stream()

        // 成功解析为文件流
        resolve(sparkMd5)
      }
    }

    fileReader.onerror = function (error) {
      reject(error)
    }

    // 读取 File 对象为 ArrayBuffer
    fileReader.readAsArrayBuffer(file)
  })
}

// 文件加密规则，当前为md5
const encrytionFile = async (_fileList) => {
  // 查看是否是数组
  if (Array.isArray(_fileList)) {
    const arr = []
    for (let i = 0; i < _fileList.length; i++) {
      arr.push(await fileToFileStream(_fileList[i]))
    }
    return arr.join(',')
  } else {
    return await fileToFileStream(_fileList)
  }
}

// 获取随机字符串
const getRadomKey = (strLength = 32) => {
  let code = ''
  const chars = 'abcdefghijklmnopqrsluvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*_+~'
  const charsArr = chars.split('')

  for (let i = 0; i < strLength; i++) {
    const num = Math.floor(Math.random() * charsArr.length)
    code += charsArr[num]
  }
  return code
}

// 整合签名
const integrationSign = (_signObj, _apisecurityKey = '') => {
  console.log('_signObj', _signObj)
  const arr: any = []
  for (const key in _signObj) {
    arr.push({
      name: key,
      value: _signObj[key]
    })
  }

  const signStr =
    arr
      .sort((a, b) => {
        return (a.name + '').localeCompare(b.name + '')
      })
      .map((el) => {
        return `${el.name}=${el.value}`
      })
      .join('&') +
    '&key=' +
    _apisecurityKey

  console.log('signStr', signStr)
  const sign = md5(signStr).toString().toUpperCase()
  return {
    ..._signObj,
    sign
  }
}

import { getApisecurityKey } from '@/api/system/upload/index'
import { func } from 'vue-types'
import { reject } from 'lodash-es'

// 上传加密
/**
 * @param formData 请求参数对象
 * @param fileList 传入的文件列表
 *
 * @returns
 * file_Digests: fileList使用md5进行加密，用,将各个已经加密的md5进行拼接
 * nonce：32位字符串
 * timestamp：以秒为单位的时间戳
 * appId，appKey： 目前固定为app1001
 * sign：签名，签名方式为，当前除开file，其他参数和值，通过“参数名=参数值&”的方式进行拼接，最后拼上盐值，再转为MD5，再转为大写
 *
 * 该方法不包含传的file或者fileList字段
 */

const defaultAppId = 'app1001'

const defaultAppKey = 'app1001'

export const uploadEncrytion = async (formData, fileList) => {
  const apisecurityKey = await getApisecurityKey({
    appId: defaultAppId,
    appKey: defaultAppKey
  })
  const file_Digests = await encrytionFile(fileList)
  //获取随机字符串
  const nonce = getRadomKey()
  const timestamp = Math.floor(new Date().getTime() / 1000)
  //appId和appKey目前固定为app1001
  const appId = defaultAppId
  const appKey = defaultAppKey
  const obj = integrationSign(
    {
      [Array.isArray(fileList) ? 'files_Digests' : 'file_Digests']: file_Digests,
      nonce,
      timestamp,
      appId,
      appKey
    },
    apisecurityKey
  )
  console.log('obj', obj)
  for (const key in obj) {
    formData.append(key, obj[key])
  }
}
