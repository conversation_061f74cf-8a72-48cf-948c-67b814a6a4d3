<template>
  <div class="json-editor-wrap">
    <section v-if="editType == 'editTable'">
      <div style="text-align: right; margin-bottom: 10px">
        <el-button type="primary" @click="handleTableAddRow">新增</el-button>
      </div>
      <el-table
        :data="JsonTable"
        border
        :tree-props="{ children: 'children' }"
        row-key="createTime"
      >
        <el-table-column prop="paramName" label="参数名称" min-width="230" class-name="param-name">
          <template #default="scope">
            <el-input
              class="param-name--input"
              v-model="scope.row.paramName"
              placeholder="请输入"
              clearable
            />
            <el-button class="add-btn" circle type="primary" @click="addChildren(scope.row)"
              >+</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="paramType" label="参数类型" min-width="150">
          <template #default="scope">
            <el-input v-model="scope.row.paramType" placeholder="请输入" clearable />
          </template>
        </el-table-column>
        <el-table-column prop="paramRequired" label="是否必填" min-width="100">
          <template #default="scope">
            <el-select v-model="scope.row.paramRequired">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="paramValue" label="默认值" min-width="200">
          <template #default="scope">
            <el-input v-model="scope.row.paramValue" placeholder="请输入" clearable />
          </template>
        </el-table-column>
        <el-table-column prop="paramRemark" label="备注" min-width="200">
          <template #default="scope">
            <el-input v-model="scope.row.paramRemark" placeholder="请输入" clearable />
          </template>
        </el-table-column>

        <el-table-column prop="paramRemark" label="操作" min-width="100" align="center">
          <template #default="scope">
            <el-button type="danger" link @click="handleDeleteItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <section v-else>
      <vue-json-pretty :data="jsonValue" />
    </section>
    <div class="btn">
      <el-button @click="close">关闭</el-button>
      <el-button type="primary" @click="submitJson">确定</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'

const jsonValue = ref<any>(null)

const props = defineProps({
  initValue: {
    type: Object,
    default: () => {}
  },
  editType: {
    type: String,
    default: ''
  }
})

const { initValue, editType } = toRefs<any>(props)

onMounted(() => {
  console.log('initValue === ', initValue.value)
  try {
    if (editType.value == 'editTable') {
      JsonTable.value = initValue.value ? JSON.parse(initValue.value) : []
    } else {
      jsonValue.value = initValue.value ? JSON.parse(initValue.value) : ''
    }
  } catch (error) {
    JsonTable.value = []
    jsonValue.value = ''
  }
})

const JsonTable = ref<any>([])
const handleTableAddRow = () => {
  JsonTable.value.push({
    createTime: `${new Date().getTime()}`,
    paramName: '',
    paramType: '',
    paramRequired: true,
    paramValue: '',
    paramRemark: '',
    children: []
  })
}
const addChildren = (row) => {
  row.children.push({
    createTime: `${new Date().getTime()}`,
    paramName: '',
    paramType: '',
    paramRequired: true,
    paramValue: '',
    paramRemark: '',
    children: []
  })
}

// const handleDeleteItem2 = (row) => {
//   const arr = contrast(JsonTable.value, row)
//   const index = arr.findIndex((item) => item === row)
//   delete arr[index]

//   function contrast(obj: any[], value: any): any {
//     let father = null as any
//     obj.some((item) => {
//       if (item === value) {
//         father = obj
//         return true
//       }
//       if (item.children) {
//         father = contrast(item.children, value)
//         if (father) {
//           return true
//         }
//       }
//       return false
//     })
//     return father
//   }
// }
const handleDeleteItem = (row) => {
  let createTime = row.createTime // 唯一标识
  removeChildren(JsonTable.value, createTime)
}

const removeChildren = (array, createTime) => {
  for (let i = 0; i < array.length; i++) {
    let obj = array[i]
    if (obj.createTime == createTime) {
      array.splice(i, 1)
    } else {
      removeChildren(obj.children, createTime)
    }
  }
}

const emits = defineEmits(['setJsonData', 'closeJsonDlg'])
const submitJson = () => {
  if (editType.value == 'editTable') {
    let flag = hasAllNameFilled(JsonTable.value)
    if (!flag) {
      ElMessage.error('存在参数名称未填写！请检查并填写')
    } else {
      console.log('table数据 === ', JsonTable.value)
      emits('setJsonData', JSON.stringify(JsonTable.value))
    }
  } else {
    console.log('返回编辑的json数据 === ', jsonValue.value)
    emits('setJsonData', JSON.stringify(jsonValue.value))
  }
}

/** 校验所有参数名称是否都填写了 */
const hasAllNameFilled = (array) => {
  let flag = true
  array.forEach((e) => {
    if (!e.paramName) {
      flag = false
      return
    }
    if (e.children.length) {
      flag = hasAllNameFilled(e.children)
    }
  })
  return flag
}

const close = () => {
  emits('closeJsonDlg')
}
</script>

<style scoped lang="scss">
.json-editor-wrap {
  :deep(.param-name .cell) {
    display: flex;
    align-items: center;
    .param-name--input {
      flex: 1;
    }
    .add-btn {
      margin-left: 6px;
      font-size: 18px;
      width: 16px;
      height: 16px;
    }
  }
  .btn {
    padding: 20px 0 0;
    text-align: right;
  }
}
</style>
