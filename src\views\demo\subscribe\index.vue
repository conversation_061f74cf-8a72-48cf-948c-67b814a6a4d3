<!-- 订阅管理,用于api市场订阅 -->
<template>
  <!-- 搜索 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item>
        <!-- <el-button @click="handleQuery"><icon-ep-search style="font-size: 12px" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><icon-ep-refresh style="font-size: 12px" class="mr-5px" /> 重置</el-button> -->
        <el-button type="primary" plain @click="showSubscribePopup">
          {{ t('system.subscribe.subscribe') }}
        </el-button>
        <el-button type="primary" plain @click="checkout">{{
          t('system.subscribe.changeRelation')
        }}</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap ifTable>
    <el-table v-loading="loading" :data="list">
      <el-table-column
        :label="t('system.subscribe.checkoutValue1')"
        align="center"
        prop="name"
        v-if="checkoutValue === 0"
      />
      <el-table-column :label="t('system.subscribe.name')" align="center" prop="name" v-else />
      <el-table-column
        :label="t('system.subscribe.checkoutValue0')"
        align="center"
        v-if="checkoutValue === 0"
      >
        <template #default="{ row }">
          <el-tooltip placement="top" :content="transferToStr(row.clientBaseVOList)">
            <span class="w-full overflow-hidden whitespace-nowrap text-ellipsis">
              {{ transferToStr(row.clientBaseVOList) }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('system.subscribe.checkoutValue1')"
        align="center"
        v-if="checkoutValue === 1"
      >
        <template #default="{ row }">
          <el-tooltip placement="top" :content="transferToStr(row.applicationVOlist)">
            <span class="w-full overflow-hidden whitespace-nowrap text-ellipsis">{{
              transferToStr(row.applicationVOlist)
            }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('system.subscribe.ver')"
        align="center"
        prop="ver"
        v-if="checkoutValue === 0"
      >
        <template #default="scope">
          <span>{{ scope.row.ver }}</span>
          <el-button
            v-if="scope.row.upgradable"
            style="margin-left: 8px"
            :size="'small'"
            @click="showUpdateMenu(scope.row)"
            >{{ t('system.subscribe.upgradable') }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('common.operate')"
        align="center"
        fixed="right"
        v-if="checkoutValue !== 0"
      >
        <template #default="{ row }">
          <!-- <el-button link type="primary" @click="showBind(row)" v-if="checkoutValue === 0"
            >绑定</el-button
          > -->
          <el-button link type="primary" @click="showDetail(row)">{{
            t('common.detail')
          }}</el-button>
          <el-button link type="primary" @click="showServiceDetail(row)">{{
            t('system.subscribe.serverCfg')
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 穿梭框，修改菜单 -->
    <SubscribePopup ref="subcribeRef" @success="getList" />
    <!-- 服务升级-继承角色和菜单 -->
    <UpdateMenu ref="updateMenuRef" @success="getList" />
    <BindPopup ref="bindPopupRef" />
    <DetailPopup ref="detailPopupRef" />
    <!-- 服务详情配置首页和排序 -->
    <ServiceListDialog ref="ServiceListDialogRef" :applicationVOlist="applicationVOlist" />
  </ContentWrap>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemTenantPackage'
})

import SubscribePopup from './components/subscribePopup.vue'
import BindPopup from './components/bindPopup.vue'
import DetailPopup from './components/detailPopup.vue'

/**服务详情配置首页和排序 组件 */
import ServiceListDialog from './components/serviceListDialog.vue'
let ServiceListDialogRef = ref()

let applicationVOlist = ref()
//服务配置
const showServiceDetail = (row) => {
  applicationVOlist.value = row.applicationVOlist
  //显示-服务详情配置首页和排序-弹出框
  ServiceListDialogRef.value.open(row.id)
}

import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据

// 对应关系值
const checkoutValue = ref(0)

// 切换对应关系
const checkout = () => {
  if (checkoutValue.value === 0) {
    checkoutValue.value = 1
  } else {
    checkoutValue.value = 0
  }
  resetQuery()
}

const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  status: null,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
import {
  getSubscriptionApplicationPage,
  getSubscriptionClientPage,
  upgradeApplication
} from '@/api/system/apply'

const getList = async () => {
  loading.value = true
  try {
    // const data = await getSubscriptionList({ ...queryParams })
    const fn =
      checkoutValue.value === 0 ? getSubscriptionApplicationPage : getSubscriptionClientPage
    const data = await fn({ ...queryParams.value })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  getList()
})

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.value.pageNo = 1
  getList()
}

const subcribeRef = ref()
// 穿梭框弹窗
const showSubscribePopup = () => {
  subcribeRef.value.open()
}

// 数组提取name转为字符串
const transferToStr = (arr) => {
  if (!arr || arr.length === 0) return ''
  return arr.reduce(
    (a, b, index) => {
      if (index === 0) {
        return { name: `${a.name}${b.name}` }
      } else {
        return { name: `${a.name},${b.name}` }
      }
    },
    { name: '' }
  ).name
}

const bindPopupRef = ref()

const detailPopupRef = ref()

// 绑定业务端
const showBind = (row) => {
  bindPopupRef.value.open(row)
}

// 展示详情
const showDetail = (row) => {
  detailPopupRef.value.open(row)
}

/**
 * 升级操作
 */

// 升级服务弹窗
const showUpdateMenu = (row) => {
  ElMessageBox.confirm('确定升级？', '提示', {
    confirmButtonText: '确 认',
    cancelButtonText: '取 消'
  })
    .then(async () => {
      try {
        await upgradeApplication(row.id)
        ElMessage.success('提交升级申请成功')
        await getList()
      } finally {
      }
    })
    .catch(async () => {})
  // upgradeApplication
}
</script>
