<template>
  <el-dialog
    title="编辑"
    v-model="show"
    width="860px"
    append-to-body
    destroy-on-close
    class="approval-edit-form"
    @close="close"
  >
    <el-form
      class="mb-16px mt-16px"
      :model="detailValue"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
      v-loading="formLoading"
    >
      <el-form-item
        label="业务端"
        prop="clientId"
        :rules="{ required: true, trigger: ['blur', 'change'], message: '请输入业务端' }"
        :style="{ width: '100%' }"
      >
        <el-select v-model="detailValue.clientId" @change="changeSizeId" :style="{ width: '100%' }">
          <el-option
            v-for="(item, index) in clientList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="权限分配" v-show="detailValue.clientId">
        <div :style="{ maxWidth: '755px' }">
          <el-tabs v-model="activeTab" type="card" :style="{ width: '100%' }">
            <el-tab-pane
              v-for="item in serviceTabs"
              :key="item.id"
              :label="item.name"
              :name="item.id"
            />
          </el-tabs>
          <el-form-item label="菜单权限" prop="menuIds" :style="{ width: '100%' }">
            <el-card class="cardHeight">
              <template #header>
                全选/全不选:
                <el-switch
                  v-model="treeNodeAll"
                  active-text="是"
                  inactive-text="否"
                  inline-prompt
                  @change="handleCheckedTreeNodeAll"
                />
                全部展开/折叠:
                <el-switch
                  v-model="menuExpand"
                  active-text="展开"
                  inactive-text="折叠"
                  inline-prompt
                  @change="handleCheckedTreeExpand"
                />
                <span>父子联动(选中父节点，自动选择子节点):</span>
                <el-switch
                  v-model="checkStrictly"
                  active-text="是"
                  inactive-text="否"
                  inline-prompt
                />
              </template>
              <el-tree
                ref="treeRef"
                :data="menuOptions"
                :props="defaultProps"
                empty-text="暂无数据"
                node-key="id"
                :check-strictly="!checkStrictly"
                :default-checked-keys="defaultCheckedKeys"
                @check="checkChange"
                @check-change="beforeChange"
                show-checkbox
              >
                <template #default="{ node, data }">
                  <span :class="[data.isDelete && 'del']">{{ node.label }}</span>
                  <el-tag type="danger" effect="plain" round class="ml-3" v-if="data.ifNew">
                    新增
                  </el-tag>
                </template>
              </el-tree>
            </el-card>
          </el-form-item>
          <div
            :gutter="16"
            v-if="detailValue?.service && detailValue?.service[activeTabIndex]"
            class="mt-16px"
          >
            <div>
              <el-checkbox v-model="detailValue.service[activeTabIndex].hasModule"
                >是否需要客户专属服务</el-checkbox
              >
            </div>
            <div v-if="detailValue.service[activeTabIndex].hasModule" class="pl-18px">
              <el-row>
                <el-input
                  placeholder="请输入镜像"
                  v-model="detailValue.service[activeTabIndex].moduleImage"
                />
              </el-row>
              <el-row class="mt-8px">
                <el-input
                  placeholder="请输入服务"
                  v-model="detailValue.service[activeTabIndex].moduleService"
                />
              </el-row>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close" v-if="oprateType !== 'edit'">关 闭</el-button>
      <el-button @click="close" v-if="oprateType === 'edit'">取 消</el-button>
      <el-button
        type="primary"
        @click="submit"
        :loading="submitLoading"
        v-if="oprateType === 'edit'"
        >确 定
      </el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ApprovalEditForm'
})

import { defaultProps, handleTree } from '@/utils/tree'
import { deepClone } from '@/utils/deep'
import {
  getTenantApplicationService,
  getSubscriptionMenu,
  applicationSubscriptionUpdate,
  getSubscriptionGetByAppId
} from '@/api/system/serviceAudit'
import { getAppMenusList } from '@/api/system/menu'
import { getTenantClientPage } from '@/api/system/businessSide'
const emit = defineEmits(['sucess'])
const show = ref(false)
const detailValue: any = ref({
  clientId: '',
  service: []
})

const checkStrictly = ref(false) // 是否严格模式，即父子不关联

const submitLoading = ref(false)

const queryFormRef = ref()

// 处理提交时交互端和对应服务的修改映射
const getHandleDetail = () => {
  return detailValue.value.service.map((el) => {
    // 后端要求过滤出新增的数组，和移除的数组传过去
    let addMenuIdList: any = [] //新增的菜单按钮权限
    let removeMenuIdList: any = [] //移除的菜单按钮权限
    // 每个服务在操作前所记录的原始的数组，操作后的数组和原始数组作比较，如果原始数组有操作后数组没有表示该元素被删除，操作后数组有原始数组没有表示该元素时新增的
    let originSerIds = orignalAllCheckedMap.value[el.applicationId]
      ? orignalAllCheckedMap.value[el.applicationId].map((el) => el.id)
      : []
    el.serviceIds.forEach((e) => {
      if (originSerIds.indexOf(e) == -1) {
        addMenuIdList.push(e)
      }
    })
    originSerIds.forEach((e) => {
      if (el.serviceIds.indexOf(e) == -1) {
        removeMenuIdList.push(e)
      }
    })
    el.addMenuIdList = addMenuIdList
    el.removeMenuIdList = removeMenuIdList
    return el
  })
}

// 提交
const submit = async () => {
  try {
    await queryFormRef?.value.validate()
    submitLoading.value = true
    getHandleDetail()
    detailValue.value.service.forEach((el) => {
      el.moduleService = el.hasModule ? el.moduleService : ''
      el.moduleImage = el.hasModule ? el.moduleImage : ''
    })

    const arr = [...detailValue.value.service].filter((el) => {
      // 如果没有做删除和添加操作就过滤掉
      if (el.addMenuIdList.length > 0 || el.removeMenuIdList.length > 0) {
        return true
      } else {
        return false
      }
    })

    await applicationSubscriptionUpdate(
      { clientId: detailValue.value.clientId, tenantId: rowData.value.id },
      [...arr]
    )
    detailValue.value = {
      clientId: '',
      service: []
    }
    emit('sucess')
    show.value = false
    ElMessage.success('修改成功')
  } finally {
    submitLoading.value = false
  }
}

// 关闭
const close = () => {
  menuOptions.value = []
  detailValue.value = {
    clientId: ''
  }
  show.value = false
  activeTab.value = undefined
  activeTabIndex.value = 0
}

const activeTab = ref()

const activeTabIndex = ref(0)

const treeRef = ref()

// 是否展开全部
const menuExpand = ref(false)

/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}

// 是否选中全部
const treeNodeAll = ref(false)

// 全选/全不选
const handleCheckedTreeNodeAll = () => {
  if (newMenuIdArr.value.length > 0) {
    checkStrictly.value = false
    nextTick(() => {
      if (treeNodeAll.value) {
        newMenuIdArr.value.forEach((menuId: string) => {
          treeRef.value.setChecked(menuId, true, false)
        })
      } else {
        treeRef.value.setCheckedNodes([])
        // todo:后续菜单功能完善,要剔除 2024-09-30
        defaultCheckedKeys.value.forEach((menuId) => {
          treeRef.value.setChecked(menuId, true, false)
        })
      }

      activeService.value.serviceIds = [...treeRef.value.getCheckedKeys()]
    })
  } else {
    checkStrictly.value = true
    nextTick(() => {
      treeRef.value.setCheckedNodes(treeNodeAll.value ? menuOptions.value : [])
      // todo:后续菜单功能完善,要剔除 2024-09-30
      defaultCheckedKeys.value.forEach((menuId) => {
        treeRef.value.setChecked(menuId, true, false)
      })
      activeService.value.serviceIds = [...treeRef.value.getCheckedKeys()]
      checkStrictly.value = false
    })
  }
}

const activeService = ref()

const oldMenuArr: Ref<any[]> = ref([])

const newMenuIdArr: Ref<string[]> = ref([])

// 将旧菜单的id转为新菜单id并转为map
const oldToNewPrarentIdMap: Ref<any> = ref({})

/**
 * 处理旧菜单数据
 * 新菜单和旧菜单只有名字相同，新菜单里匹配不上旧菜单名字的，说明是新增的菜单（即使是修改的菜单也表示为新增菜单）；
 * 旧菜单里匹配不上新菜单名字的，说明是删除的菜单（即使是修改的菜单也表示为删除菜单）
 */
const handleOldNewMenu = (oldMenuList, newMenuList) => {
  let oldMenuParentIdObj = {}
  oldToNewPrarentIdMap.value = {}
  oldMenuArr.value = []

  oldMenuList.forEach((oldItem) => {
    let isDelete = true
    newMenuList.forEach((newItem) => {
      if (
        oldItem.name == newItem.name &&
        oldItem.component == newItem.component &&
        oldItem.permission == newItem.permission &&
        oldItem.path == newItem.path
      ) {
        isDelete = false
      }
    })
    if (isDelete) {
      oldMenuArr.value.push({
        ...oldItem,
        isDelete: true,
        disabled: true
      })

      if (oldMenuParentIdObj[oldItem.parentId]) {
        oldMenuParentIdObj[oldItem.parentId].push({
          ...oldItem,
          isDelete: true,
          disabled: true
        })
      }
    }
  })

  newMenuIdArr.value = []

  newMenuList = newMenuList.map((item) => {
    newMenuIdArr.value.push(item.id)
    item['ifNew'] = true
    item['menuId'] = item.id
    oldMenuList.some((oldItem) => {
      //判断菜单名称  菜单的组件名称  菜单的权限字符,如果之一不同,则是新菜单
      if (
        item.name == oldItem.name &&
        item.component == oldItem.component &&
        item.permission == oldItem.permission &&
        item.path == item.path
      ) {
        item['ifNew'] = false
        item['oldMenuId'] = oldItem.id
        // 构建新旧菜单id映射关系
        oldToNewPrarentIdMap.value[oldItem.id] = item.id
      }
    })
    return item
  })

  // 将旧菜单的parentId映射成新的parentId
  oldMenuArr.value.forEach((el) => {
    el.parentId = oldToNewPrarentIdMap.value[el.parentId]
      ? oldToNewPrarentIdMap.value[el.parentId]
      : el.parentId
  })

  return [...newMenuList, ...oldMenuArr.value]
}

//设置默认选中菜单(目前为demo菜单,作为菜单垫片,避免需要经常更新菜单)  todo:后续菜单功能完善,要剔除 2024-09-30
let defaultCheckedKeys = ref<number[]>([])
const setDefaultCheckedMenu = (menuArr) => {
  defaultCheckedKeys.value = []
  let demoMenuParent = menuArr.find((item) => {
    if (item.name === 'demo' && item.parentId === 0) {
      item.disabled = true //设为不可点击
      return true
    }
  })
  if (demoMenuParent !== undefined) {
    defaultCheckedKeys.value.push(demoMenuParent.id)
    menuArr
      .filter((item) => item.parentId === demoMenuParent.id)
      .forEach((item) => {
        item.disabled = true //设为不可点击
        defaultCheckedKeys.value.push(item.id)
      })
  }
  console.log(demoMenuParent)
  console.log(defaultCheckedKeys.value)
}

// 监测服务tag切换
watch(
  () => activeTab.value,
  async (newVal) => {
    menuExpand.value = false
    if (newVal) {
      try {
        serviceTabs.value.forEach((el, index) => {
          if (el.id === activeTab.value) {
            activeTabIndex.value = index
          }
        })
        formLoading.value = true
        // 获取菜单和按钮信息
        /**
         * @return newMenu 新菜单数组
         * @return oldMenu 旧菜单数组
         * 如果只有新菜单有值，旧菜单没有值，表示是订阅
         */
        const res = await getAppMenusList({
          applicationId: activeTab.value,
          applicationCode: serviceMap.value[activeTab.value].code,
          tenantId: rowData.value.id
        })

        const preHandleArr =
          res.oldMenu && res.oldMenu.length > 0
            ? handleOldNewMenu(res.oldMenu, res.newMenu)
            : res.newMenu
        console.log(preHandleArr)
        //默认选择菜单(demo菜单)  todo:后续菜单功能完善,要剔除
        setDefaultCheckedMenu(preHandleArr)
        menuOptions.value = handleTree(deepClone(preHandleArr))

        // 清干净菜单树，防止取消后再点击带上之前的数据
        treeRef.value.setCheckedKeys([])
        // 切换tag时把当前tag已经选中的值赋值进菜单树
        detailValue.value.service.forEach((el) => {
          if (el.applicationId === newVal) {
            // 记录当前的tag并且绑定对应关系，在选中菜单时能记录进原数组(todo: activeService和detailValue.service 是浅拷贝)
            activeService.value = el
            nextTick(() => {
              let isTreeNodeAll = true
              el.serviceIds.forEach((menuId: number) => {
                treeRef.value.setChecked(menuId, true, false)
              })
              if (
                el.serviceIds.length !== treeRef.value.getCheckedKeys().length ||
                el.serviceIds.length === 0
              ) {
                isTreeNodeAll = false
              }
              treeNodeAll.value = isTreeNodeAll
            })
          }
        })
      } finally {
        formLoading.value = false
      }
    }
  },
  {
    immediate: true
  }
)

// 关闭父级时，清空子级，递归清空
const closeOptions = (arr) => {
  arr.forEach((el) => {
    if (el.children) {
      closeOptions(el.children)
    }
    if (activeService.value.serviceIds.indexOf(el.id) !== -1) {
      treeRef.value.setChecked(el.id, false, false)
    }
  })
}

const beforeChange = (_val, checked) => {
  if (checked) {
    //如果是勾上，检查父级有没有勾上，没有就要顺带一起勾上
    if (_val.parentId) {
      if (activeService.value.serviceIds.indexOf(_val.parentId) === -1) {
        treeRef.value.setChecked(_val.parentId, true, false)
      }
    }
  } else {
    //如果是取消选中，把子级全都清空
    if (_val.children) {
      closeOptions(_val.children)
    }
  }
}
//记录选择菜单id列表
const checkChange = (_val, checked) => {
  console.log(_val, checked)
  activeService.value.serviceIds = checked.checkedKeys
}

const formLoading = ref(false)

const menuOptions: Ref<Array<any>> = ref([])

// 初始选中的数据
const orignalAllCheckedIdList: Ref<String[] | number[]> = ref([])

const orignalAllCheckedMap: any = ref({})

// 切换业务端
const changeSizeId = async (val) => {
  try {
    formLoading.value = true
    orignalAllCheckedIdList.value = []
    detailValue.value.service = []
    activeTab.value = undefined

    // 获取当前交互端所有选中的权限，后端返回一个对象Map
    const checkMap = await getSubscriptionMenu({
      tenantId: rowData.value.id,
      clientId: val
    })

    // 保存为原始数据，用作提交时过滤出新增和删除的权限
    orignalAllCheckedMap.value = deepClone(checkMap)

    // 把所有的选中的作为每个服务的初始选中（不同服务的菜单id不同）
    // orignalAllCheckedIdList.value = res.map((el) => el.id)

    // getSubscriptionGetByAppId

    detailValue.value.service = serviceTabs.value.map((el) => {
      return {
        applicationId: el.id,
        serviceIds: checkMap[el.id] ? [...checkMap[el.id]].map((el) => el.id) : [],
        addMenuIdList: [],
        removeMenuIdList: [],
        moduleService: '',
        moduleImage: '',
        hasModule: false
      }
    })

    detailValue.value.service.forEach(async (el) => {
      const res = await getSubscriptionGetByAppId({
        tenantId: rowData.value.id,
        appId: el.applicationId
      })
      el.moduleService = res.moduleService ? res.moduleService : ''
      el.moduleImage = res.moduleImage ? res.moduleImage : ''
      el.hasModule = res.moduleImage || res.moduleService ? true : false
    })

    activeTab.value = serviceTabs.value[0]?.id
  } finally {
    formLoading.value = false
  }
}

interface serviceTabsVO {
  applicationCode: string
  name: string
  id: string
}

const serviceTabs: Ref<Array<serviceTabsVO | never>> = ref([])

const serviceMap = ref({})

const rowData = ref()

const oprateType = ref('')

import { ClientVO } from '@/store/modules/user'

const clientList: Ref<Array<ClientVO>> = ref([])

const getClientList = async () => {
  // 后端限制最大100
  const res = await getTenantClientPage({ pageNo: 1, pageSize: 100 })
  clientList.value = res.list
}

const open = async (row, type) => {
  newMenuIdArr.value = []
  getClientList()
  activeTab.value = undefined
  oprateType.value = type
  rowData.value = row
  // 申请的所有服务
  const res = await getTenantApplicationService({ tenantId: row.id })
  serviceTabs.value = res
  serviceMap.value = serviceTabs.value.reduce((a, b) => {
    return { ...a, [b.id]: b }
  }, {})
  show.value = true
}

defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
.del {
  text-decoration: line-through;
}
</style>
