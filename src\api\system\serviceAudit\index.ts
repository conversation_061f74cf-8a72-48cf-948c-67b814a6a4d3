import request from '@/config/axios'

// 订阅服务接口
export const applicationSubscriptionUpdate = async (params, data) => {
  return await request.post({ url: '/system/tenant-application-subscription/update', params, data })
}

// 租户审核接口
export const tenantAudit = async (params, data?) => {
  return await request.post({ url: '/system/tenant/audit', params, data })
}

// 获取租户审核列表接口
export const getTenantPage = async (params: PageParam) => {
  return await request.get({ url: '/system/tenant/page', params })
}

// 获取租户审核列表接口-新
export const getTenantAuditList = async (params: PageParam) => {
  return await request.get({ url: '/system/tenant/audit-list', params })
}

// 租户申请的服务列表
export const getTenantApplicationService = async (params) => {
  return await request.get({
    url: '/system/tenant-application/list-audit',
    params
  })
}

// 租户已订阅的菜单列表(端聚合)
export const getSubscriptionMenu = async (params) => {
  return await request.get({
    url: '/system/menu/subscription-menu',
    params
  })
}

// 获取镜像和服务
export const getSubscriptionGetByAppId = async (params) => {
  return await request.get({
    url: '/system/tenant-application-subscription/getSubscription',
    params
  })
}
