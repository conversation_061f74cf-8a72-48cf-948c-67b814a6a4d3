export {}
declare global {
  interface Fn<T = any> {
    (...arg: T[]): T
  }

  type Nullable<T> = T | null

  type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>

  type Recordable<T = any, K = string> = Record<K extends null | undefined ? string : K, T>

  type ComponentRef<T> = InstanceType<T>

  type LocaleType = 'zh-CN' | 'zh-TW' | 'en'
  //国际版类型  en_US 为国际版  zh_CN 为国内版
  interface internationalType {
    /**国际版 */
    en_US: boolean
    /**国内版 */
    zh_CN: boolean
  }

  type AxiosHeaders =
    | 'application/json'
    | 'application/x-www-form-urlencoded'
    | 'multipart/form-data'

  type AxiosMethod = 'get' | 'post' | 'delete' | 'put' | 'GET' | 'POST' | 'DELETE' | 'PUT'

  type AxiosResponseType = 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream'

  interface AxiosConfig {
    params?: any
    data?: any
    url?: string
    method?: AxiosMethod
    headersType?: string
    responseType?: AxiosResponseType
  }

  interface IResponse<T = any> {
    code: string
    data: T extends any ? T : T & any
  }

  interface PageParam {
    pageSize?: number
    pageNo?: number
  }

  interface Tree {
    id: number | undefined
    name: string
    children?: Tree[] | any[]
  }

  // 定义一个泛型接口，其中 T 是 list 属性的类型
  interface PromiseWithList<T> {
    list: T[]
    total: number
  }

  // 定义一个类型别名，表示返回 PromiseWithList 的 Promise 对象
  type PromiseListWithTotal<T> = Promise<PromiseWithList<T>>

  // 声明全局变量类型
  declare const __APP_INFO__: {
    buildTime: string
    GIT_COMMIT_DATE: string
    GIT_HASH: string
    GIT_LAST_COMMIT_MESSAGE: string
    GIT_TAG: string
  }
}
