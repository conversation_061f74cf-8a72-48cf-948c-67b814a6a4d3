<!--
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-16 09:44:40
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2025-03-11 09:00:50
 * @Description: 
-->
<template>
  <el-drawer v-model="agentOperationStore.ifShowDrawer" title="代客操作" direction="rtl" size="70%">
    <div v-loading="loading">
      <el-form class="" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
        <el-form-item label="客户" prop="proxyTenantId">
          <CustomerSelect v-model:customerCode="queryParams.proxyTenantId" class="!w-240px" />
        </el-form-item>

        <el-form-item :label="t('pay.app.createTime')" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            :start-placeholder="t('common.startDateText')"
            :end-placeholder="t('common.endDateText')"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"
            ><icon-ep-search style="font-size: 12px" class="mr-5px" />{{ t('common.query') }}
          </el-button>
          <el-button @click="resetQuery"
            ><icon-ep-refresh style="font-size: 12px" class="mr-5px" />{{ t('common.reset') }}
          </el-button>
          <el-button
            type="danger "
            size="small"
            @click="
              () => {
                ifShowCreateDialog = true
              }
            "
            >创建长效token
          </el-button>
        </el-form-item>
      </el-form>
      <el-table border :data="agentOperationStore.getCustomerList">
        <el-table-column property="customerName" label="客户名称" min-width="200" />
        <el-table-column
          property="proxyTenantId"
          label="租户Id"
          width="200"
          show-overflow-tooltip
        />
        <el-table-column
          :label="t('pay.app.createTime')"
          align="center"
          prop="createTime"
          width="180"
          :formatter="dateFormatter"
        />
        <el-table-column
          label="accessToken"
          align="center"
          prop="accessToken"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="refreshToken"
          align="center"
          prop="refreshToken"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column label="代客操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="agentOperationStore.setCurrentCustomer(scope.row)"
            >
              切换当前客户
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="agentOperationStore.pageTotal"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </div>
  </el-drawer>
  <Dialog v-model="ifShowCreateDialog">
    <el-form class="w-full" :model="createData" ref="createFormRef" label-width="80px">
      <el-form-item label="租户Id" prop="proxyTenantId">
        <CustomerSelect v-model:customer-code="createData.proxyTenantId" class="!w-240px" />
      </el-form-item>

      <el-form-item label="访问令牌	" prop="accessToken">
        <el-input v-model="createData.accessToken" class="!w-240px" />
      </el-form-item>
      <el-form-item label="刷新令牌		" prop="refreshToken">
        <el-input v-model="createData.refreshToken" class="!w-240px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="createNewToken">确定 </el-button>
        <el-button
          @click="
            () => {
              ifShowCreateDialog = false
            }
          "
          >取消
        </el-button>
      </el-form-item>
    </el-form>
  </Dialog>
</template>

<script setup lang="ts">
defineOptions({
  name: 'AgentOperationDrawer'
})

import { dateFormatter } from '@/utils/formatTime'

import { agentOperationQueryDataType } from '@/api/system/agentOperation'

const { t } = useI18n()

import { useAgentOperationStore } from '@/store/modules/agentOperation'
const agentOperationStore = useAgentOperationStore()

const loading = ref<boolean>(false)
//查询
const queryFormRef = ref<any>()
const queryParams = ref<agentOperationQueryDataType>({
  createTime: null,
  proxyTenantId: '',
  pageSize: 10,
  pageNo: 1
})

const handleQuery = async () => {
  try {
    loading.value = true
    await agentOperationStore.getTokenList(queryParams.value)
    loading.value = false
  } finally {
  }
}
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

//弹出框-创建token
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

const ifShowCreateDialog = ref<boolean>(false)

const createData = ref({
  userId: [wsCache.get(CACHE_KEY.USER).user.id],
  proxyTenantId: '',
  accessToken: '',
  refreshToken: ''
})

import { createLongLivedToken } from '@/api/system/agentOperation'

const createNewToken = async () => {
  ElMessageBox.alert('是否要重新创建长效token?', '提示', {
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  })
    .then(async () => {
      await createLongLivedToken(createData.value)
      ifShowCreateDialog.value = false
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消'
      })
    })
}
</script>
