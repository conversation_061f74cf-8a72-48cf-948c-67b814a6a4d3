<template>
  <Dialog
    v-model="dialogVisible"
    :title="t('common.detail')"
    width="60%"
    maxHeight="70vh"
    :scroll="true"
  >
    <el-descriptions :column="2">
      <el-descriptions-item :label="t('pay.order.payMerchantName')">{{
        detailData.merchantName ?? t('common.emptyText')
      }}</el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.appName')">{{
        detailData.appName
      }}</el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.payMerchantName')">{{
        detailData.subject
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider />
    <el-descriptions :column="2">
      <el-descriptions-item :label="t('pay.order.merchantOrderId')">
        <el-tag>{{ detailData.merchantOrderId }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.channelOrderNo')">
        <el-tag class="tag-purple">{{ detailData.channelOrderNo ?? t('common.emptyText') }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.no')">
        <el-tag class="tag-pink">
          {{ detailData.no ?? t('common.emptyText') }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.price')">
        <el-tag type="success">￥{{ (detailData.price / 100).toFixed(2) }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.channelFeePrice')">
        <el-tag type="warning"> ￥{{ (detailData.channelFeePrice / 100).toFixed(2) }} </el-tag>
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.channelFeeRate')">
        {{ detailData.channelFeeRate / 100 }}%
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.status')">
        <dict-tag :type="DICT_TYPE.PAY_ORDER_STATUS" :value="detailData.status" />
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.notifyStatus')">
        <dict-tag :type="DICT_TYPE.PAY_ORDER_NOTIFY_STATUS" :value="detailData.notifyStatus" />
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.notifyUrl2')">{{
        detailData.notifyUrl
      }}</el-descriptions-item>
      <el-descriptions-item :label="t('common.createTime')">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.successTime')">
        {{ formatDate(detailData.successTime) }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.expireTime')">
        {{ formatDate(detailData.expireTime) }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.notifyTime')">
        {{ formatDate(detailData.notifyTime) }}
      </el-descriptions-item>
    </el-descriptions>
    <el-divider />
    <el-descriptions :column="2">
      <el-descriptions-item :label="t('pay.order.payChannel')">
        {{ PayChannelEnum[detailData?.channelCode?.toLocaleUpperCase()]?.name }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.userIp')">{{
        detailData.userIp
      }}</el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.refundStatus')">
        <dict-tag :type="DICT_TYPE.PAY_ORDER_REFUND_STATUS" :value="detailData.refundStatus" />
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.refundTimes')">{{
        detailData.refundTimes
      }}</el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.amount')">
        <el-tag type="warning">
          {{ detailData.refundPrice / 100 }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    <el-divider />
    <el-descriptions :column="1" border direction="vertical">
      <el-descriptions-item :label="t('pay.order.body')">
        {{ detailData.body }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('pay.order.desc1')">
        <div style="width: 700px; overflow: auto">
          <vue-json-pretty :data="jsonContent" />
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import * as OrderApi from '@/api/pay/order'
import { formatDate } from '@/utils/formatTime'
import { PayChannelEnum } from '@/utils/constants'
//json美化插件
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
let jsonContent = ref<any>()

const { t } = useI18n() // 国际化
defineOptions({ name: 'PayOrderDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单的加载中
const detailData = ref({}) as any

/** 打开弹窗 */
const open = async (id: number) => {
  jsonContent.value = '' //先重置数据,避免重复打开弹窗时数据错乱
  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    detailData.value = await OrderApi.getOrderDetail(id)
    jsonContent.value = JSON.parse(detailData.value.extension?.channelNotifyData)
    if (jsonContent.value?.rawData) {
      jsonContent.value.rawData = JSON.parse(jsonContent.value.rawData)
    }
  } finally {
    detailLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
<style>
.tag-purple {
  color: #722ed1;
  background: #f9f0ff;
  border-color: #d3adf7;
}

.tag-pink {
  color: #eb2f96;
  background: #fff0f6;
  border-color: #ffadd2;
}
</style>
