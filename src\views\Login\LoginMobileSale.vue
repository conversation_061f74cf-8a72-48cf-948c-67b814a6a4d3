<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-25 11:47:52
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-06-09 09:55:06
 * @Description:  用于销售端短信验证码登录
-->
<template>
  <Transition
    appear
    enter-active-class="animate__animated animate__bounceInRight animate__fadeIn"
    leave-active-class="animate__fadeOut animate__animated"
  >
    <div class="w-full h-full flex flex-col justify-center items-center">
      <div
        class="w-full flex justify-center items-center absolute top-0 h-12 text-[white] bg-[#409eff] text-lg"
      >
        销售端短信验证码登录
      </div>
      <div
        class="flex justify-center z-1111 bg-white w-[100%] @2xl:max-w-600px @xl:max-w-600px @md:max-w-600px @lg:max-w-600px"
      >
        <el-form ref="formRef" label-width="auto" :model="loginForm" class="w-[80%]">
          <el-form-item
            prop="tel"
            :rules="[
              {
                validator: useValidator(allPattern.phonePattern).byPattern,
                required: true,
                trigger: ['blur', 'change'],
                message: t('sys.login.inputMobile')
              }
            ]"
          >
            <el-input
              size="large"
              v-model="loginForm.tel"
              :placeholder="t('sys.login.inputMobile')"
              :prefix-icon="iconMobile"
              name="tel"
            />
          </el-form-item>
          <el-form-item
            prop="code"
            :rules="{
              required: true,
              trigger: ['blur', 'change'],
              message: t('sys.login.smsPlaceholder')
            }"
          >
            <div class="flex flex-row flex-1">
              <el-input
                size="large"
                v-model="loginForm.code"
                :placeholder="t('sys.login.smsPlaceholder')"
              />
              <el-button
                size="large"
                :disabled="phoneTime > 1"
                class="ml-15px"
                @click="getPhoneCode()"
              >
                {{ phoneTime < 1 ? defaultStr : `${phoneTime}${t('sys.login.secSend')}` }}
              </el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button
              size="large"
              :loading="loginLoading"
              class="w-[100%]"
              type="primary"
              @click="handleLogin"
              >{{ t('login.login') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </Transition>
</template>
<script setup lang="ts">
defineOptions({
  name: 'loginSale'
})

const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('login')

const { query } = useRoute()

import { useDesign } from '@/hooks/web/useDesign'
const { t } = useI18n()
import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

import { usePermissionStore } from '@/store/modules/permission'
const permissionStore = usePermissionStore()

const emit = defineEmits(['changeFormType'])

//表单校验

import { useValidator, allPattern } from '@/hooks/web/useValidator'

//手机登录
import { useIcon } from '@/hooks/web/useIcon'
const iconMobile = useIcon({ icon: 'ep:cellphone' })

import * as LoginApi from '@/api/login'
import type { FormInstance } from 'element-plus'
let formRef = ref<FormInstance>()
let loginForm = ref({
  tel: '',
  code: ''
})
const loginLoading = ref(false)

const defaultStr = ref(t('sys.login.sendVer'))
const phoneTime: Ref<number> = ref(0)
import { useFormValid, useFormValidField, LOGIN_TYPE } from './components/useLogin'

const getPhoneCode = async () => {
  const { validFormField } = useFormValidField(formRef, 'tel')
  await validFormField()

  try {
    const res = await LoginApi.createAndSendVerificationCode({
      sourceType: 10,
      scene: 10,
      source: loginForm.value.tel
    })
    if (res.code !== 0) {
      return
    }
    ElMessage.success(t('sys.login.sendSuccess'))
    if (phoneTime.value < 60) {
      phoneTime.value = 60
      wsCache.set('phoneTime', phoneTime.value)
    }
  } catch (e) {
    // ElMessage.error(t('sys.login.sendFail'))
  } finally {
  }
}
// 倒计时器
watch(
  () => phoneTime.value,
  () => {
    if (phoneTime.value !== 0) {
      if (phoneTime.value < 1) {
        setTimeout(() => {
          phoneTime.value = 0
          wsCache.delete('phoneTime')
        }, 1000)
      } else {
        setTimeout(() => {
          phoneTime.value = phoneTime.value - 1
          wsCache.set('phoneTime', phoneTime.value)
        }, 1000)
      }
    }
  }
)
// 获取倒计时的缓存
onMounted(() => {
  phoneTime.value = wsCache.get('phoneTime')
})

//单点登录
import { useSsoStore } from '@/store/modules/sso'
const ssoStore = useSsoStore()

// 登录
import { encrypt } from '@/utils/jsencrypt'
import { aesEncrypt, getAesKey } from '@/utils/ase'
import * as authUtil from '@/utils/auth'

const clientId = import.meta.env.VITE_APP_CLIENT_ID
const handleLogin = async () => {
  const { validForm } = useFormValid(formRef)
  const data = await validForm()
  if (!data) {
    loginLoading.value = false
    return
  }

  loginLoading.value = true
  try {
    //手机验证码
    let obj: LoginApi.AuthV2LoginReqVO<any> = {
      loginMethod: 50,
      payload: {},
      sk: ''
      // imageVerificationCodeId: '',
      // imageVerificationCode: ''
    }

    let valueObj: LoginApi.TelSmsLoginPayload = {
      clientId,
      tel: loginForm.value.tel,
      code: loginForm.value.code
    }

    let publicKey = ''

    const aesKey = getAesKey()

    publicKey = await LoginApi.getEncodeKey()

    obj.payload = aesEncrypt(JSON.stringify(valueObj), aesKey)
    obj.sk = encrypt(aesKey, publicKey) as string

    const res = await LoginApi.v2login(obj)

    if (!res) {
      return
    }

    authUtil.setTenantId(res.loginInfo.tenantId)

    ElLoading.service({
      lock: true,
      text: t('login.loadingSystem'),
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 查看当前账户，是否从来没有重置过密码，产品要求必须要重置过密码才能使用其他功能
    if (res.loginInfo.loginFirstTime) {
      sessionStorage.setItem('firstLoginInfo', JSON.stringify({ ...res }))
      emit('changeFormType', LOGIN_TYPE.RESET_PASSWORD)

      loginLoading.value = false
      return
    }

    // 删除倒计时的缓存
    wsCache.delete('phoneTime')

    authUtil.setToken(res)

    if (!redirect.value) {
      redirect.value = '/HomePage'
    }
    // 判断是否为SSO登录
    if (redirect.value.indexOf('sso') !== -1) {
      //单点登录获取授权码后跳转
      let clientId = redirect.value.split('client_id=')[1] as string
      let redirectUri = query.redirect_uri as string
      ssoStore.ssoAuth(clientId, redirectUri)
      // window.location.href = window.location.href.replace('/home?redirect=', '')
    } else {
      push({ path: redirect.value || permissionStore.addRouters[0].path })
    }
  } catch (error) {
    loginLoading.value = false
    // message.error('登录失败,请联系管理员!' + error)
    // 删除倒计时的缓存
    wsCache.delete('phoneTime')
  } finally {
    setTimeout(() => {
      const loadingInstance = ElLoading.service()
      loadingInstance.close()
    }, 400)
  }
}

const loginType = ref<number>(LOGIN_TYPE.LOGIN) //1是登录，2是忘记密码，3是重置密码

const timer = ref()

const timeCount = ref(0)

const countDown = () => {
  timeCount.value = timeCount.value === 0 ? 300 : timeCount.value
  timer.value = setInterval(() => {
    if (timeCount.value > 0) {
      sessionStorage.setItem('timeCount', timeCount.value.toString())
      timeCount.value--
    } else {
      sessionStorage.removeItem('timeCount')
      clearInterval(timer.value)
    }
  }, 1000)
}

const firstLoginInfo = computed(() => sessionStorage.getItem('firstLoginInfo'))

watch(
  firstLoginInfo,
  (newVal) => {
    newVal && (loginType.value = LOGIN_TYPE.RESET_PASSWORD)
  },
  {
    immediate: true
  }
)

//跳转
import type { RouteLocationNormalizedLoaded } from 'vue-router'
const { currentRoute, push } = useRouter()

const redirect = ref<string>('')
watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    redirect.value = route?.query?.redirect as string
  },
  {
    immediate: true
  }
)

import { AccessTokenKey, RefreshTokenKey } from './components/useLogin'

onMounted(() => {
  wsCache.delete(AccessTokenKey)
  wsCache.delete(RefreshTokenKey)
  if (sessionStorage.getItem('timeCount')) {
    timeCount.value = parseInt(sessionStorage.getItem('timeCount') as string)
    countDown()
  }
})
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-login;

.#{$prefix-cls} {
}
</style>
