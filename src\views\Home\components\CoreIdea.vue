<template>
  <div class="core-idea">
    <section class="w-full">
      <el-image
        v-if="ifEn"
        class="w-full hover-scale"
        :src="`${basePath}/Home/CoreIdea/en_US/core_idea_banner.webp`"
        alt=""
      />
      <el-image
        v-else
        class="w-full hover-scale"
        :src="`${basePath}/Home/CoreIdea/core_idea_banner.webp`"
        alt=""
      />
    </section>

    <section
      style="background: #faf9f7"
      class="flex flex-col justify-center items-center py-143px text-center <2xl:(py-102px)"
    >
      <div style="color: #000000" class="text-5xl w-[80%] <2xl:(text-4xl w-[60%])">
        {{ t('home.CoreIdea.h1') }}
      </div>
      <div
        style="color: #999999"
        class="mt-39px text-center text-2xl w-[80%] <2xl:(mt-28px text-xl w-[60%])"
      >
        {{ t('home.CoreIdea.h1_item1') }}
      </div>
      <div class="mt-63px flex flex-row justify-center items-center <2xl:(mt-45px)">
        <div
          class="hover-scale w-433px min-h-282px p-2 bg-white flex flex-col justify-center items-center <2xl:(w-308px min-h-201px) shadow-xl"
        >
          <el-image
            lazy
            class="w-64px h-64px <2xl:(w-46px h-46px) scale-move-animation"
            :src="`${basePath}/Home/CoreIdea/core_idea_sendCard.png`"
            alt=""
          />
          <div
            style="color: #000000"
            class="mt-36px text-18px leading-18px font-bold <2xl:(mt-26px text-14px leading-14px)"
          >
            {{ t('home.CoreIdea.title1') }}
          </div>
          <div
            class="mt-31px text-15px px-4 leading-15px text-[#6b7280] <2xl:(mt-22px text-12px leading-12px)"
          >
            {{ t('home.CoreIdea.title1_item1') }}
          </div>
        </div>
        <div
          class="hover-scale w-433px min-h-282px p-2 bg-white flex flex-col justify-center items-center mx-12 <2xl:(mx-36px w-308px min-h-201px) shadow-xl"
        >
          <el-image
            lazy
            class="w-64px h-64px <2xl:(w-46px h-46px) scale-move-animation"
            :src="`${basePath}/Home/CoreIdea/core_idea_sendCard2.png`"
            alt=""
          />
          <div
            style="color: #000000"
            class="mt-36px text-18px leading-18px font-bold <2xl:(mt-26px text-14px leading-14px)"
          >
            {{ t('home.CoreIdea.title2') }}
          </div>
          <div
            class="mt-31px text-15px px-4 leading-15px text-[#6b7280] <2xl:(mt-22px text-12px leading-12px)"
          >
            {{ t('home.CoreIdea.title2_item1') }}
          </div>
        </div>
        <div
          class="hover-scale w-433px min-h-282px p-2 bg-white flex flex-col justify-center items-center <2xl:(w-308px min-h-201px) shadow-xl"
        >
          <el-image
            lazy
            class="w-64px h-64px <2xl:(w-46px h-46px) scale-move-animation"
            :src="`${basePath}/Home/CoreIdea/core_idea_sendCard3.png`"
            alt=""
          />
          <div
            style="color: #000000"
            class="mt-36px text-18px leading-18px font-bold <2xl:(mt-26px text-14px leading-14px)"
          >
            {{ t('home.CoreIdea.title3') }}
          </div>
          <div
            class="mt-31px text-15px leading-15px text-[#6b7280] <2xl:(mt-22px text-12px leading-12px)"
          >
            {{ t('home.CoreIdea.title3_item1') }}
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'CoreIdea'
})

const { t, ifEn } = useI18n()
const basePath = import.meta.env.BASE_URL === '/' ? '' : import.meta.env.BASE_URL
</script>

<style lang="scss" scoped>
@keyframes animation-scale-move {
  0% {
    transform: scale(1) translate(0, 0);
  }

  50% {
    transform: scale(1.3) translate(-10px, -10px);
  }

  to {
    transform: scale(1) translate(0, 0);
  }
}

.scale-move-animation {
  animation: animation-scale-move 3s infinite;
}

.core-idea {
  .bg-white {
    background: #ffffff;
  }
}
</style>
