/**支付管理 */ export default {
  merchant: {
    no: 'Merchant ID',
    name: 'Merchant Name',
    shortName: 'Merchant Short Name',
    status: 'Status',
    remark: 'Remark',
    createTime: 'Create Time',
    noPlaceholder: 'Please enter Merchant ID',
    namePlaceholder: 'Please enter Merchant Name',
    shortNamePlaceholder: 'Please enter Merchant Short Name',
    remarkPlaceholder: 'Please enter Remark',
    statusPlaceholder: 'Dictionary Status',
    id: 'Merchant Number',
    merchantXls: 'Merchant Information',
    desc1: 'Are you sure you want to {text} the merchant {name}?',
    rMsg1: 'Merchant name cannot be empty',
    rMsg2: 'Merchant short name cannot be empty',
    rMsg3: 'Status cannot be empty',
    desc2: 'Add Merchant Information',
    desc3: 'Modify Merchant Information'
  },
  order: {
    merchantId: 'Merchant ID',
    appId: 'Application ID',
    channelCode: 'Channel Code',
    merchantOrderId: 'Merchant Order ID',
    channelOrderNo: 'Channel Order Number',
    status: 'Payment Status',
    refundStatus: 'Refund Status',
    notifyStatus: 'Notification Status',
    createTime: 'Creation Time',
    merchantOrderIdPlaceholder: 'Please enter the merchant order ID',
    channelOrderNoPlaceholder: 'Please enter the channel order number',
    merchantIdPlaceholder: 'Please select the merchant',
    appIdPlaceholder: 'Please select the application',
    channelCodePlaceholder: 'Please enter the channel',
    statusPlaceholder: 'Please select the payment status',
    refundStatusPlaceholder: 'Please select the refund status',
    notifyStatusPlaceholder: 'Please select the order callback merchant status',
    id: 'ID',
    payMerchantName: 'Merchant Name',
    appName: 'Application Name',
    channelCodeName: 'Channel Name',
    subject: 'Product Title',
    body: 'Product Description',
    notifyUrl: 'Asynchronous Notification URL',
    amount: 'Payment Amount',
    channelFeeAmount: 'Handling Fee Amount',
    refundAmount: 'Refund Amount',
    bizNotifyStatusName: 'Callback Status',
    successTime: 'Payment Time',
    payOrder: 'Payment Order',
    merchant: 'Merchant',
    pay: 'Pay',
    no: 'Payment Order Number',
    price: 'Amount',
    channelFeePrice: 'Handling Fee Price',
    channelFeeRate: 'Handling Fee Rate',
    notifyUrl2: 'Callback URL',
    expireTime: 'Expiration Time',
    notifyTime: 'Notification Time',
    payChannel: 'Payment Channel',
    userIp: 'Payment IP',
    refundTimes: 'Refund Times',
    desc1: 'Payment channel asynchronous callback content',
    name: 'Barcode',
    valuePlaceholder: 'Please enter the barcode',
    payInfo: 'Payment Information',
    orderId: 'Payment Order ID',
    body2: 'Product Content',
    expireTime2: 'Expiration Time',
    returnUrl: 'Redirect URL',
    desc2: 'Select Alipay payment',
    desc3: 'Submitting payment...',
    desc4: 'Select WeChat Pay',
    desc5: 'Or use',
    desc6: '(Barcode scanner/Box scanner)',
    desc7: 'Scan',
    paySure: 'Confirm Payment',
    desc8: 'Payment order ID not provided, cannot view corresponding payment information',
    desc9: 'Payment order does not exist, please check!',
    desc10: 'Payment order is not in a pending payment status, please check!',
    desc11: '"Alipay" barcode payment',
    desc12: 'Payment method not implemented:',
    payWin: 'Payment Window',
    desc13: 'Please use the mobile browser to "Scan QR Code"',
    desc14: 'Please use "Alipay" to scan and pay',
    desc15: 'Please use WeChat "Scan QR Code" to pay',
    desc16: 'Payment successful!',
    desc17: 'Payment has been closed!'
  },
  refund: {
    merchantId: 'Merchant ID',
    appId: 'Application ID',
    channelCode: 'Channel Code',
    type: 'Refund Type',
    merchantRefundNo: 'Payment Order Number',
    status: 'Refund Status',
    notifyStatus: 'Refund Notification Status',
    createTime: 'Creation Time',
    merchantRefundNoPlaceholder: 'Please enter the merchant refund order number',
    merchantIdPlaceholder: 'Please select the merchant',
    appIdPlaceholder: 'Please select the application information',
    channelCodePlaceholder: 'Please enter the channel',
    typePlaceholder: 'Please select the refund type',
    statusPlaceholder: 'Please select the refund status',
    notifyStatusPlaceholder: 'Please select the callback status for merchant refund result',
    id: 'ID',
    payMerchantName: 'Merchant Name',
    appName: 'Application Name',
    channelCodeName: 'Channel Name',
    orderId: 'Transaction Order ID',
    merchantOrderId: 'Merchant Order ID',
    payAmount: 'Payment Amount (Yuan)',
    refundAmount: 'Refund Amount (Yuan)',
    bizNotifyStatusName: 'Callback Status',
    reason: 'Reason for Refund',
    successTime: 'Refund Success Time',
    status_0: 'Not Refunded',
    status_10: 'Refund Successful',
    status_20: 'Refund Failed',
    merchantOrderId2: 'Merchant Order Number',
    merchantRefundId: 'Merchant Refund Order ID',
    merchantRefundNoTag1: 'Transaction',
    merchantRefundNoTag2: 'Channel',
    payPrice: 'Payment Price',
    refundPrice: 'Refund Price',
    expireTime: 'Refund Expiry Time',
    channelCode2: 'Payment Channel',
    userIp: 'Payment IP',
    notifyUrl: 'Callback URL',
    notifyStatus2: 'Callback Status',
    notifyTime: 'Callback Time',
    channelOrderNo: 'Channel Order Number',
    channelRefundNo: 'Channel Refund Number',
    channelErrorCode: 'Channel Error Code',
    channelErrorMsg: 'Channel Error Message Description',
    channelNotifyData: 'Channel Additional Parameters'
  },
  app: {
    name: 'Application Name',
    contactName: 'Merchant Name',
    status: 'Enabled Status',
    createTime: 'Creation Time',
    namePlaceholder: 'Please enter the application name',
    contactNamePlaceholder: 'Please enter the merchant name',
    statusPlaceholder: 'Please select the enabled status',
    id: 'Merchant ID',
    code: 'Application Code',
    payMerchantName: 'Merchant Name',
    alipayCfg: 'Alipay Configuration',
    wxH5Cfg: 'WeChat Configuration',
    payAppXls: 'Payment Application Information',
    merchantId: 'Associated Merchant',
    type: 'Merchant Type',
    payNotifyUrl: 'Payment Result Callback URL',
    refundNotifyUrl: 'Refund Result Callback URL',
    remark: 'Remark',
    codePlaceholder: 'Please enter the application code',
    payNotifyUrlPlaceholder: 'Please enter the payment result callback URL',
    refundNotifyUrlPlaceholder: 'Please enter the refund result callback URL',
    remarkPlaceholder: 'Please enter a remark',
    merchantIdPlaceholder: 'Please select the associated merchant',
    desc1: 'The application code is the merchant ID on Alipay and WeChat',
    type_1: 'Alipay',
    type_2: 'WeChat',
    type_3: '徽商银行',
    rDesc1: 'The application name cannot be empty',
    rDesc2: 'Length should be between 1 to 30 characters',
    rDesc3: 'The application code cannot be empty',
    rDesc4: 'Length should be between 1 to 50 characters',
    rDesc5: 'Merchant type cannot be empty',
    rDesc6: 'Enabled status cannot be empty',
    rDesc7: 'Payment result callback URL cannot be empty',
    rDesc8: 'Refund result callback URL cannot be empty',
    rDesc9: 'Merchant ID cannot be empty',
    newApp: 'Add New Application',
    editApp: 'Edit Application',
    feeRate: 'Channel Fee Rate',
    title1: 'Create WeChat Pay Channel',
    title2: 'Edit WeChat Pay Channel',
    config: {
      appId: 'Public Account APPID',
      mchId: 'Merchant ID',
      status: 'Channel Status',
      apiVersion: 'API Version',
      mchKey: 'Merchant Key',
      keyContent: 'apiclient_cert.p12 Certificate',
      apiV3Key: 'API V3 Key',
      privateKeyContent: 'apiclient_key.pem Certificate',
      privateCertContent: 'apiclient_cert.pem Certificate'
    },
    feeRatePlaceholder: 'Please enter the channel fee rate',
    appIdPlaceholder: 'Please enter the Public Account APPID',
    mchKeyPlaceholder: 'Please enter the merchant key',
    keyContentPlaceholder: 'Please upload the apiclient_cert.p12 certificate',
    apiV3KeyPlaceholder: 'Please enter the API V3 key',
    privateKeyContentPlaceholder: 'Please upload the apiclient_key.pem certificate',
    privateCertContentPlaceholder: 'Please upload the apiclient_cert.pem certificate',
    clickUpload: 'Click to upload',
    rWxDesc1: 'Please enter the channel fee rate',
    rWxDesc2: 'The channel status cannot be empty',
    rWxDesc3: 'Please enter the merchant ID',
    rWxDesc4: 'Please enter the Public Account APPID',
    rWxDesc5: 'The API version cannot be empty',
    rWxDesc6: 'Please enter the merchant key',
    rWxDesc7: 'Please upload the apiclient_cert.p12 certificate',
    rWxDesc8: 'Please upload the apiclient_key.pem certificate',
    rWxDesc9: 'Please upload the apiclient_cert.pem certificate',
    rWxDesc10: 'Please upload the API V3 key value',
    desc2: 'Please upload files in the specified format {fileAccept}',
    desc3: 'File size exceeds 2MB',
    zfbConfig: {
      appId: 'Open Platform APPID',
      serverUrl: 'Gateway URL',
      signType: 'Signature Algorithm Type',
      mode: 'Public Key Type',
      privateKey: 'Application Private Key',
      alipayPublicKey: 'Alipay Public Key',
      appCertContent: 'Merchant Public Key Application Certificate',
      alipayPublicCertContent: 'Alipay Public Certificate',
      rootCertContent: 'Root Certificate',
      appIdPlaceholder: 'Please enter the Open Platform APPID'
    },
    privateKeyPlaceholder: 'Please enter the application private key',
    alipayPublicKeyPlaceholder: 'Please enter the Alipay public key',
    appCertContentPlaceholder: 'Please upload the merchant public key application certificate',
    alipayPublicCertContentPlaceholder: 'Please upload the Alipay public certificate',
    rootCertContentPlaceholder: 'Please upload the root certificate',
    zfbTitle1: 'Create Alipay Payment Channel',
    zfbTitle2: 'Edit Alipay Payment Channel',
    serverUrl_1: 'Production Environment',
    serverUrl_2: 'Sandbox Environment',
    mode_1: 'Public Key Mode',
    mode_2: 'Certificate Mode',
    rzfb1: 'Please enter the channel fee rate',
    rzfb2: 'The channel status cannot be empty',
    rzfb3: 'Please enter the ID of the application created on the open platform',
    rzfb4: 'Please enter the merchant ID',
    rzfb5: 'Please enter the gateway URL',
    rzfb6: 'Please enter the signature algorithm type',
    rzfb7: 'The public key type cannot be empty',
    rzfb8: 'Please enter the merchant private key',
    rzfb9: 'Please enter the Alipay public key string',
    rzfb10: 'Please upload the merchant public key application certificate',
    rzfb11: 'Please upload the Alipay public certificate',
    rzfb12: 'Please upload the specified root certificate',
    addChannel: 'add channel'
  }
}
