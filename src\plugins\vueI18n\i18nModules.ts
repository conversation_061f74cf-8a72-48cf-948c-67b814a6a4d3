// 定义国际化模块配置接口
export interface I18nModuleConfig {
  path: string // 模块路径
  mergeMode?: 'nested' | 'flat' // 合并模式：nested-嵌套模式，flat-扁平模式
  langFiles: {
    'zh-CN': Record<string, any>
    'zh-TW': Record<string, any>
    en: Record<string, any>
  }
}

// 国际化模块配置
const i18nModules: Record<string, I18nModuleConfig> = {
  // 主国际化文件
  main: {
    path: 'locales',
    mergeMode: 'nested',
    langFiles: {
      'zh-CN': import.meta.glob(`../../locales/zh-CN/**/*.ts`, { eager: true }),
      'zh-TW': import.meta.glob(`../../locales/zh-TW/**/*.ts`, { eager: true }),
      en: import.meta.glob(`../../locales/en/**/*.ts`, { eager: true })
    }
  },
  UmvQuery: {
    path: 'components/UmvQuery/src/locales',
    mergeMode: 'nested',
    langFiles: {
      'zh-CN': import.meta.glob(`../../components/UmvQuery/src/locales/zh-CN/**/*.ts`, {
        eager: true
      }),
      'zh-TW': import.meta.glob(`../../components/UmvQuery/src/locales/zh-TW/**/*.ts`, {
        eager: true
      }),
      en: import.meta.glob(`../../components/UmvQuery/src/locales/en/**/*.ts`, { eager: true })
    }
  },
  UmvTable: {
    path: 'components/UmvTable/src/locales',
    mergeMode: 'nested',
    langFiles: {
      'zh-CN': import.meta.glob(`../../components/UmvTable/src/locales/zh-CN/**/*.ts`, {
        eager: true
      }),
      'zh-TW': import.meta.glob(`../../components/UmvTable/src/locales/zh-TW/**/*.ts`, {
        eager: true
      }),
      en: import.meta.glob(`../../components/UmvTable/src/locales/en/**/*.ts`, { eager: true })
    }
  }
  // 可以在此处添加更多模块配置
}

export default i18nModules
