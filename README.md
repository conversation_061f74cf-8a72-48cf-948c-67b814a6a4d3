# 前端开发说明文档

#  1.目录结构

```
.
├── .github # github workflows 相关
├── .husky # husky 配置
├── .vscode # vscode 配置
├── mock # 自定义 mock 数据及配置
├── public # 静态资源
├── src # 项目代码
│   ├── api # api接口管理
│   ├── assets # 静态资源
│   ├── components # 公用组件
│   ├── hooks # 常用hooks
│   ├── layout # 布局组件
│   ├── locales # 语言文件
│   ├── plugins # 外部插件
│   ├── router # 路由配置
│   ├── store # 状态管理
│   ├── styles # 全局样式
│   ├── utils # 全局工具类
│   ├── views # 路由页面
│   ├── App.vue # 入口vue文件
│   ├── main.ts # 主入口文件
│   └── permission.ts # 路由拦截
├── types # 全局类型
├── .env.base # 本地开发环境 环境变量配置
├── .env.dev # 打包到开发环境 环境变量配置
├── .env.pro # 打包到生产环境 环境变量配置
├── .env.test # 打包到测试环境 环境变量配置
├── .eslintignore # eslint 跳过检测配置
├── .eslintrc.js # eslint 配置
├── .gitignore # git 跳过配置
├── .prettierignore # prettier 跳过检测配置
├── .stylelintignore # stylelint 跳过检测配置
├── .versionrc 自动生成版本号及更新记录配置
├── index.html # 入口页面
├── package.json
├── .postcssrc.js # postcss 配置
├── prettier.config.js # prettier 配置
├── README.md #  README
├── stylelint.config.js # stylelint 配置
├── tsconfig.json # typescript 配置
├── vite.config.ts # vite 配置
└── windi.config.ts # windicss 配置
```

# 2. 开始

### 2.1.安装依赖

```shell
# 安装依赖
pnpm i
```

### 2.2.开发环境启动

```shell
pnpm run dev
```

### 2.3.提示:

##### 使用pnpm命令创建的vite项目无法在vscode中获得组件类型提示

vscode组件类型提示为`unkonwn`

![image-20230703153923620](\doc\img\组件类型提示unkonwn.png)

正常情况下,应该能带出组件的类型提示

![image-20230703154347895](doc\img\正常的组件类型提示.png)

###### 解决:

该问题是由于`pnpm`下载依赖,element-plus的组件是通过unplugin自动按需引入的,vite引入该类型插件会自动生成的 components.d.ts 文件中的 `declare module '@vue/runtime-core'` 声明，在 pnpm 中只能访问项目的顶级依赖，而 @vue/runtime-core 是 vue 模块下的依赖，不是顶级依赖，导致声明语句失效。

解决方案: [使用pnpm命令创建的vite项目无法在vscode中获得组件类型提示 · Issue #406 · antfu/unplugin-vue-components · GitHub](https://github.com/antfu/unplugin-vue-components/issues/406)





# 3.调用接口规范

### 3.1. 要求:

#### 使用封装好的统一接口文件进行接口调用

- src/api/业务文件

```typescript
//引入统一封装接口文件
import request from '@/config/axios'
//接口返回值进行类型限定
export interface DeptVO {
  id?: number
  name: string
  parentId: number
  status: number
  sort: number
  leaderUserId: number
  phone: string
  email: string
  createTime: Date
}

// 查询部门（精简)列表
export const getSimpleDeptList = async (): Promise<DeptVO[]> => {
  return await request.get({ url: '/system/dept/list-all-simple' })
}

```

- src/views/业务文件

  ```typescript
  <script setup lang="ts" name="SystemDept">
  import * as DeptApi from '@/api/system/dept'  //整个模块引入定义好的接口方法
  const loading = ref(true) // 列表的加载中
  const list = ref() // 列表的数据
  
  
  /** 查询部门列表 */
  const getList = async () => {
    loading.value = true
    try {
      const data = await DeptApi.getDeptPage(queryParams)
      list.value = data
    } finally {
      loading.value = false
    }
  }
  </script>
  ```

  

### 3.2. 目的:

- 提高代码的可维护性

- 减少不必要的接口判断处理逻辑

- 接口统一管理

  

### 3.3. 背景知识:

后端提供的接口是符合`RESTful API`规范的,响应报文中data将是下面这个接口类型

```typescript
interface ResType{
 code:number
 data:any
 msg:string
}
```

项目已经实现对axios的封装将`code`和`msg`进行判断和处理,在写业务时,前端仅关心data是否存在,拿来即用即可;

### 3.4. 封装内容如下:

#### 3.4.1. 创建 axios 实例

- `baseURL` 基础路径
- `timeout` 超时时间，默认为 30000 毫秒

#### 3.4.2. Request 拦截器

- `Authorization`、`tenant-id` 请求头

  > 租户id和token 携带在请求头

- GET 请求参数的拼接

- 对`application/x-www-form-urlencoded`的post通过qs格式为URL 编码格式

#### 3.4.3. Response 拦截器

- 访问令牌 AccessToken 过期时，使用刷新令牌 RefreshToken 刷新，获得新的访问令牌
- 刷新令牌失败（过期）时，跳回首页进行登录
- 请求失败，Message 错误提示

#### 3.4.4.最终按用途封装-对外暴露方法和使用:

> 根据接口不同用途进行封装

- get 请求仅返回 `data`
- getOriginal  请求返回 `code`  、 `data` 、`msg`
- post
- postOriginal
- delete
- deleteOriginal
- put
- putOriginal
- download
- upload

> 方法名含`Original`即返回了响应报文中`data`的全部内容( `code`  、 `data` 、`msg`)

![image-20230629102451961](doc\img\接口封装.png)





### 3.5.业务接口调用该封装即可:

一个业务模块对应一个接口模块,即view目录下一个业务对应 api目录一个模块

![image-20230630101952696](doc\img\接口封装和业务调用对应.png)

`src/api`下创建业务对应模块文件:

![image-20230630102203904](doc\img\业务接口封装.png)

```typescript
//引入统一封装接口文件
import request from '@/config/axios'
//接口返回值进行类型限定
export interface DeptVO {
  id?: number
  name: string
  parentId: number
  status: number
  sort: number
  leaderUserId: number
  phone: string
  email: string
  createTime: Date
}

// 查询部门（精简)列表
export const getSimpleDeptList = async (): Promise<DeptVO[]> => {
  return await request.get({ url: '/system/dept/list-all-simple' })
}

```



`src/views/`下业务模块引入对应的api接口实例:

![image-20230630102420834](doc\img\接口调用实例.png)

```typescript
<script setup lang="ts" name="SystemDept">
import * as DeptApi from '@/api/system/dept'  //整个模块引入定义好的接口方法
const loading = ref(true) // 列表的加载中
const list = ref() // 列表的数据


/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeptApi.getDeptPage(queryParams)
    list.value = data
  } finally {
    loading.value = false
  }
}

</script>
```

上述引入接口实例方式,推荐使用ES6整个模块导入 ` import * as name from 'module-name' ` 

接口实例使用 `await` 关键字来等待异步请求完成，并使用 `try...catch` 语句来处理请求中的错误。

使用`try...finally`终止接口操作,进行loading操作

使用封装模块的好处是，已经做了统一错误处理和请求头封装以及token相关机制的业务，而不必在每个组件中单独设置 axios 实例和处理错误。



#### 3.5.1. 提醒:

 推荐直接使用`get `和post的封装请求,这2个请求,前端业务开发更多注意业务数据展示,项`code`和`msg`交给统一封装的接口进行处理即可

如果是需要将后端需要报文中的出了`data`以外的 `code`和`msg`进行处理可以使用,`getOriginal`和`postOriginal`

![image-20230629121709344](doc\img\axios封装接口调用提醒.png)





# 4 通用方法

> 对前端经常需要使用到的方法进行封装

## 4.1. 缓存Hooks

### 4.1.1 说明

- 缓存hook实现路径 ``/src/hooks/web/useCache.ts``

由`web-storage-cache` 插件封装,该插件对HTML5 `localStorage` 和 `sessionStorage` 进行了扩展，添加了超时时间，序列化方法。可以直接存储 `json` 对象，同时可以非常简单的进行超时时间的设置。

采用Vue3中自定义Hooks的形式封装,类似Vue2中`mixin`

在项目中，你可以看到很多地方都使用了 `wsCache.set` 或者 `wsCache.get`，这是基于 [web-storage-cache (opens new window)](https://github.com/wuchangming/web-storage-cache)进行封装，采用 `hook` 的形式。

本项目默认是采用 `localStorage` 的存储方式，如果更改，可以直接在`/src/hooks/web/useCache.ts`中把 `type: CacheType = 'localStorage'` 改为 `type: CacheType = 'sessionStorage'`，这样项目中的所有用到的地方，都会变成该方式进行数据存储。

如果只想单个更改，可以传入存储类型 `const { wsCache } = useCache('localStorage')`，既可只适用当前存储对象。

### 4.1.2 示例

- wsCache.set 

  > 保存缓存   

- wsCache.get    

  > 读取缓存

- wsCache.delete

  >删除指定缓存

- wsCache.clear()

  > 清除所有缓存

```typescript
//导入缓存hook
import { useCache } from '@/hooks/web/useCache'
//声明缓存hook
const { wsCache } = useCache()
//保存缓存
wsCache.set('xx', "缓存的内容")
//读取缓存
const XX = wsCache.get('xx') 
```





## 4.2. `message`Hook

> 封装的`Element-plus`消息提示框

### 4.2.1 说明

- `message`Hook实现路径 `src/hooks/web/useMessage.ts`

`message` Hook，，基于 ElMessage、ElMessageBox、ElNotification 封装，用于做消息提示、通知提示、对话框提醒、二次确认等。

### 4.2.2 示例

> 在`vite`中已经通过`AutoImport`插件全局引入,直接声明即可使用

```typescript
//直接声明messageHooks
const message = useMessage()
//使用message实例方法
message.success('消息按钮测试')
```

![image-20230704145336486](doc\img\messageHooks.png)





## 4.3. `download` 下载工具方法

> 对应后端返回的二进制流,下载保存到本地的前端实现封装

### 4.3.1 说明

`download` 下载工具方法实现路径 `src/utils/download.ts`

用于 Excel、Word、Zip、HTML 、Markdown等类型的文件下载。

###### 提示: 其他文件类型可以自己添加



### 4.3.2 示例

```typescript
//导入下载工具方法
import download from '@/utils/download'
//伪代码,接口调用获取 二进制数据
const res = await  api()
//调用工具方法,传入二进制数据,即可实现下载
download.excel(res as unknown as Blob, "文件名称")
```

## 4.4. `formatMoney` 金额千分位格式化

4.3.1 说明

formatMoney下载工具方法实现路径 `src/utils/formatMoney.ts`

### 4.3.2 示例

```typescript
//导入金额千分位工具方法
import formatMoney from '@/utils/formatMoney'
formatMoney(价格)
```



# 5 组件相关

## 5.1. 组件注册

> 组件注册可以分成两种类型：按需引入、全局注册。

### 5.1.1 按需引入

> 项目目前的组件注册机制是**按需注册**，是在需要用到的页面才引入。

Element-plus相关组件已经通过`vite`实现按需自动引入,无需在组件中引入,直接使用,如果出现组件类型不提示`unkonwn`,请查看`开始`目录下有相关解决方案

```vue
<script setup lang="ts">
import { ElBacktop } from 'element-plus'
import { useDesign } from '@/hooks/web/useDesign'

const { getPrefixCls, variables } = useDesign()

const prefixCls = getPrefixCls('backtop')
</script>

<template>
  <ElBacktop
    :class="`${prefixCls}-backtop`"
    :target="`.${variables.namespace}-layout-content-scrollbar .${variables.elNamespace}-scrollbar__wrap`"
  />
</template>
```

注意：**tsx 文件内不能使用全局注册组件**，需要手动引入组件使用。

### 5.1.2 全局注册

如果觉得按需引入太麻烦，可以进行全局注册，在`src/components/index.ts`，添加需要注册的组件。

##### *******. 其他组件

以 `Icon` 组件进行了全局注册，举个例子：

```typescript
import type { App } from 'vue'
import { Icon } from './Icon'

export const setupGlobCom = (app: App<Element>): void => {
  app.component('Icon', Icon)
}
```

##### *******. Element Plus 的组件

如果 Element Plus 的组件需要全局注册，在`/src/plugins/elementPlus/index.ts`添加需要注册的组件。

以 Element Plus 中只有 `ElLoading` 与 `ElScrollbar` 进行全局注册，举个例子：

```typescript
import type { App } from 'vue'

// 需要全局引入一些组件，如 ElScrollbar，不然一些下拉项样式有问题
import { ElLoading, ElScrollbar } from 'element-plus'

const plugins = [ElLoading]

const components = [ElScrollbar]

export const setupElementPlus = (app: App) => {
  plugins.forEach((plugin) => {
    app.use(plugin)
  })

  components.forEach((component) => {
    app.component(component.name, component)
  })
}
```



## 5.2. 公共组件

### `src/components`下的组件都是公共组件

已经通过`unplugin-vue-components` 实现自动引入公共组件,**不用import引入,直接使用即可**

>

#### 5.2.1 Pagination

> 分页组件 对 `Element Plus `的 Pagination 组件进行封装

- 位于 src/components/Pagination 内

  ![image-20230713155225419](doc\img\分页组件.png)

  

- 代码实例

```html
<template>
  <div class="h-100 w-full">
    <Pagination
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      :total="total"
    />
  </div>
</template>
<script setup lang="ts">
//分页功能
const total = ref(100)
const queryParams = ref({
  pageSize: 10,
  pageNum: 1
})
//接口调用
const getList = () => {
  //todo
}
</script>
```

#### 5.2.2 Dialog

> 弹窗组件对 Element Plus 的 Dialog 组件进行封装，支持最大化、最大高度、拖拽等特性

- 位于 `src/components/Dialog `内

  

   ![sso步骤总览图](doc\img\Dialog组件.png)

- 代码实例

```html
<template>
  <div class="h-100 w-full">
    <ElButton type="primary" @click="handleAdd()">打开Dialog</ElButton>
    
    <Dialog title="预览" v-model="dialogOpen" width="80%" :scroll="true" max-height="300px">
      我是个弹框
      <template #footer>
        <ElButton type="primary">提交</ElButton>
        <ElButton @click="dialogOpen = false">取 消</ElButton>
      </template>
    </Dialog>
  </div>
</template>
<script setup lang="ts">
const dialogOpen = ref(false)
function handleAdd() {
  dialogOpen.value = true
}
</script>
```

#### 5.2.3. ButtonPromise

> 异步 button，点击后自动 loading,异步结束后取消 loading

- 位于` src/components/ButtonPromise `内
- API

| 属性         | 说明                                                         | 类型                | 可选值 | 默认值 |
| ------------ | ------------------------------------------------------------ | ------------------- | ------ | ------ |
| clickPromise | 必填，按钮点击后开启按钮 loading，执行完传入的异步方法后取消 loading | () => Promise<void> | -----  | -----  |
| loadingText  | 选填，loading 时显示的字段                                   | string              | -----  | -----  |

 ![sso步骤总览图](doc\img\ButtonPromise.png)

- 代码实例

```html
<template>
  <div class="h-100 w-full">
    <ButtonPromise :clickPromise="clickPromise">异步按钮</ButtonPromise>
  </div>
</template>
<script setup lang="ts">

//模拟异步请求
const message = useMessage()
const apiDemo = async () => {
  return new Promise((reject) => {
    setTimeout(() => {
      message.success('异步请求')
      reject(true)
    }, 1000)
  })
}
async function clickPromise() {
  await apiDemo() //发送请求等异步任务，异步结束后，loading消失
}
</script>
```



#### 5.2.4 Qrcode 二维码组件

基于 `qrcode` 封装。

Qrcode 组件位于`src/components/Qrcode `内

##### Qrcode 属性

| 属性         | 说明                 | 类型                     | 可选值     | 默认值 |
| ------------ | -------------------- | ------------------------ | ---------- | ------ |
| tag          | 以什么标签生成二维码 | `string`                 | canvas/img | canvas |
| text         | 二维码内容           | `string`/`Array`         | -          | -      |
| options      | qrcode.js 配置项     | `QRCodeRenderersOptions` | -          | {}     |
| width        | 二维码宽度           | `number`                 | -          | 200    |
| logo         | 二维码 logo          | `QrcodeLogo`/`string`    | -          | -      |
| disabled     | 二维码是否过期       | `boolean`                | -          | false  |
| disabledText | 二维码过期提示内容   | `string`                 | -          | -      |

##### Qrcode 事件

| 方法名         | 说明                 | 回调参数 |
| -------------- | -------------------- | -------- |
| done           | 生成二维码后的回调   | -        |
| click          | 二维码点击事件       | -        |
| disabled-click | 二维码过期后点击事件 | -        |

代码实例:

```html
<script setup lang="ts">
import { Qrcode } from '@/components/Qrcode'
</script>

<template>
  <Qrcode text="vue-element-plus-admin" />
</template>
```



#### 5.2.5. Icon图标

在项目的 [`/src/assets/svgs` ]目录下，自定义了 Icon 图标，默认注册到全局中，可以在项目中任意地方使用。如下图所示：

![svg图标目录](doc\img\svg图标目录.png)

Icon 组件位于[`src/components/Icon `]内

> 用于项目内组件的展示，基本支持所有图标库（支持按需加载，只打包所用到的图标），支持使用本地 svg 和 [Iconify ](https://iconify.design/)图标。

##### 1.1 基本用法

如果以 `svg-icon:` 开头，则会在本地中找到该 `svg` 图标，否则，会加载 `Iconify` 图标。代码如下：

- 本地svg,如果是assets/svg下直接写对应的图标名,如果是assets/svg/xx 则写`  <Icon icon="svg-icon:xx-peoples" />`

```xml
<template>
  <!-- 加载本地 svg -->
  <Icon icon="svg-icon:peoples" />
  
  <!-- 加载 Iconify -->
  <Icon icon="ep:aim" />
</template>
```

##### 1.2 useIcon

如果需要在其他组件中如 ElButton 传入 `icon` 属性，可以使用 `useIcon`。代码如下：

```html
<script setup lang="ts">
import { useIcon } from '@/hooks/web/useIcon'
import { ElButton } from 'element-plus'

const icon = useIcon({ icon: 'svg-icon:save' })
</script>

<template>
  <ElButton :icon="icon"> button </ElButton>
</template>
```

`useIcon` 的 **props** 属性如下：

| 属性  | 说明     | 类型     | 可选值 | 默认值 |
| ----- | -------- | -------- | ------ | ------ |
| icon  | 图标名   | `string` | -      | -      |
| color | 图标颜色 | `string` | -      | -      |
| size  | 图标大小 | `number` | -      | 16     |



##### 2. 自定义图标

① 访问 [https://www.iconfont.cn/ (地址，搜索你想要的图标，下载 SVG 格式。如下图所示：

友情提示：其它 SVG 图标网站也可以。

![下载 SVG 格式](https://cloud.iocoder.cn/img/Vue3/Icon%E5%9B%BE%E6%A0%87/02.png)

② 将 SVG 图标添加到 [`/src/assets/svgs` ]目录下，然后进行使用。

```html
<Icon icon="svg-icon:helpless" />
```

##### 3.Element-Plus图标(按需加载)

Element-Plus图标(按需加载),需要用的地方才引入

```vue
<template>
  <ElIcon size="larged" color="red">
    <WarningFilled />
  </ElIcon>
</template>
<script setup name="ErrorNotificationIcon">
import { WarningFilled } from '@element-plus/icons-vue'
</script>
```













#### 5.2.6. ContentWrap 包裹组件

对 Element Plus 的 ElCard 组件进行封装，自带标题、边距

ContentWrap 组件：位于`[src/components/ContentWrap]`内



#### 5.2.7Editor 富文本组件

基于 [wangeditor](https://www.wangeditor.com/) 封装。

目前项目中的 `editor` 只是做了简单的封装，需要开发者根据实际情况，自行配置 `editorConfig` 属性，如，上传图片功能。

- Editor 组件位于 [`src/components/Editor`] 内

###### 用法:

```vue
<script setup lang="ts">
import { Editor } from '@/components/Editor'
import { ref} from 'vue'

const defaultHtml = ref('<p>hello <strong>world</strong></p>')

const change = (html: string) => {
  console.log(html)
}
</script>

<template>
  <Editor v-model="defaultHtml" ref="editorRef" @change="change" />
</template>
```

######  Editor 属性

| 属性         | 说明                        | 类型              | 可选值 | 默认值        |
| ------------ | --------------------------- | ----------------- | ------ | ------------- |
| editorId     | 富文本组件唯一值，必填项    | `string`          | -      | wangeEditor-1 |
| height       | 高度                        | `string`/`number` | -      | 500px         |
| editorConfig | wangeditor 组件的所有配置项 | `IEditorConfig`   | -      | -             |
| modelValue   | 内容双向绑定，支持v-model   | `string`          | -      | -             |

###### Editor 事件

| 方法名 | 说明                         | 回调参数           |
| ------ | ---------------------------- | ------------------ |
| change | 内容改变时，返回 editor 实例 | editor: IDomEditor |

###### Editor 方法

| 方法名       | 说明             | 回调参数                    |
| ------------ | ---------------- | --------------------------- |
| getEditorRef | 获取 editor 实例 | `() => Promise<IDomEditor>` |







### 5.3 BPM流程组件

#### 5.3.1 MyProcessDesigner 流程设计组件

- 作用: 流程设计器

- MyProcessDesigner 组件：位于`src/components/bpmnProcessDesigner/package/designer/index.ts`内



#### 5.3.2  MyProcessViewer 流程展示组件

- 作用:流程可拖拽展示图,展示流程实例的运行状态

- MyProcessViewer 组件：位于`[src/components/bpmnProcessDesigner/package`]内

  ```vue
  <template>
    <el-card v-loading="loading as any" class="box-card">
      <template #header>
        <span class="el-icon-picture-outline">流程图</span>
      </template>
      <MyProcessViewer
        key="designer"
        :activityData="activityList"
        :prefix="bpmnControlForm.prefix"
        :processInstanceData="processInstance"
        :taskData="tasks"
        :value="bpmnXml"
        v-bind="bpmnControlForm"
      />
    </el-card>
  </template>
  <script lang="ts" name="BpmProcessInstanceBpmnViewer" setup>
  import { propTypes } from '@/utils/propTypes'
  import { MyProcessViewer } from '@/components/bpmnProcessDesigner/package'
  import * as ActivityApi from '@/api/bpm/activity'
  
  const props = defineProps({
    loading: propTypes.bool, // 是否加载中
    id: propTypes.string, // 流程实例的编号
    processInstance: propTypes.any, // 流程实例的信息
    tasks: propTypes.array, // 流程任务的数组
    bpmnXml: propTypes.string // BPMN XML
  })
  
  const bpmnControlForm = ref({
    prefix: 'flowable'
  })
  const activityList = ref([]) // 任务列表
  
  
  /** 初始化 */
  onMounted(async () => {
    if (props.id) {
      activityList.value = await ActivityApi.getActivityList({
        processInstanceId: props.id
      })
    }
  })
  </script>
  <style>
  .box-card {
    width: 100%;
    margin-bottom: 20px;
  }
  </style>
  
  ```


# 6. 字典数据

## 6.1.字典管理

> 字典数据是在`综合服务平台`上统一管理的

将一些静态的、不经常变化的数据作为字典数据进行统一维护,提高了数据一致性,方便维护,可作为某些表格、下拉框或选项卡的选项，以方便用户进行数据输入或选择。

![image-20230714135619634](doc\img\字典数据.png)



## 6.2 业务端接入字典数据功能

### 6.2.1. 接入步骤:

1. 登录成功后,调用api接口获取到全量的字典数据,缓存在`store`(在`store`中全局共享)
2. 使用封装好的字典组件/字典工具方法

#### *******. 字典全局缓存

用户登录成功后，前端会从后端获取到全量的字典数据，缓存在 store 中。如下图所示：

![image-20230714142317625](doc\img\字典数据获取并存入store.png)

- 获取全量字典数据的接口

  `/system/dict-data/list-all-simple`

  

  - label :字典值名称
  - value:字典值
  - cssClass: 绑定css样式
  - colorType:字典高亮颜色

  

![image-20230714114452303](doc\img\获取全量字典数据接口请求.png)

这样，前端在使用到字典数据时，无需重复请求后端，提升用户体验。

不过，缓存暂时未提供刷新，所以在字典数据发生变化时，需要用户刷新浏览器，进行重新加载。

#### ******* 字典工具方法和组件封装及使用

##### *******.1. 前端字典枚举类型 DICT_TYPE   

在 [`src/utils/dict.ts` ]文件中，使用 `DICT_TYPE` 枚举了字典的 KEY。如下图所示：

> 用枚举对字典的KEY进行封装,为了方便开发中调用枚举相关的工具方法和组件

![image-20230714143238731](doc\img\字典key用枚举进行封装.png)

后续如果有新的字典 KEY，需要你自己进行添加。



##### *******.2. DictTag 字典标签组件

`tag`组件，翻译字段对应的字典展示文本，并根据 `colorType`、`cssClass` 进行高亮。使用示例如下：

- 举例:系统状态字典`COMMON_STATUS`

- ![image-20230714151647049](doc\img\字典管理数据实例-系统状态.png)

- 代码

  ```html
  <!--
      type: 字典 KEY
      value: 字典值
  -->
  <template>
    <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="0" />
    <br />
    <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="1" />
  </template>
  <script setup>
  import { DICT_TYPE } from '@/utils/dict'
  </script>
  ```

  ![image-20230714152328227](doc\img\字典标签组件.png)



##### *******.3. 字典工具方法

在 [`src/utils/dict.ts` ]文件中，提供了字典工具方法，方法如下：

```js
// 获取 dictType 对应的数据字典数组【object】
export const getDictOptions = (dictType: string) => {{ /** 省略代码 */ }

// 获取 dictType 对应的数据字典数组【int】
export const getIntDictOptions = (dictType: string) => { /** 省略代码 */ }

// 获取 dictType 对应的数据字典数组【string】
export const getStrDictOptions = (dictType: string) => { /** 省略代码 */ }

// 获取 dictType 对应的数据字典数组【boolean】
export const getBoolDictOptions = (dictType: string) => { /** 省略代码 */ }

// 获取 dictType 对应的数据字典数组【object】
export const getDictObj = (dictType: string, value: any) => { /** 省略代码 */ }
```

###### 1. 下拉框

结合 Element Plus 的下拉框组件，使用示例如下：

![image-20230714153033836](doc\img\字典下拉框组件.png)

```html
<template>
  <!-- select 下拉框 -->
  <el-select placeholder="请选择渠道编码" clearable>
    <el-option
      v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_STATUS)"
      :key="dict.value"
      :label="dict.label"
      :value="dict.value"
    />
  </el-select>
</template>
<script setup lang="ts">
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
</script>

```



###### 2. 单选框

结合 Element Plus 的单选框组件，使用示例如下：

![image-20230714153156329](doc\img\字典单选框组件.png)

```html
<template>
  <!-- radio 单选框 -->
  <el-radio
    v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
    :key="dict.value"
    :label="parseInt(dict.value)"
  >
    {{ dict.label }}
  </el-radio>
</template>
<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
</script>
```



# 7. 流程业务表单对接

## 对接步骤:

1. 在`综合服务平台`上流程绑定业务组件
2. 在`业务后端服务`上,启动流程,向`流程变量`中插入`业务id`
3. 在`业务前端`上,业务表单详情组件中监听`流程变量`

### 1. 流程绑定业务组件

绑定位置:`综合服务平台`

![流程绑定业务组件](doc\img\流程业务表单对接-流程绑定业务组件.png)

1.  在[`流程模型`]列表中选择对应的流程,点击[`修改流程`]按钮

2. [`表单类型`] 选择[ `业务表单`]

3. 分别填写[`编辑组件路径`] 和 [`详情组件路径`]

   > 现阶段仅需要填写**详情组件路径**即可

   

### 2. 启动流程

`业务创建接口`:

1. 创建业务,生成`业务id`
2. 流程启动由`业务后端`启动,在业务创建时同步发起流程
3. 流程启动将业务需要的数据传入`流程变量`中

前端不需要关心流程启动,前端仅需调用`业务创建接口`即可

![image-20230724091754315](doc\img\流程对接-流程新增.png)



### 3. 详情组件监听流程变量

>  在组件中直接注入流程变量`processVariables`对象

```html
<script setup lang="ts">
//在业务组件中注入-流程变量
let processVariables = inject('processVariables') as unknown as any
</script>
```

可以通过监听`processVariables` 调用业务回显接口

 ```javascript
watch(
  () => processVariables?.vaule,
  (val) => {
    if (val?.id) {
      //todo:监听业务id 调用业务表单回显接口
    }
  },
  {
    deep: true
  }
)
 ```

注意: 业务组件是`Dialog`弹出框展示的,需要将弹出框内的业务组件单独抽离

## 改造要求:

1. 业务表单需要回显使用的`业务ID`存入流程变量(让业务后端在启动流程的时候存入)
2. 监听`processVariables`拿到对应的业务ID,调用回显接口



### 路由  



# 接口文档查看

DEV环境部署地址:  http://*************

微服务部署在Dev环境的对应端口,例如8009

http://*************:8009/doc.html

[待办微服务接口文档](http://*************:8009/doc.html#/default/查看待办/addNoticeUsingPOST)



# 8.git提交规范

![git提交流程及合并概要图](doc\img\git提交流程及合并概要图.jpg)

### 分支结构

- dev 开发环境分支

- ref : sit环境分支

  >ref.yyy.MM.dd :sit发布分支
  >
  >ref_bigfix_YYYYMMdd  修复bug分支

- feature :个人分支

### DEV分支合并要求:

- 及时将开发代码提交合并到dev分支
- 每天拉取一次dev分支

### ref分支BUG修复步骤:

> rel只允许修复bug分支合并.修复bug分支需同步rel和dev分支

1. 从rel分支拉取创建`rel-bugfix-xxx`分支
2. 修复bug代码提交至`rel-bugfix-xxx`分支
3. 修复后,将`rel-bugfix-xxx`分支同时合并至**`rel`分支**以及**`dev`分支**





