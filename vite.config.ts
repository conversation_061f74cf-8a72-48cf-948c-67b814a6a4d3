/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 12:07:28
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-17 11:18:10
 * @Description: 
 */
import { resolve } from 'path'
import { loadEnv } from 'vite'
import type { UserConfig, ConfigEnv } from 'vite'
import { createVitePlugins } from './build/vite'
import { include, exclude } from "./build/vite/optimize"
import { createBuildInfo } from './build/vite/buildInfo'

// 当前执行node命令时文件夹的地址(工作目录)
const root = process.cwd()

// 路径查找
function pathResolve(dir: string) {
  return resolve(root, '.', dir)
}

/**
 * 根据环境和模式获取URL
 *
 * @param env 环境变量对象
 * @param mode 模式字符串
 * @returns 返回对应模式的URL，若未找到则返回undefined
 */
const getUrlByMode = (env, mode) => {
 if (mode === 'dev') return env.VITE_SERVICE_DEV
  if (mode === 'sit') return env.VITE_SERVICE_SIT
  if (mode === 'uat') return env.VITE_SERVICE_UAT
  if (mode === 'pro') return env.VITE_SERVICE_PRO
  // return 'http://*************:8050' //继胜
  // return 'http://*************:8080' //明达
}


// https://vitejs.dev/config/
export default ({ command, mode }: ConfigEnv): UserConfig => {
  let env = {} as any
  const isBuild = command === 'build'
  if (!isBuild) {
    env = loadEnv((process.argv[3] === '--mode' ? process.argv[4] : process.argv[3]), root)
  } else {
    env = loadEnv(mode, root)
  }
  return {
    base: env.VITE_BASE_PATH,
    root: root,
    // 服务端渲染
    server: {
      // 是否开启 https
      // 端口号
      port: env.VITE_PORT,
      host: "0.0.0.0",
      open: env.VITE_OPEN === 'true',
      // 本地跨域代理. 目前注释的原因：暂时没有用途，server 端已经支持跨域
      proxy: {
          '/admin-api/app-api': {
          target:getUrlByMode(env,mode) ,
          changeOrigin: true,
          rewrite: (path) => path.replace('/admin-api', '')
         },
          '/admin-api': {
          target:getUrlByMode(env,mode) ,
          changeOrigin: true,
        },
          '/app-api': {
          target:getUrlByMode(env,mode) ,
          changeOrigin: true,
        },
      },
    },
    // 项目使用的vite插件。 单独提取到build/vite/plugin中管理
    plugins: createVitePlugins(),
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/styles/variables.scss";',
          javascriptEnabled: true
        }
      }
    },
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'],
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
        },
        {
          find: /\@\//,
          replacement: `${pathResolve('src')}/`
        }
      ]
    },
    build: {
      minify: 'terser',
      outDir: env.VITE_OUT_DIR || 'dist',
      sourcemap: env.VITE_SOURCEMAP === 'true' ? 'inline' : false,
      // brotliSize: false,
      terserOptions: {
        compress: {
          drop_debugger: env.VITE_DROP_DEBUGGER === 'true',
          drop_console: env.VITE_DROP_CONSOLE === 'true'
        }
      },
      rollupOptions: {
        output: {
            chunkFileNames: 'js/[name].[hash:8].js', // 引入文件名的名称
            entryFileNames: 'js/[name].[hash:8].js', // 包的入口文件名称
            assetFileNames: '[ext]/[name].[hash:8].[ext]', // 资源文件像 字体，图片等
          // key自定义 value[] 插件同步package.json名称 或 src/相对路径下的指定文件 （自己鼠标点击进去可以看manualChunks ts类型）
            manualChunks: {
         // vue vue-router合并打包
            vue: ['vue', 'vue-router'],
            echarts: ['echarts', 'echarts-wordcloud'],
            jsencrypt: ['jsencrypt'],
            bpmn: ['bpmn-js', 'bpmn-js-properties-panel', 'bpmn-js-token-simulation', 'diagram-js'],
            highlight:['highlight.js'],
             // 将 Lodash 库的代码单独打包
            lodash: ['lodash-es'],
            // 将组件库的代码打包
            library: ['element-plus', '@element-plus/icons-vue'],
            // video:['video.js'],            
             //图标(该json包有点大)
            // epIconsJson: ['@iconify/json/json/ep.json'],
            // faIconsJson: ['@iconify/json/json/fa.json'],
            // faSolidIconsJson: ['@iconify/json/json/fa-solid.json'],
            }
        },
      },


    },
        //如果使用 unplugin-element-plus 并且只使用组件 API，你需要手动导入样式。 采用以下插件按需引入样式
    optimizeDeps: { include, exclude },
    define:createBuildInfo()
  }
}
