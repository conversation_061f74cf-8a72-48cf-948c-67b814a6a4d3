<template>
  <ElCard
    ref="contentWrapRef"
    :body-style="{ height: '100%', paddingBottom: '0px' }"
    :class="[
      prefixCls,
      'tableClass',
      isFullscreen ? 'content-wrap--fullscreen' : '',
      !isFullscreen && isAnimating ? 'content-wrap--exit-fullscreen' : ''
    ]"
    shadow="never"
  >
    <template v-if="title" #header>
      <div class="flex items-center">
        <span class="text-16px font-700">{{ title }}</span>
        <ElTooltip v-if="message" effect="dark" placement="right">
          <template #content>
            <div class="max-w-200px">{{ message }}</div>
          </template>
          <Icon class="ml-5px" icon="bi:question-circle-fill" :size="14" />
        </ElTooltip>
      </div>
    </template>
    <div class="!h-full flex flex-col">
      <div v-if="$slots.search" class="!mb-0">
        <div v-show="showSearchForm">
          <slot name="search"> </slot>
        </div>
        <el-divider class="!m-0 !mb-3">
          <el-link
            v-if="showSearchForm"
            :underline="false"
            @click="() => (showSearchForm = false)"
            :icon="CaretTop"
          >
            {{ t('common.shrink') }}
          </el-link>
          <el-link
            v-if="!showSearchForm"
            :underline="false"
            @click="() => (showSearchForm = true)"
            :icon="CaretBottom"
          >
            {{ t('common.expand') }}
          </el-link>
        </el-divider>
      </div>
      <slot></slot>
      <div v-if="$slots.pagination" class="!mt-0">
        <slot name="pagination"> </slot>
      </div>
    </div>
  </ElCard>
</template>

<script setup lang="ts">
import { ElCard, ElTooltip } from 'element-plus'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { nextTick, onMounted, onUnmounted, ref, provide, watch, onActivated } from 'vue'
import { debounce } from 'lodash-es'
import { useContentCache } from './useContentCache'

const { t } = useI18n()
const { getPrefixCls } = useDesign()

// 使用缓存 hook
const { getCache, setCachedHeight, setResizeTriggered } = useContentCache()

const prefixCls = getPrefixCls('umv-content')

const props = defineProps({
  title: propTypes.string.def(''),
  message: propTypes.string.def('')
})

// 定义暴露给外部的方法
const emit = defineEmits(['fullscreen-change'])

// 全屏状态
const isFullscreen = ref(false)

// 动画状态
const isAnimating = ref(false)

// ContentWrap的DOM引用
const contentWrapRef = ref<any>(null)

// 内容高度
const contentHeight = ref(0)

// 切换全屏状态
const toggleFullscreen = () => {
  // 设置正在动画中
  isAnimating.value = true

  // 直接切换，不使用nextTick，避免延迟响应
  isFullscreen.value = !isFullscreen.value

  // 处理进入全屏时的文档滚动行为
  if (isFullscreen.value) {
    // 阻止body滚动
    document.body.style.overflow = 'hidden'
  } else {
    // 恢复body滚动
    document.body.style.overflow = ''
  }

  // 动画结束后重置状态
  setTimeout(() => {
    isAnimating.value = false
  }, 300)

  // 触发全屏状态变更事件
  emit('fullscreen-change', isFullscreen.value)
}

// 使用依赖注入提供ContentWrap的全屏功能和状态
provide('contentWrapFullscreen', {
  toggleFullscreen,
  isFullscreen,
  // 添加一个方法让子组件可以监听全屏状态变化
  onFullscreenChange: (callback: (isFullscreen: boolean) => void) => {
    watch(
      isFullscreen,
      (newVal) => {
        callback(newVal)
      },
      { immediate: true }
    )
  }
})

// 为子组件提供内容高度
provide('contentHeight', {
  height: contentHeight,
  isFullscreen,
  getContentHeight: () => contentHeight.value
})

// 处理ESC键退出全屏
const handleEscKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && isFullscreen.value) {
    e.preventDefault() // 阻止默认行为
    e.stopPropagation() // 阻止事件冒泡
    toggleFullscreen()
  }
}

const setHeight = () => {
  if (!contentWrapRef.value || !contentWrapRef.value.$el) return

  // 获取当前缓存
  const contentCache = getCache()

  // 如果高度已全局初始化，且不是窗口大小变化导致的调用，则使用缓存高度
  if (contentCache.heightInitialized && !contentCache.isResizeTriggered) {
    // 直接应用缓存的高度
    contentWrapRef.value.$el.style.height = `${contentCache.cachedHeight}px`
    // 更新内容高度引用
    contentHeight.value = contentCache.cachedHeight
    // 即使使用缓存的高度，也要检查高度是否合理
    nextTick(() => checkHeightValidity())
    return
  }

  nextTick(() => {
    try {
      // console.log('重新计算')

      const tableRefEl = contentWrapRef.value.$el
      const tableTop = tableRefEl.getBoundingClientRect().top
      const clientHeight = document?.querySelector('#app')?.clientHeight as number
      const footerHeight = 0

      if (clientHeight && tableTop) {
        const calculatedHeight = clientHeight - tableTop - footerHeight - 20
        if (calculatedHeight > 100) {
          // 确保计算的高度是合理的
          tableRefEl.style.height = `${calculatedHeight}px`

          // 更新内容高度引用
          contentHeight.value = calculatedHeight

          // 更新全局共享状态
          setCachedHeight(calculatedHeight)

          // 检查高度设置后的有效性
          nextTick(() => checkHeightValidity())
        }
      }
    } catch (err) {
      console.error('UmvContent设置高度失败:', err)
    }
  })
}

// 检查高度设置的有效性，检测底部边距是否异常
const checkHeightValidity = () => {
  if (!contentWrapRef.value || !contentWrapRef.value.$el) return

  try {
    const tableRefEl = contentWrapRef.value.$el
    const rect = tableRefEl.getBoundingClientRect()
    const windowHeight = window.innerHeight
    const bottomGap = windowHeight - rect.bottom

    // 如果底部间距过大（超过50px），可能表示高度计算有问题，重新触发计算
    // 排除全屏状态，因为全屏状态下高度是固定的
    if (bottomGap > 50 && !isFullscreen.value) {
      console.log('检测到底部间距异常，重新计算高度:', bottomGap)
      // 标记为需要重新计算
      setResizeTriggered(true)
      // 延迟执行以避免可能的循环
      setTimeout(() => {
        debouncedSetHeight()
      }, 200)
    }
  } catch (err) {
    console.error('检查高度有效性失败:', err)
  }
}

// 使用lodash的debounce函数
const debouncedSetHeight = debounce(setHeight, 100)

// 声明一个全局变量，用于标记窗口大小变化事件
declare global {
  interface Window {
    _isRealWindowResize?: boolean
  }
}

// 处理窗口大小变化
const handleContentWrapResize = () => {
  // 获取当前缓存状态
  const cache = getCache()

  // 如果已经初始化了高度，则不再响应 content-wrap-resize 事件
  // 只响应浏览器窗口大小变化的 resize 事件
  if (cache.heightInitialized && !window._isRealWindowResize) {
    // console.log('已有缓存高度，忽略 content-wrap-resize 事件')
    return
  }

  setResizeTriggered(true)
  debouncedSetHeight()
}

// 处理真正的窗口大小变化
const handleWindowResize = () => {
  // 标记这是真正的窗口 resize 事件
  window._isRealWindowResize = true
  setResizeTriggered(true)
  debouncedSetHeight()
  // 延迟删除标记
  setTimeout(() => {
    window._isRealWindowResize = undefined
  }, 100)
}

onMounted(() => {
  debouncedSetHeight()
  // 添加ESC键监听 - 使用capture模式确保事件在捕获阶段被处理
  document.addEventListener('keydown', handleEscKeydown, { capture: true })
  // 添加窗口大小变化监听，使用特定的事件名称
  window.addEventListener('resize', handleWindowResize)
  // 监听自定义事件
  window.addEventListener('content-wrap-resize', handleContentWrapResize)

  // 初始化完成后额外检查一次高度有效性
  nextTick(() => {
    setTimeout(checkHeightValidity, 500)
  })
})

onActivated(() => {
  // 使用最新的缓存高度
  setHeight()
})

onUnmounted(() => {
  // 移除ESC键监听 - 确保使用相同的配置
  document.removeEventListener('keydown', handleEscKeydown, { capture: true })
  // 移除窗口大小变化监听，使用特定的事件名称
  window.removeEventListener('resize', handleWindowResize)
  // 移除自定义事件监听
  window.removeEventListener('content-wrap-resize', handleContentWrapResize)
})

//是否展示搜索
const showSearchForm = ref(true)

// 暴露方法给父组件
defineExpose({
  toggleFullscreen,
  isFullscreen,
  setHeight
})
</script>

<style scoped>
/* 全屏样式基础 */
.umv-content {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center center;
}

/* 全屏样式 */
.content-wrap--fullscreen {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2000;
  margin: 0;
  padding: 0;
  height: 100vh !important;
  width: 100vw !important;
  overflow: auto;
  background-color: var(--el-bg-color);
  border-radius: 0 !important;
  animation: umv-content-fullscreen-in 0.3s ease-in-out forwards;
}

/* 退出全屏样式 */
.content-wrap--exit-fullscreen {
  animation: umv-content-fullscreen-out 0.3s ease-in-out forwards;
}

/* 进入全屏动画 */
@keyframes umv-content-fullscreen-in {
  from {
    opacity: 0.9;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 退出全屏动画 */
@keyframes umv-content-fullscreen-out {
  from {
    transform: scale(1.02);
    opacity: 0.95;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.content-wrap--fullscreen :deep(.el-card__body) {
  height: 100% !important;
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 20px;
}

.content-wrap--fullscreen :deep(.el-card__header) {
  padding: 12px 20px;
}
</style>
