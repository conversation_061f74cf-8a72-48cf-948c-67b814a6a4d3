<!--
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-13 15:29:54
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2024-12-23 11:48:05
 * @Description: 
-->
### CustomerSelect

本组件功能为获取客户名称和客户 id，按业务部门要求，组件集成了以下功能：

1. 支持单选和~~多选~~
2. 支持返回 id 和名称，并支持 v-model 直接绑定名称
3. 需要对后端返回的数据根据首字拼音进行排序
4. 支持搜索字高亮
5. 支持是否只显示总行数据

> 本组件为 2024 年 11 月获取客户信息的临时方案，到 12 月份时后端可能要做搜索优化，到时候根据实际情况判断是否继续使用该组件，建议引入时引入上级的`index.ts`

#### 基础用法

```vue
import { CustomerSelect } from '@/components/CustomerSelect/index'

<CustomerSelect
    v-model="queryData.customerId"
    v-model:customer-name="queryData.customerName"
/>


<!-- isSearchByName属性模糊搜索 只能用在v-model:customer-name上 -->
  <CustomerSelect v-model:customer-name="customerName" isSearchByName />
<!-- 回显数据,通过props参数customer-id -->
  <CustomerSelect  v-model="queryData.customerId"   :customer-id="queryData.customerId"/>
```

#### API

| 属性 | 说明 | 类型 | Default |
| --- | --- | --- | --- |
| `v-model` | 默认绑定为`customerId` | string / array | - |
| customer-id | **props属性，用于数据回显**，数据回显需要加上该参数(id和code二选一) | String |  |
| customer-code | **props属性，用于数据回显**，数据回显需要加上该参数(id和code二选一) | String | |
| `v-model:customer-name` | `v-model:customer-name`绑定为`customerName` | string / array |  |
| `v-model:customer-code` | `v-model:customer-code`绑定为`customerCode` | string / array | |
| `v-model:customer-info` | `v-model:customer-info` 绑定为`customerInfo`,客户的详细信息 |  |  |
| multiple | 是否多选，为 true 时返回值将会变为数组 | Boolean | false |
| preOption | 预选中的数据，用作回显。比如在编辑页面已经选中`customerId`为`123`的数据，那么在`v-model`中传入的即为`123`的绑定值，而该属性则需要传入`[{customerId: '123', customerName: '测试银行', children: []}]`，单选传一个，多选传多个 | Function |  |
| isSearchByName | 当`v-model:customer-name`绑定为`customerName`时才生效,用于用户名称模糊查询 | Boolean | false |

todo:目前已知问题,由于tree是使用虚拟渲染,没找到监听滚动条的好方法,无限滚动无效
