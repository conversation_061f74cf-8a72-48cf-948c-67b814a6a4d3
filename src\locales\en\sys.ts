export default {
  api: {
    operationFailed: 'Operation failed',
    errorTip: 'Error Tip',
    errorMessage: 'The operation failed, the system is abnormal!',
    timeoutMessage: 'Login timed out, please log in again!',
    apiTimeoutMessage: 'The interface request timed out, please refresh the page and try again!',
    apiRequestFailed: 'The interface request failed, please try again later!',
    networkException: 'network anomaly',
    networkExceptionMsg:
      'Please check if your network connection is normal! The network is abnormal',
    errMsg401: 'The user does not have permission (token, user name, password error)!',
    errMsg403: 'The user is authorized, but access is forbidden!',
    errMsg404: 'Network request error, the resource was not found!',
    errMsg405: 'Network request error, request method not allowed!',
    errMsg408: 'Network request timed out!',
    errMsg500: 'Server error, please contact the administrator!',
    errMsg501: 'The network is not implemented!',
    errMsg502: 'Network Error!',
    errMsg503: 'The service is unavailable, the server is temporarily overloaded or maintained!',
    errMsg504: 'Network timeout!',
    errMsg505: 'The http version does not support the request!',
    errMsg901: 'Demo mode, no write operations are possible!'
  },
  app: {
    title: 'UMV Card Cloud',
    logoutTip: 'Reminder',
    logoutMessage: 'Confirm to exit the system?',
    menuLoading: 'Menu loading...'
  },
  exception: {
    backLogin: 'Back Login',
    backHome: 'Back Home',
    subTitle403: "Sorry, you don't have access to this page.",
    subTitle404: 'Sorry, the page you visited does not exist.',
    subTitle500: 'Sorry, the server is reporting an error.',
    noDataTitle: 'No data on the current page.',
    networkErrorTitle: 'Network Error',
    networkErrorSubTitle:
      'Sorry, Your network connection has been disconnected, please check your network!'
  },
  lock: {
    unlock: 'Click to unlock',
    alert: 'Lock screen password error',
    backToLogin: 'Back to login',
    entry: 'Enter the system',
    placeholder: 'Please enter the lock screen password or user password'
  },
  login: {
    backSignIn: 'Back sign in',
    ssoFormTitle: 'Three -party authorization',
    mobileSignInFormTitle: 'Mobile sign in',
    qrSignInFormTitle: 'Qr code sign in',
    signInFormTitle: 'Sign in',
    signUpFormTitle: 'Sign up',
    forgetFormTitle: 'Reset password',

    signInTitle: 'Backstage management system',
    signInDesc: 'Enter your personal details and get started!',
    policy: 'I agree to the xxx Privacy Policy',
    scanSign: `scanning the code to complete the login`,

    loginButton: 'Sign in',
    registerButton: 'Sign up',
    rememberMe: 'Remember me',
    forgetPassword: 'Forget Password?',
    otherSignIn: 'Sign in with',

    // notify
    loginSuccessTitle: 'Login successful',
    loginSuccessDesc: 'Welcome back',

    // placeholder
    accountPlaceholder: 'Please input username',
    passwordPlaceholder: 'Please input password',
    smsPlaceholder: 'Please input sms code',
    mobilePlaceholder: 'Please input mobile',
    policyPlaceholder: 'Register after checking',
    diffPwd: 'The two passwords are inconsistent',

    userName: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    email: 'Email',
    smsCode: 'SMS code',
    mobile: 'Mobile',
    ssoAuthTip: 'Single Login Authorized Callback Page',
    authCode: 'Authorization Code',
    usingCode: 'Now using authorization code, to get the access token',
    getToken: 'Get Token',
    loginFail: 'Login has expired',
    ssoLoading: 'Getting access, please wait...',
    // ********认证功能新增
    mailLogin: 'Email login',
    inputMail: 'Please input Email',
    secSend: 's resend',
    mobileLogin: 'Mobile login',
    inputMobile: 'Please input phone number',
    inputMainAccount: 'Please input main account',
    pwdLogin: 'password login',
    ramLogin: 'RAM account login',
    verLogin: 'Verification code login',
    sendVer: 'Send Verification code',
    sendSuccess: 'Send success',
    sendFail: 'Send fail',
    inputRightMail: 'Please enter the correct email format',
    inputNickname: 'Please input nickname',
    phone: 'Mobile',
    authMethod: 'Authentication method',
    inputPhoneVer: 'Please input mobile verification code',
    inputEmailVer: 'Please input email verification code',
    ver: 'Authentication',
    selectAuthMethod: 'Please select authentication method',
    phoneVer: 'Mobile authentication',
    mailVer: 'Mail authentication',
    inputVer: 'Please input verification code',
    phoneVered: 'Mobile certified',
    mailVered: 'Mail certified',
    linkOverdute: 'Link has expired',
    verSuccess: 'Certification successful',
    inputMobileOrMail: 'Please input mobile number or email',
    inputMobileOrMailRight: 'Please enter the correct mobile number or email format'
  },
  permission: {
    hasPermission: 'Please set the operation permission label value',
    hasRole: 'Please set the role permission label value'
  },
  user: {
    gerUserInfoFail: 'Failed to retrieve user information',
    noRole:
      'This user has not been assigned a role! Please contact the administrator to assign a user role!',
    loginVer: 'Logon authentication',
    phoneVered: 'Phone number is certified',
    mailVered: 'The mailbox is certified',
    certified: 'Certified',
    email: 'mailbox',
    linkValidTerm: 'The link is valid for:',
    phoneLink: 'Mobile phone authentication link:',
    emailLink: 'Email authentication link:',
    copyAll: 'Copy all',
    copy: 'Copy',
    min: 'minute',
    rDesc1: 'New success, authentication link is obtained in Action> More> Login Authentication',
    tips: 'prompt',
    confirms: 'Confirm',
    unlock: 'Unlock',
    isUnlock: 'Locked',
    selectUnlockUser: 'Please select the user you want to unlock',
    sureUnlockUser: 'Are you sure you want to unlock the use?'
  },
  dictFail: 'Failed to retrieve the data dictionary',
  welcomeUse: 'Welcome to use',
  errorCode: {
    code401: 'Authentication failed, cannot access system resources',
    code403: 'No permission for current operation',
    code404: 'The accessed resource does not exist',
    codeDefault: 'System unknown error, please report to the administrator'
  },
  service: {
    invalidToken: 'Invalid refresh token',
    expiredToken: 'Refresh token has expired',
    code901: 'Code is 901, please contact the administrator',
    unFindRole: 'Unable to find user role, login has expired, please log in again!',
    loginInvalid: 'Login has expired',
    pleaseRelogin: ', please log in again!'
  },
  hooks: {
    web: {
      validfail: 'Verification failed'
    }
  }
}
