# UmvQuery 查询条件组件

UmvQuery 是一个灵活的查询表单组件，支持多种配置方式和布局。

## 功能特点

- 支持多种表单控件（输入框、下拉框、单选框、复选框、日期选择器等）
- 可配置的布局网格系统
- 支持表单项的展开/收起功能
- 自定义标签渲染
- 支持插槽自定义内容
- 支持"更多条件"下拉选择
- 响应式设计，适应不同屏幕尺寸
- 通过 `controlRender` 使用 TSX/JSX 灵活渲染任意表单控件

## 最佳实践：使用 v-model 绑定数据

**推荐**使用 v-model 绑定表单数据，这是最简单高效的查询表单数据管理方式。对于控件的渲染，特别是自定义或包含复杂逻辑的控件，**强烈推荐使用 `controlRender` 属性配合 TSX/JSX**。

```vue
<template>
  <umv-query 
    :opts="queryOpts" 
    v-model="queryData"
    @check="handleCheck" 
    @reset="handleReset" 
  />
  
  <!-- 显示当前查询条件 -->
  <div class="mt-4">
    <p>当前查询条件：</p>
    <pre>{{ queryData }}</pre>
  </div>
</template>

<script lang="tsx" setup> // 注意：使用 controlRender 时，script 标签建议使用 lang="tsx"
import { ref } from 'vue'
import UmvQuery from '@/components/UmvQuery' // 请确保路径正确
import { ElInput, ElSelect, ElOption } from 'element-plus' // 引入 Element Plus 组件

const queryOpts = ref({
  keyword: {
    label: '关键词',
    defaultVal: '',
    // 使用 controlRender 自定义输入框
    controlRender: (form: any) => (
      <ElInput v-model={form.keyword} placeholder="请输入关键词" style="width: 100%" />
    )
  },
  department: {
    label: '部门',
    defaultVal: '',
    // 使用 controlRender 自定义下拉选择框
    controlRender: (form: any) => (
      <ElSelect v-model={form.department} placeholder="请选择部门" style="width: 100%">
        <ElOption label="研发部" value="dev" />
        <ElOption label="市场部" value="market" />
      </ElSelect>
    )
  }
})

// 查询条件数据对象，通过 v-model 与组件双向绑定
const queryData = ref({
  keyword: '',
  department: ''
})

const handleCheck = (form: any) => {
  console.log('查询参数：', form)
}

const handleReset = () => {
  console.log('表单已重置')
}
</script>
```

## API

### Props

| 参数                 | 说明                         | 类型                          | 默认值  |
| -------------------- | ---------------------------- | ----------------------------- | ------- |
| opts                 | 查询表单项配置               | `Record<string, QueryOption>` | -       |
| labelWidth           | 标签宽度                     | string                        | '120px' |
| btnCheckConfig       | 查询按钮配置属性             | object                        | {}      |
| btnResetConfig       | 重置按钮配置属性             | object                        | {}      |
| loading              | 查询按钮加载状态             | boolean                       | false   |
| reset                | 是否显示重置按钮             | boolean                       | true    |
| colLengthMap         | 自定义屏幕宽度与列数的映射关系 | Record<string, number>        | {}      |
| boolEnter            | 是否支持回车查询             | boolean                       | true    |
| isShowOpen           | 是否显示展开按钮             | boolean                       | true    |
| isExpansion          | 是否默认展开所有条件         | boolean                       | false   |
| maxVisibleRows       | 默认显示的行数(默认显示2行) | number                        | 2       |
| visibleSearchNum     | 默认展示的搜索条件数量       | number                        | -       |
| packUpTxt            | 收起按钮文本                 | string                        | '收起'  |
| unfoldTxt            | 展开按钮文本                 | string                        | '展开'  |
| isFooter             | 是否显示底部按钮区域         | boolean                       | true    |
| configChangedReset   | 配置变更时是否重置表单       | boolean                       | false   |
| isShowWidthSize      | 是否显示自定义宽度           | boolean                       | false   |
| widthSize            | 自定义宽度大小               | number                        | 4       |
| isDropDownSelectMore | 是否使用下拉方式选择更多条件 | boolean                       | false   |
| moreCheckList        | 更多条件选择列表             | array                         | []      |
| popoverAttrs         | 更多条件弹出框属性           | object                        | {}      |
| isShowColumn         | 是否显示列调整按钮           | boolean                       | false   |
| showDefaultButtons   | 使用footerBtn插槽时是否显示默认按钮 | boolean                | true    |
| check                | 是否显示查询按钮             | boolean                       | true    |
| rules                | 表单验证规则                 | object                        | {}      |

### QueryOption 属性

每个查询表单项的配置对象包含以下属性：

| 参数        | 说明                                     | 类型              | 默认值         |
| ----------- | ---------------------------------------- | ----------------- | -------------- |
| label       | 表单项标签                               | string            | -              |
| hideLabel   | 是否隐藏标签，设置为 true 时不显示 label | boolean           | false          |
| dataIndex   | 数据索引，如果不提供则使用配置对象的键名 | string            | 配置对象的键名 |
| defaultVal  | 默认值                                   | any               | -              |
| event       | 事件处理函数                             | string / function | -              |
| labelRender | 标签渲染函数，仅用于渲染表单项的标签部分 | function          | -              |
| controlRender | 控件渲染函数，用于TSX方式渲染整个表单控件 | function          | -              |
| width       | 组件宽度                                 | string            | '100%'         |
| className   | 组件的自定义类名                         | string            | -              |
| span        | 栅格跨度                                 | number            | 1              |
| visible     | 是否显示该查询项                         | boolean / function | true           |

### Events

| 事件名称     | 说明                 | 回调参数                                       |
| ------------ | -------------------- | ---------------------------------------------- |
| check        | 点击查询按钮时触发   | (form: object) => void                         |
| reset        | 点击重置按钮时触发   | (form: object) => void                         |
| handleEvent  | 表单控件值变化时触发 | (type: string, val: any, form: object) => void |
| getCheckList | 更多条件选择后触发   | (checkedList: object) => void                  |
| getRefs      | 获取组件引用时触发   | (el: any, opt: object, index: number) => void  |
| columnChange | 列数变更时触发       | (columns: number) => void                      |

### 暴露的方法（Methods）

UmvQuery 组件暴露了以下方法，可通过模板引用（ref）访问：

| 方法名        | 说明                       | 参数                                         |
| ------------- | -------------------------- | -------------------------------------------- |
| queryState    | 获取当前表单数据           | -                                            |
| resetData     | 重置表单数据               | -                                            |
| resetHandle   | 重置表单数据并触发事件     | -                                            |
| checkHandle   | 执行查询操作               | (flagText?: any) 可选的标记文本              |
| handleEvent   | 处理表单控件事件           | ({ type, val }, dataIndex?) 事件信息和数据索引 |
| validate      | 验证表单                   | (callback: Function) 回调函数                |
| validateField | 验证指定表单字段           | (props: string \| string[], callback: Function) |
| resetFields   | 重置表单字段为初始值       | (props?: string \| string[]) 可选的字段名    |
| scrollToField | 滚动到指定表单字段         | (prop: string) 字段名                        |
| clearValidate | 清除表单项的验证状态       | (props?: string \| string[]) 可选的字段名    |
| formRef       | 获取内部 el-form 组件引用   | -                                            |

#### 使用示例

```vue
<template>
  <umv-query 
    ref="queryRef"
    :opts="queryOpts" 
    v-model="queryData"
    :rules="rules"
    @check="handleCheck" 
    @reset="handleReset" 
  />
  <el-button @click="validateForm">验证并提交</el-button>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const queryRef = ref()
const queryOpts = ref({
  name: {
    label: '姓名',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput v-model={form.name} placeholder="请输入姓名" style="width: 100%" />
    )
  },
  phone: {
    label: '手机号',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput v-model={form.phone} placeholder="请输入手机号" style="width: 100%" />
    )
  },
  email: {
    label: '邮箱',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput v-model={form.email} placeholder="请输入邮箱" type="email" style="width: 100%" />
    )
  }
})

const queryData = ref({})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '长度应为2-10个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
})

// 验证表单并提交
const validateForm = () => {
  queryRef.value.validate((valid) => {
    if (valid) {
      console.log('表单验证通过，提交数据：', queryData.value)
      // 在此处调用API提交数据
    } else {
      console.log('表单验证失败')
    }
  })
}

const handleCheck = (form) => {
  console.log('查询参数：', form)
}

const handleReset = () => {
  console.log('表单已重置')
}
</script>
```

### Slots

| 插槽名称   | 说明                               | 作用域插槽属性                       |
| ---------- | ---------------------------------- | ------------------------------------ |
| footerBtn  | 自定义底部按钮区域                 | { check: Function, reset: Function } |
| querybar   | 在查询按钮旁边添加自定义内容       | -                                    |

#### footerBtn 作用域插槽示例

```vue
<!-- 默认情况下，使用footerBtn插槽同时会显示默认按钮 -->
<umv-query :opts="queryOpts">
  <template #footerBtn="{ check, reset }">
    <el-button type="primary" @click="check">自定义查询按钮</el-button>
    <el-button @click="reset">自定义重置按钮</el-button>
    <el-button type="success">其他按钮</el-button>
  </template>
</umv-query>

<!-- 设置 showDefaultButtons 为 false，隐藏默认按钮 -->
<umv-query :opts="queryOpts" :showDefaultButtons="false">
  <template #footerBtn="{ check, reset }">
    <el-button type="primary" @click="check">自定义查询按钮</el-button>
    <el-button @click="reset">自定义重置按钮</el-button>
    <el-button type="success">其他按钮</el-button>
  </template>
</umv-query>

<!-- 设置 check 为 false，隐藏查询按钮 -->
<umv-query :opts="queryOpts" :check="false">
  <!-- 此时只会显示重置按钮 -->
</umv-query>
```

#### 自定义表单控件插槽示例(推荐使用tsx)

```vue
<!-- 配置选项 -->
<script setup>
const queryOpts = ref({
  // 必须设置 slotName 属性来声明插槽名称
  dateRange: {
    label: '日期范围',
    slotName: 'custom-date-range', // 这里定义的名称将用于插槽
    // 不指定dataIndex时，会自动使用键名'dateRange'作为数据索引
    defaultVal: []
  }
})
</script>

<!-- 使用具名插槽提供自定义内容 -->
<template>
  <umv-query :opts="queryOpts">
    <!-- 插槽名称必须与 slotName 属性值匹配 -->
    <template #custom-date-range="{ param }">
      <!-- param 是表单数据的引用，修改它会更新表单状态 -->
      <el-date-picker
        v-model="param.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    </template>
  </umv-query>
</template>
```

## 子组件配置示例

以下是各种表单控件的配置示例。虽然传统的 `comp` 和 `childComp` 方式适用于简单静态选项场景，但我们**强烈推荐使用 `controlRender` 属性配合 TSX/JSX 来定义表单控件**，尤其是对于需要更强自定义能力、动态获取选项、复杂事件处理或动态逻辑的场景。`controlRender` 提供了无与伦比的灵活性和更好的开发体验（如类型检查、直接在 TSX 中使用 Vue 的响应式 API 等）。

```javascript
// 使用 ref 声明配置对象
const queryOpts = ref({
  department: {
    // 下拉框配置...
  },
  gender: {
    // 单选框配置...
  },
  hobbies: {
    // 复选框配置...
  }
})
```

### 下拉框 (`el-select`)

#### 推荐方式：使用 `controlRender` (TSX/JSX)

这种方式允许您完全控制 `el-select` 及其 `el-option` 的渲染，可以动态生成选项、添加复杂逻辑等。

```vue
<script lang="tsx" setup>
import { ref } from 'vue';
import { ElSelect, ElOption } from 'element-plus'; // 确保已安装并引入

const queryOpts = ref({
  department: {
    label: '部门',
    defaultVal: '', // 默认值
    placeholder: '请选择部门',
    controlRender: (form: any) => { // form 即为当前行表单数据对象
      const departmentOptions = ref([
        { label: '研发部', value: 'dev' },
        { label: '市场部', value: 'market' },
        { label: '销售部', value: 'sales' }
      ]);
      // 可以在这里加入异步获取选项的逻辑
      return (
        <ElSelect v-model={form.department} placeholder="请选择部门" style="width: 100%" clearable>
          {departmentOptions.value.map(opt => (
            <ElOption key={opt.value} label={opt.label} value={opt.value} />
          ))}
        </ElSelect>
      );
    }
  }
});
</script>
```

### 单选框组 (`el-radio-group`)

#### 推荐方式：使用 `controlRender` (TSX/JSX)

```vue
<script lang="tsx" setup>
import { ref } from 'vue';
import { ElRadioGroup, ElRadio } from 'element-plus'; // 确保已安装并引入

const queryOpts = ref({
  gender: {
    label: '性别',
    defaultVal: 'male',
    controlRender: (form: any) => {
      const genderOptions = ref([
        { label: '男', value: 'male' },
        { label: '女', value: 'female' },
        { label: '保密', value: 'secret' }
      ]);
      return (
        <ElRadioGroup v-model={form.gender}>
          {genderOptions.value.map(opt => (
            <ElRadio key={opt.value} label={opt.value}>{opt.label}</ElRadio>
          ))}
        </ElRadioGroup>
      );
    }
  }
});
</script>
```

### 复选框组 (`el-checkbox-group`)

#### 推荐方式：使用 `controlRender` (TSX/JSX)

```vue
<script lang="tsx" setup>
import { ref } from 'vue';
import { ElCheckboxGroup, ElCheckbox } from 'element-plus'; // 确保已安装并引入

const queryOpts = ref({
  hobbies: {
    label: '兴趣爱好',
    defaultVal: [], // 复选框组通常默认为空数组
    controlRender: (form: any) => {
      const hobbyOptions = ref([
        { label: '阅读', value: 'reading' },
        { label: '运动', value: 'sports' },
        { label: '音乐', value: 'music' },
        { label: '旅游', value: 'travel' }
      ]);
      return (
        <ElCheckboxGroup v-model={form.hobbies}>
          {hobbyOptions.value.map(opt => (
            <ElCheckbox key={opt.value} label={opt.value}>{opt.label}</ElCheckbox>
          ))}
        </ElCheckboxGroup>
      );
    }
  }
});
</script>
```

### 隐藏标签 (`hideLabel`)

有时您可能需要渲染不带标签的表单控件，可以使用 `hideLabel` 属性：

```javascript
const queryOpts = ref({
  searchButton: {
    label: '搜索', // 虽然设置了 label，但不会显示
    hideLabel: true, // 隐藏标签
    defaultVal: '',
    controlRender: (form: any) => (
      <ElButton type="primary" onClick={() => handleSearch(form)}>
        快速搜索
      </ElButton>
    )
  },
  keyword: {
    label: '关键词',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput v-model={form.keyword} placeholder="请输入关键词" style="width: 100%" />
    )
  }
})
```

## 高级用法

### 展开/收起功能

```vue
<template>
  <umv-query
    :opts="queryOpts"
    :max-visible-rows="1"
    :is-expansion="false"
    :is-show-open="true"
    unfold-txt="展开更多条件"
    pack-up-txt="收起条件"
    @check="handleCheck"
    @reset="handleReset"
  />
</template>

<script setup>
import { ref } from 'vue'

const queryOpts = ref({
  // 你的查询选项
})
</script>
```

### 自定义按钮

```vue
<template>
  <umv-query
    :opts="queryOpts"
    :btn-check-bind="{ btnTxt: '搜索', type: 'success' }"
    :btn-reset-bind="{ btnTxt: '清空', type: 'warning' }"
    @check="handleCheck"
    @reset="handleReset"
  >
    <template #querybar>
      <el-button type="primary" @click="handleExport">导出</el-button>
    </template>
  </umv-query>
</template>

<script setup>
import { ref } from 'vue'

const queryOpts = ref({
  // 你的查询选项
})

const handleExport = () => {
  // 导出逻辑
}
</script>
```

### 自定义表单控件

UmvQuery 组件提供了多种方式来自定义表单控件，以满足不同的需求和开发偏好。

#### 自定义表单控件-TSX

`controlRender` 是 UmvQuery 组件提供的核心功能，允许开发者使用 TSX/JSX 语法完全掌控表单输入控件的渲染逻辑。这种方式是**首选的自定义控件方案**，尤其适合以下场景：

-   需要使用 Element Plus 组件库或其他第三方 Vue 组件库中的特定组件。
-   需要实现复杂的表单联动或动态渲染逻辑。
-   需要直接在渲染函数中访问和操作组件实例或 Vue 的响应式 API。
-   希望获得 TSX/JSX 带来的类型安全和开发时提示。

##### 基本用法

在 `.vue` 文件中，首先将 script 标签设置为 `lang="tsx"`：

```vue
<template>
  <umv-query 
    :opts="queryOpts" 
    v-model="queryData"
    @check="handleCheck" 
    @reset="handleReset" 
  />
</template>

<script lang="tsx" setup>
import { ref } from 'vue' // reactive 已被移除，推荐使用 ref
import UmvQuery from '@/components/UmvQuery'; // 确保路径正确
import { ElDatePicker, ElInput, ElRadioGroup, ElRadio } from 'element-plus' // 引入所需组件

const queryData = ref({
  keyword: '',
  dateRange: [],
  status: ''
})

const queryOpts = ref({
  keyword: {
    label: '关键词',
    defaultVal: '',
    // 使用 controlRender 自定义输入框
    controlRender: (form: any) => (
      <ElInput v-model={form.keyword} placeholder="请输入关键词" style="width: 100%" />
    )
  },
  // 使用 controlRender 自定义日期范围选择器
  dateRange: {
    label: '日期范围',
    defaultVal: [], // 确保为 controlRender 内 v-model 的属性提供默认值
    controlRender: (form: any) => { // form 即为当前行表单数据对象
      return (
        <div class="w-full">
          <ElDatePicker
            v-model={form.dateRange}
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          />
        </div>
      )
    }
  },
  // 使用 controlRender 自定义状态选择
  status: {
    label: '状态',
    defaultVal: '', // 确保为 controlRender 内 v-model 的属性提供默认值
    controlRender: (form: any) => {
      return (
        <div class="flex space-x-2 w-full">
          <ElRadioGroup v-model={form.status}>
            <ElRadio label="1">启用</ElRadio>
            <ElRadio label="0">禁用</ElRadio>
            <ElRadio label="">全部</ElRadio>
          </ElRadioGroup>
        </div>
      )
    }
  }
})

const handleCheck = (form: any) => {
  console.log('查询参数：', form)
}

const handleReset = () => {
  console.log('表单已重置')
}
</script>
```

##### 高级用法：结合自定义标签和控件

您可以同时使用 `labelRender` (仅用于自定义标签文本和样式) 和 `controlRender` (用于自定义整个输入控件) 来分别定制表单项的标签和控件部分：

```vue
<template>
  <umv-query :opts="queryOpts" v-model="queryData" @check="handleCheck" @reset="handleReset" />
</template>

<script lang="tsx" setup>
import { ref } from 'vue'
import UmvQuery from '@/components/UmvQuery'; // 确保路径正确
import { ElDatePicker, ElTooltip, ElSelect, ElOption } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'

const queryData = ref({
  type: '',
  dateRange: []
})

const queryOpts = ref({
  // 同时自定义标签和控件
  type: {
    label: '查询类型', // label 属性依然有效，作为默认标签内容或被 labelRender 覆盖
    // 自定义标签部分
    labelRender: () => (
      <div class="flex items-center">
        <span>查询类型</span>
        <ElTooltip content="选择适合您的查询类型" placement="top">
          <QuestionFilled class="ml-1 text-gray-400 cursor-help" style={{ fontSize: '16px' }} />
        </ElTooltip>
      </div>
    ),
    // 自定义控件部分
    controlRender: (form: any) => (
      <div class="w-full">
        <ElSelect v-model={form.type} placeholder="请选择查询类型" clearable style="width: 100%">
          <ElOption label="普通查询" value="normal" />
          <ElOption label="高级查询" value="advanced" />
          <ElOption label="模糊查询" value="fuzzy" />
        </ElSelect>
      </div>
    ),
    defaultVal: ''
  },
  // 仅自定义控件部分，使用默认标签
  dateRange: {
    label: '日期范围',
    // 自定义控件部分
    controlRender: (form: any) => (
      <ElDatePicker
        v-model={form.dateRange}
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 100%"
      />
    ),
    defaultVal: []
  }
})

const handleCheck = (form: any) => {
  console.log('查询参数：', form)
}

const handleReset = () => {
  console.log('表单已重置')
}
</script>
```

##### `controlRender` 的核心优势

使用 `controlRender` 配合 TSX/JSX 来自定义表单控件相比于插槽或其他方式，具有以下显著优点：

1.  **极致的灵活性**：完全掌控控件的渲染逻辑，可以使用任意 Vue 组件 (Element Plus、自定义组件等) 和 HTML 结构。
2.  **强大的动态能力**：轻松实现条件渲染、动态选项加载、复杂的表单联动和交互。
3.  **完整的类型支持**：在 TSX/JSX 中可以获得完善的类型提示和编译时校验 (需配置好 TypeScript 环境)。
4.  **代码集中与可维护性**：表单控件的视图和逻辑（包括事件处理）都集中在 `controlRender` 函数中，便于理解和维护。
5.  **直接访问 Vue API**：可以在 `controlRender` 内部直接使用 Vue 的响应式 API (如 `ref`, `computed`) 和组件实例。
6.  **现代化方案**：`controlRender` 提供了统一和强大的自定义机制，是推荐的表单控件自定义方式。

##### 注意事项

-   使用 `controlRender` 时，务必在 `<script>` 标签上添加 `lang="tsx"` 属性。
-   需要手动导入在 `controlRender` 中使用的所有组件，例如 Element Plus 的 `ElDatePicker`, `ElSelect`, `ElOption` 等。
-   表单数据通过 `controlRender` 函数的 `form` 参数传入 (类型通常为 `any` 或更具体的类型，取决于您的 `queryData` 结构)，可以直接使用 `v-model` 将其绑定到 `form` 的相应属性上。
-   为 `controlRender` 中通过 `v-model` 绑定的属性，在 `queryOpts` 的相应条目中提供 `defaultVal` 是一个好习惯，以确保表单数据的正确初始化。
-   `labelRender` 仅用于自定义表单项的 **标签** 部分，而 `controlRender` 用于自定义表单项的 **输入控件** 部分。

> **核心推荐**：对于所有非简单输入框的表单控件（如下拉选择、日期选择、自定义查找组件等），以及任何需要自定义渲染逻辑或交互的场景，都应优先考虑使用 `controlRender`。

#### 自定义表单控件插槽

UmvQuery 组件的自定义表单控件插槽功能允许您完全控制表单项的渲染内容。**重要**：UmvQuery 组件只能识别在配置选项中通过 `slotName` 属性声明的插槽，不支持直接添加任意插槽。

##### 自定义表单控件插槽使用说明

1. **必须**在配置对象中通过 `slotName` 属性声明插槽名称
2. 在模板中使用 `#插槽名称` 或 `v-slot:插槽名称` 语法提供插槽内容
3. 插槽名称必须与配置中的 `slotName` 值完全匹配
4. 可以通过作用域插槽参数 `param` 或 `scope` 访问表单数据

##### 自定义表单控件插槽工作原理

UmvQuery 组件的渲染过程如下：

1. 组件遍历 `opts` 中的每个配置项
2. 对于每个配置项，检查是否存在 `slotName` 属性
3. 只有配置了 `slotName` 的表单项才会渲染对应的插槽
4. 其他表单项根据 `comp` 属性渲染标准组件

##### 完整示例

```vue
<template>
  <umv-query :opts="queryOpts" @check="handleCheck" @reset="handleReset">
    <!-- 自定义日期范围组件 -->
    <template #custom-range="{ param }">
      <el-date-picker
        v-model="param.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    </template>
    
    <!-- 自定义状态选择 -->
    <template #custom-status="{ param }">
      <div class="flex space-x-2">
        <el-radio-group v-model="param.status">
          <el-radio label="1">启用</el-radio>
          <el-radio label="0">禁用</el-radio>
          <el-radio label="">全部</el-radio>
        </el-radio-group>
      </div>
    </template>
    
    <!-- 自定义按钮区域 -->
    <template #footerBtn="{ check, reset }">
      <el-button type="primary" @click="check">查询</el-button>
      <el-button @click="reset">重置</el-button>
      <el-button type="success" @click="handleExport">导出</el-button>
    </template>
  </umv-query>
</template>

<script setup>
import { ref } from 'vue'

const queryOpts = ref({
  // 不指定dataIndex时，会自动使用键名作为数据索引
  name: {
    label: '姓名',
    comp: 'el-input',  // 使用标准组件
    // dataIndex不是必须的，不指定时会自动使用键名'name'
    defaultVal: '',
    placeholder: '请输入姓名'
  },
  // 声明使用自定义表单控件插槽的表单项
  dateRange: {
    label: '日期范围',
    slotName: 'custom-range',  // 声明插槽名称，必需
    // 不指定dataIndex时，会自动使用键名'dateRange'
    defaultVal: []
  },
  // 另一个使用自定义表单控件插槽的表单项
  status: {
    label: '状态',
    slotName: 'custom-status',  // 声明插槽名称，必需
    // 不指定dataIndex时，会自动使用键名'status'
    defaultVal: ''
  }
})

const handleCheck = (form) => {
  console.log('查询参数：', form)
}

const handleReset = () => {
  console.log('表单已重置')
}

const handleExport = () => {
  // 导出逻辑
}
</script>
```

##### 注意事项

- 使用TSX方式时，需要在script标签上添加`lang="tsx"`属性
- 需要手动导入所需的组件库组件，如ElDatePicker、ElInput等
- 表单数据通过labelRender函数的form参数传入，可以在JSX中使用
- 在JSX中使用事件处理，应使用`onClick`而不是`onclick`（驼峰命名）
- CSS样式在JSX中应使用对象语法，如`style={{ fontSize: '16px' }}`

### 自定义栅格列数

### 自定义标签渲染 (labelRender) - TSX方式

`labelRender`属性**仅用于自定义表单项的标签部分**（即label部分），不影响表单的输入控件部分。UmvQuery组件支持使用TSX/JSX方式来创建更灵活的自定义标签，这种方式比HTML字符串方式提供了更好的类型检查和代码提示。

```vue
<template>
  <umv-query :opts="queryOpts" @check="handleCheck" @reset="handleReset" />
</template>

<script lang="tsx" setup>
import { ref } from 'vue'
import { ElTooltip } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'

const queryOpts = ref({
  keyword: {
    label: '关键词',
    comp: 'el-input',
    dataIndex: 'keyword',
    defaultVal: '',
    placeholder: '请输入关键词',
    // 使用TSX语法自定义标签渲染函数（仅渲染标签部分）
    labelRender: () => (
      <div class="flex items-center">
        <span>关键词</span>
        <ElTooltip content="支持搜索姓名、编号、手机号" placement="top">
          <QuestionFilled class="ml-1 text-gray-400 cursor-help" style={{ fontSize: '16px' }} />
        </ElTooltip>
      </div>
    )
  },
  // 带必填标记的示例
  name: {
    label: '姓名',
    comp: 'el-input',
    dataIndex: 'name',
    defaultVal: '',
    placeholder: '请输入姓名',
    labelRender: () => (
      <div class="flex items-center">
        <span>姓名</span>
        <span class="text-red-500 ml-1">*</span>
      </div>
    )
  },
  // 带图标和点击事件的示例
  department: {
    label: '部门',
    comp: 'el-select',
    dataIndex: 'department',
    defaultVal: '',
    placeholder: '请选择部门',
    labelRender: (form) => (
      <div class="flex items-center">
        <span>部门</span>
        <i 
          class="el-icon-refresh ml-2 cursor-pointer text-primary" 
          title="刷新部门列表"
          onClick={() => refreshDepartments()}
        ></i>
      </div>
    ),
    childComp: {
      comp: 'el-option',
      option: [
        { label: '研发部', value: 'dev' },
        { label: '市场部', value: 'market' }
      ]
    }
  }
})

const handleCheck = (form) => {
  console.log('查询参数：', form)
}

const handleReset = () => {
  console.log('表单已重置')
}

// 刷新部门数据的函数
const refreshDepartments = () => {
  alert('刷新部门列表')
  // 这里可以实现刷新部门数据的逻辑
}
</script>
```

#### TSX方式的优点

使用TSX方式自定义标签相比于HTML字符串方式有以下优点：

1. **更好的类型提示** - 在TSX中可以获得完整的类型提示和校验
2. **更安全的事件处理** - 可以直接引用组件中的方法，无需通过全局函数处理
3. **更好的组件集成** - 可以直接使用导入的组件，无需通过字符串模板
4. **动态内容处理** - 可以更方便地添加动态内容和条件渲染

#### 注意事项

- 使用TSX方式时，需要在script标签上添加`lang="tsx"`属性
- 需要手动导入所需的组件库组件，如ElTooltip、ElIcon等
- 表单数据通过labelRender函数的form参数传入，可以在JSX中使用
- 在JSX中使用事件处理，应使用`onClick`而不是`onclick`（驼峰命名）
- CSS样式在JSX中应使用对象语法，如`style={{ fontSize: '16px' }}`

### 更多条件下拉

```vue
<template>
  <umv-query
    :opts="queryOpts"
    :is-drop-down-select-more="true"
    :more-check-list="moreCheckList"
    :popover-attrs="popoverAttrs"
    @check="handleCheck"
    @reset="handleReset"
    @get-check-list="handleGetCheckList"
  />
</template>

<script setup>
import { ref, computed } from 'vue'

const queryOpts = ref({
  // 基础查询选项
})

const popoverAttrs = ref({
  showTxt: '更多筛选',
  title: '所有查询条件',
  allTxt: '全选',
  reverseTxt: '反选',
  clearTxt: '清空'
})

const moreCheckList = computed(() => [
  { label: '职位', prop: 'position', comp: 'el-input' },
  { label: '状态', prop: 'status', comp: 'el-select' },
  // 更多选项...
])

const handleGetCheckList = (checkedList) => {
  // 处理选择的条件
}
</script>
```

### 自定义栅格列数

UmvQuery 组件默认会根据窗口宽度自动计算适合的栅格列数，但您也可以通过 `isShowWidthSize` 和 `widthSize` 属性来自定义栅格列数，这在特定布局需求下非常有用。

#### 属性说明

| 属性名 | 说明 | 类型 | 默认值 |
|-------|------|------|--------|
| isShowWidthSize | 是否启用自定义列数 | boolean | false |
| widthSize | 自定义列数大小 | number | 4 |
| colLengthMap | 自定义屏幕宽度与列数的映射关系 | Record<string, number> | {} |

#### 自适应规则（默认行为）

当 `isShowWidthSize` 为 `false` 时，组件会根据窗口宽度自动计算合适的列数：

- 窗口宽度 >= 1920px：6列
- 1600px <= 窗口宽度 < 1920px：5列
- 1280px <= 窗口宽度 < 1600px：4列
- 1000px <= 窗口宽度 < 1280px：3列
- 768px < 窗口宽度 <= 1000px：2列
- 窗口宽度 <= 768px：1列

当 `isShowWidthSize` 为 `true` 时，组件将使用 `widthSize` 指定的列数，不再根据窗口宽度自动调整。

另外，您也可以通过 `colLengthMap` 属性自定义不同屏幕宽度对应的列数。此属性是一个对象，键为屏幕宽度断点，值为对应的列数。当屏幕宽度大于或等于某个断点时，将使用该断点对应的列数。断点按照从大到小的顺序匹配。

#### 自定义列数示例

```vue
<template>
  <div>
    <h3>自定义3列布局</h3>
    <umv-query
      :opts="queryOpts"
      :is-show-width-size="true"
      :width-size="3"
      @check="handleCheck"
      @reset="handleReset"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const queryOpts = ref({
  keyword: {
    label: '关键词',
    comp: 'el-input',
    defaultVal: '',
    placeholder: '请输入关键词'
  },
  department: {
    label: '部门',
    comp: 'el-select',
    defaultVal: '',
    placeholder: '请选择部门',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '研发部', value: 'dev' },
        { label: '市场部', value: 'market' }
      ]
    }
  },
  status: {
    label: '状态',
    comp: 'el-select',
    defaultVal: '',
    placeholder: '请选择状态',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    }
  },
  createTime: {
    label: '创建时间',
    comp: 'el-date-picker',
    defaultVal: '',
    placeholder: '请选择创建时间',
    bind: {
      type: 'date',
      valueFormat: 'YYYY-MM-DD'
    }
  }
})

const handleCheck = (form) => {
  console.log('查询参数：', form)
}

const handleReset = () => {
  console.log('表单已重置')
}
</script>
```

#### 自定义屏幕断点示例

```vue
<template>
  <div>
    <h3>自定义屏幕断点与列数映射</h3>
    <umv-query
      :opts="queryOpts"
      :col-length-map="{
        1920: 8, // 屏幕宽度 >= 1920px 时使用 8 列
        1600: 6, // 屏幕宽度 >= 1600px 且 < 1920px 时使用 6 列
        1280: 5, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
        1000: 3, // 屏幕宽度 >= 1000px 且 < 1280px 时使用 3 列
        768: 2,  // 屏幕宽度 >= 768px 且 < 1000px 时使用 2 列
        0: 1     // 屏幕宽度 < 768px 时使用 1 列
      }"
      @check="handleCheck"
      @reset="handleReset"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const queryOpts = ref({
  keyword: {
    label: '关键词',
    comp: 'el-input',
    defaultVal: '',
    placeholder: '请输入关键词'
  },
  department: {
    label: '部门',
    comp: 'el-select',
    defaultVal: '',
    placeholder: '请选择部门',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '研发部', value: 'dev' },
        { label: '市场部', value: 'market' }
      ]
    }
  },
  status: {
    label: '状态',
    comp: 'el-select',
    defaultVal: '',
    placeholder: '请选择状态',
    childComp: {
      comp: 'el-option',
      option: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    }
  },
  createTime: {
    label: '创建时间',
    comp: 'el-date-picker',
    defaultVal: '',
    placeholder: '请选择创建时间',
    bind: {
      type: 'date',
      valueFormat: 'YYYY-MM-DD'
    }
  }
})

const handleCheck = (form) => {
  console.log('查询参数：', form)
}

const handleReset = () => {
  console.log('表单已重置')
}
</script>
```

### 列调整按钮功能

UmvQuery 组件提供了一个内置的列调整功能，可以通过 `isShowColumn` 属性启用列调整按钮。启用后，在查询按钮旁边会显示一个"列调整"下拉按钮，用户可以通过它快速切换表单的列数布局（1列、2列、3列或4列）。

#### 属性说明

| 属性名 | 说明 | 类型 | 默认值 |
|-------|------|------|--------|
| isShowColumn | 是否显示列调整按钮 | boolean | false |

#### 事件说明

| 事件名称 | 说明 | 回调参数 |
|---------|------|---------|
| columnChange | 列数变更时触发 | (columns: number) => void |

#### 基本用法

```vue
<template>
  <umv-query
    :opts="queryOpts"
    :is-show-column="true"
    @check="handleCheck"
    @reset="handleReset"
    @column-change="handleColumnChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const queryOpts = ref({
  // 查询选项配置...
})

// 处理列数变更事件
const handleColumnChange = (columns) => {
  console.log(`列数已调整为: ${columns}列布局`)
}

const handleCheck = (form) => {
  console.log('查询参数：', form)
}

const handleReset = () => {
  console.log('表单已重置')
}
</script>
```

#### 功能特点

- **便捷切换**：通过下拉菜单快速切换表单布局的列数（1列、2列、3列或4列）
- **实时响应**：切换后表单布局会立即调整，无需刷新页面
- **事件回调**：通过 `column-change` 事件可以感知列数变化，便于进行相应的业务处理
- **用户友好**：为用户提供了根据个人偏好或屏幕大小调整表单布局的能力

#### 与自定义列数的区别

`isShowColumn` 与 `isShowWidthSize`/`widthSize` 的区别：

- `isShowColumn`：提供UI按钮让**用户**在运行时动态切换列数
- `isShowWidthSize`/`widthSize`：由**开发者**在代码中预先设置固定列数

#### 使用场景

- 复杂表单：包含大量查询条件的表单，需要用户能够灵活调整布局
- 响应式界面：需要在不同设备上提供更好的用户体验
- 用户偏好：允许用户根据个人偏好自定义查询表单的显示方式

#### 示例效果

启用 `isShowColumn` 后，UmvQuery 组件右上角会显示"列调整"按钮，点击后显示下拉菜单：
- 1列布局：查询条件每行显示1个
- 2列布局：查询条件每行显示2个
- 3列布局：查询条件每行显示3个
- 4列布局：查询条件每行显示4个

用户选择任意选项后，表单的列数会立即调整，并且组件会触发 `column-change` 事件。

### 动态显示/隐藏查询项 (`visible` 属性)

`UmvQuery` 组件允许通过在查询项配置对象 (`QueryOption`) 中使用 `visible` 属性来动态控制单个查询条件的显示或隐藏。这对于创建依赖于其他表单项值的动态表单非常有用。

#### `visible` 属性说明

-   **类型**：`boolean | ((form: Record<string, any>) => boolean)`
-   **默认值**：`true`
-   **功能**：
    -   当为布尔值时，直接决定查询项是否显示。
    -   当为一个函数时，该函数会接收当前的整个表单数据对象 `form` 作为参数。函数应返回一个布尔值，`true` 表示显示，`false` 表示隐藏。组件会在表单数据变化时重新评估此函数。

#### 示例：根据选择动态显示输入框

假设我们有一个场景：用户选择一个"查询类型"，如果选择的是 "其他"，则显示一个 "请输入具体原因" 的输入框。

```vue
<template>
  <umv-query 
    :opts="queryOpts" 
    v-model="queryData"
    @check="handleCheck"
  />
  <div class="mt-4 p-4 border rounded">
    <p class="font-semibold mb-2">当前表单数据 (queryData):</p>
    <pre>{{ queryData }}</pre>
  </div>
</template>

<script lang="tsx" setup>
import { ref } from 'vue';
import UmvQuery from '@/components/UmvQuery'; // 确保路径正确
import { ElSelect, ElOption, ElInput } from 'element-plus';

const queryData = ref({
  queryType: 'name', // 默认查询类型
  specificReason: '',
  name: ''
});

const queryOpts = ref({
  queryType: {
    label: '查询类型',
    defaultVal: 'name',
    controlRender: (form: any) => (
      <ElSelect v-model={form.queryType} placeholder="请选择查询类型" style="width:100%">
        <ElOption label="按姓名查询" value="name" />
        <ElOption label="按ID查询" value="id" />
        <ElOption label="其他" value="other" />
      </ElSelect>
    )
  },
  name: {
    label: '姓名',
    defaultVal: '',
    // 当查询类型为 'name' 或 'other' (作为示例) 时显示
    visible: (form: Record<string, any>) => form.queryType === 'name' || form.queryType === 'other',
    controlRender: (form: any) => (
      <ElInput v-model={form.name} placeholder="请输入姓名" style="width: 100%" />
    )
  },
  specificReason: {
    label: '具体原因',
    defaultVal: '',
    // 仅当查询类型为 'other' 时显示此输入框
    visible: (form: Record<string, any>) => {
      console.log('Checking visibility for specificReason, queryType is:', form.queryType);
      return form.queryType === 'other';
    },
    controlRender: (form: any) => (
      <ElInput v-model={form.specificReason} placeholder="请输入具体原因" style="width: 100%" />
    )
  }
});

const handleCheck = (form: any) => {
  console.log('查询参数：', form);
};

</script>

<style scoped>
/* 添加一些基本样式，方便查看 */
.mt-4 {
  margin-top: 1rem;
}
.p-4 {
  padding: 1rem;
}
.border {
  border: 1px solid #e2e8f0;
}
.rounded {
  border-radius: 0.25rem;
}
.font-semibold {
  font-weight: 600;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
</style>
```

在这个例子中：
- "姓名" 输入框只在 `queryType` 为 `name` 或 `other` 时显示。
- "具体原因" 输入框 (`specificReason`) 仅当 `queryType` 的值为 `other` 时才会显示。当用户在"查询类型"下拉框中选择"其他"时，"具体原因"输入框会出现；选择其他选项时则会隐藏。


**总结**：当使用 `UmvQuery` 并通过 `opts` 属性配置查询项时，应使用 `QueryOption` 中的 `visible` 属性来控制单个查询项的显示与隐藏。如果你是在 SFC 模板中手动编写表单元素的 HTML 结构，则会使用 Vue 的 `v-if` 或 `v-show` 指令。

