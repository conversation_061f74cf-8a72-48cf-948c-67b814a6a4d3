<template>
  <Dialog
    v-model="dialogVisible"
    :title="formData.id ? '编辑规则' : '新增规则'"
    width="800"
    @close="close"
  >
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="140px"
    >
      <el-form-item label="规则名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入规则名称" />
      </el-form-item>
      <el-form-item label="规则匹配器类型" prop="matcherType">
        <el-radio-group v-model="formData.matcherType" @change="changeMatcherType">
          <el-radio
            v-for="(item, index) in rulesTypeMap"
            :label="item.value"
            :value="item.key"
            :key="index"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="规则匹配器" prop="matchScriptId" v-if="formData.matcherType === 20">
        <el-select v-model="formData.matchScriptId">
          <el-option
            v-for="(item, index) in matchersMap"
            :key="index"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="规则匹配器" prop="matcherBeanId" v-if="formData.matcherType === 10">
        <el-select v-model="formData.matcherBeanId">
          <el-option
            v-for="(item, index) in matchersMap"
            :key="index"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务场景" prop="scenes" style="width: 100%">
        <el-select v-model="formData.scenes" multiple>
          <el-option
            v-for="(item, index) in sceneList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="权限类型" prop="permissionTypes" style="width: 100%">
        <el-select v-model="formData.permissionTypes" multiple>
          <el-option
            v-for="(item, index) in getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          placeholder="请输入备注"
          type="textarea"
          :autosize="{ minRows: 6, maxRows: 20 }"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ t('common.cancel') }}</el-button>
      <el-button type="primary" @click="submitFn" v-loading="btnLoading">{{
        t('common.save')
      }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'oprateForm'
})

const { t } = useI18n() // 国际化
const dialogVisible = ref(false)

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

const close = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    matcherType: 10,
    matcherBeanId: undefined,
    matchScriptId: undefined,
    scenes: undefined,
    permissionTypes: undefined,
    remark: undefined
  }
  dialogVisible.value = false
}

const formData = ref({
  id: undefined,
  name: undefined,
  matcherType: 10,
  matcherBeanId: undefined,
  matchScriptId: undefined,
  scenes: undefined,
  permissionTypes: undefined,
  remark: undefined
})

const formRules = computed(() => {
  return {
    name: [{ required: true, message: '规则名称不能为空', trigger: 'blur' }],
    matcherType: [{ required: true, message: '规则匹配器类型不能为空', trigger: 'blur' }],
    matchScriptId: [
      {
        required: formData.value.matcherType == 20,
        message: '规则匹配器不能为空',
        trigger: ['blur', 'change']
      }
    ],
    matcherBeanId: [
      {
        required: formData.value.matcherType == 10,
        message: '规则匹配器不能为空',
        trigger: ['blur', 'change']
      }
    ],
    scenes: [{ required: true, message: '场景不能为空', trigger: 'blur' }],
    permissionTypes: [{ required: true, message: '权限类型不能为空', trigger: 'blur' }]
  }
})

const sceneList = computed(() => {
  //场景
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE) || []
})

import {
  getResourceAutomaticAuthorizationRule,
  getResourceAutomaticAuthorizationRuleListMatcherTypes,
  getResourceAutomaticAuthorizationRuleListMatchers
} from '@/api/system/authorizationRule/index'

const rulesTypeMap: Ref<{ key: number; value: string }[]> = ref([]) //

const matchersMap = ref([])

const toolMap = ref({
  matcherTypes: {},
  matcherTypesList: [],
  ruleListMatchers: {},
  ruleListMatchersMap: {}
})

const open = async (data?, _maps) => {
  try {
    toolMap.value = _maps
    formLoading.value = true
    dialogVisible.value = true
    if (data) {
      formData.value = await getResourceAutomaticAuthorizationRule(data.id)
      formData.value.id = data.id
    }

    rulesTypeMap.value = [...toolMap.value.matcherTypesList]

    if (
      Object.prototype.toString.call(rulesTypeMap.value) === '[object Array]' &&
      rulesTypeMap.value.length > 0
    ) {
      changeMatcherType(formData.value.matcherType)
    }
  } finally {
    formLoading.value = false
  }
}

const changeMatcherType = async (type) => {
  console.log('toolMap.value.ruleListMatchers', toolMap.value.ruleListMatchers)
  matchersMap.value = toolMap.value.ruleListMatchers[type]
}

defineExpose({
  open
})

const formLoading = ref(false)

const btnLoading = ref(false)

const emits = defineEmits(['success'])

import {
  createResourceAutomaticAuthorizationRule,
  updateResourceAutomaticAuthorizationRule
} from '@/api/system/authorizationRule/index'

const formRef = ref()

const submitFn = async () => {
  await formRef.value.validate()

  try {
    btnLoading.value = true
    console.log('formData', formData.value)
    const fn = formData.value.id
      ? updateResourceAutomaticAuthorizationRule
      : createResourceAutomaticAuthorizationRule
    await fn(formData.value)
    ElMessage.success('操作成功')
    emits('success')
    dialogVisible.value = false
  } finally {
    btnLoading.value = false
  }
}

/****************************  选项数据  ************************************/
// 内置Bean
const matcherBeanList = ref([
  {
    id: 1,
    name: '测试1'
  },
  {
    id: 2,
    name: '测试2'
  }
])

// 业务场景
const scenesList = ref([
  {
    id: 1,
    name: '测试1'
  },
  {
    id: 2,
    name: '测试2'
  }
])
// 权限类型
const permissionTypesList = ref([
  {
    id: 1,
    name: '测试1'
  },
  {
    id: 2,
    name: '测试2'
  }
])
</script>
