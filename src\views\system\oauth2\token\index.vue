<template>
  <!-- 列表 -->
  <UmvContent>
    <UmvQuery v-model="queryParams" :opts="queryOpts" @check="handleQuery" @reset="handleQuery" />

    <UmvTable v-loading="loading" :data="list" :columns="columns" ref="tableRef" @refresh="getList">
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>
  </UmvContent>
</template>

<script lang="tsx" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as OAuth2AccessTokenApi from '@/api/system/oauth2/token'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { checkPermi } from '@/utils/permission'
import UmvContent from '@/components/UmvContent'

defineOptions({ name: 'SystemTokenClient' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  userId: '',
  userType: undefined,
  clientId: ''
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  userId: {
    label: '用户编号',
    defaultVal: '',
    controlRender: (form) => (
      <el-input v-model={form.userId} placeholder="请输入用户编号" clearable />
    )
  },
  userType: {
    label: '用户类型',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.userType} placeholder="请选择用户类型" clearable>
        {getIntDictOptions(DICT_TYPE.USER_TYPE).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  clientId: {
    label: '客户端编号',
    defaultVal: '',
    controlRender: (form) => (
      <el-input v-model={form.clientId} placeholder="请输入客户端编号" clearable />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'accessToken',
    label: '访问令牌',
    align: 'center',
    width: '300',
    showOverflowTooltip: true
  },
  {
    prop: 'refreshToken',
    label: '刷新令牌',
    align: 'center',
    width: '300',
    showOverflowTooltip: true
  },
  { prop: 'userId', label: '用户编号', align: 'center' },
  {
    prop: 'userType',
    label: '用户类型',
    align: 'center',
    renderTemplate: (scope) => <dict-tag type={DICT_TYPE.USER_TYPE} value={scope.row.userType} />
  },
  {
    prop: 'expiresTime',
    label: '过期时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.expiresTime)}</span>
    )
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        {checkPermi(['system:oauth2-token:delete']) && (
          <el-button link type="danger" onClick={() => handleForceLogout(scope.row.accessToken)}>
            强退
          </el-button>
        )}
      </div>
    )
  }
])

const tableRef = ref() // 表格引用

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OAuth2AccessTokenApi.getAccessTokenPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 强制退出操作 */
const handleForceLogout = async (accessToken: string) => {
  try {
    // 删除的二次确认
    await message.confirm('是否要强制退出用户')
    // 发起删除
    await OAuth2AccessTokenApi.deleteAccessToken(accessToken)
    message.success(t('common.success'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
