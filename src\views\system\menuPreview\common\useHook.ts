/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-16 16:25:38
 * @LastEditors: HoJack <EMAIL>
 * @LastEditTime: 2024-12-16 16:36:15
 * @Description:
 */
import { ClientVO } from '@/store/modules/user'
import { getTenantClientPage } from '@/api/system/businessSide'

export const useHook = () => {
  //获取客户端列表
  const clientList: Ref<Array<ClientVO>> = ref([])
  const getClientList = async () => {
    // 后端限制最大100
    const res = await getTenantClientPage({ pageNo: 1, pageSize: 100 })
    clientList.value = res.list
  }
  /**
   * 根据客户端ID获取客户端名称
   *
   * @param id 客户端ID
   * @returns 返回客户端名称，如果未找到则返回undefined
   */
  const getClientNameById = (id: number) => {
    return clientList.value.find((item) => item.id === id)?.name
  }
  return {
    clientList,
    getClientList,
    getClientNameById
  }
}
