<template>
  <el-form
    ref="formLogin"
    :model="loginData.loginForm"
    :rules="LoginRules"
    class="login-form"
    label-position="top"
    label-width="120px"
    size="large"
  >
    <el-row style="maring-left: -10px; maring-right: -10px">
      <el-col :span="24" style="padding-left: 10px; padding-right: 10px">
        <el-form-item>
          <h2 class="mb-3 text-2xl font-bold text-center xl:text-3xl enter-x xl:text-center">
            {{ t('login.forgetPwd') }}
          </h2>
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-left: 10px; padding-right: 10px">
        <el-form-item prop="email">
          <el-input
            v-model="loginData.loginForm.email"
            :placeholder="t('login.emailPlaceholder')"
            :prefix-icon="iconAvatar"
            name="email"
            type="email"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-left: 10px; padding-right: 10px">
        <el-form-item prop="code">
          <div class="code-warp">
            <el-input
              v-model="loginData.loginForm.code"
              :placeholder="t('login.codePlaceholder')"
              :prefix-icon="iconLock"
            />
            <el-button
              class="ml-8px"
              @click="sendCodeFn"
              :disabled="count !== codeText"
              :loading="codeLoading"
              >{{ count }}</el-button
            >
          </div>
        </el-form-item>
      </el-col>
      <el-col
        :span="24"
        style="padding-left: 10px; padding-right: 10px; margin-top: -20px; margin-bottom: -20px"
      />
      <el-col :span="24" style="padding-left: 10px; padding-right: 10px">
        <el-form-item>
          <GoldButton
            @click="getCode()"
            :loading="loading"
            :title="t('login.next')"
            class="w-[100%]"
            type="primary"
          />
        </el-form-item>
      </el-col>

      <Verify
        ref="verify"
        :captchaType="captchaType"
        :imgSize="{ width: '400px', height: '200px' }"
        mode="pop"
        @success="nextStep"
        v-if="loginData.captchaEnable !== 'false'"
      />
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ForgetPasswordForm'
})

// ========== 表单模块 ==========
// 表单相关导入
import { useFormValid, useFormValidField, LOGIN_TYPE } from './useLogin'
import { useIcon } from '@/hooks/web/useIcon'

// 表单状态
const formLogin = ref()
const { validForm } = useFormValid(formLogin)
const { validFormField } = useFormValidField(formLogin, 'email')
const loading = ref(false)

// UI相关
const { t } = useI18n()
const iconHouse = useIcon({ icon: 'ep:house' })
const iconAvatar = useIcon({ icon: 'ep:avatar' })
const iconLock = useIcon({ icon: 'ep:lock' })

// 表单校验规则
const LoginRules = {
  email: [
    { required: true, trigger: ['blur', 'change'], message: t('sys.login.accountPlaceholder') }
  ],
  code: [{ required: true, trigger: ['blur', 'change'], message: t('login.codePlaceholder') }]
}

// 表单数据
const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    email: '',
    code: '',
    captchaVerification: '',
    rememberMe: false
  }
})

// ========== 验证码模块 ==========
// 验证码相关导入
import { sendCode, validateCode } from '@/api/login/index'

// 验证码状态
const verify = ref()
const captchaType = ref('blockPuzzle')
const emit = defineEmits(['changeFormType'])

// 验证码方法
const getCode = async () => {
  await validForm()
  // 情况一，未开启：则直接登录
  if (loginData.captchaEnable === 'false') {
    await nextStep()
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    // 弹出验证码
    verify.value.show()
  }
}

// 下一步处理
const nextStep = async () => {
  loading.value = true
  try {
    const res = await validateCode({
      scene: LOGIN_TYPE.RESET_PASSWORD,
      code: loginData.loginForm.code,
      email: loginData.loginForm.email
    })

    if (!res) {
      loading.value = false
      return
    }

    emit('changeFormType', LOGIN_TYPE.RESET_PASSWORD)
  } finally {
    loading.value = false
  }
}

// ========== 验证码计时器模块 ==========
// 计时器状态
const timer = ref()
const timeCount = ref(0)
const codeText = ref(t('login.sendCode'))
const codeLoading = ref(false)

// 计时器方法
const countDown = () => {
  timeCount.value = timeCount.value === 0 ? 300 : timeCount.value

  if (timer.value) {
    clearInterval(timer.value)
  }

  timer.value = setInterval(() => {
    if (timeCount.value > 0) {
      sessionStorage.setItem('timeCount', timeCount.value.toString())
      timeCount.value--
    } else {
      sessionStorage.removeItem('timeCount')
      clearInterval(timer.value)
      timer.value = null
    }
  }, 1000)
}

const count = computed(() => {
  if (timeCount.value === 0) return codeText.value
  return timeCount.value + t('login.resend')
})

const sendCodeFn = async () => {
  await validFormField()
  codeLoading.value = true
  try {
    await sendCode({ scene: LOGIN_TYPE.RESET_PASSWORD, email: loginData.loginForm.email })
    ElMessage.success(t('login.secdSuccess'))
    countDown()
  } finally {
    codeLoading.value = false
  }
}

// ========== 生命周期钩子 ==========
onMounted(() => {
  if (sessionStorage.getItem('timeCount')) {
    timeCount.value = parseInt(sessionStorage.getItem('timeCount') as string)
    countDown()
  }
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
})

// ========== 组件暴露 ==========
defineExpose({
  loginData
})
</script>

<style lang="scss" scoped>
:deep(.anticon) {
  &:hover {
    color: var(--el-color-primary) !important;
  }
}

.login-code {
  width: 100%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    width: 100%;
    max-width: 100px;
    height: auto;
    vertical-align: middle;
  }
}

.code-warp {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
</style>
