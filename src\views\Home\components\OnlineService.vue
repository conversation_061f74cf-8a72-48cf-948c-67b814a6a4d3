<template>
  <div class="online-service flex flex-col justify-center items-center">
    <section>
      <el-image
        v-if="ifEn"
        class="w-full"
        :src="`${basePath}/Home/OnlineService/en_US/online_service_banner.webp`"
        alt=""
      />
      <el-image
        v-else
        class="w-full"
        :src="`${basePath}/Home/OnlineService/online_service_banner.webp`"
        alt=""
      />
    </section>

    <section
      class="flex flex-col justify-center items-center w-[80%] pt-67px pb-70px <2xl:(pt-48px pb-50px w-[80%])"
    >
      <div class="flex flex-row justify-center items-top">
        <el-image
          lazy
          class="w-26px h-37px <2xl:(w-18px h-26px)"
          :src="`${basePath}/Home/OnlineService/online_service_icon.png`"
          alt=""
        />
        <div class="ml-21px <2xl:(ml-15px)">
          <div class="text-37px <2xl:(text-26px)">
            {{ t('home.OnlineService.title1') }}
          </div>
          <div style="color: #666666" class="mt-30px text-16px <2xl:(mt-22px text-14px)">
            {{ t('home.OnlineService.title1_item1') }}
          </div>
        </div>
      </div>

      <div class="flex flex-row justify-center items-center mt-83px <2xl:(mt-59px)">
        <el-image
          v-if="ifEn"
          lazy
          class="w-1400px <2xl:(w-996px) <md:(w-full)"
          :src="`${basePath}/Home/OnlineService/en_US/online_service_icons.png`"
          alt=""
        />
        <el-image
          v-else
          lazy
          class="w-1400px <2xl:(w-996px) <md:(w-full)"
          :src="`${basePath}/Home/OnlineService/online_service_icons.png`"
          alt=""
        />
      </div>

      <div class="flex flex-row justify-center items-center mt-61px <2xl:(mt-43px)">
        <div class="flex flex-row justify-center items-top">
          <el-image
            lazy
            class="w-26px h-37px <2xl:(w-18px h-26px)"
            :src="`${basePath}/Home/OnlineService/online_service_icon.png`"
            alt=""
          />
          <div class="ml-21px <2xl:(ml-15px)">
            <div class="text-37px <2xl:(text-26px)">
              {{ t('home.OnlineService.title2') }}
            </div>
            <div style="color: #666666" class="mt-30px text-16px <2xl:(mt-22px text-14px)">
              {{ t('home.OnlineService.title2_item1') }}
            </div>
            <div style="color: #666666" class="mt-10px text-16px <2xl:(mt-7px text-14px)">
              {{ t('home.OnlineService.title2_item2') }}
            </div>
          </div>
        </div>
        <el-image
          v-if="ifEn"
          lazy
          class="hover-scale ml-120px w-922px <2xl:(ml-85px w-656px) <md:(w-full)"
          :src="`${basePath}/Home/OnlineService/en_US/online_service_card.webp`"
          alt=""
        />
        <el-image
          v-else
          lazy
          class="hover-scale ml-120px w-922px <2xl:(ml-85px w-656px) <md:(w-full)"
          :src="`${basePath}/Home/OnlineService/online_service_card.png`"
          alt=""
        />
      </div>

      <div class="flex flex-row justify-center items-center">
        <el-image
          v-if="ifEn"
          lazy
          class="hover-scale mr-72px w-901px <2xl:(mr-51px w-641px) <md:(w-full)"
          :src="`${basePath}/Home/OnlineService/en_US/online_service_card2.webp`"
          alt=""
        />
        <el-image
          v-else
          lazy
          class="hover-scale mr-72px w-901px <2xl:(mr-51px w-641px) <md:(w-full)"
          :src="`${basePath}/Home/OnlineService/online_service_card2.png`"
          alt=""
        />
        <div class="flex flex-row justify-center items-top">
          <el-image
            lazy
            class="hover-scale w-26px h-37px <2xl:(w-18px h-26px)"
            :src="`${basePath}/Home/OnlineService/online_service_icon.png`"
            alt=""
          />
          <div class="ml-21px <2xl:(ml-15px)">
            <div class="text-37px <2xl:(text-26px)">
              {{ t('home.OnlineService.title3') }}
            </div>
            <div style="color: #666666" class="mt-30px text-16px <2xl:(mt-22px text-14px)">
              {{ t('home.OnlineService.title3_item1') }}
            </div>
            <div style="color: #666666" class="mt-10px text-16px <2xl:(mt-7px text-14px)">
              {{ t('home.OnlineService.title3_item2') }}
            </div>
          </div>
        </div>
      </div>
    </section>

    <section
      class="online-features w-full flex flex-col justify-top items-center h-583px pt-42px <2xl:(h-415px pt-30px)"
    >
      <div class="text-40px <2xl:(text-28px)">
        {{ t('home.OnlineService.title4') }}
      </div>

      <el-image
        v-if="ifEn"
        lazy
        class="hover-scale w-829px mt-46px <2xl:(w-590px mt-33px)"
        :src="`${basePath}/Home/OnlineService/en_US/online_service_icons2.png`"
        alt=""
      />
      <el-image
        v-else
        lazy
        class="hover-scale w-829px mt-46px <2xl:(w-590px mt-33px)"
        :src="`${basePath}/Home/OnlineService/online_service_icons2.png`"
        alt=""
      />
      <div
        style="background: #dcdcdc"
        class="inline-block w-829px h-1px mt-89px mb-69px <2xl:(w-590px mt-60px mb-49px)"
      ></div>
      <div style="color: #010101" class="text-40px <2xl:(text-28px)">
        {{ t('home.OnlineService.title5') }}
      </div>
      <div style="color: #333333" class="mt-29px text-16px <2xl:(mt-21px text-14px)"
        >400-823-8798</div
      >
    </section>

    <section class="relative top-[-25px] pb-50px text-center">
      <ArrowBtn @click="goDemo()" :btnTitle="t('home.UMVCard.title3_item1')" />
    </section>
  </div>
</template>

<script setup lang="ts">
const { t, ifEn } = useI18n()
const basePath = import.meta.env.BASE_URL === '/' ? '' : import.meta.env.BASE_URL
import ArrowBtn from './ArrowBtn.vue'
const goDemo = () => {
  window.open('https://client-demo.umvcard.com/index.html')
}
</script>

<style lang="scss" scoped>
.online-service {
  .online-features {
    background: url('/Home/OnlineService/online_service_features.png') center/100% 100% no-repeat;
  }
}
//鼠标悬浮变大
.hover-scale {
  transition: all 0.3s;
  &:hover {
    transform: scale(1.1);
  }
}
</style>
