<template>
  <div class="home" v-loading="loding">
    <el-row :gutter="20">
      <el-col :lg="6" :md="12" :sm="12" :xl="6" :xs="24" v-for="item in list" :key="item.id">
        <div class="app-item">
          <img :src="item.img || appImg" alt="" @click="() => toApp(item)" />
          <p @click="() => toApp(item)">{{ item.name }}</p>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'Home'
})

import { getTenantAppList, listSimpleAppData } from '@/api/system/apply'
import { getAppInfo, getInfo } from '@/api/login'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import { refreshToken } from '@/config/axios/service'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { setToken } from '@/utils/auth'
import router from '@/router'
import { RouteRecordRaw } from 'vue-router'
import { useTagsViewStore } from '@/store/modules/tagsView'
import appImg from './img/app.png'

const { push } = useRouter()

const userStore = useUserStore()

const { wsCache } = useCache()

const permissionStore = usePermissionStoreWithOut()

interface APPVO {
  id: number
  name: string
  img?: string
}

const loding = ref(false)

const list: Ref<APPVO[]> = ref([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

const getList = async () => {
  list.value = (await listSimpleAppData(queryParams))?.filter((el) => el.appType === 1)
}

// 重置TagsView
const resetTagsView = () => {
  const tagsViewStore = useTagsViewStore()
  tagsViewStore.delAllViews()
  router.push('/HomePage')
}

const toApp = async (item?) => {
  loding.value = true
  //   const userInfo = await getAppInfo({
  //     applicationId: item.applicationId
  //   })
  const userInfo = await getInfo()
  if (userInfo.roles.length === 0) {
    ElMessage.warning('当前应用尚未分配角色')
    loding.value = false
    return
  }
  userStore.setUser(userInfo)

  userStore.setRoles(userInfo.roles)
  //刷新token
  const refreshTokenRes = await refreshToken()
  //缓存token
  setToken((await refreshTokenRes).data.data)
  await permissionStore.generateRoutes(true)
  permissionStore.getAddRouters.forEach((route) => {
    router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
  })
  loding.value = false
  resetTagsView()
}

onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.flex-index {
  display: flex;
  align-items: center;
}
.app-item {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-bottom: 80px;

  img {
    width: 120px;
    height: 120px;
    margin-bottom: 8px;
    cursor: pointer;
  }
  p {
    font-size: 24px;
    cursor: pointer;
  }
}
</style>
