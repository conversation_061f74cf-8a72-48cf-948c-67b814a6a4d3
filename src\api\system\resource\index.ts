import request from '@/config/axios'

export interface resourcesVO {
  id: string | number
  scene: string
  permissionType: number
  resourceType: number
}

export interface resourceRowVO {
  exposeNo: string
  resourceOwnerId: string
  resourceOwnerName: string
  exposeRoleId: string
  exposeRoleCode: string
  exposeRoleName: string
  resources: Array<resourcesVO>
}

// 获取已暴露的资源分页
export const getResourceExposePage = async (params: PageParam) => {
  return await request.get({ url: '/system/resource/getResourceExposePage', params })
}

// 申请资源
export const updateTag = async (data) => {
  return await request.post({ url: '/system/resource/apply', data })
}

// 获取当前用户的数据权限
export const getDataPermissionScope = async (params: PageParam) => {
  return await request.get({ url: '/system/permission/getDataPermissionScope', params })
}

// 获取租户已申请授权列表
export const getResourceApplyOfTenantPage = async (params: PageParam) => {
  return await request.get({ url: '/system/resource/getResourceApplyOfTenantPage', params })
}

// 获取租户审批授权列表
export const getResourceApprovalOfTenantPage = async (params: PageParam) => {
  return await request.get({ url: '/system/resource/getResourceApprovalOfTenantPage', params })
}

// 审批授权
export const resourceApproval = async (data) => {
  return await request.post({ url: '/system/resource/approval', data })
}

// 审批撤回
export const resourceWithdraw = async (data) => {
  return await request.post({ url: '/system/resource/revoke', data })
}

// 获取指定租户拥有其他客户的数据权限（已经申请通过的）
export const listTenantResourceScopesBaseExpose = async (data) => {
  return await request.post({
    url: '/system/resource-authorization-assist/listTenantResourceScopesBaseExpose',
    data
  })
}

// 获取指定租户的所有用户
export const listTenantUsers = async (data) => {
  return await request.post({
    url: '/system/resource-authorization-assist/listTenantUsers',
    data
  })
}

// 批量资源暴露并申请及审批
export const quickResourceExposeAndApproval = async (data) => {
  return await request.post({
    url: '/system/resource-authorization-assist/quickResourceExposeAndApproval',
    data
  })
}

// 获取指定租户拥有其他客户的数据权限（已经申请通过的）
export const getListTenantSceneTrees = async (tenantId) => {
  return await request.get({
    url: `/system/resource-authorization-assist/listTenantSceneTrees/${tenantId}`
  })
}

// 获取指定租户拥有其他客户的数据权限（已经申请通过的）
export const getListTenantSceneTreesProxy = async (tenantId, tenantIdList) => {
  if (tenantIdList && tenantIdList.length > 0) {
    const url = '?tenantIds=' + tenantIdList.join('&tenantIds=')
    // return await request.get({
    //   url: `/system/resource-authorization-assist/listTenantSceneTrees/${tenantId}${url}`
    // })
    return await request.post({
      url: `/system/resource-authorization-assist/postListTenantSceneTrees/${tenantId}`,
      data: tenantIdList
    })
  } else {
    return await request.get({
      url: `/system/resource-authorization-assist/listTenantSceneTrees/${tenantId}`
    })
  }
}

// 批量撤回暴露出去的资源权限
export const quickRevokeResourceAuthorizationAssist = async (data) => {
  return await request.post({
    url: '/system/resource-authorization-assist/quickRevokeResourceAuthorizationAssist',
    data
  })
}
