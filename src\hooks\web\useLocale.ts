/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJack
 * @LastEditTime: 2024-01-11 15:32:15
 * @Description:
 */
import { i18n } from '@/plugins/vueI18n'
import { useLocaleStoreWithOut } from '@/store/modules/locale'
import { setHtmlPageLang, importLangModule } from '@/plugins/vueI18n/helper'

const setI18nLanguage = (locale: LocaleType) => {
  const localeStore = useLocaleStoreWithOut()

  if (i18n.mode === 'legacy') {
    i18n.global.locale = locale
  } else {
    ;(i18n.global.locale as any).value = locale
  }
  localeStore.setCurrentLocale({
    lang: locale
  })
  setHtmlPageLang(locale)
}

export const useLocale = () => {
  // Switching the language will change the locale of useI18n
  // And submit to configuration modification
  const changeLocale = async (locale: LocaleType) => {
    const globalI18n = i18n.global

    globalI18n.setLocaleMessage(locale, importLangModule(locale))

    setI18nLanguage(locale)
  }

  return {
    changeLocale
  }
}
