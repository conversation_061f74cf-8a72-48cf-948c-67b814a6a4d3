/*
 * @Author: Ho<PERSON><PERSON>
 * @Date: 2023-05-31 13:59:22
 * @LastEditors: hao-jie.chen <EMAIL>
 * @LastEditTime: 2025-02-27 14:44:36
 * @Description:
 */
import { defineStore } from 'pinia'
import { store } from '../index'
import { cloneDeep } from 'lodash-es'
import tempRoutes from '@/router/modules/tempRoutes'
import {
  generateRoute,
  flatMultiLevelRoutes,
  setHome,
  routeDuplication,
  filterRouteByCondition,
  getHandleRoute
} from '@/utils/routerHelper'
import { getRouteAndPermissionRenovation } from '@/api/login'
import { useUserStore } from '@/store/modules/user'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import router from '@/router/index'
import * as authUtil from '@/utils/auth'
const { wsCache } = useCache()

const userStore = useUserStore()

//代客操作
import { useAgentOperationStore } from '@/store/modules/agentOperation'

export interface PermissionState {
  permissions: string[]
  routers: AppRouteRecordRaw[]
  addRouters: AppRouteRecordRaw[]
  menuTabRouters: AppRouteRecordRaw[]
  menuList: AppCustomRouteRecordRaw[]
  currentRouteScene: string
}
interface RouteAndPermission {
  menus: AppCustomRouteRecordRaw[]
  permissions: string[]
}
export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    permissions: [],
    routers: [],
    addRouters: [],
    menuTabRouters: [],
    menuList: [], //菜单列表
    currentRouteScene: 'default' //当前路由的场景
  }),

  getters: {
    getPermissions(): string[] {
      return this.permissions
    },
    getRouters(): AppRouteRecordRaw[] {
      return this.routers
    },
    /**
     * 过滤隐藏的路由
     *
     * @returns 返回过滤后的路由记录数组
     */
    getRoutersFilterHidden(): AppRouteRecordRaw[] {
      const filterRouteHidden = (route: AppRouteRecordRaw[]) => {
        return route?.filter((ietmRoute) => {
          if (ietmRoute.meta.hidden) {
            return false
          }
          if (ietmRoute.children && ietmRoute.children.length > 0) {
            ietmRoute.children = filterRouteHidden(cloneDeep(ietmRoute.children))
          }
          return true
        })
      }
      return filterRouteHidden(cloneDeep(this.routers))
    },
    getAddRouters(): AppRouteRecordRaw[] {
      return flatMultiLevelRoutes(cloneDeep(this.addRouters))
    },
    getMenuTabRouters(): AppRouteRecordRaw[] {
      return this.menuTabRouters
    },
    /**
     * 获取当前路由的场景信息
     *
     * @returns 返回当前路由的场景字符串，若未设置则返回default
     */
    getCurrentRouteScene(): string {
      console.log(router.currentRoute)
      /**
       * 在树状结构中查找指定代码对应的菜单项，并返回其父节点的路径
       *
       * @param tree 树状结构的数据源
       * @param code 需要查找的菜单项代码
       * @param path 当前节点的路径，默认为空数组
       * @returns 找到的目标菜单项及其父节点的路径数组，如果未找到则返回null
       */
      function findMenueParent(tree, code, result = [] as any[]) {
        for (const item of tree) {
          if (item.code === code) {
            // 找到目标项
            result.unshift(item)
            return result
          }

          if (item.children && item.children.length > 0) {
            //根据查找到的结果往上找父级节点
            const isFind = findMenueParent(item.children, code, result)
            if (isFind) {
              result.unshift(item)
              return result
            }
          }
        }

        //走到这说明没找到目标
        return false
      }

      const currentRouteMeta = router.currentRoute.value.meta
      if (currentRouteMeta.scene) {
        console.log(currentRouteMeta.scene)

        return currentRouteMeta.scene as string
      } else {
        let target = findMenueParent(this.menuList, currentRouteMeta?.code)
        console.log(target)

        //没找到父节点，返回默认全局业务场景
        if (!target || target.length === 1) return 'default'
        target = target.filter((item) => item.scene)
        console.log(target)
        if (target.length > 0) return target[target.length - 1].scene //父级业务场景
        return 'default'
      }
    }
  },
  actions: {
    async generateRoutes(ifChangeRole?: boolean): Promise<unknown> {
      return new Promise<void>(async (resolve) => {
        let res: RouteAndPermission
        let resRoute: AppCustomRouteRecordRaw[]
        if (
          !ifChangeRole &&
          wsCache.get(CACHE_KEY.ROLE_PERMISSIONS) &&
          wsCache.get(CACHE_KEY.ROLE_ROUTERS)
        ) {
          //获取缓存-路由菜单和按钮权限
          this.permissions = wsCache.get(CACHE_KEY.ROLE_PERMISSIONS) as string[]
          resRoute = wsCache.get(CACHE_KEY.ROLE_ROUTERS) as AppCustomRouteRecordRaw[]
          console.log('resRoute', resRoute)
        } else {
          let handledRoute: AppCustomRouteRecordRaw[] = []

          if (!userStore.getCurrentClientId) {
            console.log('store/permission/noclientId')
            router.push({
              name: 'ssoError',
              query: {
                noClient: 1
              }
            })
            return
          }

          //路由和权限接口
          // res = await getRouteAndPermission()
          res = await getRouteAndPermissionRenovation({
            clientId: userStore.getCurrentClientId
          })

          handledRoute = getHandleRoute(res.menus)
          //保存menueList 包含按钮权限(业务场景功能)
          this.menuList = cloneDeep(handledRoute)

          resRoute = filterRouteByCondition(handledRoute)

          resRoute = routeDuplication(resRoute, res.menus)

          this.permissions = res.permissions

          //缓存-路由菜单和按钮权限
          resRoute.map((el) => {
            el.path = el.path || ''
          })
          wsCache.set(CACHE_KEY.ROLE_ROUTERS, resRoute)
          wsCache.set(CACHE_KEY.ROLE_PERMISSIONS, res.permissions)
        }

        console.log('authUtil.getTenantId()', authUtil.getTenantId())
        //实例化代客操作store
        const agentOperationStore = useAgentOperationStore()

        const routerMap: AppRouteRecordRaw[] = generateRoute(
          resRoute as AppCustomRouteRecordRaw[]
        ).concat(agentOperationStore.agentOperationMode ? cloneDeep(tempRoutes) : []) //代客操作模式才加载tempRoutes有订阅功能
        console.log('routerMap', routerMap)

        // 动态路由，404一定要放到最后面
        this.addRouters = routerMap.concat([
          {
            path: '/:path(.*)*',
            redirect: '/404',
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ])
        // 渲染菜单的所有路由
        this.routers = routerMap

        // 设置为首页并且将首页信息加到菜单栏
        this.routers = [setHome(this.addRouters), ...this.routers]

        resolve()
      })
    },
    setMenuTabRouters(routers: AppRouteRecordRaw[]): void {
      this.menuTabRouters = routers
    },
    /**
     * 更新当前路由场景
     */
    updateCurrentRouteScene(): void {
      this.currentRouteScene = this.getCurrentRouteScene
    }
  },
  persist: true // 开启持化
})

export const usePermissionStoreWithOut = () => {
  return usePermissionStore(store)
}
