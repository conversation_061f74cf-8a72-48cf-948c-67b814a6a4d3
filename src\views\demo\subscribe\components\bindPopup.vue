<template>
  <Dialog
    :title="t('system.subscribe.bindVisible')"
    width="800"
    v-model="bindVisible"
    @close="close"
  >
    <el-checkbox-group v-model="checkList">
      <el-checkbox
        v-for="item in detailData.list"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-checkbox-group>
    <template #footer>
      <el-button @click="close">{{ t('common.cancel') }}</el-button>
      <el-button type="primary" @click="submitFn">{{ t('common.save') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'BindPopup'
})

const bindVisible = ref(false)
const detailData = ref()
const emit = defineEmits(['success'])
const { t } = useI18n() // 国际化
const checkList: Ref<any[]> = ref([])

const open = (row) => {
  detailData.value = row
  detailData.value.list = [
    { name: t('system.subscribe.bindData1'), id: '1' },
    { name: t('system.subscribe.bindData2'), id: '2' },
    { name: t('system.subscribe.bindData3'), id: '3' },
    { name: t('system.subscribe.bindData4'), id: '4' }
  ]
  bindVisible.value = true
}

const close = () => {
  checkList.value = []
  bindVisible.value = false
}

const submitFn = () => {
  console.log('checkList.value', checkList.value)
  bindVisible.value = false
  emit('success')
}

defineExpose({ open })
</script>
