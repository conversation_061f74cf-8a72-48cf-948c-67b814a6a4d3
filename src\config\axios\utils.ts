/*
 * @Author: error: git config user.name && git config user.email & please set dead value or install git
 * @Date: 2025-01-14 15:46:55
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2025-01-14 17:27:18
 * @Description:
 */
import { Base64 } from 'js-base64'
import { v4 as uuidv4 } from 'uuid'
import dayjs from 'dayjs'

/**
 * 生成请求ID
 *
 * @param url 请求的URL
 * @returns 请求ID，格式为：YYYYMMDDHHmmss.UUID.base64Url
 */
export const getRequestId = (url) => {
  const base64Url = Base64.encode(url, true)
  const uuid = uuidv4().replace(/-/g, '')
  const yyyyMMddHHmmss = dayjs().format('YYYYMMDDHHmmss')
  return `${yyyyMMddHHmmss}.${uuid}.${base64Url}`
}
