<!-- 交互端下对应的多个服务列表,用于服务排序 -->
<template>
  <Dialog
    :fullscreen="true"
    :title="t('system.subscribe.desc6')"
    width="800"
    v-model="detailVisible"
    @close="close"
  >
    <el-card>
      <el-table :data="applicationVOlist" :default-sort="{ prop: 'sort', order: 'ascending' }">
        <el-table-column :label="t('system.subscribe.id')" align="center" width="200" prop="id" />
        <el-table-column
          :label="t('system.subscribe.applicationName')"
          align="center"
          prop="name"
        />
        <el-table-column
          :label="t('system.subscribe.applicationCode')"
          align="center"
          prop="application.code"
        />
        <el-table-column
          :label="t('system.subscribe.applicationVer')"
          align="center"
          prop="application.ver"
        />
        <el-table-column
          :label="t('system.subscribe.applicationType')"
          align="center"
          prop="application.type"
        >
          <template #default="{ row }">
            {{
              row.type == 1
                ? t('system.subscribe.type1')
                : row.type == 2
                ? t('system.subscribe.type2')
                : t('system.subscribe.type3')
            }}
          </template>
        </el-table-column>
        <el-table-column
          :label="t('system.subscribe.applicationStatus')"
          align="center"
          prop="application.status"
        />
        <el-table-column
          :label="t('system.subscribe.applicationDescription')"
          align="center"
          prop="application.description"
        />
        <el-table-column
          :label="t('system.subscribe.applicationUrl')"
          align="center"
          prop="application.url"
        />

        <el-table-column
          :label="t('system.subscribe.applicationOauthClient')"
          align="center"
          prop="application.oauthClient"
        />
        <el-table-column :label="t('system.subscribe.sort')" align="center" prop="sort" sortable />
        <el-table-column :label="t('system.subscribe.isHomePage')" align="center">
          <template #default="{ row }">
            {{ row.homePageMenuId ? '是' : '否' }}
          </template>
        </el-table-column>

        <el-table-column :label="t('common.operate')" align="center" width="300">
          <template #default="{ row }">
            <el-button type="primary" @click="getMenuListByAppid(row)">{{
              t('system.subscribe.homeSetting')
            }}</el-button>
            <el-button type="info" @click="setSort(row)">{{
              t('system.subscribe.serviceSort')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <template #footer>
      <el-button @click="close">{{ t('common.close') }}</el-button>
    </template>
  </Dialog>

  <!-- 应用对应的菜单Tree -->
  <Dialog :title="t('system.subscribe.menuDetail')" width="600" v-model="menuTreeVisible">
    <el-tree
      class="h-[600px] overflow-auto"
      ref="treeRef"
      default-expand-all
      :data="menuOptions"
      :props="defaultProps"
      :empty-text="t('common.emptyText')"
      node-key="id"
    >
      <template #default="{ node, data }">
        <span>
          <span>{{ node.label }}</span>
          <span>
            <el-tag class="ml-10" size="small" type="success" v-if="data.id === homePageMenuId">{{
              t('system.subscribe.home')
            }}</el-tag>
            <el-button
              class="ml-10"
              size="small"
              v-if="data.component && data.id !== homePageMenuId"
              type="primary"
              plain
              @click="setHomePage(data)"
            >
              {{ t('system.subscribe.setHome') }}
            </el-button>
          </span>
        </span>
      </template>
    </el-tree>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ServiceListDialog'
})

const message = useMessage()

const { t } = useI18n() // 国际化
const detailVisible = ref(false)

const loading = ref(false)

//打开弹窗
import { getClientApplicationList, applicationVO } from '@/api/system/tenantPackage'
//应用列表
let applicationVOlist = ref<applicationVO[]>()

let clientId = ref<number>()
const open = async (id) => {
  if (loading.value) return
  try {
    loading.value = true
    clientId.value = id
    applicationVOlist.value = await getClientApplicationList(id)
    detailVisible.value = true
  } finally {
    loading.value = false
  }
}
defineExpose({ open })

const close = () => {
  detailVisible.value = false
}

const emit = defineEmits(['success'])

const submitFn = () => {
  detailVisible.value = false
  emit('success')
}

//获取应用/服务的菜单列表
import { defaultProps, handleTree } from '@/utils/tree'

import { getMenuList } from '@/api/system/menu'
let menuTreeVisible = ref(false)

const menuOptions = ref<any[]>([]) // 树形结构数据

let homePageMenuId = ref<number>() //当前服务设置的首页菜单id
const getMenuListByAppid = async (row) => {
  try {
    currentServiceId.value = row.id
    homePageMenuId.value = row.homePageMenuId
    const res = await getMenuList({ applicationId: row.application.id })
    menuOptions.value = handleTree(res)
    menuTreeVisible.value = true
  } catch (error) {}
}
//首页设置
import { updateClientApplication } from '@/api/system/tenantPackage'
//当前租户对应服务的id
let currentServiceId = ref<number>()
const setHomePage = async (data) => {
  try {
    //遍历找出是否已经设置其他首页
    let serviceId = 0
    let target = applicationVOlist?.value?.some((item) => {
      if (item.homePageMenuId) {
        serviceId = item.id
        return true
      }
    })

    if (target) {
      await message.delConfirm(t('system.subscribe.desc7'))
      //重置首页
      await updateClientApplication({
        id: serviceId,
        homePageMenuId: 0
      })
    }

    await updateClientApplication({
      id: currentServiceId.value,
      homePageMenuId: data.id
    })
    console.log(data)
    menuTreeVisible.value = false
    open(clientId.value)
  } catch (error) {}
}

//服务排序
const setSort = async (row) => {
  ElMessageBox.prompt(t('system.subscribe.desc8'), t('system.subscribe.msgTitle'), {
    confirmButtonText: 'OK',
    cancelButtonText: 'Cancel',
    inputErrorMessage: t('system.subscribe.sortCode')
  })
    .then(async ({ value }) => {
      await updateClientApplication({
        id: row.id,
        sort: value
      })
      open(clientId.value)
    })
    .catch(() => {
      message.info(t('system.subscribe.cancelSetting'))
    })
}
</script>
<style lang="scss" scoped></style>
