<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-12-20 12:03:24
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2024-01-08 10:18:52
 * @Description: 升级应用菜单,继承角色和菜单
  /system/tenant-application/vers/page?pageNo=1&pageSize=10&code=UMVMGR   获取list[0].id  拿到应用id
  admin-api/system/menu/list?applicationId=215    通过应用id 获取菜单列表
  admin-api/system/auth/role-menus?applicationId=211
-->

<template>
  <div>
    <el-drawer :title="t('system.subscribe.upgradeApp')" size="70%" v-model="dialogVisible">
      <el-card v-loading="loading" class="h-full">
        <template #header>
          {{ t('system.subscribe.desc1') }}:
          <el-switch
            v-model="treeNodeAll"
            :active-text="t('common.yes')"
            :inactive-text="t('common.no')"
            inline-prompt
            @change="handleCheckedTreeNodeAll"
            :disabled="RoleType === 1"
          />
          <!-- tree-v2不支持展开 -->
          <!-- {{ t('system.subscribe.menuExpand') }}:
          <el-switch
            v-model="menuExpand"
            :active-text="t('common.expand')"
            :inactive-text="t('common.shrink')"
            inline-prompt
            @change="handleCheckedTreeExpand"
          /> -->

          <!-- <span class="float-right">
            {{ t('system.subscribe.treeNodeAll2') }}:
            <el-switch
              v-model="treeNodeAll2"
              :active-text="t('common.yes')"
              :inactive-text="t('common.no')"
              inline-prompt
              @change="handleCheckedTreeNodeAll2"
            />
          </span> -->
          <el-tooltip placement="bottom" v-if="newMenuCache.length > 0">
            <template #content>
              新增了{{ newMenuCache.length }}个菜单<br />
              <div v-for="(item, index) in newMenuCache" :key="item.menuId">
                {{ index + 1 }}:{{ item.name }}
              </div>
            </template>
            <el-icon class="ml-4 flex justify-center text-center content-center">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </template>
        <el-tree-v2
          class="h-full"
          :height="600"
          ref="treeRef"
          :data="menuOptions"
          :props="defaultProps"
          :default-checked-keys="defaultCheckIds"
          :check-strictly="isCheckStrictly"
          @check-change="(val, checked, isSonChecked) => beforeChange(val, checked, isSonChecked)"
          :empty-text="t('common.emptyText')"
          node-key="id"
          :show-checkbox="RoleType !== 1"
        >
          <template #default="{ node, data }">
            <el-tag class="mr-3" v-if="data?.id?.startsWith('role')">{{
              t('system.subscribe.role')
            }}</el-tag>
            <span>{{ node.label }}</span>
            <el-tag type="danger" effect="plain" round class="ml-3" v-if="data.ifNew">
              {{ t('system.subscribe.addMenu') }}
            </el-tag>
          </template>
        </el-tree-v2>
      </el-card>

      <template #footer>
        <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
        <el-button :loading="loading" type="primary" @click="submitFn">角色迁移初始化</el-button>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'UpdateMenu'
})

import { cloneDeep } from 'lodash-es'

import * as menuApi from '@/api/system/menu/index'
import * as applyApi from '@/api/system/apply'
import * as permissionApi from '@/api/system/permission'
import { QuestionFilled } from '@element-plus/icons-vue'
import { WarningFilled } from '@element-plus/icons-vue'
const { t } = useI18n() // 国际化
const emit = defineEmits(['success'])

const loading = ref(false)

const dialogVisible = ref(false)

const message = useMessage() // 消息弹窗

let applicationData = ref() //当前应用的信息(未升级前)
let roleList = ref<menuApi.roleDataType[]>([]) //角色List
const open = async (row: applyApi.ApplicationSubscriptionPageRes) => {
  menuOptions.value = []
  defaultCheckIds.value = []
  applicationData.value = row
  dialogVisible.value = true
  loading.value = true
  try {
    //获取最新菜单 和旧菜单
    let { oldMenuList, newMenuList, newMenuTree, oldMenuTree } = await getNewMenuTree(row)
    console.log(oldMenuList, newMenuList, newMenuTree, oldMenuTree)
    //获取角色与菜单的tree
    await setRoleMenuTree(newMenuList)
  } catch (error) {
  } finally {
    console.log(defaultCheckIds.value)
    //勾选继承的角色和菜单
    nextTick(() => {
      treeRef.value.setCheckedKeys(defaultCheckIds.value)
    })
    loading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/**
 * 获取最新菜单
 * 属性:ifNew:boolean 标记新菜单
 * 属性:oldMenuId  旧菜单Id
 */
interface NewMenuItemType extends menuApi.MenuItemType {
  ifNew: boolean
  menuId: string | number
  oldMenuId: string | number
}
let newMenuCache = ref<NewMenuItemType[]>([]) //新增菜单缓存(展示新增菜单差别)
const getNewMenuTree = async (row) => {
  newMenuCache.value = []
  //获取新应用菜单列表
  let newMenuList: NewMenuItemType[] = await menuApi
    .getAppTenantMenuList({
      tenantId: row.id,
      applicationId: row.upgradeApplicationId
    })
    .then((res) => res.filter((item) => !/^(demo\d+|demo)$/.test(item.name))) // todo:后续菜单功能完善,要剔除 2024-09-30

  let oldMenuList: menuApi.MenuItemType[] = await menuApi
    .getAppTenantMenuList({
      tenantId: row.id,
      applicationId: row.curApplicationId
    })
    .then((res) => res.filter((item) => !/^(demo\d+|demo)$/.test(item.name))) // todo:后续菜单功能完善,要剔除 2024-09-30

  let oldMenuParentIdObj = {}
  let oldMenuArr: any = []

  oldMenuList.forEach((oldItem) => {
    let isDelete = true
    newMenuList.forEach((newItem) => {
      //菜单唯一标识:name + componentName
      if (
        oldItem.name.trim() == newItem.name.trim() &&
        oldItem.componentName?.trim() == newItem.componentName?.trim()
      ) {
        isDelete = false
      }
    })
    if (isDelete) {
      oldMenuArr.push({
        ...oldItem,
        isDelete: true,
        disabled: true
      })
      if (oldMenuParentIdObj[oldItem.parentId]) {
        oldMenuParentIdObj[oldItem.parentId].push({
          ...oldItem,
          isDelete: true,
          disabled: true
        })
      }
    }
  })

  newMenuList = newMenuList.map((item) => {
    item['ifNew'] = true
    item['menuId'] = item.id
    oldMenuList.some((oldItem) => {
      //判断菜单名称  菜单的组件名称  菜单的权限字符,如果之一不同,则是新菜单
      if (
        item.name.trim() == oldItem.name.trim() &&
        item.componentName?.trim() == oldItem.componentName?.trim()
      ) {
        item['ifNew'] = false
        item['oldMenuId'] = oldItem.id
      }
    })
    if (item.ifNew) {
      newMenuCache.value.push(item)
    }
    return item
  })

  let newMenuTree = handleTree([...newMenuList, oldMenuArr]) //新菜单tree
  let oldMenuTree = handleTree(oldMenuList) //旧菜单tree
  return {
    oldMenuList,
    newMenuList,
    newMenuTree,
    oldMenuTree
  }
}
/**
 * 获取角色,将新旧菜单组成tree
 */
const setRoleMenuTree = async (newMenuList) => {
  //获取角色对应的菜单Map
  let roleMenusMapRes = await menuApi.getRoleMenus({
    applicationId: applicationData.value.curApplicationId,
    tenantId: applicationData.value.id
  })

  //1.生成角色列表(用于展示)
  for (let item of roleMenusMapRes) {
    let roleData: menuApi.roleDataType = item
    //过滤系统默认角色
    if (roleData.type == 1) continue
    // console.log(roleMenusMapRes[key])

    roleData['roleId'] = roleData.id + ''
    // roleData['menuId'] = roleData.id //为了将列表组成tree统一id为menuId
    roleData.id = `role_${roleData.id}`
    roleList.value.push(roleData) //保存角色List
    //保存默认勾选的角色
    defaultCheckIds.value.push(roleData.id)

    //生成角色列表
    menuOptions.value.push({
      ...roleData
    })
  }

  //2.查询角色拥有的菜单权限(批量)
  let roleIdsData = menuOptions.value.map((roleItem) => roleItem.roleId)

  let rolesMapMenuIds = await permissionApi.getTenantRolesMenusList(
    applicationData.value.id,
    roleIdsData
  )

  //3.使用角色列表  映射菜单列表
  menuOptions.value = menuOptions.value.map((roleItem) => {
    let currentRoleMenuIds = rolesMapMenuIds[roleItem.roleId] ?? []
    console.log('currentRoleMenuIds' + currentRoleMenuIds)

    // //新菜单做为角色的子节点
    let roleMenuTree = cloneDeep(newMenuList).map((newMenuItem) => {
      newMenuItem.id = `${roleItem.roleId}_${newMenuItem.id}`
      newMenuItem.roleId = roleItem.roleId
      //继承角色的菜单
      if (!newMenuItem.ifNew) {
        let ifCheck = currentRoleMenuIds.some((menuId) => newMenuItem.oldMenuId == menuId)
        ifCheck && defaultCheckIds.value.push(newMenuItem.id)
      }
      return newMenuItem
    })
    roleItem.children = handleTree(roleMenuTree, 'menuId')
    return roleItem
  })
  console.log(menuOptions.value)
}
/** 提交表单 */
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
const submitFn = async () => {
  let checkedData = treeRef.value.getCheckedKeys(false)
  //过滤tree选中的角色节点
  let roleData = checkedData.filter((checkedId) => checkedId.startsWith('role'))
  // console.log(checkedData)
  // console.log(roleData)
  //角色与菜单对应关系
  let oldRoleNewMenuIdsMap = {}
  roleData.forEach((roleItem) => {
    let roleTarget = roleList.value.find((item) => item.id == roleItem) as menuApi.roleDataType
    let menuIds = checkedData
      .filter((checkedId) => checkedId.startsWith(roleTarget?.roleId))
      .map((item: string) => item.split('_')[1])
    oldRoleNewMenuIdsMap[`${roleTarget.code}`] = menuIds
  })

  console.log('oldRoleNewMenuIdsMap', oldRoleNewMenuIdsMap)
  console.log(treeRef.value.getNode())
  console.log('roleList.value', roleList.value)
  console.log('roleData', roleData)
  let roleSub = roleList.value.length - roleData.length
  let confirmTips =
    roleSub > 0
      ? `请注意!有${roleSub}个角色未被继承!,如果不继承该角色将在新版本下失效!`
      : t('system.subscribe.desc2')
  ElMessageBox.confirm(confirmTips, '确认升级吗？', {
    type: 'error',
    icon: markRaw(WarningFilled)
  })
    .then(async () => {
      try {
        loading.value = true

        await applyApi.upgradeMigratedCustomRoleSubscription({
          tenantId: applicationData.value.id,
          applicationId: applicationData.value.curApplicationId,
          oldRoleCodeNewMenuIds: oldRoleNewMenuIdsMap
        })
        dialogVisible.value = false
        message.success(t('common.handleSuccess'))
        emit('success')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      loading.value = false
      message.notifyWarning(t('system.subscribe.desc5'))
      message.notifyWarning('可能由于该接口较慢,请稍后刷新页面查看')
    })
}
/**
 * 角色菜单对应关系
 */
import { defaultProps, handleTree } from '@/utils/tree'

const menuOptions = ref<any[]>([]) // 菜单树形结构
const menuExpand = ref(false) // 展开/折叠
const treeRef = ref() // 菜单树组件 Ref
const treeNodeAll = ref(true) // 全选/全不选
const isCheckStrictly = ref(true) //父子不互相关联

const RoleType = ref(2) //1是系统角色，2是自定义角色
let defaultCheckIds = ref<(string | number)[]>([]) //默认选择
/** 继承角色和菜单*/
const handleCheckedTreeNodeAll = () => {
  // isCheckStrictly.value = false
  nextTick(() => {
    treeRef.value.setCheckedKeys(treeNodeAll.value ? defaultCheckIds.value : [])
    // isCheckStrictly.value = true
  })
}
/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}

/** 全选/全不选 */
const treeNodeAll2 = ref(true) // 全选/全不选

const handleCheckedTreeNodeAll2 = () => {
  isCheckStrictly.value = false
  nextTick(() => {
    treeRef.value.setCheckedNodes(treeNodeAll2.value ? menuOptions.value : [])
    isCheckStrictly.value = true
  })
}

// 关闭父级时，清空子级，递归清空
const closeOptions = (arr) => {
  const checkedData = treeRef.value.getCheckedKeys(false)
  arr.forEach((el) => {
    if (el.children) {
      closeOptions(el.children)
    }
    if (checkedData.indexOf(el.id) !== -1) {
      treeRef.value.setChecked(el.id, false, false)
    }
  })
}

const beforeChange = (_val, checked) => {
  const checkedData = treeRef.value.getCheckedKeys(false)
  if (checked) {
    // console.log('_val', _val)
    //如果是勾上，检查父级有没有勾上，没有就要顺带一起勾上
    if (_val.parentId) {
      if (checkedData.indexOf(`${_val.roleId}_${_val.parentId}`) === -1) {
        treeRef.value.setChecked(`${_val.roleId}_${_val.parentId}`, true, false)
      }
    }
  } else {
    //如果是取消选中，把子级全都清空
    if (_val.children) {
      closeOptions(_val.children)
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  height: 100%;
}
</style>
