<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-09-12 15:45:40
 * @LastEditors: HoJack
 * @LastEditTime: 2023-10-09 16:50:15
 * @Description: copy /pay/order/PayOrderSubmit组件 
  支付宝PC: 超链接-二维码   alipay_pc
  支付宝Wap(H5) 超链接   alipay_wap
  微信H5  微信外部的浏览器调起微信支付中间页(超链接)  wx_h5
  微信pc  二维码   wx_native
  使用方式:   /PayCheckOut?id=订单id&returnUrl=支付成功后回调地址
-->

<template>
  <div class="app-container w-100% h-100vh p-5 overflow-auto" v-loading="loading">
    <div class="text-2xl my-4 border-l-8 border-l-gold pl-2">UMV收银台</div>

    <!-- 支付信息 -->
    <el-card class="mb-4">
      <el-descriptions title="支付信息" :column="ifPC ? 3 : 1" border>
        <el-descriptions-item label="支付单号">{{ payOrder.id }}</el-descriptions-item>
        <el-descriptions-item label="商品标题">{{ payOrder.subject }}</el-descriptions-item>
        <el-descriptions-item label="商品内容">{{ payOrder.body }}</el-descriptions-item>
        <el-descriptions-item label="支付金额"
          >￥{{ (payOrder.price / 100.0).toFixed(2) }}</el-descriptions-item
        >
        <el-descriptions-item label="创建时间">{{
          utils.formatTime(payOrder.createTime, 'yyyy-MM-dd HH:mm:ss')
        }}</el-descriptions-item>
        <el-descriptions-item label="过期时间">{{
          utils.formatTime(payOrder.expireTime, 'yyyy-MM-dd HH:mm:ss')
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!--  调试使用  -->
    <div v-if="ifDev" class="my-4"> 重定向地址: <ElInput v-model="returnUrl" /> </div>

    <el-tabs type="border-card" v-model="appCode" @tab-click="tabClick">
      <el-tab-pane
        v-for="item in appList"
        :key="item.id"
        :label="item.name"
        :name="item.code"
        :value="item.code"
      />
      <!-- 支付选择框 -->

      <el-descriptions :title="`请选择${ifPC ? 'PC' : 'H5'}支付方式`" class="mt-1" />
      <div class="flex flex-row">
        <!-- 支付宝 -->
        <div v-if="payType === 1">
          <!-- 支付宝pc支付 alipay_pc  -->
          <div
            class="hidden md:flex !w-30 h-30 border-1 border-[#e6ebf5] mr-8 text-center cursor-pointer flex-col flex justify-center items-center"
            @click="submit('alipay_pc')"
          >
            <Icon :icon="`svg-icon:pay-alipay_pc`" class="!text-6xl" />
            <div class="mt-2 align-center">支付宝支付</div>
          </div>
          <!-- 支付宝H5支付 alipay_wap  -->
          <div
            class="md:hidden !w-30 h-30 border-1 border-[#e6ebf5] mr-8 text-center cursor-pointer flex-col flex justify-center items-center"
            @click="submit('alipay_wap')"
          >
            <Icon :icon="`svg-icon:pay-alipay_wap`" class="!text-6xl" />
            <div class="mt-2 align-center">支付宝支付</div>
          </div>
        </div>
        <!-- 微信 -->
        <div v-else>
          <!--  微信支付PC  wx_native 二维码  -->
          <div
            class="hidden md:flex !w-30 h-30 border-1 border-[#e6ebf5] mr-8 text-center cursor-pointer flex-col flex justify-center items-center"
            @click="submit('wx_native')"
          >
            <Icon :icon="`svg-icon:pay-wx-pay`" class="!text-6xl" />
            <div class="mt-2 align-center">微信支付</div>
          </div>
          <!--  微信支付H5  wx_H5 超链接  -->
          <div
            class="md:hidden !w-30 h-30 border-1 border-[#e6ebf5] mr-8 text-center cursor-pointer flex-col flex justify-center items-center"
            @click="submit('wx_h5')"
          >
            <Icon :icon="`svg-icon:pay-wx-pay`" class="!text-6xl" />
            <div class="mt-2 align-center">微信支付</div>
          </div>
        </div>
      </div>
    </el-tabs>

    <!-- 展示形式：二维码 URL -->
    <el-dialog
      :title="qrCode.title"
      v-model="qrCode.visible"
      width="350px"
      append-to-body
      :close-on-press-escape="false"
    >
      <!-- <qrcode-vue :value="qrCode.url" size="310" level="L" /> -->
      <Qrcode :text="qrCode.url" :width="310" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'PayCheckOut'
})

import * as utils from '@/utils/index'
import { getOrder, submitOrder } from '@/api/pay/order'
import { PayChannelEnum, PayDisplayModeEnum, PayOrderStatusEnum } from '@/utils/constants'
import { useRoute } from 'vue-router'

//是否开发环境
const ifDev = import.meta.env.DEV

const ctx = getCurrentInstance()
console.log(ctx)
console.log(window.matchMedia)
const ifPC = computed(() => window.matchMedia('(min-width: 768px)').matches)

const route = useRoute()

const message = useMessage()
let loading = ref(false) // 支付信息的 loading

//1.获取订单编号
let payOrderId = ref() // 订单编号
onMounted(() => {
  payOrderId.value = route.query.payOrderId
})
//支付成功后回调地址
let returnUrl = ref<string>(window.location.origin + '/#/pay/demo-order')
onMounted(() => {
  if (!route.query.returnUrl) {
    message.error('回调地址为空,支付成功将无法重定向到本应用')
    return
  }
  returnUrl.value = route.query.returnUrl as string
})

/**3. 获得支付信息 */
let payOrder = ref({}) as any // 支付信息

const getDetail = () => {
  // 1.1 未传递订单编号
  if (!payOrderId.value) {
    message.error('未传递支付单号，无法查看对应的支付信息')
    return
  }
  getOrder(payOrderId.value).then((response) => {
    // 1.2 无法查询到支付信息
    if (!response) {
      message.error('支付订单不存在，请检查！')
      return
    }
    // 1.3 订单已支付
    if (response.status !== PayOrderStatusEnum.WAITING.status) {
      message.error('支付订单不处于待支付状态，请检查！')
      return
    }

    // 2. 可以展示
    payOrder.value = response
  })
}
onMounted(() => {
  getDetail()
})
/** 提交支付 */
let submitLoading = ref(false) // 提交支付的 loading

const submit = (channelCode) => {
  submitLoading.value = true
  submitOrder({
    id: payOrderId.value,
    returnUrl: returnUrl.value,
    channelCode: channelCode,
    appCode: appCode.value, //应用编码 等于商户id
    ...buildSubmitParam(channelCode)
  })
    .then((response) => {
      const data = response
      console.log(PayDisplayModeEnum)
      console.log(data.displayMode)

      if (data.displayMode === PayDisplayModeEnum.QR_CODE.mode) {
        displayQrCode(channelCode, data)
      } else if (data.displayMode === PayDisplayModeEnum.URL.mode) {
        window.open(data.displayContent)
      } else if (data.displayMode === PayDisplayModeEnum.IFRAME.mode) {
        message.info('未实现该支付方式:' + data.displayMode)
      } else if (data.displayMode === PayDisplayModeEnum.FORM.mode) {
        message.info('未实现该支付方式:' + data.displayMode)
      } else {
        message.info('未实现该支付方式:' + data.displayMode)
        return
      }
    })
    .catch(() => {
      submitLoading.value = false
    })
}

/** 构建提交支付的额外参数 */
const buildSubmitParam = (channelCode) => {
  // ① 支付宝 PC 支付时，有多种展示形态
  if (channelCode === PayChannelEnum.ALIPAY_PC.code) {
    // 情况【前置模式】：将二维码前置到商户的订单确认页的模式。需要商户在自己的页面中以 iframe 方式请求支付宝页面。具体支持的枚举值有以下几种：
    // 0：订单码-简约前置模式，对应 iframe 宽度不能小于 600px，高度不能小于 300px
    // return {
    //   "channelExtras": {
    //     "qr_pay_mode": "0"
    //   }
    // }
    // 1：订单码-前置模式，对应iframe 宽度不能小于 300px，高度不能小于 600px
    // return {
    //   "channelExtras": {
    //     "qr_pay_mode": "1"
    //   }
    // }
    // 3：订单码-迷你前置模式，对应 iframe 宽度不能小于 75px，高度不能小于 75px
    // return {
    //   "channelExtras": {
    //     "qr_pay_mode": "3"
    //   }
    // }
    // 4：订单码-可定义宽度的嵌入式二维码，商户可根据需要设定二维码的大小
    // return {
    //   "channelExtras": {
    //     "qr_pay_mode": "4"
    //   }
    // }
    // 情况【跳转模式】：跳转模式下，用户的扫码界面是由支付宝生成的，不在商户的域名下。支持传入的枚举值有
    return {
      channelExtras: {
        qr_pay_mode: '2'
      }
    }
    // 情况【表单模式】：直接提交当前页面到支付宝
    // return {
    //   displayMode: PayDisplayModeEnum.FORM.mode
    // }
  }

  // ② 支付宝 Wap 支付时，引导手机扫码支付
  if (channelCode === PayChannelEnum.ALIPAY_WAP.code) {
    return {
      displayMode: PayDisplayModeEnum.QR_CODE.mode
    }
  }

  // ③ 支付宝 BarCode 支付时，需要传递 authCode 条形码
  // if (channelCode === PayChannelEnum.ALIPAY_BAR.code) {
  //   return {
  //     channelExtras: {
  //       auth_code: barCode.value
  //     }
  //   }
  // }
  //  微信支付  channelExtras传入productId 即支付订单编号id
  if (channelCode === PayChannelEnum.WX_H5.code || channelCode === PayChannelEnum.WX_NATIVE.code) {
    return {
      channelExtras: {
        productId: payOrder.value.id
      }
    }
  }
  return {}
}

/** 提交支付后（支付宝扫码支付） */
let qrCode = ref({
  // 展示形式：二维码
  url: '',
  title: '',
  visible: false
})

const displayQrCode = (channelCode, data) => {
  let title = '请使用手机浏览器“扫一扫”'
  if (channelCode === PayChannelEnum.ALIPAY_WAP.code) {
    // 考虑到 WAP 测试，所以引导手机浏览器搞
  } else if (channelCode.indexOf('alipay_') === 0) {
    title = '请使用支付宝“扫一扫”扫码支付'
  } else if (channelCode.indexOf('wx_') === 0) {
    title = '请使用微信“扫一扫”扫码支付'
  }
  qrCode.value = {
    title: title,
    url: data.displayContent,
    visible: true
  }
}

//获取应用信息
let appCode = ref<any>()

//支付类型: 支付宝0  微信1
let payType = ref<number>(0)
import * as AppApi from '@/api/pay/app'
let appList = ref<any>([])
const getAppList = async () => {
  let res = await AppApi.getAppPage({ pageNo: 1, pageSize: 100 })
  appList.value = res.list

  //如果list不为空,为appCode设置默认值
  if (appList.value.length > 0) {
    appCode.value = appList.value[0].code
    payType.value = appList.value[0].type
  }
}
onMounted(() => {
  getAppList()
})

const tabClick = ({ props }) => {
  console.log(props)
  appList.value.find((item) => {
    if (item.code === props.name) {
      payType.value = item.type
    }
  })
}
</script>
<style lang="scss" scoped>
.pay-channel-container {
  display: flex;
  margin-top: -10px;
  .box {
    width: 130px;
    border: 1px solid #e6ebf5;
    cursor: pointer;
    text-align: center;
    padding-top: 10px;
    padding-bottom: 5px;
    margin-right: 10px;
    img {
      width: 40px;
      height: 40px;
    }
    .title {
      padding-top: 5px;
    }
  }
}
</style>
