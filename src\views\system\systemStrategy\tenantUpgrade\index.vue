<template>
  <div class="tenant-upgrade">
    <!-- 搜索 -->
    <ContentWrap>
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="服务" prop="applicationCode">
          <el-select
            v-model="queryParams.applicationCode"
            clearable
            filterable
            placeholder="请选择服务"
          >
            <el-option
              v-for="item in appOpions"
              :key="item.id"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" clearable placeholder="请选择状态">
            <el-option label="启用" :value="0" />
            <el-option label="停用" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <icon-ep-search style="font-size: 12px" class="mr-5px" />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <icon-ep-refresh style="font-size: 12px" class="mr-5px" />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap ifTable>
      <div class="button-warp mb-16px">
        <el-button type="primary" @click="add">添加</el-button>
      </div>
      <el-table v-loading="loading" :data="list">
        <el-table-column label="序号" align="center" type="index" width="100" />
        <el-table-column label="名称" align="center" prop="name" width="200" />
        <el-table-column label="服务名称" align="center" prop="applicationCode" width="140">
          <template #default="{ row }">
            {{ appMaps[row.applicationCode]?.name }}
          </template>
        </el-table-column>
        <el-table-column label="租户" align="center" prop="cond">
          <template #default="{ row }">
            <el-button link type="primary" @click="showTenantsDetail(row)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="时间" align="center" prop="time" width="200">
          <template #default="{ row }"> {{ row.startTime }} - {{ row.endTime }} </template>
        </el-table-column>

        <el-table-column label="状态" align="center" prop="status" width="140">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="0"
              :inactive-value="1"
              @change="(e) => onSwitchChange(e, scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" prop="oprate" fixed="right" width="200">
          <template #default="{ row }">
            <el-button type="primary" link @click="edit(row)">编辑</el-button>
            <el-button type="danger" link @click="del(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <oprateDialog ref="oprateDialogRef" @success="getList" />
    <tenantShowDialog ref="tenantShowDialogRef" />
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'TenantUpgrade'
})

import {
  getTenantUpgradeRulePage,
  deleteTenantUpgradeRule,
  updateTenantUpgradeRule,
  getTenantApplicationListAll
} from '@/api/system/systemStrategy/tenantUpgrade/index'
import oprateDialog from './components/oprateDialog.vue'
import { getTenantList } from '@/api/system/tenant'
import tenantShowDialog from './components/tenantShowDialog.vue'
const queryParams: Ref<any> = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  applicationCode: undefined,
  status: undefined
})

const list: Ref<any[]> = ref([])

const total = ref(0)

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

// 切换状态
const onSwitchChange = async (val, _row) => {
  try {
    await message.confirm(`确定${val == 1 ? '停用' : '启用'}策略？`)
    try {
      loading.value = true
      await updateTenantUpgradeRule(_row)
      message.success('操作成功')
    } finally {
      loading.value = false
    }
  } finally {
    getList()
  }
}

const getList = async () => {
  try {
    loading.value = true
    const res = await getTenantUpgradeRulePage(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const queryFormRef = ref()

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const tenantMap: Ref<any> = ref({})

const initData = async () => {
  const res = await getTenantList()
  console.log('res666', res)
  tenantMap.value = res.reduce((a, b) => {
    return {
      ...a,
      [b.id]: b.name
    }
  }, {})
  const res1: any = await getTenantApplicationListAll()
  appOpions.value = res1
}

const oprateDialogRef = ref()

const edit = (_row) => {
  oprateDialogRef.value.open(appOpions.value, 2, _row)
}

const add = () => {
  oprateDialogRef.value.open(appOpions.value, 1)
}

const message = useMessage()

const del = async (_row) => {
  await message.confirm('确定删除？')
  await deleteTenantUpgradeRule(_row.id)
  message.success('删除成功')
  getList()
}

const tenantShowDialogRef = ref()

const showTenantsDetail = (_row) => {
  tenantShowDialogRef.value.open(_row, tenantMap.value)
}

const appOpions: Ref<any> = ref([])

const appMaps: Ref<any> = computed(() => {
  return appOpions.value.reduce((a, b) => {
    return {
      ...a,
      [b.code]: b
    }
  }, {})
})

onMounted(() => {
  handleQuery()
  initData()
})

const loading = ref(false)
</script>
