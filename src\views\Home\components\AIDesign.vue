<template>
  <div class="home__ai-design">
    <section class="ai-banner">
      <el-image
        v-if="ifEn"
        class="w-full"
        :src="`${basePath}/Home/AiDesign/en_US/ai-banner-bg.webp`"
        alt=""
      />
      <el-image v-else class="w-full" :src="`${basePath}/Home/AiDesign/ai-banner-bg.webp`" alt="" />
      <div v-if="!ifEn" class="ai-banner_text">一键开启AI艺术创作之旅</div>
    </section>

    <section class="easy-image flex flex-col items-center">
      <div class="header_title">
        <div class="title-text">
          {{ t('home.AIDesign.title1_1') }}
        </div>
      </div>
      <div class="header_desc">
        {{ t('home.AIDesign.title1_item1') }}
      </div>
      <div class="easy-image-tag flex flex-row justify-center items-center">
        <div
          class="easy-image-tag-item flex flex-row justify-center items-center"
          :class="{ '!w-[150px] !text-[18px]': ifEn }"
        >
          {{ t('home.AIDesign.title1_item1_1') }}
        </div>
        <div
          class="easy-image-tag-item flex flex-row justify-center items-center"
          :class="{ '!w-[150px] !text-[18px]': ifEn }"
        >
          {{ t('home.AIDesign.title1_item1_2') }}
        </div>
        <div
          class="easy-image-tag-item flex flex-row justify-center items-center"
          :class="{ '!w-[150px] !text-[18px]': ifEn }"
        >
          {{ t('home.AIDesign.title1_item1_3') }}
        </div>
      </div>
      <div class="easy-image-ai">
        <div class="ai-image-warp">
          <el-image lazy class="ai-image" :src="`${basePath}/Home/AiDesign/ai1.webp`" />
          <div class="ai-text">
            {{ t('home.AIDesign.title1_item2_1') }}
          </div>
        </div>
        <div class="ai-divider"></div>
        <div class="ai-image-warp">
          <el-image lazy class="ai-image" :src="`${basePath}/Home/AiDesign/ai2.webp`" />
          <div class="ai-text">
            {{ t('home.AIDesign.title1_item2_2') }}
          </div>
        </div>
        <div class="ai-image-warp">
          <el-image lazy class="ai-image" :src="`${basePath}/Home/AiDesign/ai3.webp`" />
          <div class="ai-text">
            {{ t('home.AIDesign.title1_item2_3') }}
          </div>
        </div>
        <div class="ai-image-warp">
          <el-image lazy class="ai-image" :src="`${basePath}/Home/AiDesign/ai4.webp`" />
          <div class="ai-text">
            {{ t('home.AIDesign.title1_item2_4') }}
          </div>
        </div>
        <div class="ai-image-warp">
          <el-image lazy class="ai-image" :src="`${basePath}/Home/AiDesign/ai5.webp`" />
          <div class="ai-text">
            {{ t('home.AIDesign.title1_item2_5') }}
          </div>
        </div>
      </div>
    </section>

    <section class="unlimited-image flex flex-col items-center">
      <div class="header_title">
        <div class="title-text">
          {{ t('home.AIDesign.title2_1') }}
        </div>
      </div>
      <div class="header_desc">
        {{ t('home.AIDesign.title2_item1') }}
      </div>
      <div class="unlimited-image-tag flex flex-row justify-center items-center">
        {{ t('home.AIDesign.title2_item2') }}
      </div>
      <div class="unlimited-image-ai flex flex-row">
        <div class="card-example">
          <el-image
            lazy
            class="w-full h-full"
            :src="`${basePath}/Home/AiDesign/unlimitedimage-1.webp`"
            alt=""
          />
        </div>
        <div class="word-cloud flex flex-col items-center">
          <el-image
            v-if="ifEn"
            lazy
            class="w-707px <2xl:(w-503px)"
            :src="`${basePath}/Home/AiDesign/en_US/unlimitedimage-2.webp`"
            alt=""
          />
          <el-image
            v-else
            lazy
            class="w-707px <2xl:(w-503px)"
            :src="`${basePath}/Home/AiDesign/unlimitedimage-2.webp`"
            alt=""
          />
          <div class="word-cloud-tips">
            {{ t('home.AIDesign.title2_item3') }}
          </div>
        </div>
      </div>
    </section>

    <section class="demand-image flex flex-col items-center justify-center">
      <div class="header_title">
        <div class="title-text">
          {{ t('home.AIDesign.title3_1') }}
        </div>
      </div>
      <div class="demand-image-tag flex flex-row justify-center items-center">
        <div
          class="demand-image-tag-item flex flex-row justify-center items-center"
          :class="{ '!w-[260px] !text-[18px]': ifEn }"
        >
          {{ t('home.AIDesign.title3_item1_1') }}
        </div>
        <div
          class="demand-image-tag-item flex flex-row justify-center items-center"
          :class="{ '!w-[260px] !text-[18px]': ifEn }"
        >
          {{ t('home.AIDesign.title3_item1_2') }}
        </div>
        <div
          class="demand-image-tag-item flex flex-row justify-center items-center"
          :class="{ '!w-[260px] !text-[18px]': ifEn }"
        >
          {{ t('home.AIDesign.title3_item1_3') }}
        </div>
      </div>
      <div class="demand-image-ai flex flex-row justify-center items-center">
        <div class="card-example">
          <el-image
            lazy
            class="w-full h-full"
            :src="`${basePath}/Home/AiDesign/demandimg-1.webp`"
            alt=""
          />
        </div>
        <div class="demand-image-regulations flex flex-col w-[40%]">
          <div class="regulations-item">
            <div class="regulations-item-title">
              {{ t('home.AIDesign.title3_item2_1') }}
            </div>
            <div class="regulations-item-desc">
              {{ t('home.AIDesign.title3_item2_2') }}
            </div>
          </div>
          <div class="regulations-item">
            <div class="regulations-item-title">
              {{ t('home.AIDesign.title3_item3_1') }}
            </div>
            <div class="regulations-item-desc">
              {{ t('home.AIDesign.title3_item3_2') }}
            </div>
          </div>
          <div class="regulations-item">
            <div class="regulations-item-title">
              {{ t('home.AIDesign.title3_item4_1') }}
            </div>
            <div class="regulations-item-desc">
              {{ t('home.AIDesign.title3_item4_2') }}
            </div>
          </div>
          <div class="regulations-item">
            <div class="regulations-item-title">
              {{ t('home.AIDesign.title3_item5_1') }}
            </div>
            <div class="regulations-item-desc">
              {{ t('home.AIDesign.title3_item5_2') }}
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
const { t, ifEn } = useI18n()
const basePath = import.meta.env.BASE_URL === '/' ? '' : import.meta.env.BASE_URL
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'DOUYU';
  src: url('/Home/AiDesign/fonts/douyuFont-2.otf');
}

.home__ai-design {
  background: url('/Home/AiDesign/ai-design-bg.png') bottom left/100% no-repeat;
}

.ai-banner {
  position: relative;

  .ai-banner_text {
    position: absolute;
    top: 102px;
    left: 50%;
    transform: translate(-50%, 0);
    font-family: DOUYU;
    font-weight: normal;
    font-size: 60px;
    color: #ffffff;
    line-height: 111px;
    background: linear-gradient(182deg, #ffffff 0%, #65ebff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    cursor: default;
    user-select: none;
    white-space: nowrap;
  }
}

.header_title {
  position: relative;
  height: 31px;
  cursor: default;
  user-select: none;

  .title-text {
    display: flex;
    justify-content: space-between;
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: PingFang SC;
    font-size: 40px;
    color: #000000;
    line-height: 60px;
  }
}

.header_desc {
  margin-top: 17px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 20px;
  color: #333333;
  line-height: 21px;
}

.easy-image {
  padding-top: 150px;
  box-sizing: border-box;

  .header_title {
    width: 1071px;
    background: url('/Home/AiDesign/Easyimage.png') center/100% 100% no-repeat;
  }

  .easy-image-tag {
    padding: 44px 0 12px;
    .easy-image-tag-item {
      margin: 0 23px;
      width: 118px;
      height: 54px;
      background: linear-gradient(90deg, #0042ff, rgba(0, 66, 255, 0.1));
      border-radius: 27px;
      font-family: PingFang SC;
      font-size: 28px;
      letter-spacing: 3px;
      color: #ffffff;
    }
  }
}

.unlimited-image {
  padding-top: 163px;
  box-sizing: border-box;

  .header_title {
    width: 1419px;
    background: url('/Home/AiDesign/unlimitedimage.png') center/100% 100% no-repeat;
  }

  .unlimited-image-tag {
    margin: 44px 0;
    padding: 0 30px;
    height: 54px;
    background: linear-gradient(90deg, #0042ff, rgba(0, 66, 255, 0.1));
    border-radius: 27px;
    font-family: PingFang SC;
    font-size: 28px;
    color: #ffffff;
  }

  .unlimited-image-ai {
    :deep(.card-example) {
      width: 471px;
      height: 688px;

      .el-image {
        transition: all 0.3s;
      }

      &:hover {
        .el-image {
          transform: scale(1.2);
        }
      }
    }

    :deep(.word-cloud) {
      margin: 92px 0 0 175px;

      .el-image {
        transition: all 0.3s;
      }

      &:hover {
        .el-image {
          transform: scale(1.2);
        }
      }

      .word-cloud-tips {
        margin-top: 42px;
        font-family: PingFang SC;
        font-size: 16px;
        color: #333333;
      }
    }
  }
}

.demand-image {
  padding-top: 132px;
  box-sizing: border-box;

  .header_title {
    width: 940px;
    background: url('/Home/AiDesign/Demandimage.png') center/100% 100% no-repeat;
  }

  .demand-image-tag {
    padding: 33px 0 0;
    .demand-image-tag-item {
      margin: 0 26px;
      width: 185px;
      height: 54px;
      background: linear-gradient(90deg, #0042ff, rgba(0, 66, 255, 0.1));
      border-radius: 27px;
      font-family: PingFang SC;
      font-size: 28px;
      letter-spacing: 3px;
      color: #ffffff;
    }
  }

  .demand-image-ai {
    padding: 92px 0 182px;

    :deep(.card-example) {
      width: 471px;
      height: 762px;

      .el-image {
        transition: all 0.3s;
      }
      &:hover {
        .el-image {
          transform: scale(1.2);
        }
      }
    }

    .demand-image-regulations {
      margin-left: 183px;

      .regulations-item {
        margin-bottom: 70px;
        padding-left: 38px;
        font-family: PingFang SC;
        color: #333333;

        .regulations-item-title {
          position: relative;
          font-weight: bold;
          font-size: 32px;
          line-height: 32px;
          &::before {
            position: absolute;
            width: 26px;
            height: 32px;
            content: '';
            left: -30px;
            background: url('/Home/AiDesign/reg-icon.png') center/100% 100% no-repeat;
          }
        }

        .regulations-item-desc {
          margin-top: 16px;
          font-size: 16px;
        }
      }
    }
  }
}

@media (max-width: 1535.9px) {
  .ai-banner {
    .ai-banner_text {
      top: 102px;
      font-size: 43px;
      line-height: 79px;
    }
  }

  .header_title {
    height: 22px;
    .title-text {
      width: 100%;
      font-size: 28px;
      line-height: 43px;
      display: flex;
      justify-content: center;
    }
  }

  .header_desc {
    margin-top: 12px;
    font-size: 14px;
    line-height: 15px;
  }

  .easy-image {
    padding-top: 107px;

    .header_title {
      width: 762px;
    }

    .easy-image-tag {
      padding: 31px 0 9px;
      .easy-image-tag-item {
        margin: 0 16px;
        width: 84px;
        height: 38px;
        border-radius: 19px;
        font-size: 20px;
      }
    }
  }

  .unlimited-image {
    padding-top: 116px;

    .header_title {
      width: 1010px;
    }

    .unlimited-image-tag {
      margin: 30px 0;
      height: 38px;
      border-radius: 19px;
      font-size: 20px;
    }

    .unlimited-image-ai {
      .card-example {
        width: 335px;
        height: 489px;
      }

      .word-cloud {
        margin: 65px 0 0 125px;

        .word-cloud-tips {
          margin-top: 30px;
          font-size: 12px;
        }
      }
    }
  }

  .demand-image {
    padding-top: 94px;

    .header_title {
      width: 669px;
    }

    .demand-image-tag {
      padding: 23px 0 0;
      .demand-image-tag-item {
        margin: 0 18px;
        width: 132px;
        height: 38px;
        border-radius: 19px;
        font-size: 20px;
      }
    }

    .demand-image-ai {
      padding: 65px 0 129px;

      .card-example {
        width: 335px;
        height: 542px;
      }

      .demand-image-regulations {
        margin-left: 130px;
        margin-top: 50px;

        .regulations-item {
          margin-bottom: 40px;
          padding-left: 27px;

          .regulations-item-title {
            font-size: 23px;
            line-height: 28px;
            &::before {
              width: 18px;
              height: 28px;
            }
          }

          .regulations-item-desc {
            margin-top: 12px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
.easy-image-ai {
  width: 1460px;
  margin: 12px auto 0 auto;
  padding: 0 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  @media (max-width: 1535.9px) {
    width: 1000px;
  }
  .ai-image-warp {
    padding-top: 45px;
    padding-bottom: 9px;
    width: 16.43%;
    // display: flex;
    // justify-content: center;
    // align-items: center;
    // flex-direction: column-reverse;
    .el-image {
      transition: all 0.3s;
    }
    &:hover {
      .el-image {
        transform: scale(1.2);
      }
    }
  }
  .ai-image {
    width: 100%;
  }
  .ai-text {
    margin-top: 12px;
    font-size: 16px;
    text-align: center;
  }
  .ai-divider {
    height: 100%;
    width: 2px;
    border-left: 1px dashed #d5d5d5;
    height: 467px;
    margin: 0 5px;
    @media (max-width: 1535.9px) {
      height: 336px;
    }
  }
}
</style>
