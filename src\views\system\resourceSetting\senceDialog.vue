<template>
  <Dialog
    v-model="dialogVisible"
    :title="'业务场景权限'"
    width="800"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <el-table v-loading="loading" :data="list" ref="tableRef">
      <el-table-column align="center" prop="label" fixed="left" width="200">
        <template #header>
          <div class="point" style="position: relative; margin-left: 80px; text-align: left">
            <div
              class="split"
              style="
                position: absolute;
                top: 15px;
                left: -63px;
                width: 100px;
                background: #909399;
                transform: rotate(45deg);
                height: 1px;
              "
            ></div>
            权限点
          </div>
          <div style="position: relative; text-align: left; margin-left: 40px">场景</div>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in authList"
        :key="index"
        :label="item.label"
        align="center"
        :prop="item.label"
        min-width="120"
      >
        <template #default="{ row }">
          <el-checkbox
            @change="(isCheck) => changeAuth(row, isCheck, item.value)"
            v-model="row[item.value]"
          />
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button type="primary" @click="submit">{{ t('common.ok') }}</el-button>
      <el-button @click="close">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'senceDialog'
})

const { t } = useI18n() // 国际化
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
const dialogVisible = ref(false)

const loading = ref(false)

const list = ref([])

const senceList = ref([])

const authList = ref([])

const rowData = ref()

const originRowData = ref()

import { deepClone } from '@/utils/deep'
import { isArray } from 'lodash-es'

const open = (_sceneRes, _sceneList) => {
  console.log('_sceneRes', _sceneRes)
  console.log('_sceneList', _sceneList)
  console.log(
    'getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)',
    getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)
  )
  // originRowData.value = _rowData
  // rowData.value = deepClone(_rowData)
  // senceList.value = []
  list.value = []
  list.value =
    _sceneRes.length > 0
      ? deepClone(_sceneRes)
      : deepClone(_sceneList).map((el) => {
          el.resource = []
          return el
        })
  // 构建横向表格数据
  // list.value = [...getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)].map((el) => {
  //   for (let key in _rowData.resourcesMap) {
  //     el[key] = _rowData.resourcesMap[key].indexOf(el.value) !== -1
  //   }
  //   return el
  // })
  authList.value = [...getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)]
  dialogVisible.value = true
}

const changeAuth = (_row, _isCheck, _val) => {
  if (_isCheck) {
    if (_row.resource.indexOf(_val) === -1) {
      _row.resource.push(_val)
    }
  } else {
    if (_row.resource.indexOf(_val) !== -1) {
      _row.resource.splice(_row.resource.indexOf(_val), 1)
    }
  }
}

const emit = defineEmits(['submit', 'confirm'])

const submit = () => {
  console.log('list.value', list.value)
  try {
    loading.value = true
    // // 先清空
    // for (let key in rowData.value.resourcesMap) {
    //   rowData.value.resourcesMap[key] = []
    // }
    // list.value.forEach((el) => {
    //   for (let key in el) {
    //     if (rowData.value.resourcesMap.hasOwnProperty(key)) {
    //       if (el[key]) {
    //         rowData.value.resourcesMap[key].push(el.value)
    //       }
    //     }
    //   }
    // })
    // originRowData.value.resourcesMap = rowData.value.resourcesMap
    // dialogVisible.value = false
    // emit('submit', originRowData.value.id, originRowData.value)

    emit('confirm', list.value)
    dialogVisible.value = false
  } finally {
    loading.value = false
  }
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>
