<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="formType === 'create' ? t('pay.app.zfbTitle1') : t('pay.app.zfbTitle2')"
      @closed="close"
      append-to-body
      width="800px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="formLoading"
      >
        <el-form-item label-width="180px" :label="'渠道类型'" prop="code">
          <el-select v-model="formData.code" :disabled="formType !== 'create'">
            <el-option
              v-for="(item, index) in codeMap"
              :label="item.name"
              :value="item.code"
              :key="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label-width="180px" :label="t('pay.app.feeRate')" prop="feeRate">
          <el-input
            v-model="formData.feeRate"
            :placeholder="t('pay.app.feeRatePlaceholder')"
            clearable
            :style="{ width: '100%' }"
          >
            <template #append>%</template>
          </el-input>
        </el-form-item>
        <el-form-item label-width="180px" :label="t('pay.app.zfbConfig.appId')" prop="config.appId">
          <el-input
            v-model="formData.config.appId"
            :placeholder="t('pay.app.zfbConfig.appIdPlaceholder')"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label-width="180px" :label="t('pay.app.config.mchId')" prop="config.mchId">
          <el-input v-model="formData.config.mchId" :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label-width="180px" :label="t('pay.app.config.status')" prop="status">
          <el-radio-group v-model="formData.status" size="default">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label-width="180px"
          :label="t('pay.app.zfbConfig.serverUrl')"
          prop="config.serverUrl"
        >
          <el-radio-group v-model="formData.config.serverUrl" size="default">
            <el-radio label="https://openapi.alipay.com/gateway.do">{{
              t('pay.app.serverUrl_1')
            }}</el-radio>
            <el-radio label="https://openapi-sandbox.dl.alipaydev.com/gateway.do">{{
              t('pay.app.serverUrl_2')
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label-width="180px"
          :label="t('pay.app.zfbConfig.signType')"
          prop="config.signType"
        >
          <el-radio-group v-model="formData.config.signType" size="default">
            <el-radio key="RSA2" label="RSA2">RSA2</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label-width="180px" :label="t('pay.app.zfbConfig.mode')" prop="config.mode">
          <el-radio-group v-model="formData.config.mode" size="default">
            <el-radio key="公钥模式" :label="1">{{ t('pay.app.mode_1') }}</el-radio>
            <el-radio key="证书模式" :label="2">{{ t('pay.app.mode_2') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <div>
          <el-form-item
            label-width="180px"
            :label="t('pay.app.zfbConfig.privateKey')"
            prop="config.privateKey"
          >
            <el-input
              type="textarea"
              :autosize="{ minRows: 8, maxRows: 8 }"
              v-model="formData.config.privateKey"
              :placeholder="t('pay.app.privateKeyPlaceholder')"
              clearable
              :style="{ width: '100%' }"
            />
          </el-form-item>
          <el-form-item
            label-width="180px"
            :label="t('pay.app.zfbConfig.alipayPublicKey')"
            prop="config.alipayPublicKey"
            v-if="formData.config.mode === 1"
          >
            <el-input
              type="textarea"
              :autosize="{ minRows: 8, maxRows: 8 }"
              v-model="formData.config.alipayPublicKey"
              :placeholder="t('pay.app.alipayPublicKeyPlaceholder')"
              clearable
              :style="{ width: '100%' }"
            />
          </el-form-item>
        </div>
        <div v-if="formData.config.mode === 2">
          <el-form-item
            label-width="180px"
            :label="t('pay.app.zfbConfig.appCertContent')"
            prop="config.appCertContent"
          >
            <el-input
              v-model="formData.config.appCertContent"
              type="textarea"
              :placeholder="t('pay.app.appCertContentPlaceholder')"
              readonly
              :autosize="{ minRows: 8, maxRows: 8 }"
              :style="{ width: '100%' }"
            />
          </el-form-item>
          <el-form-item label-width="180px" label="">
            <el-upload
              action=""
              ref="privateKeyContentFile"
              :limit="1"
              :accept="fileAccept"
              :http-request="appCertUpload"
              :before-upload="fileBeforeUpload"
            >
              <el-button size="small" type="primary" icon="el-icon-upload">{{
                t('pay.app.clickUpload')
              }}</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item
            label-width="180px"
            :label="t('pay.app.zfbConfig.alipayPublicCertContent')"
            prop="config.alipayPublicCertContent"
          >
            <el-input
              v-model="formData.config.alipayPublicCertContent"
              type="textarea"
              :placeholder="t('pay.app.alipayPublicCertContentPlaceholder')"
              readonly
              :autosize="{ minRows: 8, maxRows: 8 }"
              :style="{ width: '100%' }"
            />
          </el-form-item>
          <el-form-item label-width="180px" label="">
            <el-upload
              ref="privateCertContentFile"
              action=""
              :limit="1"
              :accept="fileAccept"
              :before-upload="fileBeforeUpload"
              :http-request="alipayPublicCertUpload"
            >
              <el-button size="small" type="primary" icon="el-icon-upload">{{
                t('pay.app.clickUpload')
              }}</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item
            label-width="180px"
            :label="t('pay.app.zfbConfig.rootCertContent')"
            prop="config.rootCertContent"
          >
            <el-input
              v-model="formData.config.rootCertContent"
              type="textarea"
              :placeholder="t('pay.app.rootCertContentPlaceholder')"
              readonly
              :autosize="{ minRows: 8, maxRows: 8 }"
              :style="{ width: '100%' }"
            />
          </el-form-item>
          <el-form-item label-width="180px" label="">
            <el-upload
              ref="privateCertContentFile"
              :limit="1"
              :accept="fileAccept"
              action=""
              :before-upload="fileBeforeUpload"
              :http-request="rootCertUpload"
            >
              <el-button size="small" type="primary" icon="el-icon-upload">{{
                t('pay.app.clickUpload')
              }}</el-button>
            </el-upload>
          </el-form-item>
        </div>
        <el-form-item label-width="180px" :label="t('pay.app.remark')" prop="remark">
          <el-input v-model="formData.remark" :style="{ width: '100%' }" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="close">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ t('common.ok') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'AlipayChannelForm'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

import * as ChannelApi from '@/api/pay/channel'
import { CommonStatusEnum } from '@/utils/constants'
import type { FormInstance } from 'element-plus'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

let formRef = ref<FormInstance>()

let dialogVisible = ref<boolean>(false)
let formLoading = ref<boolean>(false)

let formData = ref<any>({
  id: '',
  appId: '',
  merchantId: '', //商户id
  code: '',
  status: 0,
  feeRate: '',
  remark: '',
  config: {
    appId: '',
    mchId: '', //支付宝商户号
    serverUrl: null,
    signType: '',
    mode: null,
    privateKey: '',
    alipayPublicKey: '',
    appCertContent: '',
    alipayPublicCertContent: '',
    rootCertContent: ''
  }
})

let rules = reactive({
  code: [{ required: true, message: '请选择渠道类型', trigger: 'blur' }],
  feeRate: [{ required: true, message: t('pay.app.rzfb1'), trigger: 'blur' }],
  status: [{ required: true, message: t('pay.app.rzfb2'), trigger: 'blur' }],
  'config.appId': [{ required: true, message: t('pay.app.rzfb3'), trigger: 'blur' }],
  'config.mchId': [{ required: true, message: t('pay.app.rzfb4'), trigger: 'blur' }],
  'config.serverUrl': [{ required: true, message: t('pay.app.rzfb5'), trigger: 'blur' }],
  'config.signType': [{ required: true, message: t('pay.app.rzfb6'), trigger: 'blur' }],
  'config.mode': [{ required: true, message: t('pay.app.rzfb7'), trigger: 'blur' }],
  'config.privateKey': [{ required: true, message: t('pay.app.rzfb8'), trigger: 'blur' }],
  'config.alipayPublicKey': [{ required: true, message: t('pay.app.rzfb9'), trigger: 'blur' }],
  'config.appCertContent': [{ required: true, message: t('pay.app.rzfb10'), trigger: 'blur' }],
  'config.alipayPublicCertContent': [
    { required: true, message: t('pay.app.rzfb11'), trigger: 'blur' }
  ],
  'config.rootCertContent': [{ required: true, message: t('pay.app.rzfb12'), trigger: 'blur' }]
})

/** 打开弹窗 */
let formType = ref('create')

const codeMap: Ref<any[]> = ref([]) //渠道类型

const open = async (row, code?) => {
  dialogVisible.value = true
  formLoading.value = true
  console.log(row, code)

  resetForm(row, code)
  try {
    const res = await ChannelApi.getChannelListChannelTypesByAppType(1) //1为支付宝
    codeMap.value = res
    if (code) {
      let response = await ChannelApi.getChannel(row.merchantId, row.id, code)

      if (response && response?.id) {
        formData.value = response
        formData.value.config = JSON.parse(response?.config)
      }
      formType.value = !response?.id ? 'create' : 'edit'
    } else {
      formType.value = 'create'
    }
  } catch (error) {
    console.log(error)
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 重置表单 */
let resetForm = (row, code?) => {
  formData.value = {
    id: row?.id,
    appId: row?.id,
    merchantId: row?.merchantId,
    code: code,
    status: CommonStatusEnum.ENABLE,
    feeRate: null,
    remark: '',
    config: {
      appId: '',
      mchId: '',
      serverUrl: null,
      signType: 'RSA2',
      mode: null,
      privateKey: '',
      alipayPublicKey: '',
      appCertContent: '',
      alipayPublicCertContent: '',
      rootCertContent: ''
    }
  }
  formRef.value?.resetFields()
}

const close = () => {
  dialogVisible.value = false
  resetForm(undefined, undefined)
}

//提交表单
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value?.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ChannelApi.ChannelVO
    data.config = JSON.stringify(formData.value.config)

    if (formType.value === 'create') {
      await ChannelApi.createChannel(data)
      message.success(t('common.createSuccess'))
    } else {
      await ChannelApi.updateChannel(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

let fileAccept = ref('.crt')

const fileBeforeUpload = (file) => {
  let format = '.' + file.name.split('.')[1]
  if (format !== fileAccept.value) {
    const str = t('pay.app.desc2', {
      fileAccept: fileAccept.value
    })
    message.error(str)
    return false
  }
  let isRightSize = file.size / 1024 / 1024 < 2
  if (!isRightSize) {
    message.error(t('pay.app.desc3'))
  }
  return isRightSize
}

const appCertUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.appCertContent = e?.target.result
  }
  readFile.readAsText(event.file)
}

const alipayPublicCertUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.alipayPublicCertContent = e.target.result
  }
  readFile.readAsText(event.file)
}
const rootCertUpload = (event) => {
  const readFile = new FileReader()
  readFile.onload = (e) => {
    formData.value.config.rootCertContent = e.target.result
  }
  readFile.readAsText(event.file)
}
</script>
