<template>
  <el-dialog
    title="查看菜单"
    v-model="show"
    width="860px"
    append-to-body
    destroy-on-close
    class="approval-edit-form"
    @close="close"
  >
    <el-card class="cardHeight" style="margin: 16px 0">
      <template #header>
        全部展开/折叠:
        <el-switch
          v-model="menuExpand"
          active-text="展开"
          inactive-text="折叠"
          inline-prompt
          @change="handleCheckedTreeExpand"
        />
      </template>
      <el-tree
        ref="treeRef"
        :data="menuOptions"
        :props="defaultProps"
        empty-text="暂无数据"
        node-key="id"
      >
        <template #default="{ node, data }">
          <span>{{ node.label }}({{ data.id }})</span>
          <el-tag type="primary" effect="plain" round class="ml-3" v-if="data.type === 1">
            目录
          </el-tag>
          <el-tag
            type="success"
            effect="plain"
            round
            class="ml-3"
            v-if="data.type === 2 && data.visible"
          >
            菜单
          </el-tag>
          <el-tag
            type="warning"
            effect="plain"
            round
            class="ml-3"
            v-if="data.type === 2 && !data.visible"
          >
            页面
          </el-tag>
          <el-tag
            type="info"
            effect="plain"
            round
            class="ml-3"
            v-if="!data.visible && data.type !== 3 && data.type !== 4"
          >
            隐
          </el-tag>
          <el-tag type="danger" effect="plain" round class="ml-3" v-if="data.type === 3">
            按钮
          </el-tag>
        </template>
      </el-tree>
    </el-card>

    <template #footer>
      <el-button @click="close">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'PreviewMenuDialog'
})

import { defaultProps, handleTree } from '@/utils/tree'

import { routeDuplication, filterRouteByCondition } from '@/utils/routerHelper'

const show = ref(false)

const menuOptions = ref()

const typeMap = {
  1: '目录',
  2: '菜单',
  3: '按钮',
  4: 'API'
}

// 是否展开全部
const menuExpand = ref(false)

const treeRef = ref()

/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}

const close = () => {
  show.value = false
}

import { deepClone } from '@/utils/deep'

const open = (_menu) => {
  menuExpand.value = false
  show.value = true
  let resRoute = handleTree(deepClone(_menu))
  menuOptions.value = routeDuplication(resRoute, _menu)
  // menuOptions.value = filterRouteByCondition(handleTree(deepClone(_menu)))
}

defineExpose({
  open
})
</script>
