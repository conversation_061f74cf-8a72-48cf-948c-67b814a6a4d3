<template>
  <UmvContent>
    <UmvQuery
      v-model="queryParams"
      ref="queryFormRef"
      :opts="queryOpts"
      @check="handleQuery"
      @reset="handleQuery"
    />

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="handleQuery">
      <template #tools>
        <el-button type="primary" size="small" @click="add">添加</el-button>
      </template>
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </template>
    </UmvTable>
    <oprateDialog ref="oprateDialogRef" @success="handleQuery" />
    <ApprovalEditForm ref="ApprovalEditFormRef" @success="handleQuery" />
  </UmvContent>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'Application'
})

import {
  getTenantPackage,
  getSubTenantPackage,
  subscribePackage
} from '@/api/system/application/index'
import oprateDialog from './components/oprateDialog.vue'
import { dateFormatter } from '@/utils/formatTime'
import ApprovalEditForm from './components/ApprovalEditForm.vue'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { ElButton, ElTag } from 'element-plus'
import { checkPermi } from '@/utils/permission'

//获取客户端列表
import { useHook } from './common/useHook'
const { clientList, getClientList, getClientNameById, getClientCodeById } = useHook()
onMounted(() => {
  getClientList()
})

const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  clientId: ''
})

const loading = ref(false)

const list = ref([])

const total = ref(0)

import * as authUtil from '@/utils/auth'

const subList: Ref<any[]> = ref([])

const subMap = computed(() => {
  return subList.value.reduce((a, b) => {
    return {
      ...a,
      [b.id]: b
    }
  }, {})
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '应用名',
    defaultVal: '',
    controlRender: (form) => <el-input v-model={form.name} placeholder="请输入应用名" clearable />
  },
  clientId: {
    label: '业务端',
    defaultVal: '',
    controlRender: (form) => (
      <el-select v-model={form.clientId} placeholder="请选择业务端" clearable>
        {clientList.value.map((item) => (
          <el-option key={item.id} label={`${item.name}(${item.code})`} value={item.id} />
        ))}
      </el-select>
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: '应用id', align: 'center' },
  { prop: 'name', label: '应用名称', align: 'center' },
  { prop: 'code', label: '应用编号', align: 'center' },
  { prop: 'ver', label: '版本号', align: 'center' },
  { prop: 'description', label: '描述', align: 'center' },
  {
    prop: 'subStatus',
    label: '订阅状态',
    align: 'center',
    renderTemplate: (scope) => (
      <ElTag type={subMap[scope.row.id] ? 'success' : 'primary'}>
        {subMap[scope.row.id] ? '已订阅' : '未订阅'}
      </ElTag>
    )
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'client',
    label: '业务端',
    align: 'left',
    renderTemplate: (scope) => (
      <span>
        {getClientNameById(scope.row.clientId)}({getClientCodeById(scope.row.clientId)})
      </span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'left',
    fixed: 'right',
    width: '150',
    renderTemplate: (scope) => (
      <div>
        <ElButton type="primary" link onClick={() => configFn(scope.row, 'check')}>
          查看
        </ElButton>
        <ElButton type="primary" link onClick={() => configFn(scope.row, 'edit')}>
          配置
        </ElButton>
        {!subMap[scope.row.id] && checkPermi(['system:application:subscribe']) && (
          <ElButton type="warning" link onClick={() => subscribe(scope.row)}>
            订阅
          </ElButton>
        )}
      </div>
    )
  }
])

// 搜索
const handleQuery = async () => {
  try {
    loading.value = true
    const res = await getTenantPackage(queryParams.value)
    list.value = res.list
    total.value = res.total
    const tenantId = authUtil.getTenantId()
    const subRes = await getSubTenantPackage({
      tenantId
    })
    subList.value = subRes
  } finally {
    loading.value = false
  }
}

const queryFormRef = ref()

// 订阅
const subscribe = async (_row) => {
  await message.confirm('是否订阅该应用？')
  const tenantId = authUtil.getTenantId()
  try {
    loading.value = true
    await subscribePackage({
      tenantId,
      packageId: _row.id
    })
    handleQuery()
    message.success('操作成功')
  } finally {
    loading.value = false
  }
}

const oprateDialogRef = ref()

// 添加
const add = () => {
  oprateDialogRef.value.open('add')
}

// 编辑
const edit = (_row) => {
  oprateDialogRef.value.open('edit', _row)
}

const ApprovalEditFormRef = ref()

// 配置
const configFn = (row, type) => {
  ApprovalEditFormRef.value.open({ ...row }, type)
}

const message = useMessage()

onMounted(() => {
  handleQuery()
})
</script>

import UmvContent from '@/components/UmvContent'
