import request from '@/config/axios'

export interface ChannelVO {
  id: number
  /**渠道编码 */
  code: string
  /**渠道编码 */
  config: string | object | any
  /**开启状态 */
  status: number
  /**备注 */
  remark: string
  /**渠道费率，单位：百分比 */
  feeRate: number
  /**商户编号 */
  merchantId: number
  /**应用编号 */
  appId: number
  /**创建时间 */
  createTime?: Date
}

// 查询列表支付渠道
export const getChannelPage = (params: PageParam) => {
  return request.get({ url: '/pay/channel/page', params })
}

// 查询详情支付渠道
export const getChannel = (merchantId: number, appId: string, code: string) => {
  const params = {
    merchantId: merchantId,
    appId: appId,
    code: code
  }
  return request.get({ url: '/pay/channel/get-channel', params: params })
}

// 新增支付渠道
export const createChannel = (data: ChannelVO) => {
  return request.post({ url: '/pay/channel/create', data })
}

// 修改支付渠道
export const updateChannel = (data: ChannelVO) => {
  return request.put({ url: '/pay/channel/update', data })
}

// 删除支付渠道
export const deleteChannel = (id: number) => {
  return request.delete({ url: '/pay/channel/delete?id=' + id })
}

// 导出支付渠道
export const exportChannel = (params) => {
  return request.download({ url: '/pay/channel/export-excel', params })
}

// 获取渠道支付类型接口
export const getChannelListChannelTypesByAppType = (appType) => {
  return request.get({ url: `/pay/channel/listChannelTypesByAppType/${appType}` })
}
