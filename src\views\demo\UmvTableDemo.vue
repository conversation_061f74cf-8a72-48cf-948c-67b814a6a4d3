<template>
  <div class="p-5 bg-el-bg-color rounded shadow-md min-h-[600px]">
    <h2 class="text-xl font-bold mb-2">UmvTable 示例</h2>
    <p class="mb-4 text-gray-600">UmvTable 支持两种列定义方式，但不支持混用</p>

    <h3 class="text-lg font-semibold mb-3">方式1: 使用 columns 属性定义列</h3>
    <umv-table
      :data="tableData"
      :columns="columns"
      :disabled-column-keys="['id']"
      @refresh="handleRefresh"
    >
      <!-- 工具栏插槽 (位于左侧) -->
      <template #tools>
        <el-button type="primary" :icon="Plus" size="small" @click="handleAdd">新增用户</el-button>
        <el-button type="success" :icon="Download" size="small">导出Excel</el-button>
      </template>

      <!-- 自定义插槽给columns中的列使用 -->
      <template #action="{ row }">
        <el-button type="primary" size="small" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="danger" size="small" link @click="handleDelete(row)">删除</el-button>
      </template>

      <!-- 自定义分页 -->
      <template #pagination>
        <div class="flex justify-end mt-4">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </template>
    </umv-table>

    <h3 class="text-lg font-semibold mt-8 mb-3">方式2: 使用 el-table-column 定义列</h3>
    <umv-table title="用户列表 (el-table-column)" :data="tableData" @refresh="handleRefresh">
      <!-- 工具栏插槽 (位于左侧) -->
      <template #tools>
        <el-button-group>
          <el-button type="danger" :icon="Delete" size="small">批量删除</el-button>
          <el-button type="warning" :icon="DocumentCopy" size="small">批量导出</el-button>
        </el-button-group>
      </template>

      <!-- 使用el-table-column方式定义列 -->
      <el-table-column prop="id" label="ID" width="80" align="center" />
      <el-table-column prop="name" label="姓名" min-width="120" align="center" />
      <el-table-column prop="age" label="年龄" width="80" align="center" />
      <el-table-column
        prop="address"
        label="地址"
        min-width="200"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column prop="email" label="邮箱" min-width="180" align="center" />

      <el-table-column label="状态" align="center" width="150" prop="status">
        <template #default="{ row }">
          <el-tag
            :type="row.status === '在职' ? 'success' : row.status === '离职' ? 'danger' : 'warning'"
          >
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="创建时间" width="180" align="center" />

      <el-table-column label="操作" align="center" fixed="right" width="180">
        <template #default="{ row }">
          <el-button type="primary" size="small" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" size="small" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <!-- 自定义分页 -->
      <template #pagination>
        <div class="flex justify-end mt-4">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </template>
    </umv-table>

    <h3 class="text-lg font-semibold mt-8 mb-3">方式3: 使用 renderTemplate 进行 TSX 渲染</h3>
    <umv-table
      title="用户列表 (TSX渲染)"
      :data="tableData"
      :columns="tsxColumns"
      @refresh="handleRefresh"
    >
      <!-- 工具栏插槽 (位于左侧) -->
      <template #tools>
        <el-button-group>
          <el-button type="primary" :icon="Plus" size="small">新增</el-button>
          <el-button type="info" :icon="Search" size="small">搜索</el-button>
        </el-button-group>
      </template>

      <!-- 自定义分页 -->
      <template #pagination>
        <div class="flex justify-end mt-4">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </template>
    </umv-table>

    <div class="mt-8 p-4 border border-dashed border-gray-300 rounded bg-gray-50">
      <h3 class="mt-0 mb-2.5 text-gray-600 font-semibold">说明</h3>
      <p class="mb-2">UmvTable 组件布局：</p>
      <ul class="pl-5 list-disc mb-3">
        <li>左侧：工具栏 (通过 tools 插槽自定义)</li>
        <li>中间：表格标题 (通过 title 属性设置)</li>
        <li>右侧：操作按钮组 (内置的刷新、全屏、列设置等按钮)</li>
      </ul>
      <p class="mb-2">UmvTable 组件支持三种方式定义表格内容：</p>
      <ul class="pl-5 list-disc">
        <li class="mb-1">方式1: 使用 columns 属性定义列 - 支持列设置和排序功能</li>
        <li class="mb-1">方式2: 使用 el-table-column 定义列 - 支持复杂的列配置和嵌套表头</li>
        <li class="mb-1">方式3: 使用 renderTemplate 属性 - 通过 TSX/JSX 自定义单元格渲染内容</li>
      </ul>
      <p class="mt-2">
        当组件检测到默认插槽中有 el-table-column 组件时，将会忽略 columns 属性定义的列。
        renderTemplate 和 slot 可以混合使用，但同一列不要同时定义两者，优先使用 renderTemplate。
      </p>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { ElMessage, ElTag, ElButton } from 'element-plus'
// import UmvTable from '../../components/UmvTable/index'
import type { TableColumn } from '../../components/UmvTable/src/types'
import { Plus, Download, Delete, DocumentCopy, Search, View, Edit } from '@element-plus/icons-vue'

defineOptions({
  name: 'UmvTableDemo'
})

interface User {
  id: number
  name: string
  age: number
  address: string
  email: string
  status: string
  createTime: string
}

// 模拟表格数据
const tableData = ref<User[]>([
  {
    id: 1,
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
    email: '<EMAIL>',
    status: '在职',
    createTime: '2023-01-15 09:30:00'
  },
  {
    id: 2,
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
    email: '<EMAIL>',
    status: '在职',
    createTime: '2023-02-21 14:15:00'
  },
  {
    id: 3,
    name: '王五',
    age: 28,
    address: '广州市天河区',
    email: '<EMAIL>',
    status: '离职',
    createTime: '2023-03-10 11:45:00'
  },
  {
    id: 4,
    name: '赵六',
    age: 35,
    address: '深圳市南山区',
    email: '<EMAIL>',
    status: '在职',
    createTime: '2023-04-05 16:20:00'
  },
  {
    id: 5,
    name: '钱七',
    age: 27,
    address: '杭州市西湖区',
    email: '<EMAIL>',
    status: '休假',
    createTime: '2023-05-18 10:00:00'
  }
])

// 使用columns属性定义的列
const columns = ref<TableColumn[]>([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  { prop: 'age', label: '年龄', width: '80px' },
  { prop: 'address', label: '地址', minWidth: '200px', showOverflowTooltip: true },
  { prop: 'email', label: '邮箱', minWidth: '180px' },
  { prop: 'status', label: '状态', width: '100px' },
  { prop: 'createTime', label: '创建时间', width: '180px' },
  { prop: 'action', label: '操作', slot: 'action', fixed: 'right', width: '180px' }
])

// 使用TSX渲染的列定义
const tsxColumns = ref<TableColumn[]>([
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'name', label: '姓名', minWidth: '120px' },
  {
    prop: 'age',
    label: '年龄',
    width: '100px',
    // 简单的TSX渲染
    renderTemplate: (scope) => (
      <div class="flex justify-center items-center">
        <span class={scope.row.age > 30 ? 'text-red-500' : 'text-green-500'}>
          {scope.row.age}岁
        </span>
      </div>
    )
  },
  { prop: 'address', label: '地址', minWidth: '200px', showOverflowTooltip: true },
  { prop: 'email', label: '邮箱', minWidth: '180px' },
  {
    prop: 'status',
    label: '状态',
    width: '120px',
    // 使用条件渲染
    renderTemplate: (scope) => {
      const status = scope.row.status
      const tagType = status === '在职' ? 'success' : status === '离职' ? 'danger' : 'warning'

      return (
        <div class="flex justify-center">
          <ElTag type={tagType} effect="dark">
            <i
              class={`mr-1 el-icon-${
                status === '在职' ? 'circle-check' : status === '离职' ? 'circle-close' : 'warning'
              }`}
            ></i>
            {status}
          </ElTag>
        </div>
      )
    }
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: '180px',
    renderTemplate: (scope) => {
      const date = new Date(scope.row.createTime)
      const formattedDate = date.toLocaleDateString()
      const formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })

      return (
        <div>
          <div class="text-gray-900">{formattedDate}</div>
          <div class="text-gray-500 text-xs">{formattedTime}</div>
        </div>
      )
    }
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: '200px',
    // 复杂的TSX渲染 - 操作按钮组
    renderTemplate: (scope) => (
      <div class="flex justify-center space-x-2">
        <ElButton type="primary" size="small" link onClick={() => handleView(scope.row)}>
          <el-icon class="mr-1">
            <View />
          </el-icon>
          查看
        </ElButton>
        <ElButton type="primary" size="small" link onClick={() => handleEdit(scope.row)}>
          <el-icon class="mr-1">
            <Edit />
          </el-icon>
          编辑
        </ElButton>
        <ElButton type="danger" size="small" link onClick={() => handleDelete(scope.row)}>
          <el-icon class="mr-1">
            <Delete />
          </el-icon>
          删除
        </ElButton>
      </div>
    )
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 处理新增
const handleAdd = () => {
  ElMessage.success('点击了新增按钮，这里可以打开新增表单')
}

// 处理分页变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadData()
}

// 加载数据
const loadData = () => {
  ElMessage.info(`加载第${currentPage.value}页，每页${pageSize.value}条数据`)
  // 实际项目中这里应该调用API获取数据
}

// 处理刷新
const handleRefresh = () => {
  ElMessage.success('正在刷新表格数据...')
  // 模拟加载数据
  setTimeout(() => {
    tableData.value = [
      ...tableData.value,
      {
        id: 6,
        name: '孙八',
        age: 32,
        address: '南京市鼓楼区',
        email: '<EMAIL>',
        status: '在职',
        createTime: '2023-06-25 13:40:00'
      }
    ]
    ElMessage.success('数据刷新成功')
  }, 1000)
}

// 处理查看操作
const handleView = (row: User) => {
  ElMessage.info(`正在查看: ${row.name}`)
}

// 处理编辑操作
const handleEdit = (row: User) => {
  ElMessage.info(`正在编辑: ${row.name}`)
}

// 处理删除操作
const handleDelete = (row: User) => {
  ElMessage.warning(`确定要删除 ${row.name} 吗?`)
}
</script>
