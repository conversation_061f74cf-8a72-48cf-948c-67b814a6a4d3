/**资源管理 */
export default {
  resApp: {
    name: 'Number',
    codePlaceholder: 'Please enter the role name',
    applicationReasonPlaceholder: 'Please enter the application reason',
    scene: 'Scene',
    permissionTypes: 'Permission Points',
    exposeNo: 'Operation',
    resourceOwnerId: 'Resource Owner ID',
    resourceOwnerName: 'Resource Owner Name',
    exposeRoleName: 'Role',
    application: 'Application',
    appOp: 'Application Operation',
    defaultResourceHolder: 'Default Permission Holder',
    defaultResourceHolderPlaceholder: 'Please select the default permission holder',
    applicationReason: 'Application Reason',
    applicationSuccess: 'Application Successful'
  },
  approval: {
    apply: 'I have applied for',
    approval: 'I have approved',
    name: 'Number',
    codePlaceholder: 'Please enter the role name',
    applicationReasonPlaceholder: 'Please enter the application reason',
    scene: 'Scenario',
    permissionTypes: 'Permission Types',
    exposeNo: 'Number',
    roleName: 'Role Name',
    applicantName: 'Applicant Name',
    authorizerName: 'Authorizer Name',
    applicationNo: 'Application Number',
    approvalOpinion: 'Approval Opinion',
    authStatusName: 'Status',
    applicationReason: 'Application Reason',
    approvalOpinionPlaceholder: 'Please enter the approval opinion',
    reasonPlaceholder: 'Please enter the withdrawal reason',
    resultPlaceholder: 'Please select whether to pass',
    authStatus1: 'Approval',
    authStatus2: 'Withdrawal',
    result: 'Pass or Not',
    reason: 'Withdrawal Reason',
    withdraw: 'Withdraw'
  }
}
