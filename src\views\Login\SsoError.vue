<template>
  <SsoSlot>
    <div class="mt-2">
      <h2 class="flex justify-center text-[var(--el-color-danger)] text-2xl">{{
        t('sys.errorCode.code401')
      }}</h2>
      <h2 class="flex justify-center text-[var(--el-color-danger)] text-xl" v-if="isNoClient">{{
        t('store.user.noClient')
      }}</h2>
    </div>
  </SsoSlot>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import SsoSlot from './SsoSlot.vue'
const route = useRoute()
const { t } = useI18n()
const isNoClient = computed(() => {
  return route?.query?.noClient
})

import { useUserStoreWithOut } from '@/store/modules/user'
const userStore = useUserStoreWithOut()
watch(
  () => isNoClient.value,
  (val) => {
    if (val === '1') {
      userStore.loginOut(t('store.user.noClientLoginOut'))
    } else {
      userStore.loginOut(t('sys.errorCode.code401') + t('sys.service.pleaseRelogin'))
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scope></style>
