import request from '@/config/axios'

export interface TenantVO {
  id: number
  name: string
  contactName: string
  contactMobile: string
  status: number
  domain: string
  packageId: number
  username: string
  password: string
  expireTime: Date
  accountCount: number
  createTime: Date
  applicationIds?: string[]
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantApplicationVO {
  createTime: number | string
  description: string | null
  id: number | string
  name: string
  ver: string
  code?: string
  disabled?: boolean
  type?: number
}

// 查询租户列表
export const getTenantPage = (params: TenantPageReqVO) => {
  return request.get({
    url: '/system/tenant/page',
    params
  })
}

export const getTenantPageProxyChange = async (data) => {
  return request.post({
    url: '/system/tenant/page',
    data
  })
}

export const getTenantPageProxy = async (params: PageParam, tenantIdList) => {
  if (tenantIdList && tenantIdList.length > 0) {
    let paramsStr = ''
    for (const key in params) {
      if (params[key] !== undefined) {
        paramsStr += `&${key}=${params[key]}`
      }
    }
    const url = '/system/tenant/page?tenantIds=' + tenantIdList.join('&tenantIds=') + paramsStr
    return await request.post({ url: url, data: tenantIdList })
  } else {
    return await request.get({ url: '/system/tenant/page', params })
  }
}

// 查询租户详情
export const getTenant = (id: number) => {
  return request.get({ url: '/system/tenant/get?id=' + id })
}

// 新增租户
export const createTenant = (data: TenantVO) => {
  // return request.post({ url: '/system/tenant/create', data })
  return request.post({
    // url: 'http://10.165.221.66:48080/admin-api/system/tenant/create',
    url: '/system/tenant/create',
    data
  })
}

// 新增租户-租户改造
export const createTenantReno = (data: TenantVO) => {
  // return request.post({ url: '/system/tenant/create', data })
  return request.post({
    // url: 'http://10.165.221.66:48080/admin-api/system/tenant/create',
    url: '/system/tenant/create-tenant',
    data
  })
}

// 获取应用列表
export const getTenantAppList = (params?: TenantPageReqVO) => {
  return request.get({
    url: '/system/tenant-application/page',
    // url: 'http://10.165.221.66:48080/admin-api/system/tenant-application/page',
    params
  })
}

// 修改租户
export const updateTenant = (data: TenantVO) => {
  return request.put({ url: '/system/tenant/update', data })
}

// 删除租户
export const deleteTenant = (id: number) => {
  return request.delete({ url: '/system/tenant/delete?id=' + id })
}

// 导出租户
export const exportTenant = (params: TenantExportReqVO) => {
  return request.download({ url: '/system/tenant/export-excel', params })
}

// 查询客户列表

export const getTenantList = (params?: TenantPageReqVO) => {
  return request.get({
    url: '/system/tenant/list-all-simple',
    params
  })
}

// 查询客户列表(树)
export const getTenantListTree = (params?: TenantPageReqVO) => {
  return request.get({
    url: '/system/tenant/list-all-simple-trees',
    params
  })
}

// 查询客户列表
export const getRoleCustomerScopes = (id: string | number) => {
  return request.get({
    url: `/system/permission/getRoleCustomerScopes/${id}`
  })
}

// 获取租户树
export const getTenantTree = (params?) => {
  return request.get({
    url: '/system/tenant/tenant-tree',
    params
  })
}

export const getNotEnabledCustomer = (params?) => {
  return request.get({
    url: `/app-api/customer/rpc-api/customer/getNotEnabledCustomer`,
    params
  })
}
