<template>
  <!-- 搜索工作栏 -->
  <UmvContent>
    <UmvQuery
      v-model="queryParams"
      ref="queryFormRef"
      label-width="160px"
      :opts="queryOpts"
      :col-length-map="{
        2400: 6, // 屏幕宽度 >= 2560px 时使用 8 列
        1900: 5, // 屏幕宽度 >= 1920px 时使用 8 列
        1600: 4, // 屏幕宽度 >= 1600px 且 < 1920px 时使用 6 列
        1280: 4, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
        1100: 3, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
        1000: 2, // 屏幕宽度 >= 1000px 且 < 1280px 时使用 3 列
        768: 2, // 屏幕宽度 >= 768px 且 < 1000px 时使用 2 列
        0: 1 // 屏幕宽度 < 768px 时使用 1 列
      }"
      @check="handleQuery"
      @reset="resetQuery"
    />

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="getList">
      <template #tools>
        <el-button :loading="exportLoading" plain type="success" @click="handleExport">
          <icon-ep-download class="mr-5px" style="font-size: 12px" />
          导出
        </el-button>
      </template>
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>
  </UmvContent>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'Statement'
})

import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as RefundApi from '@/api/pay/refund'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { UmvContent } from '@/components/UmvContent'
import download from '@/utils/download'

const { t } = useI18n() // 国际化
const message = useMessage()

const queryParams = ref({
  channelType: undefined,
  channelOrderNo: undefined,
  merchantOutTradeNo: undefined,
  merchantOutRefundNo: undefined,
  tradeType: undefined,
  billDate: undefined,
  businessOrderId: undefined,
  businessRefundId: undefined,
  pageNo: 1,
  pageSize: 10
})
// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  channelType: {
    label: '渠道类型',
    defaultVal: undefined,
    controlRender: () => (
      <el-select v-model={queryParams.value.channelType} placeholder="请选择渠道类型" clearable>
        {getStrDictOptions(DICT_TYPE.PAY_APP_TYPE).map((dict) => (
          <el-option key={dict.value} label={dict.label} value={dict.value} />
        ))}
      </el-select>
    )
  },
  channelOrderNo: {
    label: '渠道订单编号',
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.channelOrderNo}
        placeholder="请输入渠道订单编号"
        clearable
      />
    )
  },
  merchantOutTradeNo: {
    label: '商户交易订单编号',
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.merchantOutTradeNo}
        placeholder="请输入商户交易订单编号"
        clearable
      />
    )
  },
  merchantOutRefundNo: {
    label: '商户退款订单编号',
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.merchantOutRefundNo}
        placeholder="请输入商户退款订单编号"
        clearable
      />
    )
  },
  tradeType: {
    label: '交易类型',
    defaultVal: undefined,
    controlRender: () => (
      <el-select v-model={queryParams.value.tradeType} placeholder="请选择交易类型" clearable>
        <el-option label="付款" value={1} />
        <el-option label="退款" value={2} />
      </el-select>
    )
  },
  billDate: {
    label: '账单日期',
    defaultVal: undefined,
    controlRender: () => (
      <el-date-picker
        v-model={queryParams.value.billDate}
        type="date"
        placeholder="请选择账单日期"
        value-format="YYYY-MM-DD"
      />
    )
  },
  businessOrderId: {
    label: '业务交易订单编号',
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.businessOrderId}
        placeholder="请输入业务交易订单编号"
        clearable
      />
    )
  },
  businessRefundId: {
    label: '业务退款订单编号',
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.businessRefundId}
        placeholder="请输入业务退款订单编号"
        clearable
      />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'channelTypeName', label: '渠道类型', align: 'center', width: '140' },
  { prop: 'billDate', label: '账单日期', align: 'center', width: '120' },
  { prop: 'channelOrderNo', label: '渠道订单编号', align: 'center', width: '300' },
  { prop: 'merchantOutTradeNo', label: '商户交易订单编号', align: 'center', width: '200' },
  { prop: 'merchantOutRefundNo', label: '商户退款订单编号', align: 'center', width: '200' },
  { prop: 'tradeTypeName', label: '交易类型', align: 'center', width: '140' },
  { prop: 'tradeAmount', label: '交易金额（元）', align: 'center', width: '140' },
  {
    prop: 'tradeTime',
    label: '交易时间',
    align: 'center',
    width: '200',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.tradeTime)}</span>
    )
  },
  { prop: 'mchId', label: '商户号（mchId）', align: 'center', width: '160' },
  { prop: 'appId', label: 'appId', align: 'center', width: '160' },
  { prop: 'businessOrderId', label: '业务交易订单编号', align: 'center', width: '200' },
  { prop: 'businessRefundId', label: '业务退款订单编号', align: 'center', width: '200' },
  { prop: 'businessModule', label: '业务模块', align: 'center', width: '120' },
  { prop: 'businessNo', label: '业务交易流水号', align: 'center', width: '300' },
  { prop: 'payOrderId', label: '支付订单编号', align: 'center', width: '180' },
  { prop: 'payRefundId', label: '退款订单编号', align: 'center', width: '200' }
])

const loading = ref(false)
const list = ref([])
const total = ref(0)

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RefundApi.tradeBillDetailPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

const exportLoading = ref(false)

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    if (!queryParams.value.billDate) {
      message.error('请选择账单日期')
      exportLoading.value = false
      return
    }
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RefundApi.tradeBillDetailExportExcel(queryParams.value)
    download.excel(data.data, `交易对账单（${queryParams.value.billDate}）.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const queryFormRef = ref()

/** 重置按钮操作 */
const resetQuery = () => {
  // queryFormRef.value.resetFields()
  // handleQuery()
}

onMounted(() => {
  handleQuery()
})
</script>
