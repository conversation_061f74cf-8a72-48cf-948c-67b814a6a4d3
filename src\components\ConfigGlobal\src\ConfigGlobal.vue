<!--
 * @Author: Ho<PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJack
 * @LastEditTime: 2023-07-06 09:58:34
 * @Description: 
-->
<script setup lang="ts">
defineOptions({
  name: 'ConfigGlobal'
})

import { propTypes } from '@/utils/propTypes'
import { useLocaleStore } from '@/store/modules/locale'
import { useAppStore } from '@/store/modules/app'
import { setCssVar } from '@/utils'
import { useDesign } from '@/hooks/web/useDesign'
import { ElementPlusSize } from '@/types/elementPlus'
import { useWindowSize } from '@vueuse/core'

const { variables } = useDesign()

const appStore = useAppStore()

const props = defineProps({
  size: propTypes.oneOf<ElementPlusSize>(['default', 'small', 'large']).def('default')
})

provide('configGlobal', props)

// 初始化所有主题色
onMounted(() => {
  appStore.setCssVarTheme()
})

const { width } = useWindowSize()

// 监听窗口变化
watch(
  () => width.value,
  (width: number) => {
    if (width < 768) {
      !appStore.getMobile ? appStore.setMobile(true) : undefined
      setCssVar('--left-menu-min-width', '0')
      appStore.setCollapse(true)
      appStore.getLayout !== 'classic' ? appStore.setLayout('classic') : undefined
    } else {
      appStore.getMobile ? appStore.setMobile(false) : undefined
      setCssVar('--left-menu-min-width', '64px')
      appStore.setLayout('topLeft') //默认顶部左侧菜单
    }
  },
  {
    immediate: true
  }
)

// 多语言相关
const localeStore = useLocaleStore()

const currentLocale = computed(() => localeStore.currentLocale)
</script>

<template>
  <ElConfigProvider
    :locale="currentLocale.elLocale"
    :namespace="variables.elNamespace"
    :size="size"
  >
    <slot></slot>
  </ElConfigProvider>
</template>
