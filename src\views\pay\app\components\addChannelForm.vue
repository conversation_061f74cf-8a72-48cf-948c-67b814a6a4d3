<template>
  <div>
    <el-dialog :title="t('pay.app.addChannel')" v-model="dialogVisible" width="800px">
      123
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'AddChannelForm'
})

let dialogVisible = ref<boolean>(false)
let formLoading = ref<boolean>(false)
const { t } = useI18n() // 国际化

const open = () => {
  dialogVisible.value = true
}

defineExpose({
  open
})
</script>
