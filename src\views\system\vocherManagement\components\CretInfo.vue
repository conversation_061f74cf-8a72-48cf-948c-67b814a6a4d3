<template>
  <div class="cret-info-wrap">
    <el-tabs v-model="activeTab" type="card" class="tabs-wrap">
      <el-tab-pane label="基本信息" name="base-info" />
      <!-- <el-tab-pane label="详细信息" name="detail-info" /> -->
    </el-tabs>

    <section v-show="activeTab == 'base-info'">
      <section class="base-info-item" v-for="(item, index) in baseInfoList" :key="index">
        <div class="title">{{ item.title }}</div>
        <el-row align="middle" class="item-row" v-for="(listItem, i) in item.list" :key="i">
          <div class="label">{{ listItem.label }}</div>
          <div class="value">{{ listItem.value }}</div>
        </el-row>
      </section>
    </section>
    <section v-show="activeTab == 'detail-info'">2</section>
  </div>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime.js'
import * as x509 from '@peculiar/x509'
const props = defineProps({
  cretStr: {
    type: String,
    default: ''
  }
})
const { cretStr } = toRefs(props)

const activeTab = ref('base-info')

onMounted(() => {
  console.log('证书 str: ', cretStr.value)
  cretStrParse()
})

// 基本信息
const baseInfoList = ref([
  // {
  //   title: '颁发对象',
  //   list: [
  //     {
  //       label: '公用名(CN)',
  //       value: 'xxxx'
  //     },
  //     {
  //       label: '组织(O)',
  //       value: 'xxxx'
  //     },
  //     {
  //       label: '组织单位(OU)',
  //       value: 'xxxx'
  //     }
  //   ]
  // },
  {
    title: '颁发者',
    list: [
      {
        label: '公用名(CN)',
        value: ''
      },
      {
        label: '组织(O)',
        value: ''
      },
      {
        label: '组织单位(OU)',
        value: ''
      }
    ]
  },
  {
    title: '有效期',
    list: [
      {
        label: '颁发日期',
        value: ''
      },
      {
        label: '截止日期',
        value: ''
      }
    ]
  },
  {
    title: '序列号',
    list: [
      {
        label: '序列号',
        value: ''
      }
    ]
  }
])

// 解密证书

function pemToDer(pem) {
  const pemHeader = '-----BEGIN CERTIFICATE-----'
  const pemFooter = '-----END CERTIFICATE-----'
  const pemContents = pem.replace(pemHeader, '').replace(pemFooter, '').replace(/\s/g, '')
  return Uint8Array.from(atob(pemContents), (c) => c.charCodeAt(0))
}

const cretStrParse = () => {
  try {
    const parseResult = new x509.X509Certificate(pemToDer(cretStr.value))
    console.log('解析证书：', parseResult)
    let issuerStr = parseResult.issuer || '' // 颁发者信息
    console.log('提取issuerStr', issuerStr)

    let issuerArray = issuerStr.split(',') || []
    console.log('提取 issuerArray', issuerArray)
    const issuerObj = {}
    issuerArray.forEach((item) => {
      let itemStrToArray = item.split('=') || [] // =号分割数组，注意空格问题
      let key = itemStrToArray[0] ? itemStrToArray[0].replace(/\s/g, '') : '' // 去掉空格
      let value = itemStrToArray[1]
      key ? (issuerObj[key] = value) : null
    })
    console.log('颁发者 issuerObj === ', issuerObj)

    // 颁发者信息
    baseInfoList.value[0].list[0].value = issuerObj['CN'] || ''
    baseInfoList.value[0].list[1].value = issuerObj['O'] || ''
    baseInfoList.value[0].list[2].value = issuerObj['OU'] || ''

    // 有效期
    let startDate = parseResult.notBefore
    let endDate = parseResult.notAfter
    baseInfoList.value[1].list[0].value = startDate
      ? formatDate(startDate, 'YYYY/MM/DD HH:mm:ss')
      : ''
    baseInfoList.value[1].list[1].value = startDate
      ? formatDate(endDate, 'YYYY/MM/DD  HH:mm:ss')
      : ''

    // 序列号
    let serialNumber = parseResult.serialNumber
    baseInfoList.value[2].list[0].value = serialNumber
  } catch (error) {
    console.error('证书解密失败：', error)
    ElMessage.error('证书解密失败：' + error)
  }
}
</script>

<style scoped lang="scss">
.cret-info-wrap {
  .tabs-wrap {
    :deep(.el-tabs__nav) {
      .el-tabs__item.is-active {
        color: #333333;
        font-weight: 600;
      }
      .el-tabs__item:hover {
        color: #666666;
      }
    }
  }

  .base-info-item {
    margin-bottom: 20px;
    .title {
      color: #333333;
      margin-bottom: 10px;
    }
    .item-row {
      display: flex;
      padding: 0 20px;
      .label {
        display: inline-block;
        width: 130px;
        // background: pink;
      }
      .value {
        flex: 1;
        // background: gray;
      }
    }
  }
}
</style>
