---
description: 
globs: 
alwaysApply: false
---


# 基础规范

- 请阅读'src\components\UmvQuery'路径,UmvQuery这个组件及其规范,使用最佳实现方案进行改造
- 请阅读'src\components\UmvTable'路径,UmvTable这个组件及其规范,使用最佳实现方案进行改造
- 请阅读@'src\components\UmvQuery\README.md'文档
- 请阅读@'src\components\UmvTable\README.md'文档
- 请保留原来代码逻辑的情况下进行改造
- 改造使用UmvQuery和UmvTable组件,请根据这2个组件的要求的属性来,不要臆造,例如UmvQuery的README文档的Events事件是check触发查询而不是search或者query,请不要臆造!

## 代码生成规则

### UmvContent组件
- 请在修改的组件导入"import UmvContent from '@/components/UmvContent' "
- 路径在src\components\UmvContent
- 将组件内的ContentWrap替换成UmvContent
- UmvContent组件是根组件其他组件都被这个组件包裹,特别是那种导入的组件

### UmvTable组件
- 请在修改的组件导入"import UmvTable from '@/components/UmvTable'"
- 请阅读'src/components/UmvTable/README.md'文件内容
- 根据README.md中的示例代码和API文档生成对应的表格组件代码
- 生成的代码必须符合README.md中描述的功能和规范
- el-table请使用UmvTable进行改造
- el-table请使用UmvTable的 columns 属性 + renderTemplate进行改造
- 优先使用README.md中推荐的最佳实践方式（columns + renderTemplate）
- UmvTable的最佳实践,将el-table-column改成columns参数传入UmvTable
- UmvTable的 renderTemplate上的v-hasPermi指令应该使用checkPermi函数而不是只指令,如果是html上的不改变,只修改tsx上面的指令
- UmvTable的 renderTemplate上的dateFormatter,应该这样使用'<span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>'
- 如果有列表查询方法,请在UmvTable添加refresh函数,例如: @refresh="getList"
- 分页组件放置在UmvTable的pagination插槽中
- 如果表格列有selection功能,请使用使用 el-table-column 定义列进行改造即可,因为mvTable 不支持 selection 功能
### UmvQuery组件
- 请在修改的组件导入"import { UmvQuery, type QueryOption, type QueryForm } from '@/components/UmvQuery'
   import type { TableColumn } from '@/components/UmvTable/src/types' "
- 请阅读'src/components/UmvQuery/README.md'文件内容
- 根据README.md中的示例代码和API文档生成对应的查询组件代码
- 生成的代码必须符合README.md中描述的功能和规范
- 优先使用README.md中推荐的最佳实践方式（v-model + opts）
- 确保生成的代码包含所有必要的类型定义和接口
- 使用controlRender直接渲染控件,用tsx进行渲染,controlRender上的表单项的 v-model 都使用了 form.字段名 的形式
- UmvQuery组件上的自定义表单控件请使用controlRender的tsx进行渲染,也就是对改造组件中使用自定义组件的请使用controlRender的tsx进行渲染
- 请确保queryOpts符合UmvQuery的opts属性要求,opts的类型是`Record<string, QueryOption>`
- 保留原来el-form上的label-width属性的值,如果有
- QueryOption上defaultVal值请和 v-model 绑定表单数据的默认值
- 保留原来el-form上的规则,如果有
- 如果el-form-item 有v-if指令控制搜索条件,或者其他方式控制搜索条件展示的请使用UmvQuery属性visible
- 搜索条件的el-form请使用UmvQuery组件
- 搜索条件el-form传入的model响应式数据使用ref声明,尽量不要使用reactive
- 尽量保留el-form上的属性传递到UmvQuery中
- UmvQuery的最佳实践,使用 v-model 绑定数据
- UmvQuery的最佳实践,将原来的el-form-item改造成opts参数传入UmvQuery
- UmvQuery的最佳实践,尽量不要使用插槽,使用opts参数传入
- UmvQuery上的 @reset重置按钮方法,会默认查询列表,如果没有特殊操作,不要再次传入调用查询列表的方法
- el-form中除了查询和重置按钮请放置在UmvTable的tools插槽中,按钮使用size="small"

## 最佳实践

- 将组件lang='ts' 改成lang='tsx'
- 每个组件都必须要有defineOptions,根据路由名称或者组件名称或者<script lang="tsx" name='xx'>这个script标签的name值设置defineOptions的name,并删除script标签的name值,将<script lang="tsx" name='xx'>删掉name,为<script lang="tsx">


