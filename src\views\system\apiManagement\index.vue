<template>
  <div class="api-management-wrap">
    <!-- 左侧树形结构 -->
    <section class="left-wrap" v-loading="treeLoading">
      <el-input v-model="filterText" placeholder="请输入" maxlength="40" clearable />
      <div class="tree-block">
        <el-tree
          ref="treeRef"
          :data="treeDataList"
          :props="treeProps"
          :filter-node-method="filterNode"
          highlight-current
          check-strictly
          @node-click="handleTreeClick"
        />
      </div>
    </section>

    <el-divider direction="vertical" style="height: 100%" />

    <!-- 右侧内容 -->
    <section class="right-wrap">
      <el-tabs v-model="activeTab" @tab-click="handleClickTab">
        <el-tab-pane v-for="(item, i) in tabList" :key="i" :label="item.label" :name="item.name" />
      </el-tabs>
      <div class="content-wrap">
        <detail ref="detailRef" v-if="activeTab === tabList[0].name" :detailInfo="'987654'" />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import detail from './components/detail.vue'
// import edit from './components/edit.vue'
// import run from './components/run.vue'
import { onMounted } from 'vue'

const treeLoading = ref(false)
const filterText = ref('')
const treeRef = ref()
const treeDataList = ref<any>([])
const treeProps = ref({
  children: 'children',
  label: 'label'
})

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

onMounted(() => {
  mockTree()
})

const filterNode = (value, data) => {
  console.log('value', value)
  console.log(data)
  if (!value) return true
  return data.label.includes(value)
}

const detailRef = ref()
const handleTreeClick = (treeItem) => {
  console.log('单独点击节点')
  console.log(treeItem)
  console.log(detailRef.value)
  if (!treeItem?.children?.length) {
    ElMessage.success('刷新')
    detailRef.value.mockData()
  }
}
const mockTree = () => {
  let list = [
    {
      label: '登录',
      iconShow: false,
      children: [
        {
          label: '登录1-1',
          iconShow: false,
          children: [
            {
              label: '登录1-1-1',
              iconShow: false
            }
          ]
        },
        {
          label: '登录1-2',
          iconShow: false,
          children: []
        }
      ]
    }
  ]
  treeLoading.value = true
  setTimeout(() => {
    treeDataList.value = [...list]
    treeLoading.value = false
  }, 1000)
}

const tabList = [
  {
    label: 'api文档',
    name: 'detail'
  }
  // {
  //   label: '修改文档',
  //   name: 'edit'
  // },
  // {
  //   label: '运行',
  //   name: 'run'
  // },
  // {
  //   label: '其他',
  //   name: 'other'
  // }
]
const activeTab = ref(tabList[0].name)
const handleClickTab = (tabItem, event) => {
  // console.log('tabItem === ', tabItem)
  // console.log('event', event)
  // console.log(tabItem.props.name)
  let name = tabItem?.props?.name || ''
  if (activeTab.value === name) return
  activeTab.value = name
  console.log('执行')
}
</script>

<style scoped>
.api-management-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .left-wrap,
  .right-wrap {
    height: 100%;
    overflow: auto;
  }
  .left-wrap {
    width: 250px;
    display: flex;
    flex-direction: column;
    .tree-block {
      margin-top: 15px;
      flex: 1;
      overflow: auto;
      .tree-item {
        width: 100%;
        display: flex;
        .tree-label {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .tree-icon {
          margin-left: 10px;
        }
      }
    }
  }
  .right-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .content-wrap {
      flex: 1;
      overflow: auto;
    }
  }
}
</style>
