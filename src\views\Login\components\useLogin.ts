import { Ref } from 'vue'

/**
 * 登录表单类型常量
 * @description 用于标识当前显示的登录表单类型
 * @property {number} LOGIN - 普通登录表单，值为1
 * @property {number} REGISTER - 注册表单，值为4
 * @property {number} FORGET_PASSWORD - 忘记密码表单，值为2
 * @property {number} RESET_PASSWORD - 重置密码表单，值为3
 * @property {number} MOBILE - 手机登录表单，值为5
 * @property {number} QR_CODE - 二维码登录表单，值为6
 * @property {number} SSO - 单点登录表单，值为7
 * @property {number} SIGN_UP - 注册表单，值为8
 */
export const LOGIN_TYPE = {
  /** 普通登录表单，值为1*/
  LOGIN: 1,
  /**忘记密码表单，值为2 */
  FORGET_PASSWORD: 2,
  /** 重置密码表单，值为3*/
  RESET_PASSWORD: 3,
  /** 注册表单，值为4 */
  REGISTER: 4,
  /** 手机登录表单，值为5 */
  MOBILE: 5,
  /** 二维码登录表单，值为6 */
  QR_CODE: 6,
  /** 单点登录表单，值为7 */
  SSO: 7,
  /** 注册表单，值为8 */
  SIGN_UP: 8
} as const

// Token 相关常量
export const AccessTokenKey = 'ACCESS_TOKEN'
export const RefreshTokenKey = 'REFRESH_TOKEN'

export type tabNameType = 'verificationCodeLogin' | 'pwdLogin' | 'ramLogin'
export type loginWayType = 'mail' | 'mailPwd' | 'mobile' | 'mobilePwd' | 'ram'

const currentState = ref<number>(LOGIN_TYPE.LOGIN)

export function useLoginState() {
  function setLoginState(state: number) {
    currentState.value = state
  }
  const getLoginState = computed(() => currentState.value)

  function handleBackLogin() {
    setLoginState(LOGIN_TYPE.LOGIN)
  }

  return {
    setLoginState,
    getLoginState,
    handleBackLogin
  }
}

export function useFormValid<T extends Object = any>(formRef: Ref<any>) {
  async function validForm() {
    const form = unref(formRef)
    if (!form) return
    const data = await form.validate()
    return data as T
  }

  return {
    validForm
  }
}

export function useFormValidField<T extends Object = any>(formRef: Ref<any>, field: string) {
  async function validFormField() {
    const form = unref(formRef)
    if (!form) return
    const data = await form.validateField(field)
    return data as T
  }

  return {
    validFormField
  }
}

//登录输入框 校验hook
const { t, ifEn } = useI18n()
import { allPattern } from '@/hooks/web/useValidator'

export const useValidator = (
  loginMethodsSet: Set<any>,
  tabName: Ref<tabNameType>,
  loginWay: Ref<loginWayType>
) => {
  const LoginRules = computed(() => {
    console.log(loginMethodsSet, tabName, loginWay)

    return {
      tenantId: [
        {
          required: loginWay.value === 'ram',
          trigger: ['blur', 'change'],
          message: t('sys.login.inputMainAccount')
        }
      ],
      captchaVerification: [
        {
          required: loginWay.value === 'ram' || tabName.value === 'pwdLogin',

          trigger: ['blur', 'change'],
          message: t('sys.login.smsPlaceholder')
        }
      ],
      account: [
        {
          required: tabName.value !== 'ramLogin',
          trigger: ['blur', 'change'],
          validator: validAccount
          // message: '请输入手机或者邮箱'
        }
      ],
      username: [
        {
          required: loginWay.value === 'ram',
          trigger: ['blur', 'change'],
          message: t('sys.login.accountPlaceholder')
        }
      ],
      password: [
        {
          required: loginWay.value === 'ram' || tabName.value === 'pwdLogin',
          trigger: ['blur', 'change'],
          message: t('sys.login.passwordPlaceholder')
        }
      ],

      code: [
        {
          required: tabName.value == 'verificationCodeLogin',
          trigger: ['blur', 'change'],
          message: t('sys.login.smsPlaceholder')
        }
      ]
    }
  })
  const validPassword = (_rule, value, callback) => {
    const reg =
      !/(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+-=_\[\]\{\}\|,:;'"<>?\/.~!@#$%^&*()]{6,18}$/.test(
        value
      )
    if (!value) {
      callback(t('sys.login.passwordPlaceholder'))
    } else if (reg) {
      callback(new Error(t('login.pwdCheck')))
    } else {
      callback()
    }
  }

  const validMail = (_rule, value, callback) => {
    const reg = allPattern.emailPattern.test(value)
    if (!value) {
      callback(t('sys.login.inputMail'))
    } else if (!reg) {
      callback(new Error(t('sys.login.inputRightMail')))
    } else {
      callback()
    }
  }
  /**
   * 校验账号是否合法 (手机号码或者邮箱)
   *
   * @param _rule 校验规则
   * @param value 待校验的值
   * @param callback 回调函数，用于返回校验结果
   */
  const validAccount = (_rule, value, callback) => {
    const regEmail = allPattern.emailPattern.test(value)
    const regPhone = allPattern.phonePattern.test(value)
    if (!value) {
      if (tabName.value === 'pwdLogin') {
        if (
          loginMethodsSet.has(LoginMethodsEnum.MOBILEPWD) &&
          loginMethodsSet.has(LoginMethodsEnum.MAILPWD)
        )
          callback(t('sys.login.inputMobileOrMail'))

        if (loginMethodsSet.has(LoginMethodsEnum.MOBILEPWD)) callback(t('sys.login.inputMobile'))
        if (loginMethodsSet.has(LoginMethodsEnum.MAILPWD)) callback(t('sys.login.inputMail'))
      }

      if (tabName.value === 'verificationCodeLogin') {
        if (
          loginMethodsSet.has(LoginMethodsEnum.MAIL) &&
          loginMethodsSet.has(LoginMethodsEnum.MOBILE)
        )
          callback(t('sys.login.inputMobileOrMail'))

        if (loginMethodsSet.has(LoginMethodsEnum.MOBILE)) callback(t('sys.login.inputMobile'))
        if (loginMethodsSet.has(LoginMethodsEnum.MAIL)) callback(t('sys.login.inputMail'))
      }
    } else if (tabName.value === 'pwdLogin') {
      if (
        loginMethodsSet.has(LoginMethodsEnum.MOBILEPWD) &&
        loginMethodsSet.has(LoginMethodsEnum.MAILPWD)
      ) {
        if (!regEmail && !regPhone) {
          callback(t('sys.login.inputMobileOrMailRight'))
        } else {
          callback()
        }
      }

      if (loginMethodsSet.has(LoginMethodsEnum.MOBILEPWD) && !regPhone)
        callback(t('sys.login.inputMobile'))
      if (loginMethodsSet.has(LoginMethodsEnum.MAILPWD) && !regEmail)
        callback(t('sys.login.inputMail'))
    } else if (tabName.value === 'verificationCodeLogin') {
      if (
        loginMethodsSet.has(LoginMethodsEnum.MOBILE) &&
        loginMethodsSet.has(LoginMethodsEnum.MAIL)
      ) {
        if (!regEmail && !regPhone) {
          callback(t('sys.login.inputMobileOrMailRight'))
        } else {
          callback()
        }
      }

      if (loginMethodsSet.has(LoginMethodsEnum.MOBILE) && !regPhone)
        callback(t('sys.login.inputMobile'))
      if (loginMethodsSet.has(LoginMethodsEnum.MAIL) && !regEmail)
        callback(t('sys.login.inputMail'))
    } else {
      callback()
    }
  }

  return {
    validPassword,
    validMail,
    validAccount,
    LoginRules
  }
}
/**
 * 判断输入字符串是否为邮箱或手机号
 *
 * @param validStr 输入字符串
 * @returns 如果为邮箱，则返回字符串'email'，如果为手机号，则返回字符串'mobile'，否则返回空字符串''
 */
export const useMoblieOrEmail = (validStr) => {
  if (allPattern.emailPattern.test(validStr)) {
    return 'email'
  } else if (allPattern.phonePattern.test(validStr)) {
    return 'mobile'
  } else {
    return ''
  }
}

//登录方式枚举
export enum LoginMethodsEnum {
  /**
   * 账号密码登录
   */
  ACCOUNTPWD = 10, //账号密码登录
  /**
   * 邮箱验证码登录
   */
  MAIL = 20, //邮箱验证码登录
  /**
   * 邮箱密码登录
   */
  MAILPWD = 30, //邮箱密码登录
  /**
   * 手机号密码登录
   */
  MOBILEPWD = 40, //手机号密码登录
  /**
   * 手机验证码
   */
  MOBILE = 50, //手机验证码
  /**
   * google单点登录
   */
  OA2GOOGLE = 60 //
}

//获取登录方式
import { getLoginMethods } from '@/api/login'
/**
 * 获取登录方式的方法集合
 *
 * @returns 返回一个对象，包含以下属性：
 * - loginMethodsSet：一个 Set 集合，存储登录方式的 code
 * - ifSocialLogin：一个布尔值，表示是否有社交平台登录方式
 * - getLoginMethodsAPi：一个异步函数，用于获取登录方式并更新 loginMethodsSet 和 ifSocialLogin
 */
export const useLoginMethods = () => {
  const loginMethodsSet = ref<Set<any>>(new Set())
  const ifSocialLogin = ref(false)
  const getLoginMethodsAPi = async () => {
    const res = await getLoginMethods()
    res.forEach((item) => {
      loginMethodsSet.value.add(item.code)
      //code为60时，是社交平台登录
      if (item.code === 60) {
        ifSocialLogin.value = true
        item.children.forEach((n) => {
          loginMethodsSet.value.add(n.code)
        })
      }
    })
  }
  return {
    loginMethodsSet,
    ifSocialLogin,
    getLoginMethodsAPi
  }
}
