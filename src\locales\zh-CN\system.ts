/**系统管理 */
export default {
  /**用户管理 */
  user: {
    username: '用户名称',
    usernamePlaceholder: '请输入用户名称',
    nickname: '用户昵称',
    nicknamePlaceholder: '请输入用户昵称',
    mobile: '手机号码',
    mobilePlaceholder: '请输入手机号码',
    status: '状态',
    statusPlaceholder: '用户状态',
    id: '用户编号',
    deptName: '部门',

    deptNamePlaceholder: '请输入部门名称',
    desc1: '未分配部门',
    deptId: '归属部门',
    email: '邮箱',
    password: '用户密码',
    emailPlaceholder: '请输入邮箱',
    passwordPlaceholder: '请输入用户密码',
    remarkPlaceholder: '请输入内容',
    sexPlaceholder: '请选择',
    postIdsPlaceholder: '请选择',
    deptIdPlaceholder: '请选择归属部门',
    roleIdsPlaceholder: '请选择角色',
    roleIds: '角色',
    createTime: '创建时间',
    sex: '用户性别',
    postIds: '岗位',
    remark: '备注',
    rDesc1: '用户名称不能为空',
    rDesc2: '用户昵称不能为空',
    rDesc3: '用户密码不能为空',
    rDesc4: '请输入正确的邮箱地址',
    rDesc5: '请输入正确的手机号码',
    rDesc7: '请输入手机号码',
    sSpRole: '分配角色',
    handleResetPwd: '重置密码',
    handleRole: '分配角色',
    roleDataXls: '用户数据',
    desc2: '修改成功，新密码是：',
    userImport: '用户导入',
    desc3: '将文件拖到此处，或点击上传',
    desc4: '是否更新已经存在的用户数据',
    desc5: '仅允许导入 xls、xlsx 格式文件。',
    desc6: '下载模板',
    message1: '请上传文件',
    message2: '上传成功数量：',
    message3: '更新成功数量：',
    message4: '更新失败数量：',
    message5: '上传失败，请您重新上传！',
    message6: '最多只能上传一个文件！',
    desc7: '用户导入模版',
    desc8: '确认要{text}{username}用户吗?',
    desc9: '请输入{username}的新密码'
  },

  role: {
    name: '角色名称',
    code: '角色标识',
    status: '状态',
    applicationCode: '应用',
    createTime: '创建时间',
    namePlaceholder: '请输入角色名称',
    codePlaceholder: '请输入角色标识',
    statusPlaceholder: '请选择状态',
    applicationCodePlaceholder: '请选择应用',
    typePlaceholder: '请选择角色类型',
    id: '角色编号',
    type: '角色类型',
    tags: '角色标签',
    sort: '显示顺序',
    remark: '备注',
    desc1: '以此角色为模板新建一个复用该角色菜单和权限的自定义角色',
    createRole: '新建角色',
    menuPerm: '菜单权限',
    dataPerm: '数据权限',
    advanceDataPerm: '高级数据权限',
    resCfg: '资源配置',
    tag: '标签',
    allSelectOrNot: '全选/全不选:',
    expandAllOrNot: '全部展开/折叠',
    dataScope: '权限范围',
    tenantname: '租户名称',
    labelPlaceholder: '请输入名称',
    propNamePlaceholder: '请输入字段名称',
    keyPlaceholder: '请输入功能点key值',
    desc2: '父子联动(选中父节点，自动选择子节点):',
    systemRole: '系统角色',
    customRole: '自定义角色',
    ortherRole: '其他角色',
    newPerm: '新增权限',
    loadText: '加载中，请稍后',
    desc3: '点击查看权限',
    desc4: '点击修改权限',
    desc5: '选择权限',
    desc6: '修改权限',
    desc7: '修改业务',
    desc8: '添加业务',
    label: '名称',
    propName: '字段名称',
    authorityPoints: '功能点',
    rDesc1: '权限范围不能为空',
    msg1: '不能操作类型为系统内置的角色',
    desc9: '全局',
    read: '读',
    write: '写',

    batchDelete: '批量取消分配',
    save: '保存',
    addRole: '分配角色',
    cancelAssign: '取消分配',
    confirmCancelAssign: '是否取消分配所选中的角色？',

    newScene: '新增场景',
    resourceOwnerName: '名称',
    editPermi: '编辑权限',
    viewPermi: '查看权限',
    scene: '场景',
    scenePlaceholder: '请选择场景',
    rDesc2: '请选择角色标签',
    roleList: '角色列表',
    desc10: '{tenantAppName}应用已升级，{name}角色已过期，需要重新创建',
    autoAuthRule: '自动授权规则',

    roleForm: {
      name: '角色名称',
      code: '角色标识',
      sort: '显示顺序',
      applicationCode: '服务订阅',
      status: '状态',
      remark: '备注',
      clientName: '交互端',
      namePlaceholder: '请输入角色名称',
      codePlaceholder: '请输入角色标识',
      clientNamePlaceholder: '请输入交互端',
      sortPlaceholder: '请输入显示顺序',
      remarkPlaceholder: '请输备注',
      applicationCodePlaceholder: '请选择服务',
      statusPlaceholder: '请选择状态',
      rDesc1: '角色名称不能为空',
      rDesc2: '关联服务不能为空',
      rDesc3: '角色标识不能为空',
      rDesc4: '显示顺序不能为空',
      rDesc5: '状态不能为空',
      rDesc6: '备注不能为空'
    }
  },

  dept: {
    title: '部门名称',
    status: '状态',
    namePlaceholder: '请输入部门名称',
    statusPlaceholder: '请选择部门状态',
    name: '部门名称',
    code: '编码',
    leader: '负责人',
    sort: '排序',
    createTime: '创建时间',
    parentId: '上级部门',
    leaderUserId: '负责人',
    phone: '联系电话',
    email: '邮箱',
    codePlaceholder: '请输入编码',
    phonePlaceholder: '请输入联系电话',
    emailPlaceholder: '请输入邮箱',
    leaderUserIdPlaceholder: '请输入负责人',
    parentIdPlaceholder: '请选择上级部门',
    rDesc1: '上级部门不能为空',
    rDesc2: '部门名称不能为空',
    rDesc3: '显示排序不能为空',
    rDesc4: '请输入正确的邮箱地址',
    rDesc5: '请输入正确的手机号码',
    rDesc6: '状态不能为空',
    rDesc7: '请输入手机号码',

    rDesc8: '该部门下有关联用户，请将关联用户移至其它部门!',
    topDivision: '顶级部门'
  },

  post: {
    name: '岗位名称',
    code: '岗位编码',
    status: '状态',
    namePlaceholder: '请输入岗位名称',
    codePlaceholder: '请输入岗位编码',
    statusPlaceholder: '请选择状态',
    id: '岗位编号',
    sort: '岗位顺序',
    remark: '岗位备注',
    createTime: '创建时间',
    postListXls: '岗位列表.xls',
    sortPlaceholder: '请输入岗位顺序',
    remarkPlaceholder: '请输备注',
    rDesc1: '岗位名称不能为空',
    rDesc2: '岗位编码不能为空',
    rDesc3: '岗位状态不能为空',
    rDesc4: '岗位内容不能为空'
  },

  subscribe: {
    name: '交互端名',
    ver: '版本号',
    subscribe: '订阅',
    changeRelation: '切换对应关系',
    checkoutValue0: '交互端',
    checkoutValue1: '服务',
    upgradable: '升级',
    bindVisible: '绑定',
    bindData1: '客户端',
    bindData2: '管理端端',
    bindData3: '综合服务端',
    bindData4: '销售端',
    menuPerm: '菜单权限',
    subListContainer1: '未订阅应用',
    subListContainer2: '已订阅应用',
    upgradeApp: '应用升级',
    desc1: '继承角色和菜单',
    role: '角色',
    addMenu: '新增菜单',
    desc2: '角色迁移后当前申请所有信息不可再编辑，全部角色将被新版本继承',
    desc3: '确认升级吗？确认后将自动退出系统',
    desc4: '升级失败',
    desc5: '取消升级',
    menuExpand: '全部展开/折叠',
    treeNodeAll2: '全选/全不选',
    serverCfg: '服务配置',
    desc6: '首页设置',
    id: '当前租户的服务id',
    applicationName: '应用名称',
    applicationCode: '应用编码',
    applicationVer: '应用版本',
    applicationType: '应用类型',
    type1: '接入应用',
    type2: '租户应用',
    type3: '内置应用',
    applicationStatus: '状态',
    applicationDescription: '应用描述',
    applicationUrl: '应用地址',
    applicationOauthClient: 'OAuth客户端',
    sort: '排序',
    isHomePage: '是否有首页',
    homeSetting: '首页设置',
    serviceSort: '服务排序',
    home: '首页',
    setHome: '设为首页',
    desc7: '当前端已经设置其他首页,确定设置当前菜单为新首页吗？',
    desc8: '请输入排序编号',
    msgTitle: '提升',
    sortCode: '排序编号',
    cancelSetting: '取消设置',
    menuDetail: '菜单详情'
  }
}
