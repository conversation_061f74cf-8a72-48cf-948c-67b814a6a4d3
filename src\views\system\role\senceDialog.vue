<template>
  <Dialog
    v-model="dialogVisible"
    :title="'业务场景权限'"
    width="800"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <el-table v-loading="loading" :data="list" ref="tableRef">
      <el-table-column align="center" prop="label" fixed="left" width="200">
        <template #header>
          <div class="point" style="position: relative; margin-left: 80px; text-align: left">
            <div
              class="split"
              style="
                position: absolute;
                top: 15px;
                left: -63px;
                width: 100px;
                background: #909399;
                transform: rotate(45deg);
                height: 1px;
              "
            ></div>
            权限点
          </div>
          <div style="position: relative; text-align: left; margin-left: 40px">场景</div>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in authList"
        :key="index"
        :label="item.label"
        align="center"
        :prop="item.label"
        min-width="120"
      >
        <template #default="{ row }">
          <el-checkbox
            @change="(isCheck) => changeAuth(row, isCheck, item.value)"
            v-model="row[item.value]"
          />
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button type="primary" @click="submit()">{{ t('common.ok') }}</el-button>
      <el-button @click="close">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'senceDialog'
})

const { t } = useI18n() // 国际化
const route = useRoute()
const query = useRoute().query as any
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
const dialogVisible = ref(false)

const loading = ref(false)

const list = ref([])

const authList = ref([])

const rowData = ref()

import { deepClone } from '@/utils/deep'

const open = (row, _sceneList) => {
  rowData.value = row
  console.log('row', row)
  console.log('_sceneList', _sceneList)
  console.log(
    'getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)',
    getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)
  )

  list.value = []
  list.value = deepClone(_sceneList).map((el) => {
    el.resource = []

    if (row?.resourcesMap?.size && row?.resourcesMap?.get(el.value)?.length > 0) {
      el.resource = row.resourcesMap.get(el.value)

      row.resourcesMap.get(el.value).forEach((item) => {
        el[item] = true
      })
    }

    return el
  })

  authList.value = [...getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)]
  dialogVisible.value = true
}

const changeAuth = (_row, _isCheck, _val) => {
  if (_isCheck) {
    if (_row.resource.indexOf(_val) === -1) {
      _row.resource.push(_val)
    }
  } else {
    if (_row.resource.indexOf(_val) !== -1) {
      _row.resource.splice(_row.resource.indexOf(_val), 1)
    }
  }
}

const getScopes = () => {
  let scopes: any = []

  list.value.forEach((el: any) => {
    el.resource.forEach((e) => {
      scopes.push({
        scene: el.value,
        permissionType: e,
        resourceType: 2
      })
    })
  })
  return scopes
}

import { assignRoleSingleResourceScope } from '@/api/system/role'
const init = inject<any>('init')
const submit = async () => {
  try {
    console.log(query)
    if (query.type === '1') {
      ElMessage.error(t('system.role.msg1'))
      return
    }
    if (!rowData.value?.id) {
      ElMessage.error('租户id不能为空或者null')
      return
    }

    if (query.dataScope == 6) {
      let thisScopes = getScopes()
      console.log('thisScopes', thisScopes)

      await assignRoleSingleResourceScope({
        roleCode: query.code,
        resource: {
          resourceId: rowData.value.id,
          scopes: thisScopes
        }
      })

      //重新获取origintreeOptions
      init({
        ...(route.query as unknown as any)
      })
      ElMessage.success(t('common.handleSuccess'))
      dialogVisible.value = false
    }
  } catch (error) {
    console.log(error)
  }
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>
