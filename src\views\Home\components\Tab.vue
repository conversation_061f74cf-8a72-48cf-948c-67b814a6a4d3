<template>
  <div class="flex flex-row h-[60px]" style="border-bottom: 1px solid #f3f3f1">
    <div
      v-for="item in tabList"
      :key="item.value"
      :class="tabIndex === item.value ? 'text-gold border-b-[3px] border-b-gold' : ''"
      class="hover:(text-gold border-b-[3px] border-b-gold) mr-8 cursor-pointer content-center leading-[60px] <2xl:(text-14px leading-[60px] mr-6)"
      @click="
        () => {
          tabClick(item.value)
          emit('scrollTop')
        }
      "
    >
      {{ item.name }}
    </div>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'HomeTab'
})

const { t } = useI18n()
const emit = defineEmits(['scrollTop'])
const tabList = ref([
  {
    name: t('home.title1'),
    value: 0
  },
  {
    name: t('home.title2'),
    value: 1
  },
  {
    name: t('home.title3'),
    value: 2
  },
  {
    name: t('home.title4'),
    value: 3
  },
  {
    name: t('home.title5'),
    value: 4
  },
  {
    name: t('home.title6'),
    value: 5
  },
  {
    name: t('home.title7'),
    value: 6
  }
])

//tab切换
const tabIndex = ref(0)
// 切换选项卡
const tabClick = (index, event?: Event) => {
  console.log(index)
  tabIndex.value = index
}

defineExpose({ tabIndex })
</script>
