<template>
  <!-- 搜索工作栏 -->
  <UmvContent>
    <template #search>
      <UmvQuery v-model="queryParams" :opts="queryOpts" @check="handleQuery" @reset="resetQuery" />
    </template>

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="getList">
      <template #tools>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:tenant:export']"
        >
          <icon-ep-download style="font-size: 12px" class="mr-5px" /> {{ t('common.export') }}
        </el-button>
      </template>
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单弹窗：预览 -->
    <RefundDetail ref="detailRef" @success="getList" />

    <!-- 回调状态详情 -->
    <bizNotifyDetail ref="bizNotifyDetailRef" />
  </UmvContent>
</template>
<script lang="tsx" setup>
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { PayChannelEnum, PayRefundStatus } from '@/utils/constants'
import { dateFormatter } from '@/utils/formatTime'
import * as MerchantApi from '@/api/pay/merchant'
import * as RefundApi from '@/api/pay/refund'
import RefundDetail from './RefundDetail.vue'
import bizNotifyDetail from './bizNotifyDetail.vue'
import download from '@/utils/download'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { UmvContent } from '@/components/UmvContent'
import { ElMessage, ElMessageBox } from 'element-plus'
import { checkPermi } from '@/utils/permission'

const { t } = useI18n() // 国际化
defineOptions({ name: 'PayRefund' })

const message = useMessage() // 消息弹窗

const queryParams = ref<RefundApi.RefundPageReqVO>({
  pageNo: 1,
  pageSize: 10,
  merchantId: undefined,
  appId: undefined,
  channelId: undefined,
  channelCode: undefined,
  orderId: undefined,
  tradeNo: undefined,
  merchantOrderId: undefined,
  merchantRefundNo: undefined,
  notifyUrl: undefined,
  notifyStatus: undefined,
  status: undefined,
  type: undefined,
  payAmount: undefined,
  refundAmount: undefined,
  outRefundNo: undefined,
  outTradeNo: undefined,
  reason: undefined,
  userIp: undefined,
  channelOrderNo: undefined,
  channelRefundNo: undefined,
  channelErrorCode: undefined,
  channelErrorMsg: undefined,
  channelExtras: undefined,
  expireTime: [],
  successTime: [],
  notifyTime: [],
  createTime: []
})

// 加载应用和商户列表
import * as AppApi from '@/api/pay/app'
const appList = ref([]) // 支付应用列表集合
const merchantList = ref([])

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  merchantId: {
    label: t('pay.refund.merchantId'),
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.merchantId}
        placeholder={t('pay.refund.merchantIdPlaceholder')}
        clearable
      >
        {merchantList.value.map((item) => (
          <el-option key={item.id} label={item.name} value={item.id} />
        ))}
      </el-select>
    )
  },
  appId: {
    label: t('pay.refund.appId'),
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.appId}
        placeholder={t('pay.refund.appIdPlaceholder')}
        clearable
      >
        {appList.value.map((item) => (
          <el-option key={item.id} label={item.name} value={item.id} />
        ))}
      </el-select>
    )
  },
  channelCode: {
    label: t('pay.refund.channelCode'),
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.channelCode}
        placeholder={t('pay.refund.channelCodePlaceholder')}
        clearable
      >
        {getStrDictOptions(DICT_TYPE.PAY_CHANNEL_CODE_TYPE).map((dict) => (
          <el-option key={dict.value} label={dict.label} value={dict.value} />
        ))}
      </el-select>
    )
  },
  status: {
    label: t('pay.refund.status'),
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.status}
        placeholder={t('pay.refund.statusPlaceholder')}
        clearable
      >
        <el-option label={t('pay.refund.status_0')} value={0} />
        <el-option label={t('pay.refund.status_10')} value={10} />
        <el-option label={t('pay.refund.status_20')} value={20} />
      </el-select>
    )
  },
  createTime: {
    label: t('pay.refund.createTime'),
    defaultVal: [],
    controlRender: () => (
      <el-date-picker
        v-model={queryParams.value.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        start-placeholder={t('common.startDateText')}
        end-placeholder={t('common.endDateText')}
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
      />
    )
  },
  merchantOrderId: {
    label: '业务支付单号',
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.merchantOrderId}
        placeholder="请输入业务支付单号"
        clearable
      />
    )
  },
  merchantRefundId: {
    label: '业务退款单号',
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.merchantRefundNo}
        placeholder="请输入业务退款单号"
        clearable
      />
    )
  },
  channelOrderNo: {
    label: '渠道支付单号',
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.channelOrderNo}
        placeholder="请输入渠道支付单号"
        clearable
      />
    )
  },
  outRefundNo: {
    label: '商户退款编码',
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.outRefundNo}
        placeholder="请输入商户退款编码"
        clearable
      />
    )
  },
  outTradeNo: {
    label: '商户订单编码',
    defaultVal: undefined,
    controlRender: () => (
      <el-input v-model={queryParams.value.outTradeNo} placeholder="请输入商户订单编码" clearable />
    )
  }
})

const exportLoading = ref(false) // 导出等待

// 初始化数据
onMounted(async () => {
  // 获取商户列表和应用列表
  const [merchants, apps] = await Promise.all([
    MerchantApi.getMerchantListByName(),
    AppApi.getAppSimpleList()
  ])

  merchantList.value = merchants
  appList.value = apps

  // 获取列表数据
  getList()
})

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 查询列表 */
const loading = ref(false) // 列表遮罩层
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const getList = async () => {
  loading.value = true
  try {
    const data = await RefundApi.getRefundPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    merchantId: undefined,
    appId: undefined,
    channelId: undefined,
    channelCode: undefined,
    orderId: undefined,
    tradeNo: undefined,
    merchantOrderId: undefined,
    merchantRefundNo: undefined,
    notifyUrl: undefined,
    notifyStatus: undefined,
    status: undefined,
    type: undefined,
    payAmount: undefined,
    refundAmount: undefined,
    outRefundNo: undefined,
    outTradeNo: undefined,
    reason: undefined,
    userIp: undefined,
    channelOrderNo: undefined,
    channelRefundNo: undefined,
    channelErrorCode: undefined,
    channelErrorMsg: undefined,
    channelExtras: undefined,
    expireTime: [],
    successTime: [],
    notifyTime: [],
    createTime: []
  }
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RefundApi.exportRefund(queryParams.value)
    download.excel(data.data, t('pay.order.payOrder') + '.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 预览详情 */
const detailRef = ref()
const openDetail = (id: number) => {
  detailRef.value.open(id)
}

// 手动退款
const manualRefund = (row) => {
  ElMessageBox.confirm('是否手动补偿退款？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true
      await RefundApi.refundCompensationManually(row.id)
      ElMessage.success('退款成功')
      getList()
    } finally {
      loading.value = false
    }
  })
}

const bizNotifyDetailRef = ref()

// 查看回调状态
const checkBizNotify = (row) => {
  bizNotifyDetailRef.value.open(row)
}

// UmvTable列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: t('pay.refund.id'), align: 'center' },
  {
    prop: 'payMerchantName',
    label: t('pay.refund.payMerchantName'),
    align: 'center',
    width: '120'
  },
  { prop: 'appName', label: t('pay.refund.appName'), align: 'center', width: '120' },
  {
    prop: 'channelCodeName',
    label: t('pay.refund.channelCodeName'),
    align: 'center',
    width: '120',
    renderTemplate: (scope) => (
      <span>{PayChannelEnum[scope.row.channelCode.toLocaleUpperCase()]?.name}</span>
    )
  },
  { prop: 'orderId', label: t('pay.refund.orderId'), align: 'center', width: '140' },
  { prop: 'merchantOrderId', label: '业务支付单号', align: 'center', minWidth: '300' },
  {
    prop: 'businessOrder',
    label: '业务订单',
    align: 'left',
    minWidth: '300',
    renderTemplate: (scope) => (
      <div>
        <p class="order-font flex flex-col">
          <el-tag class="w-[100px]"> 退款订单号</el-tag>
          {scope.row.merchantRefundId}
        </p>
        <p class="order-font flex flex-col">
          <el-tag type="success" class="w-[100px]">
            支付订单号
          </el-tag>
          {scope.row.merchantOrderId}
        </p>
      </div>
    )
  },
  {
    prop: 'merchantRefundNo',
    label: t('pay.refund.merchantRefundNo'),
    align: 'center',
    minWidth: '300',
    renderTemplate: (scope) => (
      <div>
        <p class="order-font text-left">
          <el-tag>{t('pay.refund.merchantRefundNoTag1')}</el-tag>
          {scope.row.no}
        </p>
        <p class="order-font text-left">
          <el-tag type="warning">{t('pay.refund.merchantRefundNoTag2')}</el-tag>
          {scope.row.channelOrderNo}
        </p>
      </div>
    )
  },
  {
    prop: 'payAmount',
    label: t('pay.refund.payAmount'),
    align: 'center',
    width: '100',
    renderTemplate: (scope) => <span>￥{(scope.row.payPrice / 100).toFixed(2)}</span>
  },
  {
    prop: 'refundAmount',
    label: t('pay.refund.refundAmount'),
    align: 'center',
    width: '100',
    renderTemplate: (scope) => <span>￥{(scope.row.refundPrice / 100).toFixed(2)}</span>
  },
  {
    prop: 'status',
    label: t('pay.refund.status'),
    align: 'center',
    minWidth: '200',
    fixed: 'right',
    renderTemplate: (scope) => {
      const showManualButton =
        checkPermi(['pay:refund:manual']) &&
        (PayRefundStatus[scope.row.status].zh === '退款失败' ||
          PayRefundStatus[scope.row.status].zh === '未退款')

      return (
        <div class="flex" style="justify-content: center">
          <el-tag type={PayRefundStatus[scope.row.status].en}>
            {PayRefundStatus[scope.row.status].zh}
          </el-tag>

          {showManualButton && (
            <el-button
              class="ml-2"
              type="danger"
              size="small"
              plain
              onClick={() => manualRefund(scope.row)}
            >
              手动退款
            </el-button>
          )}
        </div>
      )
    }
  },
  {
    prop: 'bizNotifyStatusName',
    label: t('pay.refund.bizNotifyStatusName'),
    align: 'center',
    fixed: 'right',
    renderTemplate: (scope) => {
      if (!scope.row.bizNotifyStatusName) {
        return <span></span>
      }

      return (
        <el-tooltip content="点击查看详情">
          <span style="cursor: pointer" onClick={() => checkBizNotify(scope.row)}>
            {scope.row.bizNotifyStatusName}
          </span>
        </el-tooltip>
      )
    }
  },
  {
    prop: 'reason',
    label: t('pay.refund.reason'),
    align: 'center',
    width: '140',
    showOverflowTooltip: true
  },
  {
    prop: 'createTime',
    label: t('pay.refund.createTime'),
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'successTime',
    label: t('pay.refund.successTime'),
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.successTime)}</span>
    )
  },
  {
    prop: 'operate',
    label: t('common.operate'),
    align: 'center',
    fixed: 'right',
    renderTemplate: (scope) => {
      if (!checkPermi(['pay:order:query'])) {
        return <span></span>
      }

      return (
        <el-button type="primary" link onClick={() => openDetail(scope.row.id)}>
          {t('common.detail')}
        </el-button>
      )
    }
  }
])
</script>

<style>
.order-font {
  font-size: 12px;
  padding: 2px 0;
}
</style>
