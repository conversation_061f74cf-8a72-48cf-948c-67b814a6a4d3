<template>
  <Dialog v-model="dialogVisible" :title="t('system.role.tags')" @close="close">
    <ElForm :model="formData" :rules="rules" ref="form">
      <ElFormItem label=" " prop="roleTags">
        <ElCheckboxGroup v-model="formData.roleTags">
          <ElCheckbox v-for="(item, index) in roleTags" :key="index" :label="item.value">{{
            item.label
          }}</ElCheckbox>
        </ElCheckboxGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <el-button :loading="formLoading" type="primary" @click="submitForm">{{
        t('common.ok')
      }}</el-button>
      <el-button @click="close">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'TagForm'
})

import * as <PERSON><PERSON><PERSON> from '@/api/system/role'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
const { t } = useI18n() // 国际化

interface roleTagVO {
  label: string
  value: string
}

const emit = defineEmits(['success'])

const form = ref()

const dialogVisible = ref(false)

const formLoading = ref(false)

const roleTags: Ref<roleTagVO[] | never[]> = ref([])

const roleId: Ref<number | string> = ref('')

const close = () => {
  formData.value.roleTags = []
  dialogVisible.value = false
}

const formData = ref({
  roleTags: []
})

const rules = computed(() => {
  return {
    roleTags: [
      {
        validator: (_rule: any, _value: any, callback: any) => {
          console.log('_value', _value)
          if (!_value || JSON.stringify(_value) === '[]') {
            callback(new Error(t('system.role.rDesc2')))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }
})

const open = async (row: RoleApi.RoleVO) => {
  dialogVisible.value = true
  roleTags.value.length === 0 &&
    (roleTags.value = getStrDictOptions(DICT_TYPE.SYSTEM_ROLE_TAG) as roleTagVO[])
  roleId.value = row.id
  console.log('roleTags.value', roleTags.value)
  const tags = await RoleApi.getRoleListAssignedTags(row.id)
  formData.value.roleTags =
    tags.length > 0
      ? tags.map((el) => {
          return el.tagValue
        })
      : []
}

const submitForm = async () => {
  await form.value.validate()
  try {
    formLoading.value = true
    const res = await RoleApi.updateTag({
      roleId: roleId.value,
      roleTags: formData.value.roleTags
    })
    console.log('res', res)
    ElMessage.success(t('common.submitSuccess'))
    emit('success')
    formData.value.roleTags = []
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

defineExpose({
  open
})
</script>
