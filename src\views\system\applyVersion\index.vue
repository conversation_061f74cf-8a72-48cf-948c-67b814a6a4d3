<!-- 应用管理-应用版本 -->
<template>
  <!-- 搜索 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"
          ><icon-ep-search style="font-size: 12px" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"
          ><icon-ep-refresh style="font-size: 12px" class="mr-5px" /> 重置</el-button
        >
        <el-button type="primary" plain @click="openForm('create', firstAppId)" v-if="isAdd">
          <icon-ep-plus style="font-size: 12px" class="mr-5px" />
          新增
        </el-button>
        <span
          class="ml-5 text-gold"
          v-if="envController.getEnvironment() == 'uat' || envController.getEnvironment() == 'sit'"
        >
          修改菜单需要联系管理员,请不要随意修改,以免造成系统异常
        </span>
      </el-form-item>
    </el-form>
    <!-- ifAdmin 运维功能 -->
    <div v-if="ifAdmin">
      <el-row class="mb-10px">
        新增服务:

        <el-input v-model="thisAppId" style="max-width: 600px" placeholder="输入服务Id">
          <template #prepend>
            <el-button type="primary" plain @click="openForm('create', thisAppId)" v-if="isAdd">
              <icon-ep-plus style="font-size: 12px" class="mr-5px" />
              新增
            </el-button>
          </template>
        </el-input>
      </el-row>
    </div>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="服务编号" align="center" prop="id" />
      <el-table-column label="服务名" align="center" prop="name" />
      <el-table-column label="版本号" align="center" prop="ver">
        <template #default="scope">
          <span>{{ scope.row.ver }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
      />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link v-if="scope.row.status === 1" type="primary" @click="toMenu(scope.row)">
            配置
          </el-button>
          <el-button link v-else type="primary" @click="toMenu(scope.row, true)"> 查看 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 穿梭框，修改菜单 -->
    <!-- <SubscribePopup ref="subcribeRef" /> -->

    <!-- 表单弹窗：添加/修改 -->
    <TenantPackageForm ref="formRef" @success="getList" />
  </ContentWrap>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemApplyVersion'
})

import { dateFormatter } from '@/utils/formatTime'
import TenantPackageForm from './TenantPackageForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import envController from '@/controller/envController'

const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由

interface listVO {
  status: number
  id: number
}

const isAdd = computed(() => {
  return list.value.length > 0 && list.value[0].status !== 1
})

const route = useRoute()

const code = computed(() => {
  return route?.query?.code as string | undefined
})

/**超管模式, */
//可以直接切换appId进行创建菜单!!!
let thisAppId = ref<number>(0)
const ifAdmin = computed(() => {
  const route = useRoute()
  return route?.query?.ifAdmin as string | undefined
})

/**超管模式 */

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list: Ref<listVO[]> = ref([]) // 列表的数据

const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  status: null,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
import { getTenantVersList } from '@/api/system/apply'
const getList = async () => {
  loading.value = true
  try {
    const data = await getTenantVersList({ ...queryParams, code: code.value })
    list.value = data.list
    total.value = data.total
    getFirstAppId() //获取第一个id作为新增时过滤菜单权限的id
  } finally {
    loading.value = false
  }
}
/** 初始化 **/
onMounted(async () => {
  await getList()
})

onActivated(() => {
  getList()
})
// 获取列表的第一个值
const firstAppId: Ref<string | number | undefined> = ref('')
const getFirstAppId = () => {
  //获取列表第一项的id作为更新id
  list.value.length > 0 && (firstAppId.value = list.value[0]?.id)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number | string) => {
  let obj = { id: id, code: code.value }
  formRef.value.open(type, obj)
}

// 跳去菜单配置
const toMenu = (row, isCheck = false) => {
  push({
    name: 'SystemApplyMenu',
    query: {
      code: code.value,
      id: row.id,
      name: row.name,
      ver: row.ver,
      isCheck: isCheck ? 'true' : 'false'
    }
  })
}
</script>
