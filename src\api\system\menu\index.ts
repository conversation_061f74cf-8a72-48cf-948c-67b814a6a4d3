/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: Ho<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-12 11:39:01
 * @Description:
 */
import request from '@/config/axios'

export interface MenuVO {
  id: number
  name: string
  permission: string
  type: number
  sort: number
  parentId: number
  path: string
  icon: string
  component: string
  componentName?: string
  status: number
  visible: boolean
  keepAlive: boolean
  alwaysShow?: boolean
  createTime: Date
  requestUri?: string
  requestKey?: string
}

export interface AppMenuVO extends MenuVO {
  applicationId: string | number
}

// 查询菜单（精简）列表 (获取当前用户权限内的菜单列表)
export const getSimpleMenusList = (params?) => {
  return request.get({ url: '/system/menu/list-all-simple', params })
}

// 查询菜单（精简）列表
export const getSimpleMenusListExtends = (params?) => {
  return request.get({ url: '/system/menu/list-all-simple-by-role', params })
}

// 查询应用菜单（精简）列表
export const getAppSimpleMenusList = (params?) => {
  return request.get({
    url: '/system/menu/super-all-menus',
    params
  })
}

export const getAppMenusList = (params?) => {
  return request.get({
    url: '/system/menu/list-all',
    params
  })
}

// 查询菜单列表
export const getMenuList = (params) => {
  return request.get({ url: '/system/menu/list', params })
}

// 查询应用菜单列表
export interface MenuItemType {
  name: string
  permission: string
  type: number
  sort: number
  parentId: number
  path: string
  icon: string
  component: string
  componentName: string
  status: number
  visible: boolean
  keepAlive: boolean
  alwaysShow: boolean
  applicationId: number
  category: number
  activeMenu: string
  requestUri: string
  requestKey: string
  id: number | string
  createTime: Record<string, unknown>
}

export const getAppMenuList = (params) => {
  return request.get({
    url: '/system/menu/list',
    params
  })
}

export const getAppTenantMenuList = (params) => {
  return request.get({
    url: '/system/menu/tenantMenuList',
    params
  })
}

// 获取菜单详情
export const getMenu = (id: number) => {
  return request.get({ url: '/system/menu/get?id=' + id })
}

// 新增菜单
export const createMenu = (data: MenuVO) => {
  return request.post({ url: '/system/menu/create', data })
}

// 新增应用菜单
export const createAppMenu = (data: MenuVO) => {
  return request.post({
    url: '/system/menu/application/create',
    data
  })
}

// 修改菜单
export const updateMenu = (data: MenuVO) => {
  return request.put({ url: '/system/menu/update', data })
}

// 删除菜单
export const deleteMenu = (id: number) => {
  return request.delete({ url: '/system/menu/delete?id=' + id })
}

// 前登录租户的角色菜单对应关系
export interface roleDataType {
  /**角色名称    */
  name: string
  /**角色编码    */
  code: string
  /**角色编号 */
  id: number | string
  roleId?: number | string
  /**数据范围，参见 DataScopeEnum 枚举类 */
  dataScope: number
  /**数据范围(指定部门数组) */
  dataScopeDeptIds: number[]
  /**状态，参见 CommonStatusEnum 枚举类 */
  status: number
  /**角色类型，参见 RoleTypeEnum 枚举类 */
  type: number
  /**创建时间 */
  createTime: string
  /**是否过期 */
  isStale: boolean
  /** 角色标签*/
  tags: string[]
}
export const getRoleMenus = (params) => {
  return request.get({
    url: `/system/auth/role-menus`,
    params
  })
}
/**
 * 批量创建菜单
 * @param menus  菜单json
 * @returns
 */
export const batchCreateMenu = (menus) => {
  return request.post({
    url: `/system/menu/batchCreateMenu`,
    data: {
      // menus: JSON.stringify(menus)
      menus
    }
  })
}

/**
 * 获取所有租户菜单接口
 * @param code  业务端code
 * @returns
 */
export const getAllTenantMenus = (params: { code: String }) => {
  return request.get({
    url: `/system/tenant/getAllTenantMenus`,
    params
  })
}

/**
 * 获取分組租户菜单接口
 * @param code  业务端code
 * @returns
 */
export const getGroupTenantMenus = (params: { code: String }) => {
  return request.get({
    url: `/system/tenant/getGroupTenantMenus`,
    params
  })
}
