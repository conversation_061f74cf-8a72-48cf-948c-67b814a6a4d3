<!--
 * @Author: Ho<PERSON><PERSON>
 * @Date: 2023-05-31 13:59:22
 * @LastEditors: HoJack
 * @LastEditTime: 2023-07-18 11:37:10
 * @Description:  
-->

<template> <div></div></template>
<script setup lang="ts">
defineOptions({
  name: 'SSOLogin'
})

import type { RouteLocationNormalizedLoaded } from 'vue-router'
import { setLoginForm, getLoginForm } from '@/utils/auth'

const { currentRoute } = useRouter() // 路由

import { removeToken } from '@/utils/auth'
import { useUserStoreWithOut } from '@/store/modules/user'
import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()
const userStore = useUserStoreWithOut()
/** 监听当前路由为 SSOLogin 时，进行数据的初始化 */
watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    if (route.name === 'SSOLogin') {
      removeToken()
      const loginForm = getLoginForm()
      loginForm && setLoginForm(loginForm)
      wsCache.clear()
      userStore.resetState()

      // 链接加‘&’符号是兼容火狐浏览器赋相同值时不刷新页面，无法重新走一遍permission.ts下的路由守卫，无法进入单点登录页
      window.location.href = window.location.href + '&'
    }
  },
  { immediate: true }
)
</script>
