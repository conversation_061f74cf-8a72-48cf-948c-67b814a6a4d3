import request from '@/config/axios'

export interface ApplicationSubscriptionPageRes {
  applicationId: number
  name: string
  applicationCode: string
  ver: string
  description: string
  appType: number
  application: {
    createTime: Record<string, unknown>
    updateTime: Record<string, unknown>
    creator: string
    updater: string
    deleted: boolean
    id: number
    name: string
    code: string
    ver: string
    description: string
    url: string
    oauthClient: string
    applicationOwner: string
    status: number
    type: number
  }
  upgradable: boolean
}

// 获取应用列表
export const getTenantAppList = (params): Promise<ApplicationSubscriptionPageRes> => {
  return request.get({
    url: '/system/tenant-application/page',
    params
  })
}
// 获取可订阅应用列表(过滤租户,也就是当前租户下的应用)
export const getTenantAppListFilter = (params?): Promise<ApplicationSubscriptionPageRes[]> => {
  return request.get({
    url: '/system/tenant-application/subscription-list',
    params
  })
}

// 获取租户应用版本历史
export const getTenantVersList = (params?) => {
  return request.get({
    url: '/system/tenant-application/vers/page',
    params
  })
}

export const applicationClone = (data?) => {
  return request.post({
    url: '/system/menu/application/clone',
    data
  })
}

// 获取订阅列表
export const getSubscriptionList = (params?) => {
  return request.get({
    url: '/system/tenant-application-subscription/page',
    params
  })
}

// 创建新版本
export const createVerApplication = (data?) => {
  return request.post({
    url: '/system/tenant-application/ver/create',
    data
  })
}

// 创建订阅
export const createSubscription = (data) => {
  return request.post({
    url: '/system/tenant-application-subscription/create',
    data
  })
}

// 更新订阅
export const upgradeSubscription = (data) => {
  return request.post({
    url: '/system/tenant-application-subscription/upgrade',
    data
  })
}

// 更新订阅-新
export const upgradeMigratedCustomRoleSubscription = (data) => {
  return request.post({
    url: '/system/tenant-application-subscription/upgradeMigratedCustomRole',
    data,
    timeout: 5 * 60 * 1000 // 5分钟超时
  })
}

// 创建应用
export const createApplicationCreate = (data) => {
  return request.post({
    url: '/system/tenant-application/create',
    data
  })
}

// 编辑应用
export const updateApplicationCreate = (data) => {
  return request.post({
    url: '/system/tenant-application/ver/update',
    data
  })
}

// 查询应用（精简)列表
export const listSimpleAppData = (params?) => {
  return request.get({ url: '/system/tenant-application-subscription/list-all-simple', params })
}

// 获取服务订阅列表
export const getSubscriptionApplicationPage = (params?) => {
  return request.get({
    url: '/system/tenant-application-subscription/application-page',
    params
  })
}
// 获取交互端订阅列表
export const getSubscriptionClientPage = (params?) => {
  return request.get({
    url: '/system/tenant-application-subscription/client-page',
    params
  })
}

// 租户菜单详情列表
export const getTenantMenu = (params?) => {
  return request.get({
    url: '/system/menu/tenant-menu',
    params
  })
}

// 升级应用
export const upgradeApplication = (id) => {
  return request.post({
    url: `/system/tenant-application-subscription/upgradeApplication/${id}`
  })
}
