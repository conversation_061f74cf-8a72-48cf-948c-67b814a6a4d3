<template>
  <Dialog v-model="dialogVisible" :title="'回调详情'" width="70%">
    <h3 class="mt-5px mb-5px" style="font-size: 24px">任务</h3>
    <ContentWrap>
      <el-form label-width="120px">
        <el-form-item label="回调地址">
          {{ formData?.notifyUrl }}
        </el-form-item>
        <el-form-item label="最新回调结果">
          {{ payNotifyStatusMap[formData?.status] }}
        </el-form-item>
        <el-form-item label="上次回调时间">
          {{ formData?.lastExecuteTime && formatDate(formData?.lastExecuteTime) }}
        </el-form-item>
        <el-form-item label="下次回调时间">
          {{ formData?.nextNotifyTime && formatDate(formData?.nextNotifyTime) }}
        </el-form-item>
        <el-form-item label="已回调次数">
          {{ formData?.notifyTimes }}
        </el-form-item>
        <el-form-item label="最多可回调次数">
          {{ formData?.maxNotifyTimes }}
        </el-form-item>
      </el-form>
    </ContentWrap>
    <h3 class="mt-5px mb-5px" style="font-size: 24px">日志</h3>
    <ContentWrap>
      <el-table v-loading="loading" :data="formData?.logs" max-height="200">
        <el-table-column label="回调编号" align="center" prop="notifyTimes" />
        <el-table-column label="回调结果" align="center" prop="status">
          <template #default="scope">
            {{ payNotifyStatusMap[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column label="响应内容" align="center" prop="response">
          <template #default="scope">
            <el-button link type="primary" @click="checkResDetail(scope.row)"
              >查看响应内容</el-button
            >
          </template>
        </el-table-column>
        <el-table-column label="回调时间" align="center" prop="createTime">
          <template #default="scope">
            {{ scope.row.createTime && formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </ContentWrap>
    <template #footer>
      <el-button @click="close">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { finotyGetDetail } from '@/api/pay/refund'
import { formatDate } from '@/utils/formatTime'
const { t } = useI18n() // 国际化

const payNotifyStatusMap = ref({
  1: '等待通知',
  2: '通知成功',
  3: '通知失败',
  4: '请求成功，但是结果失败',
  5: '请求失败'
})

const dialogVisible = ref(false)

const loading = ref(false)

const formData: any = ref()

const open = async (row) => {
  formData.value = undefined
  loading.value = true
  dialogVisible.value = true
  try {
    const res = await finotyGetDetail({
      id: row.bizNotifyTaskId
    })
    formData.value = res
  } finally {
    loading.value = false
  }
}

const checkResDetail = (row) => {
  ElMessageBox.alert(row.response, '响应内容')
}

const close = () => {
  formData.value = undefined
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>
