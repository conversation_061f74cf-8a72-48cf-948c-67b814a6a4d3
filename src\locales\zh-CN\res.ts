/**资源管理 */
export default {
  resApp: {
    name: '编号',
    codePlaceholder: '请输入角色名称',
    applicationReasonPlaceholder: '请输入申请原因',
    scene: '场景',
    permissionTypes: '权限点',
    exposeNo: '操作',
    resourceOwnerId: '资源所有者编号',
    resourceOwnerName: '资源所有者名称',
    exposeRoleName: '角色',
    application: '申请',
    appOp: '申请操作',
    defaultResourceHolder: '默认权限持有人',
    defaultResourceHolderPlaceholder: '请选择默认权限持有人',
    applicationReason: '申请原因',
    applicationSuccess: '申请成功'
  },
  approval: {
    apply: '我申请的',
    approval: '我审批的',
    name: '编号',
    codePlaceholder: '请输入角色名称',
    applicationReasonPlaceholder: '请输入申请原因',
    scene: '场景',
    permissionTypes: '权限点',
    exposeNo: '编号',
    roleName: '角色名称',
    applicantName: '申请者名称',
    authorizerName: '审批者名称',
    applicationNo: '申请编号',
    approvalOpinion: '审批意见',
    authStatusName: '状态',
    applicationReason: '申请原因',
    approvalOpinionPlaceholder: '请输入审批意见',
    reasonPlaceholder: '请输入撤销原因',
    resultPlaceholder: '请选择是否通过',
    authStatus1: '审批',
    authStatus2: '撤回',
    result: '是否通过',
    reason: '撤销原因',
    withdraw: '撤销'
  }
}
