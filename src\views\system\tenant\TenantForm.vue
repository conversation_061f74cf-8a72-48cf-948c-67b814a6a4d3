<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" @close="resetForm" width="50%">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      autocomplete="off"
    >
      <el-form-item label="租户名" prop="name">
        <el-input v-model="formData.name" placeholder="请输入租户名" />
      </el-form-item>
      <el-form-item label="客户" prop="id">
        <el-select
          v-model="formData.id"
          :disabled="!queryType && formType !== 'create'"
          placeholder="请选择绑定的客户"
          filterable
          @change="selectCustomer"
        >
          <el-option
            v-for="(item, index) in notEnabledCustomerList"
            :value="item.customerCode"
            :label="item.customerName"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="银行网点类型" prop="branchType">
        <dict-tag type="branch_type" :value="formData.branchType" v-if="formData.branchType" />
        <el-tag size="small" v-else> 暂无数据</el-tag>
      </el-form-item>
      <el-form-item label="父级租户" prop="parentTenantId">
        <el-tree-select
          v-model="formData.parentTenantId"
          :placeholder="'请选择父级租户'"
          :data="parentTenantOptions"
          :disabled="!queryType && formType !== 'create'"
          :props="tenantTreePorps"
          node-key="id"
          check-strictly
          default-expand-all
          filterable
          :validate-event="false"
        />
      </el-form-item>
      <!-- <el-form-item label="服务订阅" prop="applicationIds" v-if="!isEdit">
        <el-select
          v-model="formData.applicationIds"
          multiple
          :clearable="false"
          :collapse-tags="true"
          :collapse-tags-tooltip="true"
          placeholder="请选择服务"
        >
          <el-option
            v-for="item in tenantAppList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            :disabled="item.disabled || isEdit"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="平台能力" prop="applicationAbility" v-if="!isEdit">
        <el-checkbox-group v-model="formData.applicationAbility">
          <el-checkbox
            v-for="item in applicationAbilityList"
            :key="item.id"
            :label="item.name"
            :disabled="item.disabled || isEdit"
          />
        </el-checkbox-group>
      </el-form-item> -->
      <el-form-item label="联系人" prop="contactName">
        <el-input v-model="formData.contactName" placeholder="请输入联系人" />
      </el-form-item>
      <el-form-item label="联系手机" prop="contactMobile">
        <el-input v-model="formData.contactMobile" :maxlength="11" placeholder="请输入联系手机" />
      </el-form-item>
      <el-form-item v-if="formType === 'create'" label="用户名称" prop="username">
        <el-input v-model="formData.username" autocomplete="off" placeholder="请输入用户名称" />
      </el-form-item>
      <el-form-item v-if="formType === 'create'" label="用户密码" prop="password">
        <el-input
          v-model="formData.password"
          placeholder="请输入用户密码"
          autocomplete="off"
          show-password
          type="password"
        />
      </el-form-item>
      <!-- <el-form-item label="账号额度" prop="accountCount">
        <el-input-number
          v-model="formData.accountCount"
          :min="0"
          :max="*********"
          controls-position="right"
          placeholder="请输入账号额度"
        />
      </el-form-item> -->
      <el-form-item label="过期时间" prop="expireTime">
        <el-date-picker
          v-model="formData.expireTime"
          clearable
          placeholder="请选择过期时间"
          type="date"
          value-format="x"
        />
      </el-form-item>
      <!-- <el-form-item label="绑定域名" prop="domain">
        <el-input v-model="formData.domain" placeholder="请输入绑定域名" />
      </el-form-item> -->
      <el-form-item label="租户状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemTenantForm'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as TenantApi from '@/api/system/tenant'
import { CommonStatusEnum } from '@/utils/constants'
import { getTenantAppList } from '@/api/system/apply'
const tenantTreePorps = {
  label: 'name'
}

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData: any = ref({
  id: undefined,
  name: undefined,
  // applicationIds: [],
  contactName: undefined,
  contactMobile: undefined,
  branchType: undefined, //银行类型
  accountCount: 99999, //默认值
  expireTime: *************, //'2099-12-31',
  domain: undefined,
  status: CommonStatusEnum.ENABLE,
  parentTenantId: undefined
  // applicationAbility: []
})

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    contactName: undefined,
    contactMobile: undefined,
    branchType: undefined, //银行类型
    accountCount: 99999,
    expireTime: *************, //'2099-12-31',
    domain: undefined,
    status: CommonStatusEnum.ENABLE,
    parentTenantId: undefined
    // applicationAbility: []
  }
  formRef.value?.resetFields()
}

const parentTenantOptions: Ref<any> = ref()

const applicationAbilityList: Ref<TenantApi.TenantApplicationVO[]> = ref([])

// 服务检测
const applicationValidator = (_rule: any, value: any, callback: any) => {
  if (value.length <= 0) {
    callback(new Error('请订阅1个或1个以上的服务'))
  } else {
    callback()
  }
}

const validPhone = (_rule, value, callback) => {
  // let reg =
  //   !/(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+-=_\[\]\{\}\|,:;'"<>?\/.~!@#$%^&*()]{6,18}$/.test(
  //     value
  //   )
  console.log('value', value)
  let reg = !/^1\d{10}$|^(0\d{2,3}-?|\(0\d{2,3}\))?[1-9]\d{4,7}(-\d{1,8})?$/.test(value)
  console.log('reg', reg)
  if (!value) {
    callback()
  } else if (reg) {
    callback(new Error('请输入正确格式的手机号'))
  } else {
    callback()
  }
}

import { useRoute } from 'vue-router'
const route = useRoute()

const queryType = computed(() => {
  return route.query?.type
})

const formRules = computed(() => {
  return {
    name: [{ required: true, message: '租户名不能为空', trigger: 'blur' }],
    id: [
      {
        required: !queryType.value && formType.value === 'create',
        message: '租户Id不能为空',
        trigger: 'blur'
      }
    ],
    parentTenantId: [
      {
        required: !queryType.value && formType.value === 'create',
        message: '父级租户不能为空',
        trigger: 'blur'
      }
    ],
    // contactMobile: [{ required: true, validator: validPhone, trigger: 'blur' }],
    contactMobile: [{ validator: validPhone, trigger: 'blur' }],
    // applicationIds: [{ required: true, validator: applicationValidator, trigger: 'blur' }],
    contactName: [{ required: false, message: '联系人不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '租户状态不能为空', trigger: 'blur' }],
    accountCount: [{ required: true, message: '账号额度不能为空', trigger: 'blur' }],
    expireTime: [{ required: true, message: '过期时间不能为空', trigger: 'blur' }],
    domain: [{ required: false, message: '绑定域名不能为空', trigger: 'blur' }],
    username: [{ required: true, message: '用户名称不能为空', trigger: 'blur' }],
    password: [{ required: true, message: '用户密码不能为空', trigger: 'blur' }]
  }
})
const formRef = ref() // 表单 Ref
// const packageList = ref([]) // 租户套餐
let tenantAppList: TenantApi.TenantApplicationVO[] = reactive([]) //服务列表

const isEdit = ref(false)

// 初始化父级租户
const initTenantParent = async () => {
  try {
    if (!parentTenantOptions.value) {
      const res = await TenantApi.getTenantTree()
      parentTenantOptions.value = [res]
    }
  } finally {
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = `${t('action.' + type)}绑定`
  formType.value = type

  initNotEnabledCustomer()
  await initTenantParent()

  resetForm()

  // 修改时，设置数据
  if (id) {
    isEdit.value = true
    formLoading.value = true
    try {
      const tempArr = await TenantApi.getTenant(id)
      formData.value = { ...tempArr }
    } finally {
      formLoading.value = false
    }
  } else {
    isEdit.value = false
    console.log('parentTenantOptions.value', parentTenantOptions.value)
    formData.value.parentTenantId = parentTenantOptions.value[0].id
  }
  // 加载套餐列表
  // packageList.value = await TenantPackageApi.getTenantPackageList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TenantApi.TenantVO

    if (formType.value === 'create') {
      // await TenantApi.createTenant(data)
      data.tenantId = data.id
      delete data.id
      await TenantApi.createTenantReno(data)
      message.success(t('common.createSuccess'))
    } else {
      await TenantApi.updateTenant(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 客户列表
const notEnabledCustomerList: Ref<any> = ref([])

// 获取客户列表，用以选择客户
const initNotEnabledCustomer = async () => {
  if (!notEnabledCustomerList.value || notEnabledCustomerList.value.length == 0) {
    try {
      notEnabledCustomerList.value = await TenantApi.getNotEnabledCustomer()
    } finally {
    }
  }
}
//选择客户触发,联动填充联系人
const selectCustomer = (customerCode) => {
  notEnabledCustomerList.value?.find((item) => {
    if (item.customerCode === customerCode) {
      console.log('item', item)
      formData.value.contactName = item.managerName //联动填充联系人
      formData.value.branchType = item.branchType //银行类型
    }
  })
}

/** 初始化 **/
onMounted(async () => {
  let temp_app_list: TenantApi.TenantApplicationVO[] = []

  tenantAppList.length === 0 &&
    (temp_app_list = (await getTenantAppList({ pageNo: 1, pageSize: 100 })).list)

  tenantAppList = temp_app_list
})
</script>

<style lang="scss" scoped>
.que-pionter {
  cursor: pointer;
}
</style>
