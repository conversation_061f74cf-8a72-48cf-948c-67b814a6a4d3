<template>
  <UmvContent>
    <el-row :gutter="20" class="h-full">
      <!-- 左侧部门树 -->
      <el-col :span="4" :xs="24">
        <DeptTree
          ref="deptTreeRef"
          @node-click="handleDeptNodeClick"
          @node-dblclick="handleDeptNodeDblClick"
        />
      </el-col>
      <el-col :span="20" :xs="24" class="h-full !flex flex-col">
        <!-- 搜索 -->
        <UmvQuery
          v-model="queryParams"
          :opts="queryOpts"
          :col-length-map="{
            1920: 4, // 屏幕宽度 >= 1920px 时使用 8 列
            1600: 4, // 屏幕宽度 >= 1600px 且 < 1920px 时使用 6 列
            1280: 3, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
            1000: 2, // 屏幕宽度 >= 1000px 且 < 1280px 时使用 3 列
            768: 2, // 屏幕宽度 >= 768px 且 < 1000px 时使用 2 列
            0: 1 // 屏幕宽度 < 768px 时使用 1 列
          }"
          @check="handleQuery"
          @reset="resetQuery"
        />

        <UmvTable v-loading="loading" :data="list" ref="tableRef" row-key="id" @refresh="getList">
          <template #tools>
            <el-button
              type="primary"
              plain
              size="small"
              @click="openForm('create')"
              v-hasPermi="['system:user:create']"
            >
              <icon-ep-plus style="font-size: 12px; margin-right: 2px" /> {{ t('common.newAdd') }}
            </el-button>
            <el-button
              type="warning"
              plain
              size="small"
              @click="handleImport"
              v-hasPermi="['system:user:import']"
            >
              <icon-ep-upload style="font-size: 12px; margin-right: 2px" />
              {{ t('common.import') }}
            </el-button>
            <el-button
              type="success"
              plain
              size="small"
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['system:user:export']"
            >
              <icon-ep-download style="font-size: 12px; margin-right: 2px" />{{
                t('common.export')
              }}
            </el-button>
            <el-button
              type="primary"
              plain
              size="small"
              @click="unlock"
              v-hasPermi="['system:user:unlock']"
            >
              <icon-ep-unlock style="font-size: 12px; margin-right: 2px" />
              {{ t('sys.user.unlock') }}
            </el-button>
          </template>

          <el-table-column type="selection" width="55" fixed="left" />
          <el-table-column
            :label="t('system.user.id')"
            align="center"
            key="id"
            prop="id"
            width="120"
          />
          <el-table-column
            :label="t('system.user.username')"
            align="center"
            prop="username"
            :show-overflow-tooltip="true"
            width="140"
          />
          <el-table-column
            :label="t('system.user.nickname')"
            align="center"
            prop="nickname"
            :show-overflow-tooltip="true"
            width="140"
          />
          <el-table-column
            :label="t('system.user.deptName')"
            align="center"
            key="deptName"
            prop="dept.name"
            :show-overflow-tooltip="true"
            width="140"
          />
          <el-table-column
            :label="t('system.user.mobile')"
            align="center"
            prop="mobile"
            :min-width="'150px'"
          >
            <template #default="scope">
              <el-row>
                <el-col :span="20">
                  <span style="">{{ scope.row.mobile }}</span>
                </el-col>
                <el-col :span="4">
                  <el-tooltip
                    v-if="scope.row.mobileAuthenticated"
                    :content="t('sys.login.phoneVered')"
                  >
                    <icon-ep-success-filled
                      :size="16"
                      class="mt-2px"
                      :style="{ color: 'var(--el-color-success)' }"
                    />
                  </el-tooltip>
                </el-col>
              </el-row>
            </template>
          </el-table-column>
          <el-table-column
            :label="t('system.user.email')"
            align="center"
            prop="email"
            :min-width="'180px'"
          >
            <template #default="scope">
              <el-row>
                <el-col :span="18">
                  <span style="">{{ scope.row.email }}</span>
                </el-col>
                <el-col :span="4">
                  <el-tooltip
                    v-if="scope.row.emailAuthenticated"
                    :content="t('sys.login.mailVered')"
                  >
                    <icon-ep-success-filled
                      :size="16"
                      class="mt-2px"
                      :style="{ color: 'var(--el-color-success)' }"
                    />
                  </el-tooltip>
                </el-col>
              </el-row>
            </template>
          </el-table-column>
          <el-table-column :label="t('system.user.status')" key="status">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="0"
                :inactive-value="1"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="t('sys.user.isUnlock')" key="locked" align="center">
            <template #default="scope">
              <el-tag type="primary" v-if="!scope.row.locked">{{ t('common.no') }}</el-tag>
              <el-tag type="danger" v-else>{{ t('common.yes') }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            :label="t('system.user.createTime')"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            width="180"
          />
          <el-table-column :label="t('common.operate')" align="center" width="160" fixed="right">
            <template #default="scope">
              <div class="flex justify-center items-center">
                <el-button
                  type="primary"
                  link
                  @click="openForm('update', scope.row.id)"
                  v-hasPermi="['system:user:update']"
                >
                  <icon-ep-edit style="font-size: 12px; margin-right: 2px" />{{
                    t('common.modify')
                  }}
                </el-button>
                <el-dropdown
                  @command="(command) => handleCommand(command, scope.row)"
                  v-hasPermi="[
                    'system:user:delete',
                    'system:user:update-password',
                    'system:permission:assign-user-role'
                  ]"
                >
                  <el-button type="primary" link
                    ><Icon icon="ep:d-arrow-right" /> {{ t('common.more') }}</el-button
                  >
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        command="handleDelete"
                        v-if="checkPermi(['system:user:delete'])"
                      >
                        <icon-ep-delete style="font-size: 12px; margin-right: 2px" />{{
                          t('common.delete')
                        }}
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="handleResetPwd"
                        v-if="checkPermi(['system:user:update-password'])"
                      >
                        <Icon icon="ep:key" />{{ t('system.user.handleResetPwd') }}
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="handleRole"
                        v-if="checkPermi(['system:permission:assign-user-role'])"
                      >
                        <icon-ep-circle-check style="font-size: 12px; margin-right: 2px" />{{
                          t('system.user.handleRole')
                        }}
                      </el-dropdown-item>
                      <el-dropdown-item command="invit">
                        <Icon icon="ep:link" />{{ t('sys.user.loginVer') }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>

          <template #pagination>
            <Pagination
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </template>
        </UmvTable>
      </el-col>
    </el-row>

    <!-- 添加或修改用户对话框 -->
    <UserForm ref="formRef" @success="getList" />
    <!-- 用户导入对话框 -->
    <UserImportForm ref="importFormRef" @success="getList" />
    <InvitCopyForm ref="invitCopyFormRef" />
  </UmvContent>
</template>
<script lang="tsx" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CommonStatusEnum } from '@/utils/constants'
import * as UserApi from '@/api/system/user'
import UserForm from './UserForm.vue'
import UserImportForm from './UserImportForm.vue'
import DeptTree from './DeptTree.vue'
import InvitCopyForm from './InvitCopyForm.vue'
import { useIcon } from '@/hooks/web/useIcon'
import { useRouter } from 'vue-router'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import UmvContent from '@/components/UmvContent'

defineOptions({ name: 'SystemUser' })

const router = useRouter()
const iconCheck = useIcon({ icon: 'ep:success-filled' })
const message = useMessage() // 消息弹窗
const { t, ifEn } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  nickname: undefined,
  username: undefined,
  mobile: undefined,
  status: undefined,
  deptId: undefined,
  createTime: []
})

// 查询表单配置
const queryOpts = ref<Record<string, QueryOption>>({
  username: {
    label: t('system.user.username'),
    defaultVal: undefined,
    controlRender: (form) => (
      <el-input
        v-model={form.username}
        placeholder={t('system.user.usernamePlaceholder')}
        clearable
        onKeyup={(e) => e.key === 'Enter' && handleQuery()}
      />
    )
  },
  nickname: {
    label: t('system.user.nickname'),
    defaultVal: undefined,
    controlRender: (form) => (
      <el-input
        v-model={form.nickname}
        placeholder={t('system.user.nicknamePlaceholder')}
        clearable
        onKeyup={(e) => e.key === 'Enter' && handleQuery()}
      />
    )
  },
  mobile: {
    label: t('system.user.mobile'),
    defaultVal: undefined,
    controlRender: (form) => (
      <el-input
        v-model={form.mobile}
        placeholder={t('system.user.mobilePlaceholder')}
        clearable
        onKeyup={(e) => e.key === 'Enter' && handleQuery()}
      />
    )
  },
  status: {
    label: t('system.user.status'),
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.status} placeholder={t('system.user.statusPlaceholder')} clearable>
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  createTime: {
    label: t('common.createTime'),
    defaultVal: [],
    controlRender: (form) => (
      <el-date-picker
        v-model={form.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        start-placeholder={t('common.startDateText')}
        end-placeholder={t('common.endDateText')}
      />
    )
  }
})

const queryFormRef = ref() // 搜索的表单

const deptTreeRef = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await UserApi.getUserPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  // queryFormRef.value?.resetFields()
  queryParams.value.deptId = undefined
  // 重置部门树的选中状态
  deptTreeRef.value?.resetCurrentKey()
  // handleQuery()
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.value.deptId = row.id
  await getList()
}

/** 处理部门被双击取消选中 */
const handleDeptNodeDblClick = async () => {
  // 当传入undefined时，表示取消选中
  queryParams.value.deptId = undefined
  await getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 修改用户状态 */
const handleStatusChange = async (row: UserApi.UserVO) => {
  try {
    // 修改状态的二次确认
    const text = row.status === CommonStatusEnum.ENABLE ? t('common.enable') : t('common.disable')
    const str = t('system.user.desc8', {
      text,
      username: row.username
    })
    await message.confirm(str) //'确认要' + text + '"' + row.username + '"用户吗?')

    // 发起修改状态
    await UserApi.updateUserStatus(row.id, row.status)
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.status =
      row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await UserApi.exportUser(queryParams.value)
    download.excel(data.data, t('system.user.roleDataXls') + '.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 操作分发 */
const handleCommand = (command: string, row: UserApi.UserVO) => {
  switch (command) {
    case 'handleDelete':
      handleDelete(row.id)
      break
    case 'handleResetPwd':
      handleResetPwd(row)
      break
    case 'handleRole':
      handleRole(row)
      break
    case 'invit':
      invit(row)
      break
    default:
      break
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserApi.deleteUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 重置密码 */
const handleResetPwd = async (row: UserApi.UserVO) => {
  try {
    // 重置的二次确认

    const str = t('system.user.desc9', {
      username: row.username
    })

    const result = await message.prompt(str, t('common.reminder'))
    const password = result.value
    // 发起重置
    await UserApi.resetUserPwd(row.id, password)
    message.success(t('system.user.desc2') + password)
  } catch {}
}

/** 分配角色 */
const handleRole = (row: UserApi.UserVO) => {
  // 修改为路由跳转
  router.push({
    name: 'UserAssignRole',
    params: { userId: row.id },
    query: { username: row.username, nickname: row.nickname }
  })
}

const invitCopyFormRef = ref()

// 邀请
const invit = async (row) => {
  try {
    loading.value = true
    const phoneRes = await UserApi.postGenerateAuthenticateShortlink({
      userId: row.id,
      authenticationType: 10
    })
    const mailRes = await UserApi.postGenerateAuthenticateShortlink({
      userId: row.id,
      authenticationType: 20
    })
    invitCopyFormRef.value.open(phoneRes, mailRes)
  } finally {
    loading.value = false
  }
}

const tableRef = ref()

// 解锁
const unlock = async () => {
  if (tableRef.value.getSelectionRows().length == 0) {
    message.error(t('sys.user.selectUnlockUser'))
    return
  }
  await message.confirm(t('sys.user.sureUnlockUser'))
  try {
    loading.value = true
    console.log('tableRef.value.getSelectionRows()', tableRef.value.getSelectionRows())
    const arr = tableRef.value.getSelectionRows().map((el) => {
      return el.id
    })
    await UserApi.unlockAccount(arr)
    message.success(t('common.handleSuccess'))
    getList()
  } finally {
    loading.value = false
  }
}

/** 初始化 */
onMounted(() => {
  getList()
})
</script>
