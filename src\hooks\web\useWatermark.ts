const domSymbol = Symbol('watermark-dom')

export function useWatermark(appendEl: HTMLElement | null = document.body) {
  let func: Fn = () => {}
  const id = domSymbol.toString()

  const clear = () => {
    const domId = document.getElementById(id)
    if (domId) {
      // Use a separate variable to avoid redeclaration and handle null
      const currentEl = appendEl
      if (currentEl) {
        currentEl.removeChild(domId)
      }
    }
    window.removeEventListener('resize', func)
  }

  const createWatermark = (str: string) => {
    clear()

    const can = document.createElement('canvas')
    const cans = can.getContext('2d')

    if (cans) {
      const font = '18px Vedana'
      cans.font = font
      const textMetrics = cans.measureText(str)
      const textWidth = textMetrics.width

      const angle = (-20 * Math.PI) / 180
      const canvasWidth =
        Math.abs(textWidth * Math.cos(angle)) + Math.abs(18 * Math.sin(angle)) + 160 // Add more padding
      const canvasHeight =
        Math.abs(textWidth * Math.sin(angle)) + Math.abs(18 * Math.cos(angle)) + 160 // Add more padding

      can.width = canvasWidth
      can.height = canvasHeight

      // Re-apply font after canvas resize
      cans.font = font
      cans.fillStyle = 'rgba(0, 0, 0, 0.15)'
      cans.textAlign = 'center'
      cans.textBaseline = 'middle'

      // Translate and rotate from the center
      cans.translate(can.width / 2, can.height / 2)
      cans.rotate(angle)
      cans.fillText(str, 0, 0)
    }

    const div = document.createElement('div')
    div.id = id
    div.style.pointerEvents = 'none'
    div.style.top = '0px'
    div.style.left = '0px'
    div.style.position = 'absolute'
    div.style.zIndex = '100000000'
    div.style.width = document.documentElement.clientWidth + 'px'
    div.style.height = document.documentElement.clientHeight + 'px'
    div.style.background = `url(${can.toDataURL('image/png')}) left top repeat`

    if (appendEl) {
      appendEl.appendChild(div)
    }

    return id
  }

  function setWatermark(str: string) {
    createWatermark(str)
    func = () => {
      createWatermark(str)
    }
    window.addEventListener('resize', func)
  }

  return { setWatermark, clear }
}
