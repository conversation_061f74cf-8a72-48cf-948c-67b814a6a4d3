/**資源管理 */
export default {
  resApp: {
    name: '編號',
    codePlaceholder: '請輸入角色名稱',
    applicationReasonPlaceholder: '請輸入申請原因',
    scene: '場景',
    permissionTypes: '權限點',
    exposeNo: '操作',
    resourceOwnerId: '資源所有者編號',
    resourceOwnerName: '資源所有者名稱',
    exposeRoleName: '角色',
    application: '申請',
    appOp: '申請操作',
    defaultResourceHolder: '默認權限持有人',
    defaultResourceHolderPlaceholder: '請選擇默認權限持有人',
    applicationReason: '申請原因',
    applicationSuccess: '申請成功'
  },
  approval: {
    apply: '我申請的',
    approval: '我審批的',
    name: '編號',
    codePlaceholder: '請輸入角色名稱',
    applicationReasonPlaceholder: '請輸入申請原因',
    scene: '場景',
    permissionTypes: '權限點',
    exposeNo: '編號',
    roleName: '角色名稱',
    applicantName: '申請者名稱',
    authorizerName: '審批者名稱',
    applicationNo: '申請編號',
    approvalOpinion: '審批意見',
    authStatusName: '狀態',
    applicationReason: '申請原因',
    approvalOpinionPlaceholder: '請輸入審批意見',
    reasonPlaceholder: '請輸入撤銷原因',
    resultPlaceholder: '請選擇是否通過',
    authStatus1: '審批',
    authStatus2: '撤回',
    result: '是否通過',
    reason: '撤銷原因',
    withdraw: '撤銷'
  }
}
