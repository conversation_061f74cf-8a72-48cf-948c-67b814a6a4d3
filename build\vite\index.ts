import { resolve } from 'path'
import Vue from '@vitejs/plugin-vue'
import VueJsx from '@vitejs/plugin-vue-jsx'
import WindiCSS from 'vite-plugin-windicss'
import progress from 'vite-plugin-progress'
import EslintPlugin from 'vite-plugin-eslint'
import { ViteEjsPlugin } from 'vite-plugin-ejs'
// @ts-ignore
import ElementPlus from 'unplugin-element-plus/vite' //为 Element Plus 按需引入样式。
//unplugin-vue-components对应会在根目录生成components.d.ts文件
import AutoImport from 'unplugin-auto-import/vite'
// unplugin-auto-import会默认在要目录生成auto-imports.d.ts文件，
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
// 图标按需加载、自动添加前缀和大小调整
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'

import PurgeIcons from 'vite-plugin-purge-icons'
import viteCompression from 'vite-plugin-compression'
import topLevelAwait from 'vite-plugin-top-level-await'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { visualizer } from 'rollup-plugin-visualizer' //打包视图分析工具
import { gitVersionPlugin } from './buildInfo' // 构建过程中向html插入版本信息
import VueDevTools from 'vite-plugin-vue-devtools'
import legacy from '@vitejs/plugin-legacy' //兼容旧浏览器

export function createVitePlugins() {
  const root = process.cwd()

  // 路径查找
  function pathResolve(dir: string) {
    return resolve(root, '.', dir)
  }

  return [
    Vue(),
    VueJsx(),
    WindiCSS(),
    progress(),
    PurgeIcons(),
    gitVersionPlugin(),
    ElementPlus({}),
    AutoImport({
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
        /\.md$/ // .md
      ],
      imports: [
        'vue', //自动导入vue常用api
        'vue-router',
        // 可额外添加需要 autoImport 的组件
        {
          '@/hooks/web/useI18n': ['useI18n'],
          '@/hooks/web/useMessage': ['useMessage'],
          '@/hooks/web/useTable': ['useTable'],
          '@/hooks/web/useCrudSchemas': ['useCrudSchemas'],
          '@/utils/formRules': ['required'],
          '@/utils/dict': [
            'DICT_TYPE',
            'getDictLabel',
            'getDictOptions',
            'getStrDictOptions',
            'getIntDictOptions',
            'getBoolDictOptions',
            'getDictObj'
          ]
        }
      ],
      dts: 'src/types/auto-imports.d.ts',
      // 自动导入element相关函数，如：ElMessage, ElMessageBox..
      resolvers: [ElementPlusResolver()],
      eslintrc: {
        enabled: false, // Default `false`
        filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
        globalsPropValue: true // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
      }
    }),
    Components({
      // 要搜索组件的目录的相对路径
      dirs: ['src/components'],
      // 组件的有效文件扩展名
      extensions: ['vue', 'md'],
      // 搜索子目录
      deep: true,
      include: [/\.vue$/, /\.vue\?vue/],
      // 生成自定义 `auto-components.d.ts` 全局声明
      dts: 'src/types/auto-components.d.ts',
      // ElementPlusResolver 自动导入 element相关组件
      //IconsResolver 解析器自动导入组件,必须遵循这个组件命名规则{prefix}-{collection}-{icon}
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          prefix: 'icon' // 例如图标: ep:xx   应该写: <icon-ep-xxx />
        })
      ],
      exclude: [/[\\/]node_modules[\\/]/, 'src/components/Common']
    }),
    //图标组件 自动引入
    Icons({
      compiler: 'vue3',
      defaultClass: 'icons',
      autoInstall: true
    }),
    EslintPlugin({
      cache: false,
      include: ['src/**/*.vue', 'src/**/*.ts', 'src/**/*.tsx'] // 检查的文件
    }),
    VueI18nPlugin({
      runtimeOnly: true,
      compositionOnly: true,
      include: [resolve(__dirname, 'src/locales/**')]
    }),
    createSvgIconsPlugin({
      iconDirs: [pathResolve('src/assets/svgs')],
      symbolId: 'icon-[dir]-[name]',
      svgoOptions: true
    }),
    viteCompression({
      verbose: true, // 是否在控制台输出压缩结果
      disable: false, // 是否禁用
      threshold: 10240, // 体积大于 threshold 才会被压缩,单位 b
      algorithm: 'gzip', // 压缩算法,可选 [ 'gzip' , 'brotliCompress' ,'deflate' , 'deflateRaw']
      ext: '.gz', // 生成的压缩包后缀
      deleteOriginFile: false, //压缩后是否删除源文件
      compressionOptions: { level: 9 } // 指定gzip压缩级别，默认为9（最高级别）
    }),
    ViteEjsPlugin(), //支持ejs语法
    //todo :在模块顶层中使用await  后续如果vite 支持将移除
    topLevelAwait({
      // https://juejin.cn/post/7152191742513512485
      // The export name of top-level await promise for each chunk module
      promiseExportName: '__tla',
      // The function to generate import names of top-level await promise in each chunk module
      promiseImportName: (i) => `__tla_${i}`
    }),
    legacy({
      targets: ['defaults', 'not IE 11']
    }),
    visualizer({
      open: true, //在默认用户代理中打开生成的文件
      gzipSize: true, // 收集 gzip 大小并将其显示
      brotliSize: true, // 收集 brotli 大小并将其显示
      filename: 'stats.html' // 分析图生成的文件名
    }),
    VueDevTools()
  ]
}
