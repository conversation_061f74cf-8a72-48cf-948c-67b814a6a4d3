<template>
  <div class="resource-application-apply">
    <!-- <ContentWrap>
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item :label="t('res.resApp.name')" prop="name">
          <el-input
            v-model="queryParams.code"
            class="!w-240px"
            clearable
            :placeholder="t('res.resApp.codePlaceholder')"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <icon-ep-search class="mr-5px" />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <icon-ep-refresh class="mr-5px" style="font-size: 12px" />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap> -->
    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list">
        <el-table-column type="expand">
          <template #default="props">
            <el-table :data="props.row.showResources" :border="true">
              <el-table-column align="center" :label="t('res.resApp.scene')" prop="scene">
                <template #default="{ row }">
                  {{ systemBusinessSceneMap[row.scene] }}
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                :label="t('res.resApp.permissionTypes')"
                prop="permissionTypes"
              >
                <template #default="{ row }">
                  {{ row.permissionTypes.map((el) => permissionPointMap[el]).join() }}
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="t('res.resApp.exposeNo')" prop="exposeNo" />
        <el-table-column
          align="center"
          :label="t('res.resApp.resourceOwnerId')"
          prop="resourceOwnerId"
        />
        <el-table-column
          align="center"
          :label="t('res.resApp.resourceOwnerName')"
          prop="resourceOwnerName"
        />
        <el-table-column
          align="center"
          :label="t('res.resApp.exposeRoleName')"
          prop="exposeRoleName"
        />
        <el-table-column align="center" :label="t('res.resApp.exposeNo')" prop="exposeNo">
          <template #default="{ row }">
            <el-button link type="primary" @click="showApplication(row)">{{
              t('res.resApp.application')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </ContentWrap>
    <Dialog :title="t('res.resApp.appOp')" v-model="visible" width="600">
      <el-form :model="formData" label-width="140px" ref="formRef">
        <el-form-item
          :label="t('res.resApp.defaultResourceHolder')"
          prop="defaultResourceHolder"
          :rules="{
            required: true,
            trigger: ['blur', 'change'],
            message: t('res.resApp.defaultResourceHolderPlaceholder')
          }"
        >
          <el-select v-model="formData.defaultResourceHolder">
            <el-option
              v-for="(item, index) in userList"
              :key="index"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('res.resApp.applicationReason')">
          <el-input
            v-model="formData.applicationReason"
            :placeholder="t('res.resApp.applicationReasonPlaceholder')"
            type="textarea"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button type="primary" @click="application">{{ t('common.ok') }}</el-button>
        <el-button @click="close">{{ t('common.cancel') }}</el-button>
      </template>
    </Dialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ResourceApplicationApply'
})

import {
  getResourceExposePage,
  resourceRowVO,
  updateTag,
  getDataPermissionScope
} from '@/api/system/resource'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { getSimpleUserList } from '@/api/system/user'
const { t } = useI18n() // 国际化
const loading = ref(false)

const list: Ref<Array<resourceRowVO>> = ref([])

const formRef = ref()

const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  code: undefined
})

// 转业务场景为map，查名字用
const systemBusinessSceneMap = computed(() => {
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

const permissionPointMap = computed(() => {
  return getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

const total = ref(0)

const handleQuery = async () => {
  try {
    loading.value = true
    const res = await getResourceExposePage({ ...queryParams.value })
    console.log('res', res)
    const tempList = res.list
    total.value = res.total
    list.value = tempList.map((el) => {
      let resourcesMap = {}
      let showResources = []
      el.resources.forEach((e) => {
        if (!resourcesMap[e.scene]) {
          resourcesMap[e.scene] = [e.permissionType]
        } else {
          resourcesMap[e.scene].push(e.permissionType)
        }
      })
      for (let key in resourcesMap) {
        showResources.push({
          scene: key,
          permissionTypes: resourcesMap[key]
        })
      }
      return {
        ...el,
        showResources
      }
    })
  } catch (e) {
  } finally {
    loading.value = false
  }
}

const resetQuery = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    code: undefined
  }
  handleQuery()
}

const visible = ref(false)

const activeRow = ref()

// 用户自身的权限信息
const ownDataPermissionScope = ref()

// 打开申请窗口
const showApplication = (row) => {
  activeRow.value = row
  visible.value = true
}

const formData = ref({
  defaultResourceHolder: undefined,
  applicationReason: undefined
})

const close = () => {
  formData.value = {
    defaultResourceHolder: undefined,
    applicationReason: undefined
  }
  visible.value = false
}

// 申请资源
const application = async () => {
  await formRef.value.validate()
  try {
    loading.value = true
    await updateTag({
      ...formData.value,
      exposeNo: activeRow.value.exposeNo,
      authorizer: activeRow.value.resourceOwnerId,
      defaultResourceHolderName: userMap.value[formData.value.defaultResourceHolder]
    })
    ElMessage.success(t('res.resApp.applicationSuccess'))
    close()
  } finally {
    loading.value = false
  }
}

// 获取当前用户的数据权限
const getOwnDataPermissionScope = async () => {
  try {
    const res = await getDataPermissionScope()
    ownDataPermissionScope.value = {
      authorizer: '49003',
      defaultResourceHolder: 1000
    }
  } catch {
    ownDataPermissionScope.value = {
      authorizer: '49003',
      defaultResourceHolder: 1000
    }
  } finally {
  }
}

const userList = ref([])

const userMap = ref({})

// 获取客户列表
const getSimpleUserListFn = async () => {
  const res = await getSimpleUserList()
  userList.value = res
  userMap.value = userList.value.reduce((a, b) => {
    return { ...a, [b.id]: b.nickname }
  }, {})
}

onMounted(() => {
  handleQuery()
  getSimpleUserListFn()
})
</script>
