<template>
  <UmvContent>
    <!-- 搜索工作栏 -->
    <template #search>
      <UmvQuery
        ref="queryFormRef"
        v-model="queryParams"
        :opts="queryOpts"
        @check="handleQuery"
        @reset="getList"
      />
    </template>

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="getList">
      <!-- 自定义工具栏 -->
      <template #tools>
        <el-button
          type="primary"
          plain
          size="small"
          @click="() => openForm('create')"
          v-hasPermi="['bpm:user-group:create']"
        >
          <icon-ep-plus style="font-size: 12px" class="mr-5px" /> 新增
        </el-button>
      </template>

      <!-- 分页区域 -->
      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单弹窗：添加/修改 -->
    <UserGroupForm ref="formRef" @success="getList" />
  </UmvContent>
</template>

<script setup lang="tsx">
defineOptions({
  name: 'BpmUserGroup'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as UserGroupApi from '@/api/bpm/userGroup'
import * as UserApi from '@/api/system/user'
import UserGroupForm from './UserGroupForm.vue'
import { UserVO } from '@/api/system/user'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { checkPermi } from '@/utils/permission'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const userList = ref<UserVO[]>([]) // 用户列表
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: '',
  status: undefined,
  createTime: []
})

// 搜索条件配置
const queryOpts = ref<Record<string, QueryOption>>({
  name: {
    label: '组名',
    defaultVal: '',
    controlRender: (form) => <el-input v-model={form.name} placeholder="请输入组名" clearable />
  },
  status: {
    label: '状态',
    defaultVal: undefined,
    controlRender: (form) => (
      <el-select v-model={form.status} placeholder="请选择状态" clearable>
        {getIntDictOptions(DICT_TYPE.COMMON_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  createTime: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form) => (
      <el-date-picker
        v-model={form.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        clearable
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
      />
    )
  }
})

const queryFormRef = ref() // 搜索的表单

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: '编号', align: 'center' },
  { prop: 'name', label: '组名', align: 'center' },
  { prop: 'description', label: '描述', align: 'center', showOverflowTooltip: true },
  {
    prop: 'memberUserIds',
    label: '成员',
    align: 'center',
    showOverflowTooltip: true,
    renderTemplate: (scope) => (
      <>
        {scope.row.memberUserIds.map((userId) => (
          <span key={userId} class="pr-5px">
            {userList.value.find((user) => user.id === userId)?.nickname}
          </span>
        ))}
      </>
    )
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    renderTemplate: (scope) => <dict-tag type={DICT_TYPE.COMMON_STATUS} value={scope.row.status} />
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: '操作',
    align: 'center',
    renderTemplate: (scope) => (
      <>
        {checkPermi(['bpm:user-group:update']) && (
          <el-button link type="primary" onClick={() => openForm('update', scope.row.id)}>
            编辑
          </el-button>
        )}
        {checkPermi(['bpm:user-group:delete']) && (
          <el-button link type="danger" onClick={() => handleDelete(scope.row.id)}>
            删除
          </el-button>
        )}
      </>
    )
  }
])

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await UserGroupApi.getUserGroupPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserGroupApi.deleteUserGroup(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  // 加载用户列表
  userList.value = await UserApi.getSimpleUserList()
})
</script>
