/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJ<PERSON> <EMAIL>
 * @LastEditTime: 2025-04-14 16:44:22
 * @Description:
 */
import request from '@/config/axios'

export type TaskVO = {
  id: number
}

//审批人选择弹出框
export enum handleTypeEnum {
  /** 转办*/
  Forward = 'Forward',
  /** 委派*/
  Delegate = 'Delegate',
  /**抄送3 */
  CC = 'CC'
}

//抄送列表查询
export const getCcTaskPage = async (data) => {
  return await request.post({ url: '/bpm/task/cc/of/user', data })
}
export const getTodoTaskPage = async (params) => {
  return await request.get({ url: '/bpm/task/todo-page', params })
}

export const getDoneTaskPage = async (params) => {
  return await request.get({ url: '/bpm/task/done-page', params })
}
export const completeTask = async (data) => {
  return await request.put({ url: '/bpm/task/complete', data })
}

/**
 * 任务审批请求参数类型
 */
export type TaskApproveReqVO = {
  /** 任务编号 */
  id: string
  /** 审批意见 */
  reason: string
  /** 抄送用户ID列表 */
  ccUserIds: number[]
  /** 流程变量 */
  variables?: Record<string, any>
  /** 审批人类型 */
  assigneeType: number
  /** 审批人选项ID列表 */
  assigneeOptions: string[] | number[]
  /** 审批人选项对象列表 */
  assigneeOptionObjects?: Array<{
    /** 选项ID */
    optionId: string
    /** 租户ID */
    tenantId: string
  }>
}
export const approveTask = async (data: TaskApproveReqVO) => {
  return await request.put({ url: '/bpm/task/approve', data })
}

export const rejectTask = async (data) => {
  return await request.put({ url: '/bpm/task/reject', data })
}
export const backTask = async (data) => {
  return await request.put({ url: '/bpm/task/back', data })
}
/**转办 */
export const updateTaskAssignee = async (data) => {
  return await request.put({ url: '/bpm/task/update-assignee', data })
}
/**委派 */
export interface delegateReqType {
  /***任务编号 */
  id: string
  /**委派理由 */
  delegateReason: string
  /**被委派用户编号 */
  userId: string
}
export const delegate = async (data: delegateReqType) => {
  return await request.post({ url: '/bpm/task/delegate', data })
}

/**获取流程实例的任务列表 */
export const getTaskListByProcessInstanceId = async (processInstanceId) => {
  return await request.get({
    url: '/bpm/task/list-by-process-instance-id?processInstanceId=' + processInstanceId
  })
}

// 导出任务
export const exportTask = async (params) => {
  return await request.download({ url: '/bpm/task/export', params })
}

//我委派的任务记录列表
export const getDelegateOwnerList = async (data) => {
  return await request.post({ url: '/bpm/task/page/delegate/of/owner', data })
}

//委派我的任务记录列表
export const getDelegateAssigneeList = async (data) => {
  return await request.post({ url: '/bpm/task/page/delegate/of/assignee', data })
}
