export type UserLoginVO = {
  username: string
  password: string
  captchaVerification: string
}

export type TokenType = {
  id: number // 编号
  accessToken: string // 访问令牌
  refreshToken: string // 刷新令牌
  userId: number // 用户编号
  userType: number //用户类型
  clientId: string //客户端编号
  expires_in: number //过期时间
  first: boolean //是否第一次登录后重置了密码
}

export interface UrlTokenVO {
  id: number // 编号
  accessToken?: string // 访问令牌
  refreshToken?: string // 刷新令牌
  userId: number // 用户编号
  userType: number //用户类型
  clientId: string //客户端编号
  expires_in: number | string //过期时间
  first?: boolean //是否第一次登录后重置了密码
  tenantId: string //租户Id
  roleId: string //角色Id
  lang: LocaleType
}

export type UserVO = {
  id: number
  username: string
  nickname: string
  deptId: number
  email: string
  mobile: string
  sex: number
  avatar: string
  loginIp: string
  loginDate: string
}

export type OAuth2OpenAccessTokenRespType = {
  /**
   * 过期时间,单位：秒
   */
  expires_in?: number

  /**
   * 授权范围,如果多个授权范围，使用空格分隔
   */
  scope?: string
  /**
   * 令牌类型
   */
  token_type?: string
  /**
   * 访问令牌
   */
  access_token?: string
  /**
   * 刷新令牌
   */
  refresh_token?: string
  /**
   * 访问令牌
   */
  accessToken?: string
  /**
   * 刷新令牌
   */
  refreshToken?: string

  loginInfo?: {
    loginFirstTime: boolean
    tenantId: string
    userId: string
  }
}

/**单点登录url携带参数 */
export interface OauthUrlQuery {
  id?: number // 编号
  accessToken: string // 访问令牌
  access_token: string // 访问令牌
  refreshToken: string // 刷新令牌
  refresh_token: string // 刷新令牌
  userId?: number // 用户编号
  userType?: number //用户类型
  clientId: string //客户端编号
  expiresTime: number | string //过期时间
  expires_in: number | string // 过期时间,单位：秒
  tenantId: string //租户Id
  roleId: string //角色Id
  lang: LocaleType
  goPath?: string //跳转路径
}
