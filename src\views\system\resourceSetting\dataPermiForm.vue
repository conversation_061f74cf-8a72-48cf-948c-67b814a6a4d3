<template>
  <Dialog
    v-model="dialogVisible"
    :title="isAdvanced ? t('system.role.advanceDataPerm') : t('system.role.dataPerm')"
    width="80%"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :rules="rules"
      :model="formData"
      label-width="140px"
    >
      <el-form-item :label="t('system.role.name')">
        <el-tag @click="test">{{ formData.name }}</el-tag>
      </el-form-item>
      <el-form-item :label="t('system.role.code')">
        <el-tag>{{ formData.code }}</el-tag>
      </el-form-item>
      <el-form-item :label="t('system.role.autoAuthRule')" prop="autoAuthRuleIds">
        <el-select v-model="formData.autoAuthRuleIds" multiple>
          <el-option
            v-for="(item, index) in rulesList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-form-item :label="t('system.role.dataScope')" style="display: flex">
      <el-card class="card" shadow="never">
        <div style="width: 1000px; height: 400px">
          <el-auto-resizer>
            <template #default="{ height, width }">
              <el-table-v2
                :data="treeOptions"
                :height="height"
                ref="tableRef"
                :width="width"
                row-key="id"
                :columns="columns"
                :expand-column-key="'name'"
                v-model:expanded-row-keys="expandedRowKeys"
                :default-expanded-row-keys="defaultExpandedRowKeys"
                size="small"
                :estimated-row-height="40"
                fixed
              />
            </template>
          </el-auto-resizer>
        </div>
      </el-card>
    </el-form-item>
    <template #footer>
      <el-button
        :disabled="formLoading"
        type="primary"
        @click="submitForm"
        v-if="roleTypeStr !== '系统角色'"
        >{{ t('common.ok') }}</el-button
      >
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
    <Dialog
      v-model="authorityPointsVisiable"
      @close="closeAuthorityPointsChange"
      :title="t('system.role.desc6')"
    >
      <el-checkbox-group v-model="editAuthorityPoints">
        <el-checkbox
          v-for="(it, index) in getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS)"
          :key="index"
          :label="it.value"
          >{{ it.label }}</el-checkbox
        >
      </el-checkbox-group>

      <template #footer>
        <el-button
          type="primary"
          @click="submitAuthorityPointsChange"
          v-if="roleTypeStr !== '系统角色'"
          >{{ t('common.ok') }}</el-button
        >
        <el-button @click="closeAuthorityPointsChange">{{ t('common.cancel') }}</el-button>
      </template>
    </Dialog>
  </Dialog>
</template>
<script setup lang="tsx">
defineOptions({
  name: 'SystemRoleDataPermissionForm'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { defaultProps, handleTree } from '@/utils/tree'
import { SystemDataScopeEnum } from '@/utils/constants'
import * as RoleApi from '@/api/system/role'
import * as DeptApi from '@/api/system/dept'
import * as PermissionApi from '@/api/system/permission'
import { getTenantListTree, getRoleCustomerScopes } from '@/api/system/tenant'
import { getTenantId } from '@/utils/auth'
import { Plus, Minus, Edit } from '@element-plus/icons-vue'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const roleTypeStr = ref('')
const roleTypeEnum = ['系统角色', '自定义角色']

const isAdvanced = ref(false)

// 410724

const expandedRowKeys = ref(['id'])
const defaultExpandedRowKeys = ref(['goldpac.com'])

const test = () => {
  // defaultExpandedRowKeys.value.push('410724')
  console.log(456456456)
}

const columns = computed(() => {
  return [
    {
      dataKey: 'name',
      key: 'name',
      width: 460,
      title: '租户名称',
      fixed: 'left'
    },
    {
      width: 460,
      title: '按钮',
      cellRenderer: ({ rowData }) => (
        <span style={{ color: 'red', cursor: 'pointer' }} onClick={() => test()}>
          全局（可读、可写、可查看）
        </span>
      )
    }
  ]
})

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
let formData = reactive({
  id: 0,
  name: '',
  code: '',
  dataScope: undefined,
  autoAuthRuleIds: undefined,
  dataScopeDeptIds: []
})

const originFormData = {
  id: 0,
  name: '',
  code: '',
  dataScope: undefined,
  autoAuthRuleIds: undefined,
  dataScopeDeptIds: []
}

const rules = ref({
  dataScope: [{ required: true, message: t('system.role.rDesc1'), trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref
const treeOptions = ref<any[]>([]) // 部门树形结构
const deptExpand = ref(false) // 展开/折叠
const treeRef = ref() // 菜单树组件 Ref
const tableRef = ref() // 权限表格组件 Ref
const treeNodeAll = ref(false) // 全选/全不选
const checkStrictly = ref(true) // 是否严格模式，即父子不关联

const managementCustomerDataScope = ref()
const deptListDataScope = ref()
const roleCustomerScopesIds = ref([])
const dataScopeDeptIds = ref([])

const changeDataScope = (_val) => {
  let depts: any = []
  if (_val === SystemDataScopeEnum.MANAGEMENT_CUSTOMER) {
    treeOptions.value = managementCustomerDataScope.value
    depts = roleCustomerScopesIds.value
  } else {
    treeOptions.value = deptListDataScope.value
    depts = dataScopeDeptIds.value
  }
  nextTick(() => {
    depts?.forEach((deptId: number) => {
      if (
        formData.dataScope !== SystemDataScopeEnum.DEPT_CUSTOM &&
        formData.dataScope !== SystemDataScopeEnum.MANAGEMENT_CUSTOMER
      )
        return
      treeRef.value.setChecked(deptId, true, false)
    })
  })
}

const systemDataScope = computed(() => {
  return isAdvanced.value
    ? getTenantId() === 'goldpac.com'
      ? getIntDictOptions(DICT_TYPE.SYSTEM_DATA_SCOPE_ADVANCED)
      : // : getIntDictOptions(DICT_TYPE.SYSTEM_DATA_SCOPE_ADVANCED).filter((el) => el.value !== 1)
        getIntDictOptions(DICT_TYPE.SYSTEM_DATA_SCOPE_ADVANCED)
    : getIntDictOptions(DICT_TYPE.SYSTEM_DATA_SCOPE)
})

// 表格选中回显
const checkRows = (checkIds, rows) => {
  rows.forEach((el) => {
    if (checkIds.indexOf(el.id) === -1) {
      // tableRef.value.toggleRowSelection(el, false)
    } else {
      // tableRef.value.toggleRowSelection(el, true)
    }
    if (el.children) {
      checkRows(checkIds, el.children)
    }
  })
}

const rulesList = ref([])

const getRules = async () => {
  rulesList.value = await RoleApi.getResourceAutomaticAuthorizationRule()
}

/** 打开弹窗 */
const open = async (row: RoleApi.RoleVO, isAd = false, _tenantList) => {
  getRules()
  formData = reactive({ ...originFormData })
  isAdvanced.value = isAd
  dialogVisible.value = true
  resetForm()
  // 加载 Dept 列表。注意，必须放在前面，不然下面 setChecked 没数据节点

  // 设置数据
  formData.id = row.id
  formData.name = row.name
  formData.code = row.code
  formData.autoAuthRuleIds = row.autoAuthRuleIds
  roleTypeStr.value = roleTypeEnum[row.type - 1] //判断是否内置角色，如果是就不给编辑

  managementCustomerDataScope.value = initTreeData(
    await RoleApi.getListResourceListTenantSceneTreesProxy(row.id, _tenantList)
  ) //指定客户数据权限
  // deptListDataScope.value = handleTree(await DeptApi.getSimpleDeptList()) //指定部门数据权限

  // 现在指定客户权限（6）需要和其他1-5独立开来：普通数据权限选中时，高级数据权限中的指定客户权限需要能够选中和展示；但是选中全部（1）时，就不显示并且普通数据权限显示未未选中
  // 因此，普通数据权限弹窗时，判断一下dataScope在不在数据里就可以了，但是高级数据权限弹窗，需要需要判断一下是不是全部数据权限，只要不是全部数据权限，都按指定客户权限（6）处理
  // 后端接收到scope为6时，会判断原有scope是否为1或空，是的话会覆盖，否则不会覆盖

  if (
    !systemDataScope.value.find((_el) => {
      return row.dataScope === _el.value
    }) &&
    !isAdvanced.value
  ) {
    return
  }

  formData.dataScope = row.dataScope

  roleCustomerScopesIds.value =
    formData.dataScope !== SystemDataScopeEnum.ALL
      ? // ? (await getRoleCustomerScopes(row.id)).map((el) => el.customerId)
        []
      : [] //指定客户数据权限的选择框

  // 所以在高级数据权限弹窗时需要判断dataScope是否为1，不是的话全都转为6，显示客户，提交也是按6提交
  if (isAdvanced.value && formData.dataScope !== SystemDataScopeEnum.ALL) {
    formData.dataScope = 6
  }

  dataScopeDeptIds.value =
    formData.dataScope === SystemDataScopeEnum.DEPT_CUSTOM ? row.dataScopeDeptIds : [] //指定部门数据权限的选择框

  treeOptions.value =
    formData.dataScope === SystemDataScopeEnum.MANAGEMENT_CUSTOMER
      ? managementCustomerDataScope.value
      : deptListDataScope.value

  let depts: any = []

  if (formData.dataScope === SystemDataScopeEnum.MANAGEMENT_CUSTOMER) {
    depts = roleCustomerScopesIds.value
  } else {
    depts = dataScopeDeptIds.value
  }

  nextTick(() => {
    if (formData.dataScope !== 6) {
      depts?.forEach((deptId: number) => {
        treeRef.value.setChecked(deptId, true, false)
      })
    } else {
      checkRows(depts, treeOptions.value)
    }
  })
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const recursionHandleSubDataFn = (options, resources) => {
  options.forEach((el) => {
    let scopes = []
    if (el.children && el.children.length > 0) {
      recursionHandleSubDataFn(el.children, resources)
    }
    for (let key in el.resourcesMap) {
      el.resourcesMap[key].forEach((e) => {
        scopes.push({
          scene: key,
          permissionType: e,
          resourceType: 2
        })
      })
    }
    resources.push({
      resourceId: el.id,
      scopes: scopes
    })
  })
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  let validateStatus = await formRef.value.validate()

  if (!validateStatus) return // 验证不通过直接返回
  if (roleTypeStr.value === '系统角色') {
    ElMessage.error(t('system.role.msg1'))
    return
  }
  formLoading.value = true
  try {
    if (formData.dataScope === 6) {
      let resources = []
      recursionHandleSubDataFn(treeOptions.value, resources)
      await RoleApi.assignRoleResourceScope({
        roleCode: formData.code,
        autoAuthRuleIds: formData.autoAuthRuleIds,
        resources: resources
      })
      ElMessage.success(t('common.handleSuccess'))
      dialogVisible.value = false
      // 发送操作成功的事件
      emit('success')
    } else {
      const data = {
        roleId: formData.id,
        dataScope: formData.dataScope,
        dataScopeDeptIds:
          formData.dataScope !== SystemDataScopeEnum.DEPT_CUSTOM
            ? []
            : formData.dataScope === 6
            ? tableRef.value.getSelectionRows().map((el) => el.id)
            : treeRef.value.getCheckedKeys(false),
        dataScopeManageCustomerIds:
          formData.dataScope !== SystemDataScopeEnum.MANAGEMENT_CUSTOMER
            ? []
            : formData.dataScope === 6
            ? tableRef.value.getSelectionRows().map((el) => el.id)
            : treeRef.value.getCheckedKeys(false)
      }
      await PermissionApi.assignRoleDataScope(data)
      message.success(t('common.updateSuccess'))
      dialogVisible.value = false
      // 发送操作成功的事件
      emit('success')
    }
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  // 重置选项
  treeNodeAll.value = false
  deptExpand.value = false
  checkStrictly.value = true
  // 重置表单
  formData.value = {
    id: 0,
    name: '',
    code: '',
    dataScope: undefined,
    dataScopeDeptIds: []
  }
  treeRef.value?.setCheckedNodes([])
  tableRef.value?.clearSelection()
  formRef.value?.resetFields()
}

// 是否显示弹窗
const authorityPointsVisiable = ref(false)

/*************************************** 高级数据权限改造start *****************************************/

const sceneList = computed(() => {
  return getStrDictOptions(DICT_TYPE.SYSTEM_BUSINESS_SCENE) || []
})

// 初始化处理树结构数据
const initTreeData = (tree) => {
  console.log('tree', tree)
  return tree.map((el) => {
    if (el.children) {
      el.children = initTreeData(el.children)
    }
    if (!el.resources) {
      el.resourcesMap = {}
    } else {
      let tempResourcesMap = {}
      el.resources.forEach((e) => {
        if (!tempResourcesMap[e.scene]) {
          tempResourcesMap[e.scene] = [e.permissionType]
        } else {
          tempResourcesMap[e.scene].push(e.permissionType)
        }
      })
      el.resourcesMap = tempResourcesMap
    }
    sceneList.value.forEach((e) => {
      if (!el.resourcesMap[e.value]) {
        el.resourcesMap[e.value] = []
      }
    })
    return el
  })
}

const permiPointMap = computed(() => {
  return getIntDictOptions(DICT_TYPE.SYSTEM_PERMISSION_POINTS).reduce((a, b) => {
    return { ...a, [b.value]: b.label }
  }, {})
})

// 获取数组名称组合
const authorityPointsStr = (arr) => {
  if (!arr || arr.length === 0) return
  return arr.reduce((a, b, index) => {
    if (index === arr.length - 1) {
      return a + permiPointMap.value[b]
    } else {
      return a + permiPointMap.value[b] + ','
    }
  }, '')
}

// 关闭权限点修改弹窗
const closeAuthorityPointsChange = () => {
  authorityPointsVisiable.value = false
}

// 提交权限逻辑修改
const submitAuthorityPointsChange = async () => {
  try {
    console.log('editAuthorityPoints.value', editAuthorityPoints.value)
    editRow.value.resourcesMap[editAuthorityKey.value] = editAuthorityPoints.value
    authorityPointsVisiable.value = false
  } finally {
    closeAuthorityPointsChange()
  }
}

const editRow = ref()

// 当前编辑的权限点
const editAuthorityPoints: Ref<Array<number | string | never>> = ref([])

const editAuthorityKey = ref()

// 打开权限选择弹窗
const openAuthorityPoints = (row, key) => {
  // 洗数据
  editRow.value = undefined
  editAuthorityPoints.value = []
  editAuthorityKey.value = undefined

  console.log('...row', row)

  editRow.value = row
  editAuthorityKey.value = key
  editAuthorityPoints.value = [...row.resourcesMap[key]]
  authorityPointsVisiable.value = true
}

/*************************************** 高级数据权限改造end *****************************************/
</script>
<style lang="scss" scoped></style>
