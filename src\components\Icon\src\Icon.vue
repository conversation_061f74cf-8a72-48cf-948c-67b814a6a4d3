<script setup lang="ts">
defineOptions({
  name: 'Icon'
})

import { propTypes } from '@/utils/propTypes'
// import Iconify from '@iconify/iconify'
//引入iconify的Icon组件
import { Icon } from '@iconify/vue'
import { useDesign } from '@/hooks/web/useDesign'

//离线加载icon包
// import epIcon from '@iconify/json/json/ep.json'
// import faIcon from '@iconify/json/json/fa.json'
// import faSolidIcon from '@iconify/json/json/fa-solid.json'
// import ionIcon from '@iconify/json/json/ion.json'
// import mdiIcon from '@iconify/json/json/mdi.json'
// import tablerIcon from '@iconify/json/json/tabler.json'
// import zmdiIcon from '@iconify/json/json/zmdi.json'
// import antDesignIcon from '@iconify/json/json/ant-design.json'
// Iconify.addCollection(epIcon)
// Iconify.addCollection(faIcon)
// Iconify.addCollection(faSolidIcon)
// Iconify.addCollection(ionIcon)
// Iconify.addCollection(mdiIcon)
// Iconify.addCollection(tablerIcon)
// Iconify.addCollection(zmdiIcon)
// Iconify.addCollection(antDesignIcon)

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('icon')

const props = defineProps({
  // icon name
  icon: propTypes.string,
  // icon color
  color: propTypes.string,
  // icon size
  size: propTypes.number.def(16),
  // icon svg class
  svgClass: propTypes.string.def('')
})

const elRef = ref<ElRef>(null)

const isLocal = computed(() => props.icon.startsWith('svg-icon:'))

const symbolId = computed(() => {
  return unref(isLocal) ? `#icon-${props.icon.split('svg-icon:')[1]}` : props.icon
})

const getIconifyStyle = computed(() => {
  const { color, size } = props
  return {
    fontSize: `${size}px`,
    color
  }
})

const getSvgClass = computed(() => {
  const { svgClass } = props
  return `iconify ${svgClass}`
})
</script>

<template>
  <ElIcon :class="prefixCls" :color="color" :size="size">
    <svg v-if="isLocal" :class="getSvgClass" aria-hidden="true">
      <use :xlink:href="symbolId" />
    </svg>

    <span v-else ref="elRef" :class="$attrs.class" :style="getIconifyStyle">
      <Icon :icon="symbolId" />
    </span>
  </ElIcon>
</template>
