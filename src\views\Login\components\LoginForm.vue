<template>
  <el-form
    v-show="getShow"
    ref="formLogin"
    :model="loginData.loginForm"
    :rules="LoginRules"
    class="login-form"
    label-position="top"
    label-width="120px"
    size="large"
    :validate-on-rule-change="false"
  >
    <el-row class="mx-[-10px]">
      <el-col :span="24" class="px-[10px]">
        <el-form-item>
          <LoginFormTitle style="width: 100%" />
        </el-form-item>
      </el-col>

      <el-tabs class="w-full mt-2" v-model="tabName" @tab-change="tabChange" :stretch="true">
        <el-tab-pane
          :label="t('sys.login.pwdLogin')"
          name="pwdLogin"
          v-if="
            tabName !== 'ramLogin' &&
            (loginMethodsSet.has(LoginMethodsEnum.MAILPWD) ||
              loginMethodsSet.has(LoginMethodsEnum.MOBILEPWD))
          "
        >
          <el-col :span="24" class="px-[10px]">
            <el-form-item prop="account">
              <el-input
                v-model="loginData.loginForm.account"
                :placeholder="t('sys.login.inputMobileOrMail')"
                name="account"
                @change="(e) => (loginData.loginForm.account = e)"
                @blur="checkValidate('account')"
                @keyup.enter="getCode()"
              >
                <template #prefix>
                  <icon-ep-avatar />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="px-[10px]">
            <el-form-item prop="password">
              <el-input
                v-model="loginData.loginForm.password"
                :placeholder="t('login.passwordPlaceholder')"
                show-password
                type="password"
                @blur="checkValidate('password')"
                @keyup.enter="getCode()"
                :validate-event="false"
              >
                <template #prefix>
                  <icon-ep-lock />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="px-[10px]">
            <el-form-item prop="captchaVerification">
              <el-input
                v-model="loginData.loginForm.captchaVerification"
                :placeholder="t('sys.login.smsPlaceholder')"
                @keyup.enter="getCode()"
                :validate-event="false"
                @blur="checkValidate('captchaVerification')"
                class="code-img-input"
              >
                <template #suffix>
                  <!-- <el-image @click.stop="getCaptcha" class="code-img" :src="codeImg" /> -->
                  <div class="code-img" @click.stop="getCaptcha">
                    <el-image class="w-full h-full" :src="codeImg" />
                  </div>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-tab-pane>
        <el-tab-pane
          :label="t('sys.login.verLogin')"
          name="verificationCodeLogin"
          v-if="
            tabName !== 'ramLogin' &&
            (loginMethodsSet.has(LoginMethodsEnum.MAIL) ||
              loginMethodsSet.has(LoginMethodsEnum.MOBILE))
          "
        >
          <el-col :span="24" class="px-[10px]">
            <el-form-item prop="account">
              <el-input
                v-model="loginData.loginForm.account"
                :placeholder="t('sys.login.inputMobileOrMail')"
                name="account"
                autocomplete="on"
                @change="(e) => (loginData.loginForm.account = e)"
                @keyup.enter="getCode()"
                @blur="checkValidate('account')"
              >
                <template #prefix>
                  <icon-ep-avatar />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- 邮箱验证码 -->
          <el-col :span="24" class="px-[10px]">
            <el-form-item prop="code">
              <div class="code-warp">
                <el-input
                  v-model="loginData.loginForm.code"
                  :placeholder="t('sys.login.smsPlaceholder')"
                  @keyup.enter="getCode()"
                  @blur="checkValidate('code')"
                  :validate-event="false"
                  class="code-img-input"
                />
                <el-button
                  v-if="loginWay == 'mail'"
                  :loading="loadingMail"
                  :disabled="
                    mailTime !== defaultStr ||
                    useMoblieOrEmail(loginData.loginForm.account) !== 'email'
                  "
                  class="mt-5px mb-5px ml-5px"
                  @click="getMailCode"
                  >{{
                    mailTime === defaultStr ? defaultStr : `${mailTime}${t('sys.login.secSend')}`
                  }}
                </el-button>
                <el-button
                  v-else
                  :disabled="
                    phoneTime !== defaultStr ||
                    useMoblieOrEmail(loginData.loginForm.account) !== 'mobile' ||
                    !loginMethodsSet.has(LoginMethodsEnum.MOBILE)
                  "
                  :loading="loadingPhone"
                  class="mt-5px mb-5px ml-5px"
                  @click="getPhoneCode"
                >
                  {{
                    phoneTime === defaultStr ? defaultStr : `${phoneTime}${t('sys.login.secSend')}`
                  }}
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-tab-pane>
        <div v-if="tabName == 'ramLogin'">
          <el-col :span="24" class="px-[10px]">
            <el-form-item prop="tenantId">
              <el-input
                v-model="loginData.loginForm.tenantId"
                :placeholder="t('sys.login.inputMainAccount')"
                @blur="checkValidate('tenantId')"
                @keyup.enter="getCode()"
                type="primary"
              >
                <template #prefix>
                  <icon-ep-house />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="px-[10px]">
            <el-form-item prop="username">
              <el-input
                v-model="loginData.loginForm.username"
                :placeholder="t('sys.login.accountPlaceholder')"
                name="username"
                type="username"
                @change="(e) => (loginData.loginForm.username = e)"
                @blur="checkValidate('username')"
                @keyup.enter="getCode()"
                :validate-event="false"
              >
                <template #prefix>
                  <icon-ep-avatar />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="px-[10px]">
            <el-form-item prop="password">
              <el-input
                v-model="loginData.loginForm.password"
                :placeholder="t('login.passwordPlaceholder')"
                show-password
                type="password"
                @blur="checkValidate('password')"
                @keyup.enter="getCode()"
                :validate-event="false"
              >
                <template #prefix>
                  <icon-ep-lock />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="px-[10px]">
            <el-form-item prop="captchaVerification">
              <el-input
                v-model="loginData.loginForm.captchaVerification"
                :placeholder="t('sys.login.smsPlaceholder')"
                @keyup.enter="getCode()"
                :validate-event="false"
                @blur="checkValidate('captchaVerification')"
                class="code-img-input"
              >
                <template #suffix>
                  <!-- <el-image @click.stop="getCaptcha" class="code-img" :src="codeImg" /> -->
                  <div class="code-img" @click.stop="getCaptcha">
                    <el-image class="w-full h-full" :src="codeImg" />
                  </div>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </div>
      </el-tabs>

      <el-col :span="24" class="px-[10px] mt-[-13px] mb-[-20px]">
        <el-form-item>
          <el-row justify="space-between" style="width: 100%">
            <el-col :span="6">
              <el-checkbox v-model="loginData.loginForm.rememberMe">
                {{ t('login.remember') }}
              </el-checkbox>
            </el-col>
            <!-- 忘记密码功能暂时屏蔽 -->
            <!-- <el-col :offset="6" :span="12">
              <el-link style="float: right" type="info" @click="changeFormType">{{
                t('login.forgetPassword')
              }}</el-link>
            </el-col> -->
          </el-row>
        </el-form-item>
      </el-col>

      <el-col :span="24" class="px-[10px]">
        <el-form-item>
          <GoldButton
            :loading="loginLoading"
            :title="t('login.login')"
            class="w-[100%]"
            type="primary"
            @click="getCode()"
          />
        </el-form-item>
      </el-col>
      <Verify
        ref="verify"
        :captchaType="captchaType"
        :imgSize="{ width: '400px', height: '200px' }"
        mode="pop"
        @success="handleLogin"
        v-if="loginData.captchaEnable !== 'false'"
      />

      <!-- 其他登录方式注释 -->
      <el-divider content-position="center" border-style="dashed" class="w-[80%]">
        {{ t('login.otherLogin') }}
      </el-divider>

      <div
        class="w-full text-center text-[14px] text-[#a8abb2] flex flex-row justify-center items-center"
      >
        <template v-if="tabName == 'ramLogin'">
          <div
            v-if="
              loginMethodsSet.has(LoginMethodsEnum.MAILPWD) ||
              loginMethodsSet.has(LoginMethodsEnum.MOBILEPWD)
            "
          >
            <span class="cursor-pointer hover:text-client" @click="tabChange('pwdLogin')">
              {{ t('sys.login.pwdLogin') }}
            </span>
          </div>
          <el-divider direction="vertical" />

          <div
            v-if="
              loginMethodsSet.has(LoginMethodsEnum.MAIL) ||
              loginMethodsSet.has(LoginMethodsEnum.MOBILE)
            "
          >
            <span
              class="cursor-pointer hover:text-client"
              @click="tabChange('verificationCodeLogin')"
            >
              {{ t('sys.login.verLogin') }}
            </span>
          </div>
        </template>
        <div v-if="tabName !== 'ramLogin' && loginMethodsSet.has(LoginMethodsEnum.ACCOUNTPWD)">
          <span
            class="cursor-pointer hover:text-client"
            @click="tabChange('ramLogin')"
            v-if="loginMethodsSet.has(LoginMethodsEnum.ACCOUNTPWD)"
          >
            {{ t('sys.login.ramLogin') }}
          </span>
        </div>

        <div
          class="flex justify-center items-center"
          @click="bindThirdplatformOAuth2(34, true, true)"
          v-if="loginMethodsSet.has(LoginMethodsEnum.OA2GOOGLE)"
        >
          <el-divider direction="vertical" />

          <Icon
            icon="svg-icon:login-google"
            class="absolute right-0 cursor-pointer hover:text-client !text-xl"
          />
          <span class="cursor-pointer hover:text-client ml-1">
            {{ t('login.thirdplatformLoginGoogle') }}
          </span>
        </div>
      </div>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
defineOptions({
  name: 'LoginForm'
})

import LoginFormTitle from './LoginFormTitle.vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

import * as authUtil from '@/utils/auth'
import { usePermissionStore } from '@/store/modules/permission'
import * as LoginApi from '@/api/login'
import {
  useFormValid,
  useLoginState,
  useFormValidField,
  useValidator,
  useLoginMethods,
  LoginMethodsEnum,
  useMoblieOrEmail,
  tabNameType,
  loginWayType,
  LOGIN_TYPE
} from './useLogin'
import { getVerify } from '@/api/login'
import { useRoute } from 'vue-router'

import { encrypt } from '@/utils/jsencrypt'
import { aesEncrypt, getAesKey } from '@/utils/ase'

import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

//单点登录
import { useSsoStore } from '@/store/modules/sso'
const ssoStore = useSsoStore()

const { t, ifEn } = useI18n()

const message = useMessage()

const formLogin = ref()
const { validForm } = useFormValid(formLogin)

const loginData = ref({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    // tenantName: '',
    account: '', // 邮箱/手机账号
    username: '',
    password: '',
    captchaVerification: '',
    rememberMe: false,
    captchaKey: '',
    code: '',
    tenantId: ''
  }
})
defineExpose({
  loginData
})

// const { validFormField } = useFormValidField(formLogin, 'tenantName')

const { currentRoute, push } = useRouter()
const { query } = useRoute()
// 登录后跳转地址
const redirect = ref<string>('')
watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    redirect.value = route?.query?.redirect as string
  },
  {
    immediate: true
  }
)

const permissionStore = usePermissionStore()

const loginLoading = ref(false)
const captchaType = ref('blockPuzzle') // blockPuzzle 滑块 clickWord 点击文字

const { setLoginState, getLoginState } = useLoginState()
onMounted(() => {
  setLoginState(LOGIN_TYPE.LOGIN)
})
const getShow = computed(() => unref(getLoginState) === LOGIN_TYPE.LOGIN)

const tabName = ref<tabNameType>('pwdLogin')
// 切换tab，切换时给一个默认值
const tabChange = (name: tabNameType) => {
  tabName.value = name
  nextTick(() => {
    // for (let key in loginData.value.loginForm) {
    //   formLogin.value.validateField(`${key}`)
    // }
    // formLogin.value.resetFields()
  })
}

const loginWayMap = {
  pwdLogin: {
    email: 'mailPwd',
    mobile: 'mobilePwd'
  },
  verificationCodeLogin: {
    email: 'mail',
    mobile: 'mobile'
  },
  ramLogin: 'ram'
}

const loginWay = ref<loginWayType>('mail')
watch(
  [() => tabName.value, () => loginData.value.loginForm.account],
  (newVal, oldVal) => {
    if (newVal[0] == 'ramLogin') {
      loginWay.value = 'ram'
    } else {
      console.log(loginWayMap[tabName.value])
      console.log(useMoblieOrEmail(newVal[1]))
      if (useMoblieOrEmail(newVal[1]) === '') {
        return
      }

      loginWay.value = loginWayMap[tabName.value][useMoblieOrEmail(newVal[1])]
    }
  },
  {
    deep: true,
    immediate: true
  }
)

//动态获取登录方式
const { ifSocialLogin, loginMethodsSet, getLoginMethodsAPi } = useLoginMethods()
onMounted(() => {
  getLoginMethodsAPi()
})

//校验规则
let { LoginRules } = useValidator(loginMethodsSet.value, tabName, loginWay)

//第三方平台oauth2登录
import useBindThirdplatformOAuth2 from '@/views/Profile/common/useBindThirdplatformOAuth2'

const { bindThirdplatformOAuth2 } = useBindThirdplatformOAuth2()

/**************************************** 邮箱和手机号验证码发送逻辑 start ***********************************************/
//图片验证码
const codeImg = ref('')
const captchaKey = ref('') //登录获取验证码所需要的key，用于确认是哪一张验证码

/**
 * 获取图片验证码
 *
 * @returns 无返回值，将获取到的验证码图片和key赋值给对应的响应式变量
 */
const getCaptcha = async () => {
  const res = await getVerify()
  if (!res) {
    return
  }
  codeImg.value = res?.imageVerificationCodeImg
  captchaKey.value = res?.imageVerificationCodeId
}
onMounted(() => {
  getCaptcha()
})

const defaultStr = ref(t('sys.login.sendVer'))
const loadingMail = ref(false)
const mailTime: Ref<string | number> = ref(defaultStr.value)
/**
 * 获取邮件验证码
 *
 * @returns 无返回值
 */
const getMailCode = async () => {
  loadingMail.value = true
  const { validFormField } = useFormValidField(formLogin, 'mail')
  await validFormField()
  try {
    const res = await LoginApi.createAndSendVerificationCode({
      sourceType: 20,
      scene: 10,
      source: loginData.value.loginForm.account
    })
    if (res.code !== 0) {
      return
    }
    ElMessage.success(t('sys.login.sendSuccess'))
    if (mailTime.value === defaultStr.value) {
      mailTime.value = 60
      wsCache.set('mailTime', mailTime.value)
    }
  } catch (e) {
    // ElMessage.error(t('sys.login.sendFail'))
  } finally {
    loadingMail.value = false
  }
}

watch(
  () => mailTime.value,
  () => {
    if (mailTime.value !== defaultStr.value) {
      if (mailTime.value === 1) {
        setTimeout(() => {
          mailTime.value = defaultStr.value
          wsCache.delete('mailTime')
        }, 1000)
      } else {
        setTimeout(() => {
          mailTime.value = mailTime.value - 1
          wsCache.set('mailTime', mailTime.value)
        }, 1000)
      }
    }
  }
)

const loadingPhone = ref(false)
const phoneTime: Ref<string | number> = ref(defaultStr.value)
/**
 * 获取手机验证码
 *
 * @returns 无返回值
 */
const getPhoneCode = async () => {
  loadingPhone.value = true
  const { validFormField } = useFormValidField(formLogin, 'tel')
  await validFormField()
  try {
    const res = await LoginApi.createAndSendVerificationCode({
      sourceType: 10,
      scene: 10,
      source: loginData.value.loginForm.account
    })
    if (res.code !== 0) {
      return
    }
    ElMessage.success(t('sys.login.sendSuccess'))
    if (phoneTime.value === defaultStr.value) {
      phoneTime.value = 60
      wsCache.set('phoneTime', phoneTime.value)
    }
  } catch (e) {
    // ElMessage.error(t('sys.login.sendFail'))
  } finally {
    loadingPhone.value = false
  }
}

watch(
  () => phoneTime.value,
  () => {
    if (phoneTime.value !== defaultStr.value) {
      if (phoneTime.value === 1) {
        setTimeout(() => {
          phoneTime.value = defaultStr.value
          wsCache.delete('phoneTime')
        }, 1000)
      } else {
        setTimeout(() => {
          phoneTime.value = phoneTime.value - 1
          wsCache.set('phoneTime', phoneTime.value)
        }, 1000)
      }
    }
  }
)

// 获取倒计时的缓存
const getTimeCache = () => {
  if (wsCache.get('mailTime')) {
    mailTime.value = wsCache.get('mailTime')
  }
  if (wsCache.get('phoneTime')) {
    phoneTime.value = wsCache.get('phoneTime')
  }
}
onMounted(() => {
  getTimeCache()
})

/**************************************** 邮箱和手机号验证码发送逻辑 end ***********************************************/

// 忘记密码
const changeFormType = async () => {
  if (!authUtil.getTenantId() && query?.tenantId) {
    authUtil.setTenantId(query?.tenantId as string)
  }
  emit('changeFormType', LOGIN_TYPE.FORGET_PASSWORD)
}

// 获取验证码
const verify = ref()
const getCode = async () => {
  // 情况一，未开启：则直接登录
  if (loginData.value.captchaEnable === 'false') {
    await handleLogin({})
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    // 弹出验证码
    verify.value.show()
  }
}

// 记住我
const getCookie = () => {
  const loginForm = authUtil.getLoginForm()
  if (loginForm) {
    loginData.value.loginForm = {
      ...loginData.value.loginForm,
      account: loginForm.account ? loginForm.account : loginData.value.loginForm.account,
      username: loginForm.username ? loginForm.username : loginData.value.loginForm.username,
      password: loginForm.password ? loginForm.password : loginData.value.loginForm.password,
      rememberMe: loginForm.rememberMe ? true : false,
      tenantId: loginForm.tenantId ? loginForm.tenantId : loginData.value.loginForm.tenantId
    }
  }
}
onMounted(() => {
  getCookie()
})

const emit = defineEmits(['changeFormType'])
// 登录
let clientId = import.meta.env.VITE_APP_CLIENT_ID

const handleLogin = async (params) => {
  // let oldPassword = ''

  // 上保险，防止获取不到路由的租户id
  // if (!authUtil.getTenantId() && query?.tenantId) {
  //   authUtil.setTenantId(query?.tenantId as string)
  // }

  const data = await validForm()
  if (!data) {
    loginLoading.value = false
    return
  }

  loginLoading.value = true
  try {
    // 优先使用输入，如果不是输入则使用滑动验证
    loginData.value.loginForm.captchaVerification = loginData.value.loginForm.captchaVerification
      ? loginData.value.loginForm.captchaVerification
      : params.captchaVerification

    loginData.value.loginForm.captchaKey = captchaKey.value
    // oldPassword = loginData.value.loginForm?.password || ''

    let obj: LoginApi.AuthV2LoginReqVO<any> = {
      loginMethod: 0,
      payload: '',
      sk: ''
      // imageVerificationCodeId: '',
      // imageVerificationCode: ''
    }
    //payload的加密处理
    let payloadData:
      | LoginApi.MailCodeLoginPayload
      | LoginApi.MailLoginPayload
      | LoginApi.SocialLoginPayload
      | LoginApi.TelLoginPayload
      | LoginApi.TelSmsLoginPayload
      | LoginApi.UsernameLoginPayload = {} as any
    let publicKey = ''

    const aesKey = getAesKey()

    publicKey = await LoginApi.getEncodeKey()

    if (loginWay.value === 'ram') {
      //账号登录
      obj.loginMethod = 10
      payloadData as LoginApi.UsernameLoginPayload
      payloadData = {
        username: loginData.value.loginForm.username,
        password: loginData.value.loginForm.password,
        tenantId: loginData.value.loginForm.tenantId,

        clientId
      }
      obj.imageVerificationCode = loginData.value.loginForm.captchaVerification
      obj.imageVerificationCodeId = loginData.value.loginForm.captchaKey
    }

    if (loginWay.value === 'mobile') {
      //手机验证码
      obj.loginMethod = 50
      payloadData as LoginApi.TelSmsLoginPayload
      payloadData = {
        tel: loginData.value.loginForm.account,
        code: loginData.value.loginForm.code,
        clientId
      }
    }
    if (loginWay.value === 'mail') {
      //邮箱验证码
      obj.loginMethod = 30

      payloadData as LoginApi.MailCodeLoginPayload
      payloadData = {
        mail: loginData.value.loginForm.account,
        code: loginData.value.loginForm.code,
        clientId
      }
    }
    if (loginWay.value === 'mailPwd') {
      //邮箱密码
      obj.loginMethod = 20
      payloadData as LoginApi.MailLoginPayload
      payloadData = {
        mail: loginData.value.loginForm.account,
        password: loginData.value.loginForm.password,
        clientId
      }
      obj.imageVerificationCodeId = loginData.value.loginForm.captchaKey
      obj.imageVerificationCode = loginData.value.loginForm.captchaVerification
    }
    if (loginWay.value === 'mobilePwd') {
      //手机密码
      obj.loginMethod = 40
      payloadData as LoginApi.TelLoginPayload
      payloadData = {
        tel: loginData.value.loginForm.account,
        password: loginData.value.loginForm.password,
        clientId
      }
      obj.imageVerificationCodeId = loginData.value.loginForm.captchaKey
      obj.imageVerificationCode = loginData.value.loginForm.captchaVerification
    }

    obj.payload = aesEncrypt(JSON.stringify(payloadData), aesKey)
    obj.sk = encrypt(aesKey, publicKey) as string

    const res = await LoginApi.v2login(obj)

    if (!res) {
      return
    }

    // const loginUserInfo = await LoginApi.getLoginUserInfo()
    authUtil.setTenantId(res.loginInfo?.tenantId)

    ElLoading.service({
      lock: true,
      text: t('login.loadingSystem'),
      background: 'rgba(0, 0, 0, 0.7)'
    })
    if (loginData.value.loginForm.rememberMe) {
      authUtil.setLoginForm({ ...loginData.value.loginForm })
    } else {
      authUtil.removeLoginForm()
    }

    // 查看当前账户，是否从来没有重置过密码，产品要求必须要重置过密码才能使用其他功能
    if (res.loginInfo.loginFirstTime) {
      sessionStorage.setItem('firstLoginInfo', JSON.stringify({ ...res }))
      emit('changeFormType', LOGIN_TYPE.RESET_PASSWORD)
      loginLoading.value = false
      return
    }

    // 删除倒计时的缓存
    wsCache.delete('phoneTime')
    wsCache.delete('mailTime')

    authUtil.setToken(res)

    if (!redirect.value) {
      redirect.value = '/HomePage'
    }
    // 判断是否为SSO登录
    if (redirect.value.indexOf('sso') !== -1) {
      //单点登录获取授权码后跳转
      let clientId = redirect.value.split('client_id=')[1] as string
      let redirectUri = query.redirect_uri as string
      ssoStore.ssoAuth(clientId, redirectUri)
      // window.location.href = window.location.href.replace('/home?redirect=', '')
    } else {
      push({ path: redirect.value || permissionStore.addRouters[0].path })
    }
  } catch (error) {
    loginLoading.value = false
    // message.error('登录失败,请联系管理员!' + error)
    // 删除倒计时的缓存
    wsCache.delete('phoneTime')
    wsCache.delete('mailTime')
  } finally {
    getCaptcha()

    setTimeout(() => {
      const loadingInstance = ElLoading.service()
      loadingInstance.close()
    }, 400)
  }
}
/**
 * 校验表单项或整个表单
 *
 * @param val 表单项的值，可选
 * @returns 无返回值
 */
const checkValidate = (val?: string) => {
  if (val) {
    const { validFormField } = useFormValidField(formLogin, val)
    validFormField()
  } else {
    const { validForm } = useFormValid(formLogin)
    validForm()
  }
}

// 认证类型
const at = computed(() => {
  return query?.at as string | number
})

const setDefaultType = () => {
  if (at.value == 10) {
    tabName.value = 'verificationCodeLogin'
  }
  if (at.value == 20) {
    tabName.value = 'verificationCodeLogin'
  }
}
onMounted(() => {
  setDefaultType()
})
</script>

<style lang="scss" scoped>
:deep(.anticon) {
  &:hover {
    color: var(--el-color-primary) !important;
  }
}

.login-code {
  width: 100%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    width: 100%;
    max-width: 100px;
    height: auto;
    vertical-align: middle;
  }
}
.code-img {
  width: 127px;
  height: 41px;
  padding: 4px;
}

.code-img-input {
  :deep(.el-input__wrapper) {
    padding-right: 0;
  }
}

.login-ways {
  width: 100%;
  text-align: center;
  color: #00000080;
  font-size: 14px;
  & > span {
    cursor: pointer;
    &:hover {
      color: var(--el-color-primary);
    }
  }
}

.code-warp {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
</style>
