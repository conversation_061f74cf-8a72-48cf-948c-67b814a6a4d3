<!-- 应用发布管理-应用菜单-菜单修改弹出框 -->
<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="上级菜单">
        <el-tree-select
          v-model="formData.parentId"
          :data="menuTree"
          :default-expanded-keys="[0]"
          :props="defaultProps"
          check-strictly
          node-key="id"
        />
      </el-form-item>
      <el-form-item label="菜单名称" prop="name">
        <el-input v-model.trim="formData.name" clearable placeholder="请输入菜单名称" />
      </el-form-item>
      <el-form-item label="菜单编码" prop="code">
        <el-input v-model.trim="formData.code" clearable placeholder="请输入菜单唯一编码" />
      </el-form-item>
      <el-form-item label="业务场景" prop="scene">
        <el-select v-model="formData.scene" placeholder="请选择业务场景" clearable>
          <el-option
            v-for="dict in getStrDictOptions('system_business_scene')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="菜单类型" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio-button
            v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_MENU_TYPE)"
            :key="dict.label"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.type !== 3 && formData.type !== 4" label="菜单图标">
        <div class="flex flex-row">
          <IconSelect v-model="formData.icon" clearable class="flex-1" />
          <el-input v-model="formData.icon" clearable class="flex-1" />
        </div>
      </el-form-item>
      <el-form-item v-if="formData.type !== 3 && formData.type !== 4" label="路由地址" prop="path">
        <template #label>
          <Tooltip
            message="访问的路由地址，如：`user`。如需外网地址时，则以 `http(s)://` 开头"
            titel="路由地址"
          />
        </template>
        <el-input v-model="formData.path" clearable placeholder="请输入路由地址" />
      </el-form-item>
      <el-form-item v-if="formData.type === 2" label="组件名字" prop="componentName">
        <el-input
          v-model.trim="formData.componentName"
          clearable
          placeholder="例如说：SystemUser"
        />
      </el-form-item>
      <el-form-item v-if="formData.type === 2" label="组件地址" prop="component">
        <el-input
          v-model.trim="formData.component"
          clearable
          placeholder="例如说：system/user/index"
        />
      </el-form-item>

      <el-form-item v-if="formData.type === 2" label="激活菜单" prop="activeMenu">
        <template #label>
          <Tooltip message="显示高亮的路由路径" titel="激活菜单" />
        </template>
        <el-input
          v-model="formData.activeMenu"
          clearable
          placeholder="例如说：/example/example-page"
        />
      </el-form-item>
      <el-form-item v-if="formData.type !== 1" label="权限标识" prop="permission">
        <template #label>
          <Tooltip
            message="Controller 方法上的权限字符，如：@PreAuthorize(`@ss.hasPermission('system:user:list')`)"
            titel="权限标识"
          />
        </template>
        <el-input v-model="formData.permission" clearable placeholder="请输入权限标识" />
      </el-form-item>

      <el-form-item
        v-if="formData.type === 3 || formData.type === 4"
        label="接口路径"
        prop="requestUri"
        :required="formData.type === 4"
      >
        <el-input v-model="formData.requestUri" clearable placeholder="请输入接口路径" />
      </el-form-item>
      <el-form-item
        v-if="formData.type === 3 || formData.type === 4"
        label="接口唯一标识"
        prop="requestKey"
      >
        <el-input v-model="formData.requestKey" clearable placeholder="请输入接口唯一标识" />
      </el-form-item>
      <!-- <el-form-item
        v-if="formData.type === 1 || formData.type === 2"
        label="唯一标识"
        prop="requestKey"
      >
        <el-input v-model="formData.requestKey" clearable placeholder="请输入唯一标识" />
      </el-form-item> -->
      <el-form-item label="显示排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" clearable controls-position="right" />
      </el-form-item>
      <el-form-item label="描述" prop="requestKey">
        <el-input v-model="formData.requestKey" type="textarea" placeholder="请输入描述" />
      </el-form-item>
      <el-form-item label="菜单状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.label"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="formData.type !== 3 && formData.type !== 4"
        label="显示状态"
        prop="visible"
      >
        <template #label>
          <Tooltip message="选择隐藏时，路由将不会出现在侧边栏，但仍然可以访问" titel="显示状态" />
        </template>
        <el-radio-group v-model="formData.visible">
          <el-radio key="true" :label="true" border>显示</el-radio>
          <el-radio key="false" :label="false" border>隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="formData.type !== 3 && formData.type !== 4"
        label="总是显示"
        prop="alwaysShow"
      >
        <template #label>
          <Tooltip
            message="选择不是时，当该菜单只有一个子菜单时，不展示自己，直接展示子菜单"
            titel="总是显示"
          />
        </template>
        <el-radio-group v-model="formData.alwaysShow">
          <el-radio key="true" :label="true" border>总是</el-radio>
          <el-radio key="false" :label="false" border>不是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.type === 2" label="缓存状态" prop="keepAlive">
        <template #label>
          <Tooltip
            message="选择缓存时，则会被 `keep-alive` 缓存，必须填写「组件名称」字段"
            titel="缓存状态"
          />
        </template>
        <el-radio-group v-model="formData.keepAlive">
          <el-radio key="true" :label="true" border>缓存</el-radio>
          <el-radio key="false" :label="false" border>不缓存</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 接口TODO: start -->
      <el-form-item v-if="formData.type === 4" label="接口说明" prop="apiDocDescribe">
        <el-input v-model="formData.apiDocDescribe" clearable placeholder="请输入接口说明" />
      </el-form-item>
      <el-form-item v-if="formData.type === 4" label="请求方式" prop="apiDocMethod">
        <el-input
          v-model="formData.apiDocMethod"
          clearable
          placeholder="请输入请求方式，例如POST/GET"
        />
      </el-form-item>
      <el-form-item v-if="formData.type === 4" label="Header参数" prop="apiDocHeader">
        <el-input v-model="formData.apiDocHeader" placeholder="请输入Header参数" disabled>
          <template #append>
            <el-button
              :icon="Edit"
              type="primary"
              @click="handleEditJson('apiDocHeader', 'Header参数', 'editTable')"
          /></template>
        </el-input>
        <!-- <el-input v-model="formData.apiDocHeader" clearable placeholder="请输入Header参数" /> -->
      </el-form-item>
      <el-form-item v-if="formData.type === 4" label="Body参数" required>
        <el-col :span="10">
          <el-form-item prop="apiDocBody">
            <el-input v-model="formData.apiDocBody" placeholder="请输入Body参数" disabled>
              <template #append>
                <el-button
                  :icon="Edit"
                  type="primary"
                  @click="handleEditJson('apiDocBody', 'Body参数', 'editTable')"
              /></template>
            </el-input>
            <!-- <el-input
              type="textarea"
              v-model="formData.apiDocBody"
              clearable
              placeholder="请输入Body参数"
            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="14" style="display: flex; align-items: center">
          <span style="margin: 0 10px">示例</span>
          <el-form-item prop="apiDocBodySample" style="flex: 1">
            <el-input v-model="formData.apiDocBodySample" placeholder="请输入Body参数示例" disabled>
              <template #append>
                <el-button
                  :icon="Edit"
                  type="primary"
                  @click="handleEditJson('apiDocBodySample', 'Body参数示例', 'editJson')"
              /></template>
            </el-input>
            <!-- <el-input
              type="textarea"
              v-model="formData.apiDocBodySample"
              clearable
              placeholder="请输入Body参数示例"
            /> -->
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item v-if="formData.type === 4" label="成功状态码" required>
        <el-col :span="10">
          <el-form-item prop="apiDocSuccessCode">
            <el-input
              v-model="formData.apiDocSuccessCode"
              clearable
              placeholder="请输入成功状态码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="14" style="display: flex; align-items: center">
          <span style="margin: 0 10px">内容格式</span>
          <el-form-item prop="apiDocSuccessFormat" style="flex: 1">
            <el-input
              v-model="formData.apiDocSuccessFormat"
              clearable
              placeholder="请输入成功返回的内容格式，如：JSON"
            />
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item v-if="formData.type === 4" label="成功数据结构" required>
        <el-col :span="10">
          <el-form-item prop="apiDocSuccessStruct">
            <el-input
              v-model="formData.apiDocSuccessStruct"
              placeholder="请输入成功返回的数据结构"
              disabled
            >
              <template #append>
                <el-button
                  :icon="Edit"
                  type="primary"
                  @click="handleEditJson('apiDocSuccessStruct', '成功返回的数据结构', 'editTable')"
              /></template>
            </el-input>
            <!-- <el-input
              type="textarea"
              v-model="formData.apiDocSuccessStruct"
              clearable
              placeholder="请输入成功返回的数据结构"
            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="14" style="display: flex; align-items: center">
          <span style="margin: 0 10px">示例</span>
          <el-form-item prop="apiDocSuccessStructSample" style="flex: 1">
            <el-input
              v-model="formData.apiDocSuccessStructSample"
              placeholder="请输入成功数据示例"
              disabled
            >
              <template #append>
                <el-button
                  :icon="Edit"
                  type="primary"
                  @click="handleEditJson('apiDocSuccessStructSample', '成功数据示例', 'editJson')"
              /></template>
            </el-input>
            <!-- <el-input
              type="textarea"
              v-model="formData.apiDocSuccessStructSample"
              clearable
              placeholder="请输入成功数据示例"
            /> -->
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item v-if="formData.type === 4" label="失败状态码" required>
        <el-col :span="10">
          <el-form-item prop="apiDocFailedCode">
            <el-input
              v-model="formData.apiDocFailedCode"
              clearable
              placeholder="请输入失败状态码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="14" style="display: flex; align-items: center">
          <span style="margin: 0 10px">内容格式</span>
          <el-form-item prop="apiDocFailedFormat" style="flex: 1">
            <el-input
              v-model="formData.apiDocFailedFormat"
              clearable
              placeholder="请输入失败返回的内容格式，如：JSON"
            />
          </el-form-item>
        </el-col>
      </el-form-item>

      <el-form-item v-if="formData.type === 4" label="失败数据结构" required>
        <el-col :span="10">
          <el-form-item prop="apiDocFailedStruct">
            <el-input
              v-model="formData.apiDocFailedStruct"
              placeholder="请输入失败返回的数据结构"
              disabled
            >
              <template #append>
                <el-button
                  :icon="Edit"
                  type="primary"
                  @click="handleEditJson('apiDocFailedStruct', '失败返回的数据结构', 'editTable')"
              /></template>
            </el-input>
            <!-- <el-input
              type="textarea"
              v-model="formData.apiDocFailedStruct"
              clearable
              placeholder="请输入失败返回的数据结构"
            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="14" style="display: flex; align-items: center">
          <span style="margin: 0 10px">示例</span>
          <el-form-item prop="apiDocFailedStructSample" style="flex: 1">
            <el-input
              v-model="formData.apiDocFailedStructSample"
              placeholder="请输入失败示例"
              disabled
            >
              <template #append>
                <el-button
                  :icon="Edit"
                  type="primary"
                  @click="handleEditJson('apiDocFailedStructSample', '失败示例', 'editJson')"
              /></template>
            </el-input>
            <!-- <el-input
              type="textarea"
              v-model="formData.apiDocFailedStructSample"
              clearable
              placeholder="请输入失败示例"
            /> -->
          </el-form-item>
        </el-col>
      </el-form-item>
      <!-- 接口TODO: end -->
    </el-form>
    <template #footer>
      <el-button @click="copyFormData" v-if="formType == 'detail'" class="float-left">
        复制FormData
      </el-button>
      <el-button @click="handlePaste" v-else class="float-left"> 插入FormData </el-button>

      <el-button
        :disabled="formLoading"
        type="primary"
        @click="submitForm"
        v-if="formType !== 'detail'"
      >
        确 定
      </el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>

    <el-dialog
      v-model="jsonDlgVisable"
      :title="jsonDlgTitle"
      :close-on-click-modal="false"
      width="60%"
      destroy-on-close
    >
      <JsonEditorDlg
        @set-json-data="setJsonData"
        @close-json-dlg="closeJsonDlg"
        :initValue="jsonInitValue"
        :editType="editType"
      />
    </el-dialog>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SystemMenuForm'
})

import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import * as MenuApi from '@/api/system/menu'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { CommonStatusEnum, SystemMenuTypeEnum } from '@/utils/constants'
import { defaultProps, handleTree } from '@/utils/tree'
import { Edit } from '@element-plus/icons-vue'
import JsonEditorDlg from './JsonEditorDlg.vue'
import IconSelect from './IconSelect.vue'
const props = defineProps({
  appId: {
    type: [String, Number],
    default: ''
  }
})

const { wsCache } = useCache()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改；detail - 查看详情
const formData = ref({
  id: 0,
  name: '',
  code: '', //唯一编码
  scene: '', //业务场景
  permission: '',
  type: SystemMenuTypeEnum.DIR,
  sort: Number(undefined),
  parentId: 0,
  path: '',
  icon: '',
  component: '',
  componentName: '',
  activeMenu: '',
  status: CommonStatusEnum.ENABLE,
  visible: true,
  keepAlive: true,
  alwaysShow: false,
  requestUri: undefined,
  requestKey: undefined,
  apiDocDescribe: '', // 接口说明
  apiDocMethod: '', // 请求方式
  apiDocHeader: '', // Header参数
  apiDocBody: '', // Body参数
  apiDocBodySample: '', // Body参数示例
  apiDocSuccessCode: '', // 成功状态
  apiDocSuccessFormat: '', // 成功内容格式
  apiDocSuccessStruct: '', // 成功数据结构
  apiDocSuccessStructSample: '', // 成功数据示例
  apiDocFailedCode: '', // 失败状态
  apiDocFailedFormat: '', // 失败内容格式
  apiDocFailedStruct: '', // 失败数据结构
  apiDocFailedStructSample: '' // 失败示例
})
import type { FormRules } from 'element-plus'
import { useValidator } from '@/hooks/web/useValidator'

const formRules = reactive<FormRules<any>>({
  name: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],
  code: [
    { required: true, message: '菜单编码不能为空', trigger: 'blur' },
    {
      validator: useValidator(/^[A-Za-z0-9]+$/).byPattern,
      message: '请输入英文',
      trigger: 'blur'
    }
  ],
  sort: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }],
  path: [{ required: true, message: '路由地址不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  requestUri: [{ message: '接口路径不能为空', trigger: 'blur' }],
  // requestKey: [{ message: '接口唯一标识不能为空', trigger: 'blur' }],
  apiDocDescribe: [{ required: true, message: '接口说明不能为空', trigger: 'blur' }], // 接口说明
  apiDocMethod: [{ required: true, message: '请求方式不能为空', trigger: 'blur' }], // 请求方式
  apiDocHeader: [{ required: true, message: 'Header参数不能为空', trigger: 'blur' }], // Header参数
  apiDocBody: [{ required: true, message: 'Body参数不能为空', trigger: 'blur' }], // Body参数
  apiDocBodySample: [{ required: true, message: 'Body参数示例不能为空', trigger: 'blur' }], // Body参数示例
  apiDocSuccessCode: [{ required: true, message: '成功状态不能为空', trigger: 'blur' }], // 成功状态
  apiDocSuccessFormat: [{ required: true, message: '成功返回的内容格式不能为空', trigger: 'blur' }], // 成功内容格式
  apiDocSuccessStruct: [{ required: true, message: '成功数据结构不能为空', trigger: 'blur' }], // 成功数据结构
  apiDocSuccessStructSample: [{ required: true, message: '成功数据示例不能为空', trigger: 'blur' }], // 成功数据示例
  apiDocFailedCode: [{ required: true, message: '失败状态不能为空', trigger: 'blur' }], // 失败状态
  apiDocFailedFormat: [{ required: true, message: '失败返回的内容格式不能为空', trigger: 'blur' }], // 失败内容格式
  apiDocFailedStruct: [{ required: true, message: '失败数据结构不能为空', trigger: 'blur' }], // 失败数据结构
  apiDocFailedStructSample: [{ required: true, message: '失败示例不能为空', trigger: 'blur' }] // 失败示例
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number, parentId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  if (parentId) {
    formData.value.parentId = parentId
  }
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await MenuApi.getMenu(id)
    } finally {
      formLoading.value = false
    }
  }
  // 获得菜单列表
  await getTree()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    if (
      formData.value.type === SystemMenuTypeEnum.DIR ||
      formData.value.type === SystemMenuTypeEnum.MENU
    ) {
      if (!isExternal(formData.value.path)) {
        if (formData.value.parentId === 0 && formData.value.path.charAt(0) !== '/') {
          message.error('路径必须以 / 开头')
          return
        } else if (formData.value.parentId !== 0 && formData.value.path.charAt(0) === '/') {
          message.error('路径不能以 / 开头')
          return
        }
      }
    }
    let data = formData.value as unknown as MenuApi.AppMenuVO
    data = { ...data, applicationId: parseInt(props.appId as string) }
    if (formData.value.type !== 3 && formData.value.type !== 4) {
      delete data.requestUri
      delete data.requestKey
    }
    console.log('%c 提交的表单参数', 'color: blue', data)
    // if (true) return
    if (formType.value === 'create') {
      await MenuApi.createAppMenu(data)
      message.success(t('common.createSuccess'))
    } else {
      await MenuApi.updateMenu(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
    // 清空，从而触发刷新
    wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
  }
}

/** 获取下拉框[上级菜单]的数据  */
const menuTree = ref<Tree[]>([]) // 树形结构
const getTree = async () => {
  menuTree.value = []
  const res = await MenuApi.getMenuList({ applicationId: props.appId })
  let menu: Tree = { id: 0, name: '主类目', children: [] }
  menu.children = handleTree(res)
  menuTree.value.push(menu)
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: 0,
    name: '',
    code: '',
    scene: '',
    permission: '',
    type: SystemMenuTypeEnum.DIR,
    sort: Number(undefined),
    parentId: 0,
    path: '',
    icon: '',
    component: '',
    componentName: '',
    activeMenu: '',
    status: CommonStatusEnum.ENABLE,
    visible: true,
    keepAlive: true,
    alwaysShow: false
  }
  formRef.value?.resetFields()
}

/** 判断 path 是不是外部的 HTTP 等链接 */
const isExternal = (path: string) => {
  return /^(https?:|mailto:|tel:)/.test(path)
}

const jsonDlgVisable = ref(false)
const jsonDlgType = ref('') // 用于记录key，方便赋值
const jsonInitValue = ref<any>(null)
const jsonDlgTitle = ref('')
const editType = ref('')
/** 点击编辑，弹出json输入框 */
const handleEditJson = (type, title, editTypeStr) => {
  jsonDlgType.value = type
  jsonDlgTitle.value = title
  jsonDlgVisable.value = true
  jsonInitValue.value = formData.value[type]
  editType.value = editTypeStr
}

/** json输入框完成输入 */
const setJsonData = (data) => {
  formData.value[jsonDlgType.value] = data
  closeJsonDlg()
}

/** 关闭json输入框 */
const closeJsonDlg = () => {
  jsonDlgVisable.value = false
  jsonDlgType.value = ''
  jsonDlgTitle.value = ''
  editType.value = ''
}

//复制menue数据
import { useClipboard } from '@vueuse/core'

const copyFormData = async (text: string) => {
  const { copy, copied, isSupported } = useClipboard({ source: JSON.stringify(formData.value) })
  if (!isSupported) {
    message.error(t('common.copyError'))
  } else {
    await copy()
    if (unref(copied)) {
      message.success(t('common.copySuccess'))
    }
  }
}

const handlePaste = (event) => {
  // 阻止默认粘贴行为

  event.preventDefault()
  navigator.clipboard
    .readText()
    .then((text) => {
      // pastedText.value = text
      console.log('粘贴的内容:', text)
      // formData.value = JSON.parse(text)
      console.log(JSON.parse(text))
      const data = JSON.parse(text)
      if (data.name && data.code) {
        data.id = formData.value.id
        data.parentId = formData.value.parentId
        data.applicationId = parseInt(props.appId as string)

        formData.value = data
      }
    })
    .catch((error) => {
      console.error('读取粘贴内容失败:', error)
      dialogVisible.value = false
    }) // 获取粘贴的内容
}
</script>
