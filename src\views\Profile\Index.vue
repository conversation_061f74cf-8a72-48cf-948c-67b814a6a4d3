<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: <PERSON>J<PERSON>
 * @LastEditTime: 2023-12-19 09:17:47
 * @Description: 
-->
<template>
  <div class="flex">
    <el-card class="w-1/3 user" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>{{ t('profile.user.title') }}</span>
        </div>
      </template>
      <ProfileUser />
    </el-card>
    <el-card class="w-2/3 user ml-3" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>{{ t('profile.info.title') }}</span>
        </div>
      </template>
      <div>
        <el-tabs v-model="activeName" tab-position="top" style="height: 400px" class="profile-tabs">
          <el-tab-pane :label="t('profile.info.basicInfo')" name="basicInfo">
            <BasicInfo />
          </el-tab-pane>
          <el-tab-pane :label="t('profile.info.resetPwd')" name="resetPwd">
            <ResetPwd />
          </el-tab-pane>

          <el-tab-pane :label="t('profile.info.userSocial')" name="userSocial">
            <UserSocial />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'Profile'
})

import { BasicInfo, ProfileUser, ResetPwd, UserSocial } from './components/index'
const { t } = useI18n()

const activeName = ref('basicInfo')
</script>
<style scoped>
.user {
  max-height: 960px;
  padding: 15px 20px 20px 20px;
}
.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
}
:deep(.el-card .el-card__header, .el-card .el-card__body) {
  padding: 15px !important;
}
.profile-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-weight: 600;
}
.el-tabs--left .el-tabs__content {
  height: 100%;
}
</style>
