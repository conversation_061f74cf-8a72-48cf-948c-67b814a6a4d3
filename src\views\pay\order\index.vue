<template>
  <UmvContent>
    <UmvQuery v-model="queryParams" :opts="queryOpts" @check="handleQuery" @reset="resetQuery" />

    <UmvTable v-loading="loading" :data="list" :columns="columns" ref="tableRef">
      <template #tools>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:tenant:export']"
        >
          <icon-ep-download style="font-size: 12px" class="mr-5px" /> {{ t('common.export') }}
        </el-button>
      </template>

      <template #pagination>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </UmvTable>

    <!-- 表单弹窗：预览 -->
    <OrderDetail ref="detailRef" @success="getList" />

    <!-- 回调状态详情 -->
    <bizNotifyDetail ref="bizNotifyDetailRef" />
  </UmvContent>
</template>
<script lang="tsx" setup>
import { PayChannelEnum } from '@/utils/constants'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as MerchantApi from '@/api/pay/merchant'
import * as OrderApi from '@/api/pay/order'
import OrderDetail from './OrderDetail.vue'
import bizNotifyDetail from './bizNotifyDetail.vue'
import download from '@/utils/download'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { checkPermi } from '@/utils/permission'
import { UmvContent } from '@/components/UmvContent'

const { t } = useI18n() // 国际化

defineOptions({ name: 'PayOrder' })

const message = useMessage() // 消息弹窗

const loading = ref(false) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  merchantId: undefined,
  appId: undefined,
  channelId: undefined,
  channelCode: undefined,
  merchantOrderId: undefined,
  subject: undefined,
  body: undefined,
  notifyUrl: undefined,
  notifyStatus: undefined,
  amount: undefined,
  channelFeeRate: undefined,
  channelFeeAmount: undefined,
  status: undefined,
  userIp: undefined,
  successExtensionId: undefined,
  refundStatus: undefined,
  refundTimes: undefined,
  refundAmount: undefined,
  channelUserId: undefined,
  channelOrderNo: undefined,
  expireTime: [],
  successTime: [],
  notifyTime: [],
  createTime: []
})

// 查询选项配置
const queryOpts = ref<Record<string, QueryOption>>({
  merchantId: {
    label: t('pay.order.merchantId'),
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.merchantId}
        placeholder={t('pay.order.merchantIdPlaceholder')}
        clearable
      >
        {merchantList.value.map((item) => (
          <el-option key={item.id} label={item.name} value={item.id} />
        ))}
      </el-select>
    )
  },
  appId: {
    label: t('pay.order.appId'),
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.appId}
        placeholder={t('pay.order.appIdPlaceholder')}
        clearable
      >
        {appList.value.map((item) => (
          <el-option key={item.id} label={item.name} value={item.id} />
        ))}
      </el-select>
    )
  },
  channelCode: {
    label: t('pay.order.channelCode'),
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.channelCode}
        placeholder={t('pay.order.channelCodePlaceholder')}
        clearable
      >
        {getStrDictOptions(DICT_TYPE.PAY_CHANNEL_CODE_TYPE).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  merchantOrderId: {
    label: t('pay.order.merchantOrderId'),
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.merchantOrderId}
        placeholder={t('pay.order.merchantOrderIdPlaceholder')}
        clearable
        onKeyup={(e) => e.key === 'Enter' && handleQuery()}
      />
    )
  },
  channelOrderNo: {
    label: t('pay.order.channelOrderNo'),
    defaultVal: undefined,
    controlRender: () => (
      <el-input
        v-model={queryParams.value.channelOrderNo}
        placeholder={t('pay.order.channelOrderNoPlaceholder')}
        clearable
        onKeyup={(e) => e.key === 'Enter' && handleQuery()}
      />
    )
  },
  status: {
    label: t('pay.order.status'),
    defaultVal: undefined,
    controlRender: () => (
      <el-select
        v-model={queryParams.value.status}
        placeholder={t('pay.order.statusPlaceholder')}
        clearable
      >
        {getIntDictOptions(DICT_TYPE.PAY_ORDER_STATUS).map((item) => (
          <el-option key={item.value} label={item.label} value={item.value} />
        ))}
      </el-select>
    )
  },
  createTime: {
    label: t('pay.order.createTime'),
    defaultVal: [],
    controlRender: () => (
      <el-date-picker
        v-model={queryParams.value.createTime}
        type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        start-placeholder={t('common.startDateText')}
        end-placeholder={t('common.endDateText')}
        default-time={[new Date('1 00:00:00'), new Date('1 23:59:59')]}
      />
    )
  }
})

const queryFormRef = ref() // 搜索的表单
const tableRef = ref()
const exportLoading = ref(false) // 导出等待
const merchantList = ref<any>([]) // 商户列表
const appList = ref<any>([]) // 支付应用列表集合

const bizNotifyDetailRef = ref()

// 表格列配置
const columns = ref<TableColumn[]>([
  { prop: 'id', label: t('pay.order.id'), align: 'center' },
  { prop: 'payMerchantName', label: t('pay.order.payMerchantName'), align: 'center', width: '120' },
  { prop: 'appName', label: t('pay.order.appName'), align: 'center', width: '150' },
  {
    prop: 'channelCodeName',
    label: t('pay.order.channelCodeName'),
    align: 'center',
    width: '120',
    renderTemplate: (scope) => (
      <span>{PayChannelEnum[scope.row.channelCode?.toLocaleUpperCase()]?.name}</span>
    )
  },
  {
    prop: 'merchantOrderId',
    label: t('pay.order.merchantOrderId'),
    align: 'center',
    width: '150',
    showOverflowTooltip: true
  },
  { prop: 'subject', label: t('pay.order.subject'), align: 'center', width: '100' },
  {
    prop: 'body',
    label: t('pay.order.body'),
    align: 'center',
    width: '100',
    showOverflowTooltip: true
  },
  {
    prop: 'notifyUrl',
    label: t('pay.order.notifyUrl'),
    align: 'center',
    width: '120',
    showOverflowTooltip: true
  },
  {
    prop: 'notifyStatus',
    label: t('pay.order.notifyStatus'),
    align: 'center',
    renderTemplate: (scope) => (
      <dict-tag type={DICT_TYPE.PAY_ORDER_NOTIFY_STATUS} value={scope.row.notifyStatus} />
    )
  },
  {
    prop: 'payOrder',
    label: t('pay.order.payOrder'),
    width: '280',
    renderTemplate: (scope) => (
      <div>
        <p class="order-font">
          <el-tag>{t('pay.order.merchant')}</el-tag>
          {scope.row.merchantOrderId}
        </p>
        <p class="order-font">
          <el-tag type="warning">{t('pay.order.pay')}</el-tag>
          {scope.row.channelOrderNo}
        </p>
      </div>
    )
  },
  {
    prop: 'amount',
    label: t('pay.order.amount'),
    align: 'center',
    renderTemplate: (scope) => <span>￥{(scope.row.price / 100).toFixed(2)}</span>
  },
  {
    prop: 'channelFeeAmount',
    label: t('pay.order.channelFeeAmount'),
    align: 'center',
    renderTemplate: (scope) => <span>￥{(scope.row.channelFeePrice / 100).toFixed(2)}</span>
  },
  {
    prop: 'refundAmount',
    label: t('pay.order.refundAmount'),
    align: 'center',
    renderTemplate: (scope) => <span>￥{(scope.row.refundPrice / 100).toFixed(2)}</span>
  },
  {
    prop: 'status',
    label: t('pay.order.status'),
    align: 'center',
    renderTemplate: (scope) => (
      <dict-tag type={DICT_TYPE.PAY_ORDER_STATUS} value={scope.row.status} />
    )
  },
  {
    prop: 'bizNotifyStatusName',
    label: t('pay.refund.bizNotifyStatusName'),
    align: 'center',
    renderTemplate: (scope) => (
      <div>
        {scope.row.bizNotifyStatusName && (
          <el-tooltip content="点击查看详情">
            <span style="cursor: pointer" onClick={() => checkBizNotify(scope.row)}>
              {scope.row.bizNotifyStatusName}
            </span>
          </el-tooltip>
        )}
      </div>
    )
  },
  {
    prop: 'createTime',
    label: t('pay.order.createTime'),
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.createTime)}</span>
    )
  },
  {
    prop: 'successTime',
    label: t('pay.order.successTime'),
    align: 'center',
    width: '180',
    renderTemplate: (scope) => (
      <span>{dateFormatter(scope.row, scope.column, scope.row.successTime)}</span>
    )
  },
  {
    prop: 'operation',
    label: t('common.operate'),
    align: 'center',
    fixed: 'right',
    renderTemplate: (scope) => (
      <div>
        {checkPermi(['pay:order:query']) && (
          <el-button type="primary" link onClick={() => openDetail(scope.row.id)}>
            {t('common.detail')}
          </el-button>
        )}
      </div>
    )
  }
])

// 查看回调状态
const checkBizNotify = (row) => {
  bizNotifyDetailRef.value.open(row)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderApi.getOrderPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 重置按钮操作 */
const resetQuery = () => {
  // handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderApi.exportOrder(queryParams.value)
    download.excel(data.data, t('pay.order.payOrder') + '.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 预览详情 */
const detailRef = ref()
const openDetail = (id: number) => {
  detailRef.value.open(id)
}

/** 初始化 **/
import * as AppApi from '@/api/pay/app'

onMounted(async () => {
  await getList()
  // 加载商户列表
  merchantList.value = await MerchantApi.getMerchantListByName()
  // 加载 App 列表
  appList.value = await AppApi.getAppSimpleList()
})
</script>
<style>
.order-font {
  font-size: 12px;
  padding: 2px 0;
}
</style>
