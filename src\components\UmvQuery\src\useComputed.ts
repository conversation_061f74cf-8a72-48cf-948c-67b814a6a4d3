/**
 * UmvQuery组件中使用的计算属性钩子
 * @returns 返回组件需要的计算属性
 */
export function useComputed() {
  /**
   * 获取列数
   * @param colLengthMap 自定义屏幕宽度与列数的映射关系
   * @returns 根据窗口宽度返回合适的列数
   */
  const getColLength = (colLengthMap?: Record<string, number>): number => {
    // 行列数
    const width = window.innerWidth
    let colLength = 4

    // 如果提供了自定义的屏幕宽度与列数映射关系，则使用它
    if (colLengthMap && Object.keys(colLengthMap).length > 0) {
      // 按照屏幕宽度从大到小排序
      const breakpoints = Object.keys(colLengthMap)
        .map(Number)
        .sort((a, b) => b - a)

      // 找到第一个小于或等于当前屏幕宽度的断点
      for (const breakpoint of breakpoints) {
        if (width >= breakpoint) {
          return colLengthMap[breakpoint]
        }
      }
      // 如果没有找到匹配的断点，使用默认值
      return 4
    }

    // 默认的屏幕宽度与列数映射关系
    if (width >= 1920) {
      colLength = 6
    } else if (width >= 1600 && width < 1920) {
      colLength = 5
    } else if (width >= 1280 && width < 1600) {
      colLength = 4
    } else if (width >= 1000 && width < 1280) {
      colLength = 3
    } else if (width > 768 && width < 1000) {
      colLength = 2
    } else if (width <= 768) {
      colLength = 1
    }

    return colLength
  }

  return {
    getColLength
  }
}
