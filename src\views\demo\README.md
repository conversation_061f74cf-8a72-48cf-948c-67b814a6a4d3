# API市场功能订阅模块

> 该功能屏蔽,需要时再开启

菜单详情如下

```javascript
  {
    path: '/systemSubscribeTest',
    component: Layout,
    name: 'systemSubscribeTest',
    meta: {
      alwaysShow: false,
      canTo: true
    },
    children: [
      {
        path: 'systemSubscribe',
        component: () => import('@/views/demo/subscribe/index.vue'),
        name: 'systemSubscribe',
        meta: {
          canTo: true,
          noCache: true,
          noTagsView: false,
          title: '订阅管理'
        }
      }
    ]
  }
