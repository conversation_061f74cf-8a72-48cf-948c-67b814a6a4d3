<template>
  <Dialog
    v-model="dialogVisible"
    :title="t('common.detail')"
    width="60%"
    maxHeight="70vh"
    :scroll="true"
  >
    <vue-json-pretty :data="jsonContent" showIcon showLength />
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'OrderExtensionDetail'
})

const { t } = useI18n() // 国际化
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
/** 打开弹窗 */
import { getOrderExtensionDeatail, OrderExtensionVO } from '@/api/pay/orderExtension'

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单的加载中

let jsonContent = ref<any>('')

const open = async (id: number) => {
  jsonContent.value = '' //先重置数据,避免重复打开弹窗时数据错乱

  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    jsonContent.value = await getOrderExtensionDeatail(id)
    if (jsonContent.value.rawData.body) {
      jsonContent.value.rawData.body = JSON.parse(jsonContent.value.rawData.body)
    }
  } finally {
    detailLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
<style>
.tag-purple {
  color: #722ed1;
  background: #f9f0ff;
  border-color: #d3adf7;
}

.tag-pink {
  color: #eb2f96;
  background: #fff0f6;
  border-color: #ffadd2;
}
</style>
