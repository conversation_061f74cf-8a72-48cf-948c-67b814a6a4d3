/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-04 16:58:51
 * @LastEditors: HoJ<PERSON> <EMAIL>
 * @LastEditTime: 2024-12-16 10:57:04
 * @Description: 代客操作
 */
import request from '@/config/axios'

export type agentOperationDataType = {
  id?: number
  userId?: number | string | string[]
  accessToken?: string
  refreshToken?: string
  proxyTenantId?: string
  customerName?: string
  createTime?: any
}
export type agentOperationQueryDataType = {
  pageNo: number
  pageSize: number
} & agentOperationDataType
/**
 * 获取客户Token列表
 *
 * @param params 请求参数（可选）
 * @returns 返回客户Token列表
 */
export const getCustomerTokenList = async (
  data: agentOperationQueryDataType
): PromiseListWithTotal<agentOperationDataType> => {
  return await request.post({
    url: '/system/oauth2-long-lived-token/page',
    headersType: 'application/json',
    data
  })
}

/**
 * 创建长期访问令牌
 *
 * @param data 请求的数据，类型为agentOperationDataType
 * @returns 返回Promise，解析为任意类型的结果
 */
export const createLongLivedToken = async (data: agentOperationDataType): Promise<any> => {
  return await request.post({
    url: '/system/oauth2-long-lived-token/create',
    headersType: 'application/json',
    data
  })
}
