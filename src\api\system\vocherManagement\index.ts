import request from '@/config/axios'

export const getVocherListApi = (params?) => {
  return request.get({
    url: '/system/api-credential/page',
    params
  })
}

export const getTenantByIdApi = (params?) => {
  return request.get({
    url: '/system/tenant/get',
    params
  })
}

export const newAddCredentialApi = (params?) => {
  return request.postOriginal({
    url: '/system/api-credential/create',
    data: params
  })
}

export const getTenantListApi = (params?) => {
  return request.get({
    url: '/system/tenant-application-subscription/page',
    params
  })
}

// 初始化密钥
export const initCaKeyPhraseApi = (params?) => {
  return request.postOriginal({
    url: `/system/api-credential/init`,
    params
  })
}

// 移交密钥
export const transferPeerKeyApi = (params?) => {
  return request.postOriginal({
    url: `/system/api-credential/transfer`,
    params
  })
}
//修改api凭证
export const updateCredentialApi = (params?) => {
  return request.post({
    url: '/system/api-credential/update',
    data: params
  })
}
